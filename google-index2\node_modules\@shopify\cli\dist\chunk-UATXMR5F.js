import {
  __commonJS,
  __require,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/base64.js
var require_base64 = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/base64.js"(exports) {
    init_cjs_shims();
    var intToCharMap = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");
    exports.encode = function(number) {
      if (0 <= number && number < intToCharMap.length)
        return intToCharMap[number];
      throw new TypeError("Must be between 0 and 63: " + number);
    };
    exports.decode = function(charCode) {
      var bigA = 65, bigZ = 90, littleA = 97, littleZ = 122, zero = 48, nine = 57, plus = 43, slash = 47, littleOffset = 26, numberOffset = 52;
      return bigA <= charCode && charCode <= bigZ ? charCode - bigA : littleA <= charCode && charCode <= littleZ ? charCode - littleA + littleOffset : zero <= charCode && charCode <= nine ? charCode - zero + numberOffset : charCode == plus ? 62 : charCode == slash ? 63 : -1;
    };
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/base64-vlq.js
var require_base64_vlq = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/base64-vlq.js"(exports) {
    init_cjs_shims();
    var base64 = require_base64(), VLQ_BASE_SHIFT = 5, VLQ_BASE = 1 << VLQ_BASE_SHIFT, VLQ_BASE_MASK = VLQ_BASE - 1, VLQ_CONTINUATION_BIT = VLQ_BASE;
    function toVLQSigned(aValue) {
      return aValue < 0 ? (-aValue << 1) + 1 : (aValue << 1) + 0;
    }
    function fromVLQSigned(aValue) {
      var isNegative = (aValue & 1) === 1, shifted = aValue >> 1;
      return isNegative ? -shifted : shifted;
    }
    exports.encode = function(aValue) {
      var encoded = "", digit, vlq = toVLQSigned(aValue);
      do
        digit = vlq & VLQ_BASE_MASK, vlq >>>= VLQ_BASE_SHIFT, vlq > 0 && (digit |= VLQ_CONTINUATION_BIT), encoded += base64.encode(digit);
      while (vlq > 0);
      return encoded;
    };
    exports.decode = function(aStr, aIndex, aOutParam) {
      var strLen = aStr.length, result = 0, shift = 0, continuation, digit;
      do {
        if (aIndex >= strLen)
          throw new Error("Expected more digits in base 64 VLQ value.");
        if (digit = base64.decode(aStr.charCodeAt(aIndex++)), digit === -1)
          throw new Error("Invalid base64 digit: " + aStr.charAt(aIndex - 1));
        continuation = !!(digit & VLQ_CONTINUATION_BIT), digit &= VLQ_BASE_MASK, result = result + (digit << shift), shift += VLQ_BASE_SHIFT;
      } while (continuation);
      aOutParam.value = fromVLQSigned(result), aOutParam.rest = aIndex;
    };
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/util.js
var require_util = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/util.js"(exports) {
    init_cjs_shims();
    function getArg(aArgs, aName, aDefaultValue) {
      if (aName in aArgs)
        return aArgs[aName];
      if (arguments.length === 3)
        return aDefaultValue;
      throw new Error('"' + aName + '" is a required argument.');
    }
    exports.getArg = getArg;
    var urlRegexp = /^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/, dataUrlRegexp = /^data:.+\,.+$/;
    function urlParse(aUrl) {
      var match = aUrl.match(urlRegexp);
      return match ? {
        scheme: match[1],
        auth: match[2],
        host: match[3],
        port: match[4],
        path: match[5]
      } : null;
    }
    exports.urlParse = urlParse;
    function urlGenerate(aParsedUrl) {
      var url = "";
      return aParsedUrl.scheme && (url += aParsedUrl.scheme + ":"), url += "//", aParsedUrl.auth && (url += aParsedUrl.auth + "@"), aParsedUrl.host && (url += aParsedUrl.host), aParsedUrl.port && (url += ":" + aParsedUrl.port), aParsedUrl.path && (url += aParsedUrl.path), url;
    }
    exports.urlGenerate = urlGenerate;
    function normalize(aPath) {
      var path = aPath, url = urlParse(aPath);
      if (url) {
        if (!url.path)
          return aPath;
        path = url.path;
      }
      for (var isAbsolute = exports.isAbsolute(path), parts = path.split(/\/+/), part, up = 0, i = parts.length - 1; i >= 0; i--)
        part = parts[i], part === "." ? parts.splice(i, 1) : part === ".." ? up++ : up > 0 && (part === "" ? (parts.splice(i + 1, up), up = 0) : (parts.splice(i, 2), up--));
      return path = parts.join("/"), path === "" && (path = isAbsolute ? "/" : "."), url ? (url.path = path, urlGenerate(url)) : path;
    }
    exports.normalize = normalize;
    function join(aRoot, aPath) {
      aRoot === "" && (aRoot = "."), aPath === "" && (aPath = ".");
      var aPathUrl = urlParse(aPath), aRootUrl = urlParse(aRoot);
      if (aRootUrl && (aRoot = aRootUrl.path || "/"), aPathUrl && !aPathUrl.scheme)
        return aRootUrl && (aPathUrl.scheme = aRootUrl.scheme), urlGenerate(aPathUrl);
      if (aPathUrl || aPath.match(dataUrlRegexp))
        return aPath;
      if (aRootUrl && !aRootUrl.host && !aRootUrl.path)
        return aRootUrl.host = aPath, urlGenerate(aRootUrl);
      var joined = aPath.charAt(0) === "/" ? aPath : normalize(aRoot.replace(/\/+$/, "") + "/" + aPath);
      return aRootUrl ? (aRootUrl.path = joined, urlGenerate(aRootUrl)) : joined;
    }
    exports.join = join;
    exports.isAbsolute = function(aPath) {
      return aPath.charAt(0) === "/" || urlRegexp.test(aPath);
    };
    function relative(aRoot, aPath) {
      aRoot === "" && (aRoot = "."), aRoot = aRoot.replace(/\/$/, "");
      for (var level = 0; aPath.indexOf(aRoot + "/") !== 0; ) {
        var index = aRoot.lastIndexOf("/");
        if (index < 0 || (aRoot = aRoot.slice(0, index), aRoot.match(/^([^\/]+:\/)?\/*$/)))
          return aPath;
        ++level;
      }
      return Array(level + 1).join("../") + aPath.substr(aRoot.length + 1);
    }
    exports.relative = relative;
    var supportsNullProto = function() {
      var obj = /* @__PURE__ */ Object.create(null);
      return !("__proto__" in obj);
    }();
    function identity(s) {
      return s;
    }
    function toSetString(aStr) {
      return isProtoString(aStr) ? "$" + aStr : aStr;
    }
    exports.toSetString = supportsNullProto ? identity : toSetString;
    function fromSetString(aStr) {
      return isProtoString(aStr) ? aStr.slice(1) : aStr;
    }
    exports.fromSetString = supportsNullProto ? identity : fromSetString;
    function isProtoString(s) {
      if (!s)
        return !1;
      var length = s.length;
      if (length < 9 || s.charCodeAt(length - 1) !== 95 || s.charCodeAt(length - 2) !== 95 || s.charCodeAt(length - 3) !== 111 || s.charCodeAt(length - 4) !== 116 || s.charCodeAt(length - 5) !== 111 || s.charCodeAt(length - 6) !== 114 || s.charCodeAt(length - 7) !== 112 || s.charCodeAt(length - 8) !== 95 || s.charCodeAt(length - 9) !== 95)
        return !1;
      for (var i = length - 10; i >= 0; i--)
        if (s.charCodeAt(i) !== 36)
          return !1;
      return !0;
    }
    function compareByOriginalPositions(mappingA, mappingB, onlyCompareOriginal) {
      var cmp = strcmp(mappingA.source, mappingB.source);
      return cmp !== 0 || (cmp = mappingA.originalLine - mappingB.originalLine, cmp !== 0) || (cmp = mappingA.originalColumn - mappingB.originalColumn, cmp !== 0 || onlyCompareOriginal) || (cmp = mappingA.generatedColumn - mappingB.generatedColumn, cmp !== 0) || (cmp = mappingA.generatedLine - mappingB.generatedLine, cmp !== 0) ? cmp : strcmp(mappingA.name, mappingB.name);
    }
    exports.compareByOriginalPositions = compareByOriginalPositions;
    function compareByGeneratedPositionsDeflated(mappingA, mappingB, onlyCompareGenerated) {
      var cmp = mappingA.generatedLine - mappingB.generatedLine;
      return cmp !== 0 || (cmp = mappingA.generatedColumn - mappingB.generatedColumn, cmp !== 0 || onlyCompareGenerated) || (cmp = strcmp(mappingA.source, mappingB.source), cmp !== 0) || (cmp = mappingA.originalLine - mappingB.originalLine, cmp !== 0) || (cmp = mappingA.originalColumn - mappingB.originalColumn, cmp !== 0) ? cmp : strcmp(mappingA.name, mappingB.name);
    }
    exports.compareByGeneratedPositionsDeflated = compareByGeneratedPositionsDeflated;
    function strcmp(aStr1, aStr2) {
      return aStr1 === aStr2 ? 0 : aStr1 === null ? 1 : aStr2 === null ? -1 : aStr1 > aStr2 ? 1 : -1;
    }
    function compareByGeneratedPositionsInflated(mappingA, mappingB) {
      var cmp = mappingA.generatedLine - mappingB.generatedLine;
      return cmp !== 0 || (cmp = mappingA.generatedColumn - mappingB.generatedColumn, cmp !== 0) || (cmp = strcmp(mappingA.source, mappingB.source), cmp !== 0) || (cmp = mappingA.originalLine - mappingB.originalLine, cmp !== 0) || (cmp = mappingA.originalColumn - mappingB.originalColumn, cmp !== 0) ? cmp : strcmp(mappingA.name, mappingB.name);
    }
    exports.compareByGeneratedPositionsInflated = compareByGeneratedPositionsInflated;
    function parseSourceMapInput(str) {
      return JSON.parse(str.replace(/^\)]}'[^\n]*\n/, ""));
    }
    exports.parseSourceMapInput = parseSourceMapInput;
    function computeSourceURL(sourceRoot, sourceURL, sourceMapURL) {
      if (sourceURL = sourceURL || "", sourceRoot && (sourceRoot[sourceRoot.length - 1] !== "/" && sourceURL[0] !== "/" && (sourceRoot += "/"), sourceURL = sourceRoot + sourceURL), sourceMapURL) {
        var parsed = urlParse(sourceMapURL);
        if (!parsed)
          throw new Error("sourceMapURL could not be parsed");
        if (parsed.path) {
          var index = parsed.path.lastIndexOf("/");
          index >= 0 && (parsed.path = parsed.path.substring(0, index + 1));
        }
        sourceURL = join(urlGenerate(parsed), sourceURL);
      }
      return normalize(sourceURL);
    }
    exports.computeSourceURL = computeSourceURL;
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/array-set.js
var require_array_set = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/array-set.js"(exports) {
    init_cjs_shims();
    var util = require_util(), has = Object.prototype.hasOwnProperty, hasNativeMap = typeof Map < "u";
    function ArraySet() {
      this._array = [], this._set = hasNativeMap ? /* @__PURE__ */ new Map() : /* @__PURE__ */ Object.create(null);
    }
    ArraySet.fromArray = function(aArray, aAllowDuplicates) {
      for (var set = new ArraySet(), i = 0, len = aArray.length; i < len; i++)
        set.add(aArray[i], aAllowDuplicates);
      return set;
    };
    ArraySet.prototype.size = function() {
      return hasNativeMap ? this._set.size : Object.getOwnPropertyNames(this._set).length;
    };
    ArraySet.prototype.add = function(aStr, aAllowDuplicates) {
      var sStr = hasNativeMap ? aStr : util.toSetString(aStr), isDuplicate = hasNativeMap ? this.has(aStr) : has.call(this._set, sStr), idx = this._array.length;
      (!isDuplicate || aAllowDuplicates) && this._array.push(aStr), isDuplicate || (hasNativeMap ? this._set.set(aStr, idx) : this._set[sStr] = idx);
    };
    ArraySet.prototype.has = function(aStr) {
      if (hasNativeMap)
        return this._set.has(aStr);
      var sStr = util.toSetString(aStr);
      return has.call(this._set, sStr);
    };
    ArraySet.prototype.indexOf = function(aStr) {
      if (hasNativeMap) {
        var idx = this._set.get(aStr);
        if (idx >= 0)
          return idx;
      } else {
        var sStr = util.toSetString(aStr);
        if (has.call(this._set, sStr))
          return this._set[sStr];
      }
      throw new Error('"' + aStr + '" is not in the set.');
    };
    ArraySet.prototype.at = function(aIdx) {
      if (aIdx >= 0 && aIdx < this._array.length)
        return this._array[aIdx];
      throw new Error("No element indexed by " + aIdx);
    };
    ArraySet.prototype.toArray = function() {
      return this._array.slice();
    };
    exports.ArraySet = ArraySet;
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/mapping-list.js
var require_mapping_list = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/mapping-list.js"(exports) {
    init_cjs_shims();
    var util = require_util();
    function generatedPositionAfter(mappingA, mappingB) {
      var lineA = mappingA.generatedLine, lineB = mappingB.generatedLine, columnA = mappingA.generatedColumn, columnB = mappingB.generatedColumn;
      return lineB > lineA || lineB == lineA && columnB >= columnA || util.compareByGeneratedPositionsInflated(mappingA, mappingB) <= 0;
    }
    function MappingList() {
      this._array = [], this._sorted = !0, this._last = { generatedLine: -1, generatedColumn: 0 };
    }
    MappingList.prototype.unsortedForEach = function(aCallback, aThisArg) {
      this._array.forEach(aCallback, aThisArg);
    };
    MappingList.prototype.add = function(aMapping) {
      generatedPositionAfter(this._last, aMapping) ? (this._last = aMapping, this._array.push(aMapping)) : (this._sorted = !1, this._array.push(aMapping));
    };
    MappingList.prototype.toArray = function() {
      return this._sorted || (this._array.sort(util.compareByGeneratedPositionsInflated), this._sorted = !0), this._array;
    };
    exports.MappingList = MappingList;
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/source-map-generator.js
var require_source_map_generator = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/source-map-generator.js"(exports) {
    init_cjs_shims();
    var base64VLQ = require_base64_vlq(), util = require_util(), ArraySet = require_array_set().ArraySet, MappingList = require_mapping_list().MappingList;
    function SourceMapGenerator(aArgs) {
      aArgs || (aArgs = {}), this._file = util.getArg(aArgs, "file", null), this._sourceRoot = util.getArg(aArgs, "sourceRoot", null), this._skipValidation = util.getArg(aArgs, "skipValidation", !1), this._sources = new ArraySet(), this._names = new ArraySet(), this._mappings = new MappingList(), this._sourcesContents = null;
    }
    SourceMapGenerator.prototype._version = 3;
    SourceMapGenerator.fromSourceMap = function(aSourceMapConsumer) {
      var sourceRoot = aSourceMapConsumer.sourceRoot, generator = new SourceMapGenerator({
        file: aSourceMapConsumer.file,
        sourceRoot
      });
      return aSourceMapConsumer.eachMapping(function(mapping) {
        var newMapping = {
          generated: {
            line: mapping.generatedLine,
            column: mapping.generatedColumn
          }
        };
        mapping.source != null && (newMapping.source = mapping.source, sourceRoot != null && (newMapping.source = util.relative(sourceRoot, newMapping.source)), newMapping.original = {
          line: mapping.originalLine,
          column: mapping.originalColumn
        }, mapping.name != null && (newMapping.name = mapping.name)), generator.addMapping(newMapping);
      }), aSourceMapConsumer.sources.forEach(function(sourceFile) {
        var sourceRelative = sourceFile;
        sourceRoot !== null && (sourceRelative = util.relative(sourceRoot, sourceFile)), generator._sources.has(sourceRelative) || generator._sources.add(sourceRelative);
        var content = aSourceMapConsumer.sourceContentFor(sourceFile);
        content != null && generator.setSourceContent(sourceFile, content);
      }), generator;
    };
    SourceMapGenerator.prototype.addMapping = function(aArgs) {
      var generated = util.getArg(aArgs, "generated"), original = util.getArg(aArgs, "original", null), source = util.getArg(aArgs, "source", null), name = util.getArg(aArgs, "name", null);
      this._skipValidation || this._validateMapping(generated, original, source, name), source != null && (source = String(source), this._sources.has(source) || this._sources.add(source)), name != null && (name = String(name), this._names.has(name) || this._names.add(name)), this._mappings.add({
        generatedLine: generated.line,
        generatedColumn: generated.column,
        originalLine: original != null && original.line,
        originalColumn: original != null && original.column,
        source,
        name
      });
    };
    SourceMapGenerator.prototype.setSourceContent = function(aSourceFile, aSourceContent) {
      var source = aSourceFile;
      this._sourceRoot != null && (source = util.relative(this._sourceRoot, source)), aSourceContent != null ? (this._sourcesContents || (this._sourcesContents = /* @__PURE__ */ Object.create(null)), this._sourcesContents[util.toSetString(source)] = aSourceContent) : this._sourcesContents && (delete this._sourcesContents[util.toSetString(source)], Object.keys(this._sourcesContents).length === 0 && (this._sourcesContents = null));
    };
    SourceMapGenerator.prototype.applySourceMap = function(aSourceMapConsumer, aSourceFile, aSourceMapPath) {
      var sourceFile = aSourceFile;
      if (aSourceFile == null) {
        if (aSourceMapConsumer.file == null)
          throw new Error(
            `SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`
          );
        sourceFile = aSourceMapConsumer.file;
      }
      var sourceRoot = this._sourceRoot;
      sourceRoot != null && (sourceFile = util.relative(sourceRoot, sourceFile));
      var newSources = new ArraySet(), newNames = new ArraySet();
      this._mappings.unsortedForEach(function(mapping) {
        if (mapping.source === sourceFile && mapping.originalLine != null) {
          var original = aSourceMapConsumer.originalPositionFor({
            line: mapping.originalLine,
            column: mapping.originalColumn
          });
          original.source != null && (mapping.source = original.source, aSourceMapPath != null && (mapping.source = util.join(aSourceMapPath, mapping.source)), sourceRoot != null && (mapping.source = util.relative(sourceRoot, mapping.source)), mapping.originalLine = original.line, mapping.originalColumn = original.column, original.name != null && (mapping.name = original.name));
        }
        var source = mapping.source;
        source != null && !newSources.has(source) && newSources.add(source);
        var name = mapping.name;
        name != null && !newNames.has(name) && newNames.add(name);
      }, this), this._sources = newSources, this._names = newNames, aSourceMapConsumer.sources.forEach(function(sourceFile2) {
        var content = aSourceMapConsumer.sourceContentFor(sourceFile2);
        content != null && (aSourceMapPath != null && (sourceFile2 = util.join(aSourceMapPath, sourceFile2)), sourceRoot != null && (sourceFile2 = util.relative(sourceRoot, sourceFile2)), this.setSourceContent(sourceFile2, content));
      }, this);
    };
    SourceMapGenerator.prototype._validateMapping = function(aGenerated, aOriginal, aSource, aName) {
      if (aOriginal && typeof aOriginal.line != "number" && typeof aOriginal.column != "number")
        throw new Error(
          "original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values."
        );
      if (!(aGenerated && "line" in aGenerated && "column" in aGenerated && aGenerated.line > 0 && aGenerated.column >= 0 && !aOriginal && !aSource && !aName)) {
        if (aGenerated && "line" in aGenerated && "column" in aGenerated && aOriginal && "line" in aOriginal && "column" in aOriginal && aGenerated.line > 0 && aGenerated.column >= 0 && aOriginal.line > 0 && aOriginal.column >= 0 && aSource)
          return;
        throw new Error("Invalid mapping: " + JSON.stringify({
          generated: aGenerated,
          source: aSource,
          original: aOriginal,
          name: aName
        }));
      }
    };
    SourceMapGenerator.prototype._serializeMappings = function() {
      for (var previousGeneratedColumn = 0, previousGeneratedLine = 1, previousOriginalColumn = 0, previousOriginalLine = 0, previousName = 0, previousSource = 0, result = "", next, mapping, nameIdx, sourceIdx, mappings = this._mappings.toArray(), i = 0, len = mappings.length; i < len; i++) {
        if (mapping = mappings[i], next = "", mapping.generatedLine !== previousGeneratedLine)
          for (previousGeneratedColumn = 0; mapping.generatedLine !== previousGeneratedLine; )
            next += ";", previousGeneratedLine++;
        else if (i > 0) {
          if (!util.compareByGeneratedPositionsInflated(mapping, mappings[i - 1]))
            continue;
          next += ",";
        }
        next += base64VLQ.encode(mapping.generatedColumn - previousGeneratedColumn), previousGeneratedColumn = mapping.generatedColumn, mapping.source != null && (sourceIdx = this._sources.indexOf(mapping.source), next += base64VLQ.encode(sourceIdx - previousSource), previousSource = sourceIdx, next += base64VLQ.encode(mapping.originalLine - 1 - previousOriginalLine), previousOriginalLine = mapping.originalLine - 1, next += base64VLQ.encode(mapping.originalColumn - previousOriginalColumn), previousOriginalColumn = mapping.originalColumn, mapping.name != null && (nameIdx = this._names.indexOf(mapping.name), next += base64VLQ.encode(nameIdx - previousName), previousName = nameIdx)), result += next;
      }
      return result;
    };
    SourceMapGenerator.prototype._generateSourcesContent = function(aSources, aSourceRoot) {
      return aSources.map(function(source) {
        if (!this._sourcesContents)
          return null;
        aSourceRoot != null && (source = util.relative(aSourceRoot, source));
        var key = util.toSetString(source);
        return Object.prototype.hasOwnProperty.call(this._sourcesContents, key) ? this._sourcesContents[key] : null;
      }, this);
    };
    SourceMapGenerator.prototype.toJSON = function() {
      var map = {
        version: this._version,
        sources: this._sources.toArray(),
        names: this._names.toArray(),
        mappings: this._serializeMappings()
      };
      return this._file != null && (map.file = this._file), this._sourceRoot != null && (map.sourceRoot = this._sourceRoot), this._sourcesContents && (map.sourcesContent = this._generateSourcesContent(map.sources, map.sourceRoot)), map;
    };
    SourceMapGenerator.prototype.toString = function() {
      return JSON.stringify(this.toJSON());
    };
    exports.SourceMapGenerator = SourceMapGenerator;
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/binary-search.js
var require_binary_search = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/binary-search.js"(exports) {
    init_cjs_shims();
    exports.GREATEST_LOWER_BOUND = 1;
    exports.LEAST_UPPER_BOUND = 2;
    function recursiveSearch(aLow, aHigh, aNeedle, aHaystack, aCompare, aBias) {
      var mid = Math.floor((aHigh - aLow) / 2) + aLow, cmp = aCompare(aNeedle, aHaystack[mid], !0);
      return cmp === 0 ? mid : cmp > 0 ? aHigh - mid > 1 ? recursiveSearch(mid, aHigh, aNeedle, aHaystack, aCompare, aBias) : aBias == exports.LEAST_UPPER_BOUND ? aHigh < aHaystack.length ? aHigh : -1 : mid : mid - aLow > 1 ? recursiveSearch(aLow, mid, aNeedle, aHaystack, aCompare, aBias) : aBias == exports.LEAST_UPPER_BOUND ? mid : aLow < 0 ? -1 : aLow;
    }
    exports.search = function(aNeedle, aHaystack, aCompare, aBias) {
      if (aHaystack.length === 0)
        return -1;
      var index = recursiveSearch(
        -1,
        aHaystack.length,
        aNeedle,
        aHaystack,
        aCompare,
        aBias || exports.GREATEST_LOWER_BOUND
      );
      if (index < 0)
        return -1;
      for (; index - 1 >= 0 && aCompare(aHaystack[index], aHaystack[index - 1], !0) === 0; )
        --index;
      return index;
    };
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/quick-sort.js
var require_quick_sort = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/quick-sort.js"(exports) {
    init_cjs_shims();
    function swap(ary, x, y) {
      var temp = ary[x];
      ary[x] = ary[y], ary[y] = temp;
    }
    function randomIntInRange(low, high) {
      return Math.round(low + Math.random() * (high - low));
    }
    function doQuickSort(ary, comparator, p, r) {
      if (p < r) {
        var pivotIndex = randomIntInRange(p, r), i = p - 1;
        swap(ary, pivotIndex, r);
        for (var pivot = ary[r], j = p; j < r; j++)
          comparator(ary[j], pivot) <= 0 && (i += 1, swap(ary, i, j));
        swap(ary, i + 1, j);
        var q = i + 1;
        doQuickSort(ary, comparator, p, q - 1), doQuickSort(ary, comparator, q + 1, r);
      }
    }
    exports.quickSort = function(ary, comparator) {
      doQuickSort(ary, comparator, 0, ary.length - 1);
    };
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/source-map-consumer.js
var require_source_map_consumer = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/source-map-consumer.js"(exports) {
    init_cjs_shims();
    var util = require_util(), binarySearch = require_binary_search(), ArraySet = require_array_set().ArraySet, base64VLQ = require_base64_vlq(), quickSort = require_quick_sort().quickSort;
    function SourceMapConsumer(aSourceMap, aSourceMapURL) {
      var sourceMap = aSourceMap;
      return typeof aSourceMap == "string" && (sourceMap = util.parseSourceMapInput(aSourceMap)), sourceMap.sections != null ? new IndexedSourceMapConsumer(sourceMap, aSourceMapURL) : new BasicSourceMapConsumer(sourceMap, aSourceMapURL);
    }
    SourceMapConsumer.fromSourceMap = function(aSourceMap, aSourceMapURL) {
      return BasicSourceMapConsumer.fromSourceMap(aSourceMap, aSourceMapURL);
    };
    SourceMapConsumer.prototype._version = 3;
    SourceMapConsumer.prototype.__generatedMappings = null;
    Object.defineProperty(SourceMapConsumer.prototype, "_generatedMappings", {
      configurable: !0,
      enumerable: !0,
      get: function() {
        return this.__generatedMappings || this._parseMappings(this._mappings, this.sourceRoot), this.__generatedMappings;
      }
    });
    SourceMapConsumer.prototype.__originalMappings = null;
    Object.defineProperty(SourceMapConsumer.prototype, "_originalMappings", {
      configurable: !0,
      enumerable: !0,
      get: function() {
        return this.__originalMappings || this._parseMappings(this._mappings, this.sourceRoot), this.__originalMappings;
      }
    });
    SourceMapConsumer.prototype._charIsMappingSeparator = function(aStr, index) {
      var c = aStr.charAt(index);
      return c === ";" || c === ",";
    };
    SourceMapConsumer.prototype._parseMappings = function(aStr, aSourceRoot) {
      throw new Error("Subclasses must implement _parseMappings");
    };
    SourceMapConsumer.GENERATED_ORDER = 1;
    SourceMapConsumer.ORIGINAL_ORDER = 2;
    SourceMapConsumer.GREATEST_LOWER_BOUND = 1;
    SourceMapConsumer.LEAST_UPPER_BOUND = 2;
    SourceMapConsumer.prototype.eachMapping = function(aCallback, aContext, aOrder) {
      var context = aContext || null, order = aOrder || SourceMapConsumer.GENERATED_ORDER, mappings;
      switch (order) {
        case SourceMapConsumer.GENERATED_ORDER:
          mappings = this._generatedMappings;
          break;
        case SourceMapConsumer.ORIGINAL_ORDER:
          mappings = this._originalMappings;
          break;
        default:
          throw new Error("Unknown order of iteration.");
      }
      var sourceRoot = this.sourceRoot;
      mappings.map(function(mapping) {
        var source = mapping.source === null ? null : this._sources.at(mapping.source);
        return source = util.computeSourceURL(sourceRoot, source, this._sourceMapURL), {
          source,
          generatedLine: mapping.generatedLine,
          generatedColumn: mapping.generatedColumn,
          originalLine: mapping.originalLine,
          originalColumn: mapping.originalColumn,
          name: mapping.name === null ? null : this._names.at(mapping.name)
        };
      }, this).forEach(aCallback, context);
    };
    SourceMapConsumer.prototype.allGeneratedPositionsFor = function(aArgs) {
      var line = util.getArg(aArgs, "line"), needle = {
        source: util.getArg(aArgs, "source"),
        originalLine: line,
        originalColumn: util.getArg(aArgs, "column", 0)
      };
      if (needle.source = this._findSourceIndex(needle.source), needle.source < 0)
        return [];
      var mappings = [], index = this._findMapping(
        needle,
        this._originalMappings,
        "originalLine",
        "originalColumn",
        util.compareByOriginalPositions,
        binarySearch.LEAST_UPPER_BOUND
      );
      if (index >= 0) {
        var mapping = this._originalMappings[index];
        if (aArgs.column === void 0)
          for (var originalLine = mapping.originalLine; mapping && mapping.originalLine === originalLine; )
            mappings.push({
              line: util.getArg(mapping, "generatedLine", null),
              column: util.getArg(mapping, "generatedColumn", null),
              lastColumn: util.getArg(mapping, "lastGeneratedColumn", null)
            }), mapping = this._originalMappings[++index];
        else
          for (var originalColumn = mapping.originalColumn; mapping && mapping.originalLine === line && mapping.originalColumn == originalColumn; )
            mappings.push({
              line: util.getArg(mapping, "generatedLine", null),
              column: util.getArg(mapping, "generatedColumn", null),
              lastColumn: util.getArg(mapping, "lastGeneratedColumn", null)
            }), mapping = this._originalMappings[++index];
      }
      return mappings;
    };
    exports.SourceMapConsumer = SourceMapConsumer;
    function BasicSourceMapConsumer(aSourceMap, aSourceMapURL) {
      var sourceMap = aSourceMap;
      typeof aSourceMap == "string" && (sourceMap = util.parseSourceMapInput(aSourceMap));
      var version = util.getArg(sourceMap, "version"), sources = util.getArg(sourceMap, "sources"), names = util.getArg(sourceMap, "names", []), sourceRoot = util.getArg(sourceMap, "sourceRoot", null), sourcesContent = util.getArg(sourceMap, "sourcesContent", null), mappings = util.getArg(sourceMap, "mappings"), file = util.getArg(sourceMap, "file", null);
      if (version != this._version)
        throw new Error("Unsupported version: " + version);
      sourceRoot && (sourceRoot = util.normalize(sourceRoot)), sources = sources.map(String).map(util.normalize).map(function(source) {
        return sourceRoot && util.isAbsolute(sourceRoot) && util.isAbsolute(source) ? util.relative(sourceRoot, source) : source;
      }), this._names = ArraySet.fromArray(names.map(String), !0), this._sources = ArraySet.fromArray(sources, !0), this._absoluteSources = this._sources.toArray().map(function(s) {
        return util.computeSourceURL(sourceRoot, s, aSourceMapURL);
      }), this.sourceRoot = sourceRoot, this.sourcesContent = sourcesContent, this._mappings = mappings, this._sourceMapURL = aSourceMapURL, this.file = file;
    }
    BasicSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);
    BasicSourceMapConsumer.prototype.consumer = SourceMapConsumer;
    BasicSourceMapConsumer.prototype._findSourceIndex = function(aSource) {
      var relativeSource = aSource;
      if (this.sourceRoot != null && (relativeSource = util.relative(this.sourceRoot, relativeSource)), this._sources.has(relativeSource))
        return this._sources.indexOf(relativeSource);
      var i;
      for (i = 0; i < this._absoluteSources.length; ++i)
        if (this._absoluteSources[i] == aSource)
          return i;
      return -1;
    };
    BasicSourceMapConsumer.fromSourceMap = function(aSourceMap, aSourceMapURL) {
      var smc = Object.create(BasicSourceMapConsumer.prototype), names = smc._names = ArraySet.fromArray(aSourceMap._names.toArray(), !0), sources = smc._sources = ArraySet.fromArray(aSourceMap._sources.toArray(), !0);
      smc.sourceRoot = aSourceMap._sourceRoot, smc.sourcesContent = aSourceMap._generateSourcesContent(
        smc._sources.toArray(),
        smc.sourceRoot
      ), smc.file = aSourceMap._file, smc._sourceMapURL = aSourceMapURL, smc._absoluteSources = smc._sources.toArray().map(function(s) {
        return util.computeSourceURL(smc.sourceRoot, s, aSourceMapURL);
      });
      for (var generatedMappings = aSourceMap._mappings.toArray().slice(), destGeneratedMappings = smc.__generatedMappings = [], destOriginalMappings = smc.__originalMappings = [], i = 0, length = generatedMappings.length; i < length; i++) {
        var srcMapping = generatedMappings[i], destMapping = new Mapping();
        destMapping.generatedLine = srcMapping.generatedLine, destMapping.generatedColumn = srcMapping.generatedColumn, srcMapping.source && (destMapping.source = sources.indexOf(srcMapping.source), destMapping.originalLine = srcMapping.originalLine, destMapping.originalColumn = srcMapping.originalColumn, srcMapping.name && (destMapping.name = names.indexOf(srcMapping.name)), destOriginalMappings.push(destMapping)), destGeneratedMappings.push(destMapping);
      }
      return quickSort(smc.__originalMappings, util.compareByOriginalPositions), smc;
    };
    BasicSourceMapConsumer.prototype._version = 3;
    Object.defineProperty(BasicSourceMapConsumer.prototype, "sources", {
      get: function() {
        return this._absoluteSources.slice();
      }
    });
    function Mapping() {
      this.generatedLine = 0, this.generatedColumn = 0, this.source = null, this.originalLine = null, this.originalColumn = null, this.name = null;
    }
    BasicSourceMapConsumer.prototype._parseMappings = function(aStr, aSourceRoot) {
      for (var generatedLine = 1, previousGeneratedColumn = 0, previousOriginalLine = 0, previousOriginalColumn = 0, previousSource = 0, previousName = 0, length = aStr.length, index = 0, cachedSegments = {}, temp = {}, originalMappings = [], generatedMappings = [], mapping, str, segment, end, value; index < length; )
        if (aStr.charAt(index) === ";")
          generatedLine++, index++, previousGeneratedColumn = 0;
        else if (aStr.charAt(index) === ",")
          index++;
        else {
          for (mapping = new Mapping(), mapping.generatedLine = generatedLine, end = index; end < length && !this._charIsMappingSeparator(aStr, end); end++)
            ;
          if (str = aStr.slice(index, end), segment = cachedSegments[str], segment)
            index += str.length;
          else {
            for (segment = []; index < end; )
              base64VLQ.decode(aStr, index, temp), value = temp.value, index = temp.rest, segment.push(value);
            if (segment.length === 2)
              throw new Error("Found a source, but no line and column");
            if (segment.length === 3)
              throw new Error("Found a source and line, but no column");
            cachedSegments[str] = segment;
          }
          mapping.generatedColumn = previousGeneratedColumn + segment[0], previousGeneratedColumn = mapping.generatedColumn, segment.length > 1 && (mapping.source = previousSource + segment[1], previousSource += segment[1], mapping.originalLine = previousOriginalLine + segment[2], previousOriginalLine = mapping.originalLine, mapping.originalLine += 1, mapping.originalColumn = previousOriginalColumn + segment[3], previousOriginalColumn = mapping.originalColumn, segment.length > 4 && (mapping.name = previousName + segment[4], previousName += segment[4])), generatedMappings.push(mapping), typeof mapping.originalLine == "number" && originalMappings.push(mapping);
        }
      quickSort(generatedMappings, util.compareByGeneratedPositionsDeflated), this.__generatedMappings = generatedMappings, quickSort(originalMappings, util.compareByOriginalPositions), this.__originalMappings = originalMappings;
    };
    BasicSourceMapConsumer.prototype._findMapping = function(aNeedle, aMappings, aLineName, aColumnName, aComparator, aBias) {
      if (aNeedle[aLineName] <= 0)
        throw new TypeError("Line must be greater than or equal to 1, got " + aNeedle[aLineName]);
      if (aNeedle[aColumnName] < 0)
        throw new TypeError("Column must be greater than or equal to 0, got " + aNeedle[aColumnName]);
      return binarySearch.search(aNeedle, aMappings, aComparator, aBias);
    };
    BasicSourceMapConsumer.prototype.computeColumnSpans = function() {
      for (var index = 0; index < this._generatedMappings.length; ++index) {
        var mapping = this._generatedMappings[index];
        if (index + 1 < this._generatedMappings.length) {
          var nextMapping = this._generatedMappings[index + 1];
          if (mapping.generatedLine === nextMapping.generatedLine) {
            mapping.lastGeneratedColumn = nextMapping.generatedColumn - 1;
            continue;
          }
        }
        mapping.lastGeneratedColumn = 1 / 0;
      }
    };
    BasicSourceMapConsumer.prototype.originalPositionFor = function(aArgs) {
      var needle = {
        generatedLine: util.getArg(aArgs, "line"),
        generatedColumn: util.getArg(aArgs, "column")
      }, index = this._findMapping(
        needle,
        this._generatedMappings,
        "generatedLine",
        "generatedColumn",
        util.compareByGeneratedPositionsDeflated,
        util.getArg(aArgs, "bias", SourceMapConsumer.GREATEST_LOWER_BOUND)
      );
      if (index >= 0) {
        var mapping = this._generatedMappings[index];
        if (mapping.generatedLine === needle.generatedLine) {
          var source = util.getArg(mapping, "source", null);
          source !== null && (source = this._sources.at(source), source = util.computeSourceURL(this.sourceRoot, source, this._sourceMapURL));
          var name = util.getArg(mapping, "name", null);
          return name !== null && (name = this._names.at(name)), {
            source,
            line: util.getArg(mapping, "originalLine", null),
            column: util.getArg(mapping, "originalColumn", null),
            name
          };
        }
      }
      return {
        source: null,
        line: null,
        column: null,
        name: null
      };
    };
    BasicSourceMapConsumer.prototype.hasContentsOfAllSources = function() {
      return this.sourcesContent ? this.sourcesContent.length >= this._sources.size() && !this.sourcesContent.some(function(sc) {
        return sc == null;
      }) : !1;
    };
    BasicSourceMapConsumer.prototype.sourceContentFor = function(aSource, nullOnMissing) {
      if (!this.sourcesContent)
        return null;
      var index = this._findSourceIndex(aSource);
      if (index >= 0)
        return this.sourcesContent[index];
      var relativeSource = aSource;
      this.sourceRoot != null && (relativeSource = util.relative(this.sourceRoot, relativeSource));
      var url;
      if (this.sourceRoot != null && (url = util.urlParse(this.sourceRoot))) {
        var fileUriAbsPath = relativeSource.replace(/^file:\/\//, "");
        if (url.scheme == "file" && this._sources.has(fileUriAbsPath))
          return this.sourcesContent[this._sources.indexOf(fileUriAbsPath)];
        if ((!url.path || url.path == "/") && this._sources.has("/" + relativeSource))
          return this.sourcesContent[this._sources.indexOf("/" + relativeSource)];
      }
      if (nullOnMissing)
        return null;
      throw new Error('"' + relativeSource + '" is not in the SourceMap.');
    };
    BasicSourceMapConsumer.prototype.generatedPositionFor = function(aArgs) {
      var source = util.getArg(aArgs, "source");
      if (source = this._findSourceIndex(source), source < 0)
        return {
          line: null,
          column: null,
          lastColumn: null
        };
      var needle = {
        source,
        originalLine: util.getArg(aArgs, "line"),
        originalColumn: util.getArg(aArgs, "column")
      }, index = this._findMapping(
        needle,
        this._originalMappings,
        "originalLine",
        "originalColumn",
        util.compareByOriginalPositions,
        util.getArg(aArgs, "bias", SourceMapConsumer.GREATEST_LOWER_BOUND)
      );
      if (index >= 0) {
        var mapping = this._originalMappings[index];
        if (mapping.source === needle.source)
          return {
            line: util.getArg(mapping, "generatedLine", null),
            column: util.getArg(mapping, "generatedColumn", null),
            lastColumn: util.getArg(mapping, "lastGeneratedColumn", null)
          };
      }
      return {
        line: null,
        column: null,
        lastColumn: null
      };
    };
    exports.BasicSourceMapConsumer = BasicSourceMapConsumer;
    function IndexedSourceMapConsumer(aSourceMap, aSourceMapURL) {
      var sourceMap = aSourceMap;
      typeof aSourceMap == "string" && (sourceMap = util.parseSourceMapInput(aSourceMap));
      var version = util.getArg(sourceMap, "version"), sections = util.getArg(sourceMap, "sections");
      if (version != this._version)
        throw new Error("Unsupported version: " + version);
      this._sources = new ArraySet(), this._names = new ArraySet();
      var lastOffset = {
        line: -1,
        column: 0
      };
      this._sections = sections.map(function(s) {
        if (s.url)
          throw new Error("Support for url field in sections not implemented.");
        var offset = util.getArg(s, "offset"), offsetLine = util.getArg(offset, "line"), offsetColumn = util.getArg(offset, "column");
        if (offsetLine < lastOffset.line || offsetLine === lastOffset.line && offsetColumn < lastOffset.column)
          throw new Error("Section offsets must be ordered and non-overlapping.");
        return lastOffset = offset, {
          generatedOffset: {
            // The offset fields are 0-based, but we use 1-based indices when
            // encoding/decoding from VLQ.
            generatedLine: offsetLine + 1,
            generatedColumn: offsetColumn + 1
          },
          consumer: new SourceMapConsumer(util.getArg(s, "map"), aSourceMapURL)
        };
      });
    }
    IndexedSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);
    IndexedSourceMapConsumer.prototype.constructor = SourceMapConsumer;
    IndexedSourceMapConsumer.prototype._version = 3;
    Object.defineProperty(IndexedSourceMapConsumer.prototype, "sources", {
      get: function() {
        for (var sources = [], i = 0; i < this._sections.length; i++)
          for (var j = 0; j < this._sections[i].consumer.sources.length; j++)
            sources.push(this._sections[i].consumer.sources[j]);
        return sources;
      }
    });
    IndexedSourceMapConsumer.prototype.originalPositionFor = function(aArgs) {
      var needle = {
        generatedLine: util.getArg(aArgs, "line"),
        generatedColumn: util.getArg(aArgs, "column")
      }, sectionIndex = binarySearch.search(
        needle,
        this._sections,
        function(needle2, section2) {
          var cmp = needle2.generatedLine - section2.generatedOffset.generatedLine;
          return cmp || needle2.generatedColumn - section2.generatedOffset.generatedColumn;
        }
      ), section = this._sections[sectionIndex];
      return section ? section.consumer.originalPositionFor({
        line: needle.generatedLine - (section.generatedOffset.generatedLine - 1),
        column: needle.generatedColumn - (section.generatedOffset.generatedLine === needle.generatedLine ? section.generatedOffset.generatedColumn - 1 : 0),
        bias: aArgs.bias
      }) : {
        source: null,
        line: null,
        column: null,
        name: null
      };
    };
    IndexedSourceMapConsumer.prototype.hasContentsOfAllSources = function() {
      return this._sections.every(function(s) {
        return s.consumer.hasContentsOfAllSources();
      });
    };
    IndexedSourceMapConsumer.prototype.sourceContentFor = function(aSource, nullOnMissing) {
      for (var i = 0; i < this._sections.length; i++) {
        var section = this._sections[i], content = section.consumer.sourceContentFor(aSource, !0);
        if (content)
          return content;
      }
      if (nullOnMissing)
        return null;
      throw new Error('"' + aSource + '" is not in the SourceMap.');
    };
    IndexedSourceMapConsumer.prototype.generatedPositionFor = function(aArgs) {
      for (var i = 0; i < this._sections.length; i++) {
        var section = this._sections[i];
        if (section.consumer._findSourceIndex(util.getArg(aArgs, "source")) !== -1) {
          var generatedPosition = section.consumer.generatedPositionFor(aArgs);
          if (generatedPosition) {
            var ret = {
              line: generatedPosition.line + (section.generatedOffset.generatedLine - 1),
              column: generatedPosition.column + (section.generatedOffset.generatedLine === generatedPosition.line ? section.generatedOffset.generatedColumn - 1 : 0)
            };
            return ret;
          }
        }
      }
      return {
        line: null,
        column: null
      };
    };
    IndexedSourceMapConsumer.prototype._parseMappings = function(aStr, aSourceRoot) {
      this.__generatedMappings = [], this.__originalMappings = [];
      for (var i = 0; i < this._sections.length; i++)
        for (var section = this._sections[i], sectionMappings = section.consumer._generatedMappings, j = 0; j < sectionMappings.length; j++) {
          var mapping = sectionMappings[j], source = section.consumer._sources.at(mapping.source);
          source = util.computeSourceURL(section.consumer.sourceRoot, source, this._sourceMapURL), this._sources.add(source), source = this._sources.indexOf(source);
          var name = null;
          mapping.name && (name = section.consumer._names.at(mapping.name), this._names.add(name), name = this._names.indexOf(name));
          var adjustedMapping = {
            source,
            generatedLine: mapping.generatedLine + (section.generatedOffset.generatedLine - 1),
            generatedColumn: mapping.generatedColumn + (section.generatedOffset.generatedLine === mapping.generatedLine ? section.generatedOffset.generatedColumn - 1 : 0),
            originalLine: mapping.originalLine,
            originalColumn: mapping.originalColumn,
            name
          };
          this.__generatedMappings.push(adjustedMapping), typeof adjustedMapping.originalLine == "number" && this.__originalMappings.push(adjustedMapping);
        }
      quickSort(this.__generatedMappings, util.compareByGeneratedPositionsDeflated), quickSort(this.__originalMappings, util.compareByOriginalPositions);
    };
    exports.IndexedSourceMapConsumer = IndexedSourceMapConsumer;
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/source-node.js
var require_source_node = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/lib/source-node.js"(exports) {
    init_cjs_shims();
    var SourceMapGenerator = require_source_map_generator().SourceMapGenerator, util = require_util(), REGEX_NEWLINE = /(\r?\n)/, NEWLINE_CODE = 10, isSourceNode = "$$$isSourceNode$$$";
    function SourceNode(aLine, aColumn, aSource, aChunks, aName) {
      this.children = [], this.sourceContents = {}, this.line = aLine ?? null, this.column = aColumn ?? null, this.source = aSource ?? null, this.name = aName ?? null, this[isSourceNode] = !0, aChunks != null && this.add(aChunks);
    }
    SourceNode.fromStringWithSourceMap = function(aGeneratedCode, aSourceMapConsumer, aRelativePath) {
      var node = new SourceNode(), remainingLines = aGeneratedCode.split(REGEX_NEWLINE), remainingLinesIndex = 0, shiftNextLine = function() {
        var lineContents = getNextLine(), newLine = getNextLine() || "";
        return lineContents + newLine;
        function getNextLine() {
          return remainingLinesIndex < remainingLines.length ? remainingLines[remainingLinesIndex++] : void 0;
        }
      }, lastGeneratedLine = 1, lastGeneratedColumn = 0, lastMapping = null;
      return aSourceMapConsumer.eachMapping(function(mapping) {
        if (lastMapping !== null)
          if (lastGeneratedLine < mapping.generatedLine)
            addMappingWithCode(lastMapping, shiftNextLine()), lastGeneratedLine++, lastGeneratedColumn = 0;
          else {
            var nextLine = remainingLines[remainingLinesIndex] || "", code = nextLine.substr(0, mapping.generatedColumn - lastGeneratedColumn);
            remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn - lastGeneratedColumn), lastGeneratedColumn = mapping.generatedColumn, addMappingWithCode(lastMapping, code), lastMapping = mapping;
            return;
          }
        for (; lastGeneratedLine < mapping.generatedLine; )
          node.add(shiftNextLine()), lastGeneratedLine++;
        if (lastGeneratedColumn < mapping.generatedColumn) {
          var nextLine = remainingLines[remainingLinesIndex] || "";
          node.add(nextLine.substr(0, mapping.generatedColumn)), remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn), lastGeneratedColumn = mapping.generatedColumn;
        }
        lastMapping = mapping;
      }, this), remainingLinesIndex < remainingLines.length && (lastMapping && addMappingWithCode(lastMapping, shiftNextLine()), node.add(remainingLines.splice(remainingLinesIndex).join(""))), aSourceMapConsumer.sources.forEach(function(sourceFile) {
        var content = aSourceMapConsumer.sourceContentFor(sourceFile);
        content != null && (aRelativePath != null && (sourceFile = util.join(aRelativePath, sourceFile)), node.setSourceContent(sourceFile, content));
      }), node;
      function addMappingWithCode(mapping, code) {
        if (mapping === null || mapping.source === void 0)
          node.add(code);
        else {
          var source = aRelativePath ? util.join(aRelativePath, mapping.source) : mapping.source;
          node.add(new SourceNode(
            mapping.originalLine,
            mapping.originalColumn,
            source,
            code,
            mapping.name
          ));
        }
      }
    };
    SourceNode.prototype.add = function(aChunk) {
      if (Array.isArray(aChunk))
        aChunk.forEach(function(chunk) {
          this.add(chunk);
        }, this);
      else if (aChunk[isSourceNode] || typeof aChunk == "string")
        aChunk && this.children.push(aChunk);
      else
        throw new TypeError(
          "Expected a SourceNode, string, or an array of SourceNodes and strings. Got " + aChunk
        );
      return this;
    };
    SourceNode.prototype.prepend = function(aChunk) {
      if (Array.isArray(aChunk))
        for (var i = aChunk.length - 1; i >= 0; i--)
          this.prepend(aChunk[i]);
      else if (aChunk[isSourceNode] || typeof aChunk == "string")
        this.children.unshift(aChunk);
      else
        throw new TypeError(
          "Expected a SourceNode, string, or an array of SourceNodes and strings. Got " + aChunk
        );
      return this;
    };
    SourceNode.prototype.walk = function(aFn) {
      for (var chunk, i = 0, len = this.children.length; i < len; i++)
        chunk = this.children[i], chunk[isSourceNode] ? chunk.walk(aFn) : chunk !== "" && aFn(chunk, {
          source: this.source,
          line: this.line,
          column: this.column,
          name: this.name
        });
    };
    SourceNode.prototype.join = function(aSep) {
      var newChildren, i, len = this.children.length;
      if (len > 0) {
        for (newChildren = [], i = 0; i < len - 1; i++)
          newChildren.push(this.children[i]), newChildren.push(aSep);
        newChildren.push(this.children[i]), this.children = newChildren;
      }
      return this;
    };
    SourceNode.prototype.replaceRight = function(aPattern, aReplacement) {
      var lastChild = this.children[this.children.length - 1];
      return lastChild[isSourceNode] ? lastChild.replaceRight(aPattern, aReplacement) : typeof lastChild == "string" ? this.children[this.children.length - 1] = lastChild.replace(aPattern, aReplacement) : this.children.push("".replace(aPattern, aReplacement)), this;
    };
    SourceNode.prototype.setSourceContent = function(aSourceFile, aSourceContent) {
      this.sourceContents[util.toSetString(aSourceFile)] = aSourceContent;
    };
    SourceNode.prototype.walkSourceContents = function(aFn) {
      for (var i = 0, len = this.children.length; i < len; i++)
        this.children[i][isSourceNode] && this.children[i].walkSourceContents(aFn);
      for (var sources = Object.keys(this.sourceContents), i = 0, len = sources.length; i < len; i++)
        aFn(util.fromSetString(sources[i]), this.sourceContents[sources[i]]);
    };
    SourceNode.prototype.toString = function() {
      var str = "";
      return this.walk(function(chunk) {
        str += chunk;
      }), str;
    };
    SourceNode.prototype.toStringWithSourceMap = function(aArgs) {
      var generated = {
        code: "",
        line: 1,
        column: 0
      }, map = new SourceMapGenerator(aArgs), sourceMappingActive = !1, lastOriginalSource = null, lastOriginalLine = null, lastOriginalColumn = null, lastOriginalName = null;
      return this.walk(function(chunk, original) {
        generated.code += chunk, original.source !== null && original.line !== null && original.column !== null ? ((lastOriginalSource !== original.source || lastOriginalLine !== original.line || lastOriginalColumn !== original.column || lastOriginalName !== original.name) && map.addMapping({
          source: original.source,
          original: {
            line: original.line,
            column: original.column
          },
          generated: {
            line: generated.line,
            column: generated.column
          },
          name: original.name
        }), lastOriginalSource = original.source, lastOriginalLine = original.line, lastOriginalColumn = original.column, lastOriginalName = original.name, sourceMappingActive = !0) : sourceMappingActive && (map.addMapping({
          generated: {
            line: generated.line,
            column: generated.column
          }
        }), lastOriginalSource = null, sourceMappingActive = !1);
        for (var idx = 0, length = chunk.length; idx < length; idx++)
          chunk.charCodeAt(idx) === NEWLINE_CODE ? (generated.line++, generated.column = 0, idx + 1 === length ? (lastOriginalSource = null, sourceMappingActive = !1) : sourceMappingActive && map.addMapping({
            source: original.source,
            original: {
              line: original.line,
              column: original.column
            },
            generated: {
              line: generated.line,
              column: generated.column
            },
            name: original.name
          })) : generated.column++;
      }), this.walkSourceContents(function(sourceFile, sourceContent) {
        map.setSourceContent(sourceFile, sourceContent);
      }), { code: generated.code, map };
    };
    exports.SourceNode = SourceNode;
  }
});

// ../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/source-map.js
var require_source_map = __commonJS({
  "../../node_modules/.pnpm/source-map@0.6.1/node_modules/source-map/source-map.js"(exports) {
    init_cjs_shims();
    exports.SourceMapGenerator = require_source_map_generator().SourceMapGenerator;
    exports.SourceMapConsumer = require_source_map_consumer().SourceMapConsumer;
    exports.SourceNode = require_source_node().SourceNode;
  }
});

// ../../node_modules/.pnpm/buffer-from@1.1.2/node_modules/buffer-from/index.js
var require_buffer_from = __commonJS({
  "../../node_modules/.pnpm/buffer-from@1.1.2/node_modules/buffer-from/index.js"(exports, module) {
    init_cjs_shims();
    var toString = Object.prototype.toString, isModern = typeof Buffer < "u" && typeof Buffer.alloc == "function" && typeof Buffer.allocUnsafe == "function" && typeof Buffer.from == "function";
    function isArrayBuffer(input) {
      return toString.call(input).slice(8, -1) === "ArrayBuffer";
    }
    function fromArrayBuffer(obj, byteOffset, length) {
      byteOffset >>>= 0;
      var maxLength = obj.byteLength - byteOffset;
      if (maxLength < 0)
        throw new RangeError("'offset' is out of bounds");
      if (length === void 0)
        length = maxLength;
      else if (length >>>= 0, length > maxLength)
        throw new RangeError("'length' is out of bounds");
      return isModern ? Buffer.from(obj.slice(byteOffset, byteOffset + length)) : new Buffer(new Uint8Array(obj.slice(byteOffset, byteOffset + length)));
    }
    function fromString(string, encoding) {
      if ((typeof encoding != "string" || encoding === "") && (encoding = "utf8"), !Buffer.isEncoding(encoding))
        throw new TypeError('"encoding" must be a valid string encoding');
      return isModern ? Buffer.from(string, encoding) : new Buffer(string, encoding);
    }
    function bufferFrom(value, encodingOrOffset, length) {
      if (typeof value == "number")
        throw new TypeError('"value" argument must not be a number');
      return isArrayBuffer(value) ? fromArrayBuffer(value, encodingOrOffset, length) : typeof value == "string" ? fromString(value, encodingOrOffset) : isModern ? Buffer.from(value) : new Buffer(value);
    }
    module.exports = bufferFrom;
  }
});

// ../../node_modules/.pnpm/source-map-support@0.5.21/node_modules/source-map-support/source-map-support.js
var require_source_map_support = __commonJS({
  "../../node_modules/.pnpm/source-map-support@0.5.21/node_modules/source-map-support/source-map-support.js"(exports, module) {
    init_cjs_shims();
    var SourceMapConsumer = require_source_map().SourceMapConsumer, path = __require("path"), fs;
    try {
      fs = __require("fs"), (!fs.existsSync || !fs.readFileSync) && (fs = null);
    } catch {
    }
    var bufferFrom = require_buffer_from();
    function dynamicRequire(mod, request) {
      return mod.require(request);
    }
    var errorFormatterInstalled = !1, uncaughtShimInstalled = !1, emptyCacheBetweenOperations = !1, environment = "auto", fileContentsCache = {}, sourceMapCache = {}, reSourceMap = /^data:application\/json[^,]+base64,/, retrieveFileHandlers = [], retrieveMapHandlers = [];
    function isInBrowser() {
      return environment === "browser" ? !0 : environment === "node" ? !1 : typeof window < "u" && typeof XMLHttpRequest == "function" && !(window.require && window.module && window.process && window.process.type === "renderer");
    }
    function hasGlobalProcessEventEmitter() {
      return typeof process == "object" && process !== null && typeof process.on == "function";
    }
    function globalProcessVersion() {
      return typeof process == "object" && process !== null ? process.version : "";
    }
    function globalProcessStderr() {
      if (typeof process == "object" && process !== null)
        return process.stderr;
    }
    function globalProcessExit(code) {
      if (typeof process == "object" && process !== null && typeof process.exit == "function")
        return process.exit(code);
    }
    function handlerExec(list) {
      return function(arg) {
        for (var i = 0; i < list.length; i++) {
          var ret = list[i](arg);
          if (ret)
            return ret;
        }
        return null;
      };
    }
    var retrieveFile = handlerExec(retrieveFileHandlers);
    retrieveFileHandlers.push(function(path2) {
      if (path2 = path2.trim(), /^file:/.test(path2) && (path2 = path2.replace(/file:\/\/\/(\w:)?/, function(protocol, drive) {
        return drive ? "" : (
          // file:///C:/dir/file -> C:/dir/file
          "/"
        );
      })), path2 in fileContentsCache)
        return fileContentsCache[path2];
      var contents = "";
      try {
        if (fs)
          fs.existsSync(path2) && (contents = fs.readFileSync(path2, "utf8"));
        else {
          var xhr = new XMLHttpRequest();
          xhr.open(
            "GET",
            path2,
            /** async */
            !1
          ), xhr.send(null), xhr.readyState === 4 && xhr.status === 200 && (contents = xhr.responseText);
        }
      } catch {
      }
      return fileContentsCache[path2] = contents;
    });
    function supportRelativeURL(file, url) {
      if (!file) return url;
      var dir = path.dirname(file), match = /^\w+:\/\/[^\/]*/.exec(dir), protocol = match ? match[0] : "", startPath = dir.slice(protocol.length);
      return protocol && /^\/\w\:/.test(startPath) ? (protocol += "/", protocol + path.resolve(dir.slice(protocol.length), url).replace(/\\/g, "/")) : protocol + path.resolve(dir.slice(protocol.length), url);
    }
    function retrieveSourceMapURL(source) {
      var fileData;
      if (isInBrowser())
        try {
          var xhr = new XMLHttpRequest();
          xhr.open("GET", source, !1), xhr.send(null), fileData = xhr.readyState === 4 ? xhr.responseText : null;
          var sourceMapHeader = xhr.getResponseHeader("SourceMap") || xhr.getResponseHeader("X-SourceMap");
          if (sourceMapHeader)
            return sourceMapHeader;
        } catch {
        }
      fileData = retrieveFile(source);
      for (var re = /(?:\/\/[@#][\s]*sourceMappingURL=([^\s'"]+)[\s]*$)|(?:\/\*[@#][\s]*sourceMappingURL=([^\s*'"]+)[\s]*(?:\*\/)[\s]*$)/mg, lastMatch, match; match = re.exec(fileData); ) lastMatch = match;
      return lastMatch ? lastMatch[1] : null;
    }
    var retrieveSourceMap = handlerExec(retrieveMapHandlers);
    retrieveMapHandlers.push(function(source) {
      var sourceMappingURL = retrieveSourceMapURL(source);
      if (!sourceMappingURL) return null;
      var sourceMapData;
      if (reSourceMap.test(sourceMappingURL)) {
        var rawData = sourceMappingURL.slice(sourceMappingURL.indexOf(",") + 1);
        sourceMapData = bufferFrom(rawData, "base64").toString(), sourceMappingURL = source;
      } else
        sourceMappingURL = supportRelativeURL(source, sourceMappingURL), sourceMapData = retrieveFile(sourceMappingURL);
      return sourceMapData ? {
        url: sourceMappingURL,
        map: sourceMapData
      } : null;
    });
    function mapSourcePosition(position) {
      var sourceMap = sourceMapCache[position.source];
      if (!sourceMap) {
        var urlAndMap = retrieveSourceMap(position.source);
        urlAndMap ? (sourceMap = sourceMapCache[position.source] = {
          url: urlAndMap.url,
          map: new SourceMapConsumer(urlAndMap.map)
        }, sourceMap.map.sourcesContent && sourceMap.map.sources.forEach(function(source, i) {
          var contents = sourceMap.map.sourcesContent[i];
          if (contents) {
            var url = supportRelativeURL(sourceMap.url, source);
            fileContentsCache[url] = contents;
          }
        })) : sourceMap = sourceMapCache[position.source] = {
          url: null,
          map: null
        };
      }
      if (sourceMap && sourceMap.map && typeof sourceMap.map.originalPositionFor == "function") {
        var originalPosition = sourceMap.map.originalPositionFor(position);
        if (originalPosition.source !== null)
          return originalPosition.source = supportRelativeURL(
            sourceMap.url,
            originalPosition.source
          ), originalPosition;
      }
      return position;
    }
    function mapEvalOrigin(origin) {
      var match = /^eval at ([^(]+) \((.+):(\d+):(\d+)\)$/.exec(origin);
      if (match) {
        var position = mapSourcePosition({
          source: match[2],
          line: +match[3],
          column: match[4] - 1
        });
        return "eval at " + match[1] + " (" + position.source + ":" + position.line + ":" + (position.column + 1) + ")";
      }
      return match = /^eval at ([^(]+) \((.+)\)$/.exec(origin), match ? "eval at " + match[1] + " (" + mapEvalOrigin(match[2]) + ")" : origin;
    }
    function CallSiteToString() {
      var fileName, fileLocation = "";
      if (this.isNative())
        fileLocation = "native";
      else {
        fileName = this.getScriptNameOrSourceURL(), !fileName && this.isEval() && (fileLocation = this.getEvalOrigin(), fileLocation += ", "), fileName ? fileLocation += fileName : fileLocation += "<anonymous>";
        var lineNumber = this.getLineNumber();
        if (lineNumber != null) {
          fileLocation += ":" + lineNumber;
          var columnNumber = this.getColumnNumber();
          columnNumber && (fileLocation += ":" + columnNumber);
        }
      }
      var line = "", functionName = this.getFunctionName(), addSuffix = !0, isConstructor = this.isConstructor(), isMethodCall = !(this.isToplevel() || isConstructor);
      if (isMethodCall) {
        var typeName = this.getTypeName();
        typeName === "[object Object]" && (typeName = "null");
        var methodName = this.getMethodName();
        functionName ? (typeName && functionName.indexOf(typeName) != 0 && (line += typeName + "."), line += functionName, methodName && functionName.indexOf("." + methodName) != functionName.length - methodName.length - 1 && (line += " [as " + methodName + "]")) : line += typeName + "." + (methodName || "<anonymous>");
      } else isConstructor ? line += "new " + (functionName || "<anonymous>") : functionName ? line += functionName : (line += fileLocation, addSuffix = !1);
      return addSuffix && (line += " (" + fileLocation + ")"), line;
    }
    function cloneCallSite(frame) {
      var object = {};
      return Object.getOwnPropertyNames(Object.getPrototypeOf(frame)).forEach(function(name) {
        object[name] = /^(?:is|get)/.test(name) ? function() {
          return frame[name].call(frame);
        } : frame[name];
      }), object.toString = CallSiteToString, object;
    }
    function wrapCallSite(frame, state) {
      if (state === void 0 && (state = { nextPosition: null, curPosition: null }), frame.isNative())
        return state.curPosition = null, frame;
      var source = frame.getFileName() || frame.getScriptNameOrSourceURL();
      if (source) {
        var line = frame.getLineNumber(), column = frame.getColumnNumber() - 1, noHeader = /^v(10\.1[6-9]|10\.[2-9][0-9]|10\.[0-9]{3,}|1[2-9]\d*|[2-9]\d|\d{3,}|11\.11)/, headerLength = noHeader.test(globalProcessVersion()) ? 0 : 62;
        line === 1 && column > headerLength && !isInBrowser() && !frame.isEval() && (column -= headerLength);
        var position = mapSourcePosition({
          source,
          line,
          column
        });
        state.curPosition = position, frame = cloneCallSite(frame);
        var originalFunctionName = frame.getFunctionName;
        return frame.getFunctionName = function() {
          return state.nextPosition == null ? originalFunctionName() : state.nextPosition.name || originalFunctionName();
        }, frame.getFileName = function() {
          return position.source;
        }, frame.getLineNumber = function() {
          return position.line;
        }, frame.getColumnNumber = function() {
          return position.column + 1;
        }, frame.getScriptNameOrSourceURL = function() {
          return position.source;
        }, frame;
      }
      var origin = frame.isEval() && frame.getEvalOrigin();
      return origin && (origin = mapEvalOrigin(origin), frame = cloneCallSite(frame), frame.getEvalOrigin = function() {
        return origin;
      }), frame;
    }
    function prepareStackTrace(error, stack) {
      emptyCacheBetweenOperations && (fileContentsCache = {}, sourceMapCache = {});
      for (var name = error.name || "Error", message = error.message || "", errorString = name + ": " + message, state = { nextPosition: null, curPosition: null }, processedStack = [], i = stack.length - 1; i >= 0; i--)
        processedStack.push(`
    at ` + wrapCallSite(stack[i], state)), state.nextPosition = state.curPosition;
      return state.curPosition = state.nextPosition = null, errorString + processedStack.reverse().join("");
    }
    function getErrorSource(error) {
      var match = /\n    at [^(]+ \((.*):(\d+):(\d+)\)/.exec(error.stack);
      if (match) {
        var source = match[1], line = +match[2], column = +match[3], contents = fileContentsCache[source];
        if (!contents && fs && fs.existsSync(source))
          try {
            contents = fs.readFileSync(source, "utf8");
          } catch {
            contents = "";
          }
        if (contents) {
          var code = contents.split(/(?:\r\n|\r|\n)/)[line - 1];
          if (code)
            return source + ":" + line + `
` + code + `
` + new Array(column).join(" ") + "^";
        }
      }
      return null;
    }
    function printErrorAndExit(error) {
      var source = getErrorSource(error), stderr = globalProcessStderr();
      stderr && stderr._handle && stderr._handle.setBlocking && stderr._handle.setBlocking(!0), source && (console.error(), console.error(source)), console.error(error.stack), globalProcessExit(1);
    }
    function shimEmitUncaughtException() {
      var origEmit = process.emit;
      process.emit = function(type) {
        if (type === "uncaughtException") {
          var hasStack = arguments[1] && arguments[1].stack, hasListeners = this.listeners(type).length > 0;
          if (hasStack && !hasListeners)
            return printErrorAndExit(arguments[1]);
        }
        return origEmit.apply(this, arguments);
      };
    }
    var originalRetrieveFileHandlers = retrieveFileHandlers.slice(0), originalRetrieveMapHandlers = retrieveMapHandlers.slice(0);
    exports.wrapCallSite = wrapCallSite;
    exports.getErrorSource = getErrorSource;
    exports.mapSourcePosition = mapSourcePosition;
    exports.retrieveSourceMap = retrieveSourceMap;
    exports.install = function(options) {
      if (options = options || {}, options.environment && (environment = options.environment, ["node", "browser", "auto"].indexOf(environment) === -1))
        throw new Error("environment " + environment + " was unknown. Available options are {auto, browser, node}");
      if (options.retrieveFile && (options.overrideRetrieveFile && (retrieveFileHandlers.length = 0), retrieveFileHandlers.unshift(options.retrieveFile)), options.retrieveSourceMap && (options.overrideRetrieveSourceMap && (retrieveMapHandlers.length = 0), retrieveMapHandlers.unshift(options.retrieveSourceMap)), options.hookRequire && !isInBrowser()) {
        var Module = dynamicRequire(module, "module"), $compile = Module.prototype._compile;
        $compile.__sourceMapSupport || (Module.prototype._compile = function(content, filename) {
          return fileContentsCache[filename] = content, sourceMapCache[filename] = void 0, $compile.call(this, content, filename);
        }, Module.prototype._compile.__sourceMapSupport = !0);
      }
      if (emptyCacheBetweenOperations || (emptyCacheBetweenOperations = "emptyCacheBetweenOperations" in options ? options.emptyCacheBetweenOperations : !1), errorFormatterInstalled || (errorFormatterInstalled = !0, Error.prepareStackTrace = prepareStackTrace), !uncaughtShimInstalled) {
        var installHandler = "handleUncaughtExceptions" in options ? options.handleUncaughtExceptions : !0;
        try {
          var worker_threads = dynamicRequire(module, "worker_threads");
          worker_threads.isMainThread === !1 && (installHandler = !1);
        } catch {
        }
        installHandler && hasGlobalProcessEventEmitter() && (uncaughtShimInstalled = !0, shimEmitUncaughtException());
      }
    };
    exports.resetRetrieveHandlers = function() {
      retrieveFileHandlers.length = 0, retrieveMapHandlers.length = 0, retrieveFileHandlers = originalRetrieveFileHandlers.slice(0), retrieveMapHandlers = originalRetrieveMapHandlers.slice(0), retrieveSourceMap = handlerExec(retrieveMapHandlers), retrieveFile = handlerExec(retrieveFileHandlers);
    };
  }
});

export {
  require_source_map,
  require_buffer_from,
  require_source_map_support
};
//# sourceMappingURL=chunk-UATXMR5F.js.map
