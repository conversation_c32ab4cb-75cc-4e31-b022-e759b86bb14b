import {
  describe,
  globalExpect,
  test,
  vi
} from "../../../chunk-BQ3PZIHZ.js";
import {
  extractCommandData,
  writeCommandDocumentation,
  writeCommandFlagInterface,
  writeCommandUsageExampleFile
} from "../../../chunk-4JN3JAPL.js";
import "../../../chunk-2IA24ROR.js";
import "../../../chunk-VLSFD7SJ.js";
import "../../../chunk-SHWOPMLQ.js";
import "../../../chunk-K2VBTQSL.js";
import "../../../chunk-ZAVXS5HH.js";
import "../../../chunk-C4XAKIGB.js";
import "../../../chunk-PUO72IWW.js";
import "../../../chunk-25IMI7TH.js";
import "../../../chunk-G2VTHDI5.js";
import "../../../chunk-WRIQTRQE.js";
import {
  writeFile
} from "../../../chunk-B36FYNEM.js";
import "../../../chunk-F7F4BQYW.js";
import "../../../chunk-UMUTXITN.js";
import "../../../chunk-UATXMR5F.js";
import "../../../chunk-B5EXYCV3.js";
import "../../../chunk-G2ZZKGSV.js";
import "../../../chunk-75LV6AQS.js";
import "../../../chunk-UV5N2VL7.js";
import "../../../chunk-XE5EOEBL.js";
import "../../../chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "../../../chunk-PKR7KJ6P.js";

// src/cli/commands/docs/generate.test.ts
init_cjs_shims();
vi.mock("@shopify/cli-kit/node/fs");
var testCommand = {
  aliases: [],
  args: {},
  flags: {
    flag1: {
      name: "flag1",
      type: "option",
      char: "f",
      description: "flag description"
    },
    flag2: {
      name: "flag2",
      type: "boolean",
      char: "a",
      description: "another flag description",
      env: "FLAG_ENV",
      allowNo: !1
    }
  },
  descriptionWithMarkdown: "command markdown description",
  description: "command description",
  summary: "command summary",
  hidden: !1,
  hiddenAliases: [],
  id: "topic:test-command",
  load: () => Promise.resolve({})
}, commandData = {
  commandName: "topic test-command",
  fileName: "topic-test-command",
  interfaceName: "topictestcommand",
  hasTopic: !0,
  topic: "topic",
  hasFlags: !0
};
describe("extractCommandData", () => {
  test("returns the correct data", () => {
    globalExpect(extractCommandData(testCommand)).toEqual(commandData);
  });
});
describe("writeCommandFlagInterface", () => {
  test("calls writeFile with the correct content", async () => {
    await writeCommandFlagInterface(testCommand, commandData), globalExpect(writeFile).toHaveBeenCalledWith(
      globalExpect.stringContaining("docs-shopify.dev/commands/interfaces/topic-test-command.interface.ts"),
      `// This is an autogenerated file. Don't edit this file manually.
export interface topictestcommand {
  /**
   * flag description
   *
   */
  '-f, --flag1 <value>'?: string

  /**
   * another flag description
   * @environment FLAG_ENV
   */
  '-a, --flag2'?: ''
}
`
    );
  }), test("calls writeFile with the correct content for a command with no flags", async () => {
    await writeCommandFlagInterface({ ...testCommand, flags: {} }, commandData), globalExpect(writeFile).toHaveBeenCalledWith(
      globalExpect.stringContaining("docs-shopify.dev/commands/interfaces/topic-test-command.interface.ts"),
      `// This is an autogenerated file. Don't edit this file manually.
export interface topictestcommand {

}
`
    );
  });
});
describe("writeCommandDocs", () => {
  test("calls writeFile with the correct content", async () => {
    await writeCommandDocumentation(testCommand, commandData), globalExpect(writeFile).toHaveBeenCalledWith(
      globalExpect.stringContaining("docs-shopify.dev/commands/topic-test-command.doc.ts"),
      `// This is an autogenerated file. Don't edit this file manually.
import {ReferenceEntityTemplateSchema} from '@shopify/generate-docs'

const data: ReferenceEntityTemplateSchema = {
  name: 'topic test-command',
  description: \`command markdown description\`,
  overviewPreviewDescription: \`command summary\`,
  type: 'command',
  isVisualComponent: false,
  defaultExample: {
    codeblock: {
      tabs: [
        {
          title: 'topic test-command',
          code: './examples/topic-test-command.example.sh',
          language: 'bash',
        },
      ],
      title: 'topic test-command',
    },
  },
  definitions: [
  {
    title: 'Flags',
    description: 'The following flags are available for the \`topic test-command\` command:',
    type: 'topictestcommand',
  },
  ],
  category: 'topic',
  related: [
  ],
}

export default data`
    );
  });
});
describe("writeCommandUsageExampleFile", () => {
  test("calls writeFile with the correct content", async () => {
    await writeCommandUsageExampleFile(testCommand, commandData), globalExpect(writeFile).toHaveBeenCalledWith(
      globalExpect.stringContaining("docs-shopify.dev/commands/examples/topic-test-command.example.sh"),
      "shopify topic test-command [flags]"
    );
  });
});
//# sourceMappingURL=generate.test.js.map
