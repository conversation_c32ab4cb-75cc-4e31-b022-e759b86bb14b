{"name": "@shopify/cli", "version": "3.85.0", "private": false, "description": "A CLI tool to build for the Shopify platform", "keywords": ["shopify", "shopify-cli", "shopify-partners"], "homepage": "https://github.com/shopify/cli#readme", "bugs": {"url": "https://github.com/Shopify/cli/issues"}, "repository": {"type": "git", "url": "https://github.com/Shopify/cli.git", "directory": "packages/cli"}, "license": "MIT", "type": "module", "exports": {"./assets/*": "./assets/*", ".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./package.json": "./package.json"}, "main": "src/index.js", "module": "src/index.js", "bin": {"shopify": "./bin/run.js"}, "files": ["/assets", "/bin/run.cmd", "/bin/run.js", "/dist", "/oclif.manifest.json"], "eslintConfig": {"extends": ["../../.eslintrc.cjs"], "overrides": [{"files": ["**/bin/*.js"], "parser": "espree", "extends": ["plugin:@typescript-eslint/disable-type-checked"], "rules": {"@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-floating-promises": "off", "@typescript-eslint/no-misused-promises": "off", "@typescript-eslint/no-unnecessary-type-assertion": "off", "@typescript-eslint/switch-exhaustiveness-check": "off", "import/first": "off", "import/no-unresolved": "off", "node/shebang": "off", "@shopify/cli/specific-imports-in-bootstrap-code": ["error", {"static": ["../dist/index.js"]}], "@typescript-eslint/consistent-type-assertions": "off"}}, {"files": ["src/index.ts"], "rules": {"@shopify/cli/specific-imports-in-bootstrap-code": ["error", {"static": ["@shopify/cli-kit/node/cli"]}]}}]}, "dependencies": {"@ast-grep/napi": "0.33.0", "esbuild": "0.25.5", "global-agent": "3.0.0"}, "devDependencies": {"@oclif/core": "4.5.3", "@oclif/plugin-commands": "4.1.33", "@oclif/plugin-plugins": "5.4.47", "@shopify/app": "3.85.0", "@shopify/cli-kit": "3.85.0", "@shopify/plugin-cloudflare": "3.85.0", "@shopify/plugin-did-you-mean": "3.85.0", "@shopify/theme": "3.85.0", "@shopify/store": "3.85.0", "@shopify/cli-hydrogen": "11.1.3", "@types/global-agent": "3.0.0", "@typescript-eslint/eslint-plugin": "7.13.1", "@vitest/coverage-istanbul": "^3.1.4", "esbuild-plugin-copy": "^2.1.1", "espree": "9.6.1"}, "engines": {"node": ">=20.10.0"}, "os": ["darwin", "linux", "win32"], "publishConfig": {"@shopify:registry": "https://registry.npmjs.org", "access": "public"}, "engine-strict": true, "oclif": {"bin": "shopify", "commands": {"strategy": "explicit", "target": "./dist/index.js", "identifier": "COMMANDS"}, "scope": "shopify", "topicSeparator": " ", "topics": {"hydrogen": {"description": "Build Hydrogen storefronts."}, "theme": {"description": "Build Liquid themes."}, "store": {"description": "Manage stores and store data."}, "app": {"description": "Build Shopify apps."}, "app:config": {"description": "Manage app configuration."}, "app:env": {"description": "Manage environment variables."}, "app:function": {"description": "Manage Shopify Functions."}, "auth": {"description": "Auth operations."}, "config": {"description": "CLI configuration options."}, "config:autocorrect": {"description": "Command autocorrection options. If enabled, Shopify CLI will automatically run a corrected version of your command if a correction is available. Off by default."}, "kitchen-sink": {"description": "View the available UI kit components.", "hidden": true}, "plugins": {"hidden": true}}, "additionalHelpFlags": ["-h"], "hooks": {"init": [{"target": "./dist/index.js", "identifier": "AppInitHook"}, {"target": "./dist/index.js", "identifier": "HydrogenInitHook"}], "prerun": "./dist/hooks/prerun.js", "postrun": "./dist/hooks/postrun.js", "command_not_found": {"target": "./dist/index.js", "identifier": "DidYouMeanHook"}, "tunnel_start": {"target": "./dist/index.js", "identifier": "TunnelStartHook"}, "tunnel_provider": {"target": "./dist/index.js", "identifier": "TunnelProviderHook"}, "update": {"target": "./dist/index.js", "identifier": "PluginHook"}, "sensitive_command_metadata": [{"target": "./dist/index.js", "identifier": "AppSensitiveMetadataHook"}], "public_command_metadata": [{"target": "./dist/index.js", "identifier": "AppPublicMetadataHook"}]}}, "scripts": {"build": "nx build", "bundle": "nx bundle", "clean": "nx clean", "lint": "nx lint", "lint:fix": "nx lint:fix", "vitest": "vitest", "type-check": "nx type-check"}}