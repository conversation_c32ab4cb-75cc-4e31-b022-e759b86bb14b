import {
  openURL
} from "./chunk-B36FYNEM.js";
import {
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/services/commands/search.ts
init_cjs_shims();
async function searchService(query) {
  let searchParams = new URLSearchParams();
  searchParams.append("search", query ?? ""), await openURL(`https://shopify.dev?${searchParams.toString()}`);
}

export {
  searchService
};
//# sourceMappingURL=chunk-BB4VAGLH.js.map
