import {
  getOutputUpdateCLIReminder
} from "../chunk-XO7GQ2WG.js";
import {
  startAnalytics
} from "../chunk-ZAVXS5HH.js";
import {
  fetchNotificationsInBackground
} from "../chunk-C4XAKIGB.js";
import "../chunk-PUO72IWW.js";
import "../chunk-25IMI7TH.js";
import {
  checkForCachedNewVersion,
  checkForNewVersion,
  runAtMinimumInterval
} from "../chunk-G2VTHDI5.js";
import {
  CLI_KIT_VERSION
} from "../chunk-WRIQTRQE.js";
import {
  isPreReleaseVersion,
  outputDebug,
  outputWarn
} from "../chunk-B36FYNEM.js";
import "../chunk-F7F4BQYW.js";
import "../chunk-UMUTXITN.js";
import "../chunk-UATXMR5F.js";
import "../chunk-B5EXYCV3.js";
import "../chunk-G2ZZKGSV.js";
import "../chunk-75LV6AQS.js";
import "../chunk-UV5N2VL7.js";
import "../chunk-XE5EOEBL.js";
import "../chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "../chunk-PKR7KJ6P.js";

// src/hooks/prerun.ts
init_cjs_shims();

// ../cli-kit/dist/public/node/hooks/prerun.js
init_cjs_shims();
var hook = async (options) => {
  let commandContent = parseCommandContent({
    id: options.Command.id,
    aliases: options.Command.aliases,
    pluginAlias: options.Command.plugin?.alias
  }), args = options.argv;
  await warnOnAvailableUpgrade(), outputDebug(`Running command ${commandContent.command}`), await startAnalytics({ commandContent, args, commandClass: options.Command }), fetchNotificationsInBackground(options.Command.id);
};
function parseCommandContent(cmdInfo) {
  let commandContent = parseCreateCommand(cmdInfo.pluginAlias);
  return commandContent || (commandContent = parseNormalCommand(cmdInfo.id, cmdInfo.aliases)), commandContent;
}
function parseNormalCommand(id, aliases) {
  return {
    command: id.replace(/:/g, " "),
    topic: parseTopic(id),
    alias: findAlias(aliases)
  };
}
function parseCreateCommand(pluginAlias) {
  if (pluginAlias?.startsWith("@shopify/create-"))
    return { command: pluginAlias.substring(pluginAlias.indexOf("/") + 1) };
}
function parseTopic(cmd) {
  if (cmd.lastIndexOf(":") !== -1)
    return cmd.slice(0, cmd.lastIndexOf(":")).replace(/:/g, " ");
}
function findAlias(aliases) {
  let existingAlias = aliases.find((alias) => alias.split(":").every((aliasToken) => process.argv.includes(aliasToken)));
  if (existingAlias)
    return existingAlias.replace(/:/g, " ");
}
async function warnOnAvailableUpgrade() {
  let cliDependency = "@shopify/cli", currentVersion = CLI_KIT_VERSION;
  isPreReleaseVersion(currentVersion) || (checkForNewVersion(cliDependency, currentVersion, { cacheExpiryInHours: 24 }), await runAtMinimumInterval("warn-on-available-upgrade", { days: 1 }, async () => {
    let newerVersion = checkForCachedNewVersion(cliDependency, currentVersion);
    newerVersion && outputWarn(getOutputUpdateCLIReminder(newerVersion));
  }));
}
export {
  hook as default
};
//# sourceMappingURL=prerun.js.map
