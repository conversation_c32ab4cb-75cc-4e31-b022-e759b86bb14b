import {
  searchService
} from "../../../chunk-BB4VAGLH.js";
import {
  describe,
  globalExpect,
  test,
  vi
} from "../../../chunk-BQ3PZIHZ.js";
import {
  openURL
} from "../../../chunk-B36FYNEM.js";
import "../../../chunk-F7F4BQYW.js";
import "../../../chunk-UMUTXITN.js";
import "../../../chunk-UATXMR5F.js";
import "../../../chunk-B5EXYCV3.js";
import "../../../chunk-G2ZZKGSV.js";
import "../../../chunk-75LV6AQS.js";
import "../../../chunk-UV5N2VL7.js";
import "../../../chunk-XE5EOEBL.js";
import "../../../chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "../../../chunk-PKR7KJ6P.js";

// src/cli/services/commands/search.test.ts
init_cjs_shims();
vi.mock("@shopify/cli-kit/node/system");
describe("searchService", () => {
  test("the right URL is open in the system when a query is passed", async () => {
    await searchService("deploy app"), globalExpect(openURL).toBeCalledWith("https://shopify.dev?search=deploy+app");
  }), test("the right URL is open in the system when a query is not passed", async () => {
    await searchService(), globalExpect(openURL).toBeCalledWith("https://shopify.dev?search=");
  });
});
//# sourceMappingURL=search.test.js.map
