import {
  reportAnalyticsEvent
} from "../chunk-K2VBTQSL.js";
import {
  getNextDeprecationDate
} from "../chunk-ZAVXS5HH.js";
import "../chunk-PUO72IWW.js";
import "../chunk-25IMI7TH.js";
import "../chunk-G2VTHDI5.js";
import "../chunk-WRIQTRQE.js";
import {
  addSensitiveMetadata,
  getAllSensitiveMetadata,
  outputDebug,
  renderWarning
} from "../chunk-B36FYNEM.js";
import "../chunk-F7F4BQYW.js";
import "../chunk-UMUTXITN.js";
import "../chunk-UATXMR5F.js";
import "../chunk-B5EXYCV3.js";
import "../chunk-G2ZZKGSV.js";
import "../chunk-75LV6AQS.js";
import "../chunk-UV5N2VL7.js";
import "../chunk-XE5EOEBL.js";
import "../chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "../chunk-PKR7KJ6P.js";

// src/hooks/postrun.ts
init_cjs_shims();

// ../cli-kit/dist/public/node/hooks/postrun.js
init_cjs_shims();

// ../cli-kit/dist/public/node/hooks/deprecations.js
init_cjs_shims();
var postrun = (Command) => {
  let nextDeprecationDate = getNextDeprecationDate();
  if (nextDeprecationDate) {
    let forThemes = Command.id.includes("theme");
    renderUpgradeWarning(nextDeprecationDate, forThemes);
  }
};
function renderUpgradeWarning(upgradeByDate, forThemes) {
  let headline = `Upgrade to the latest CLI version by ${new Intl.DateTimeFormat("default", {
    year: "numeric",
    month: "long",
    day: "numeric"
  }).format(upgradeByDate)}.`;
  renderWarning({
    headline,
    body: "This command requires an upgrade to continue working as intended.",
    nextSteps: [["Run", { command: "upgrade" }, "to", {
      link: {
        label: "upgrade Shopify CLI",
        url: forThemes ? "https://shopify.dev/docs/themes/tools/cli#upgrade-shopify-cli" : "https://shopify.dev/docs/apps/tools/cli#upgrade-shopify-cli"
      }
    }]]
  });
}

// ../cli-kit/dist/public/node/hooks/postrun.js
var hook = async ({ config, Command }) => {
  await detectStopCommand(Command), await reportAnalyticsEvent({ config, exitMode: "ok" }), postrun(Command);
  let command = Command.id.replace(/:/g, " ");
  outputDebug(`Completed command ${command}`);
};
async function detectStopCommand(commandClass) {
  let currentTime = (/* @__PURE__ */ new Date()).getTime();
  if (commandClass && Object.prototype.hasOwnProperty.call(commandClass, "analyticsStopCommand")) {
    let stopCommand = commandClass.analyticsStopCommand();
    if (stopCommand) {
      let { commandStartOptions } = getAllSensitiveMetadata();
      if (!commandStartOptions)
        return;
      await addSensitiveMetadata(() => ({
        commandStartOptions: {
          ...commandStartOptions,
          startTime: currentTime,
          startCommand: stopCommand
        }
      }));
    }
  }
}
export {
  hook as default
};
//# sourceMappingURL=postrun.js.map
