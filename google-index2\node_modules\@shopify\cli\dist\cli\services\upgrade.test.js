import {
  upgrade
} from "../../chunk-AJOVRT5K.js";
import {
  mockAndCaptureOutput
} from "../../chunk-DJVJNSKZ.js";
import {
  afterEach,
  beforeEach,
  describe,
  globalExpect,
  test,
  vi
} from "../../chunk-BQ3PZIHZ.js";
import {
  node_package_manager_exports
} from "../../chunk-G2VTHDI5.js";
import {
  AbortError,
  captureOutput,
  exec,
  inTemporaryDirectory,
  platformAndArch,
  touchFile,
  writeFile
} from "../../chunk-B36FYNEM.js";
import "../../chunk-F7F4BQYW.js";
import "../../chunk-UMUTXITN.js";
import "../../chunk-UATXMR5F.js";
import "../../chunk-B5EXYCV3.js";
import "../../chunk-G2ZZKGSV.js";
import "../../chunk-75LV6AQS.js";
import "../../chunk-UV5N2VL7.js";
import "../../chunk-XE5EOEBL.js";
import {
  joinPath,
  normalizePath
} from "../../chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "../../chunk-PKR7KJ6P.js";

// src/cli/services/upgrade.test.ts
init_cjs_shims();
var oldCliVersion = "3.0.0", currentCliVersion = "3.10.0";
vi.mock("@shopify/cli-kit/node/os", async () => ({
  platformAndArch: vi.fn()
}));
vi.mock("@shopify/cli-kit/node/system");
beforeEach(async () => {
  vi.mocked(platformAndArch).mockReturnValue({ platform: "windows", arch: "amd64" });
});
afterEach(() => {
  mockAndCaptureOutput().clear();
});
describe("upgrade global CLI", () => {
  test("does not upgrade globally if the latest version is found", async () => {
    await inTemporaryDirectory(async (tmpDir) => {
      let outputMock = mockAndCaptureOutput();
      vi.spyOn(node_package_manager_exports, "checkForNewVersion").mockResolvedValue(void 0), await upgrade(tmpDir, currentCliVersion, { env: {} }), globalExpect(outputMock.info()).toMatchInlineSnapshot(`
        "You're on the latest version, ${currentCliVersion}, no need to upgrade!"
      `);
    });
  }), test("upgrades globally using npm if the latest version is not found", async () => {
    await inTemporaryDirectory(async (tmpDir) => {
      let outputMock = mockAndCaptureOutput();
      vi.spyOn(node_package_manager_exports, "checkForNewVersion").mockResolvedValue(currentCliVersion), await upgrade(tmpDir, oldCliVersion, { env: {} }), globalExpect(vi.mocked(exec)).toHaveBeenCalledWith(
        "npm",
        ["install", "-g", "@shopify/cli@latest", "@shopify/theme@latest"],
        { stdio: "inherit" }
      ), globalExpect(outputMock.info()).toMatchInlineSnapshot(`
        "Upgrading CLI from ${oldCliVersion} to ${currentCliVersion}...
Attempting to upgrade via \`npm install -g @shopify/cli@latest @shopify/theme@latest\`..."
      `), globalExpect(outputMock.success()).toMatchInlineSnapshot(`
        "Upgraded Shopify CLI to version ${currentCliVersion}"
      `);
    });
  }), ["shopify-cli", "shopify-cli@3"].forEach((homebrewPackageName) => {
    test("upgrades globally using Homebrew if the latest version is not found and the CLI was installed via Homebrew", async () => {
      await inTemporaryDirectory(async (tmpDir) => {
        vi.spyOn(node_package_manager_exports, "checkForNewVersion").mockResolvedValue(currentCliVersion), await globalExpect(async () => {
          await upgrade(tmpDir, oldCliVersion, { env: { SHOPIFY_HOMEBREW_FORMULA: homebrewPackageName } });
        }).rejects.toThrowError(AbortError);
      });
    });
  });
});
describe("upgrade local CLI", () => {
  test("throws an error if a valid app config file is missing", async () => {
    await inTemporaryDirectory(async (tmpDir) => {
      await Promise.all([
        writeFile(
          joinPath(tmpDir, "package.json"),
          JSON.stringify({ dependencies: { "@shopify/cli": currentCliVersion, "@shopify/app": currentCliVersion } })
        ),
        touchFile(joinPath(tmpDir, "shopify.wrongapp.toml"))
      ]);
      let outputMock = mockAndCaptureOutput();
      vi.spyOn(node_package_manager_exports, "checkForNewVersion").mockResolvedValue(void 0), await globalExpect(upgrade(tmpDir, currentCliVersion, { env: { npm_config_user_agent: "npm" } })).rejects.toBeInstanceOf(
        AbortError
      );
    });
  }), test("does not upgrade locally if the latest version is found", async () => {
    await inTemporaryDirectory(async (tmpDir) => {
      await Promise.all([
        writeFile(
          joinPath(tmpDir, "package.json"),
          JSON.stringify({ dependencies: { "@shopify/cli": currentCliVersion, "@shopify/app": currentCliVersion } })
        ),
        touchFile(joinPath(tmpDir, "shopify.app.toml"))
      ]);
      let outputMock = mockAndCaptureOutput();
      vi.spyOn(node_package_manager_exports, "checkForNewVersion").mockResolvedValue(void 0), await upgrade(tmpDir, currentCliVersion, { env: { npm_config_user_agent: "npm" } }), globalExpect(outputMock.info()).toMatchInlineSnapshot(`
        "You're on the latest version, ${currentCliVersion}, no need to upgrade!"
      `);
    });
  }), test("upgrades locally if the latest version is not found", async () => {
    await inTemporaryDirectory(async (tmpDir) => {
      await Promise.all([
        writeFile(
          joinPath(tmpDir, "package.json"),
          JSON.stringify({ dependencies: { "@shopify/cli": oldCliVersion, "@shopify/app": oldCliVersion } })
        ),
        touchFile(joinPath(tmpDir, "shopify.app.toml"))
      ]), vi.mocked(captureOutput).mockResolvedValueOnce(tmpDir);
      let outputMock = mockAndCaptureOutput();
      vi.spyOn(node_package_manager_exports, "checkForNewVersion").mockResolvedValueOnce(currentCliVersion);
      let addNPMDependenciesMock = vi.spyOn(node_package_manager_exports, "addNPMDependencies").mockResolvedValue(void 0);
      await upgrade(tmpDir, oldCliVersion, { env: {} }), globalExpect(captureOutput).toHaveBeenCalledWith("npm", ["prefix"], { cwd: normalizePath(tmpDir) }), globalExpect(outputMock.info()).toMatchInlineSnapshot(`
        "Upgrading CLI from ${oldCliVersion} to ${currentCliVersion}..."
      `), globalExpect(addNPMDependenciesMock).toHaveBeenCalledWith([{ name: "@shopify/cli", version: "latest" }], {
        packageManager: "npm",
        type: "prod",
        directory: normalizePath(tmpDir),
        stdout: process.stdout,
        stderr: process.stderr,
        addToRootDirectory: !1
      }), globalExpect(outputMock.success()).toMatchInlineSnapshot(`
        "Upgraded Shopify CLI to version ${currentCliVersion}"
      `);
    });
  }), test("upgrades locally if CLI is on latest version but APP isnt", async () => {
    await inTemporaryDirectory(async (tmpDir) => {
      await Promise.all([
        writeFile(
          joinPath(tmpDir, "package.json"),
          JSON.stringify({ dependencies: { "@shopify/cli": currentCliVersion, "@shopify/app": oldCliVersion } })
        ),
        touchFile(joinPath(tmpDir, "shopify.app.nondefault.toml"))
      ]), vi.mocked(captureOutput).mockResolvedValueOnce(tmpDir);
      let outputMock = mockAndCaptureOutput();
      vi.spyOn(node_package_manager_exports, "checkForNewVersion").mockResolvedValueOnce(void 0).mockResolvedValueOnce(currentCliVersion);
      let addNPMDependenciesMock = vi.spyOn(node_package_manager_exports, "addNPMDependencies").mockResolvedValue(void 0);
      await upgrade(tmpDir, oldCliVersion, { env: {} }), globalExpect(captureOutput).toHaveBeenCalledWith("npm", ["prefix"], { cwd: normalizePath(tmpDir) }), globalExpect(outputMock.info()).toMatchInlineSnapshot(`
        "Upgrading CLI from ${oldCliVersion} to ${currentCliVersion}..."
      `), globalExpect(addNPMDependenciesMock).toHaveBeenCalledWith([{ name: "@shopify/cli", version: "latest" }], {
        packageManager: "npm",
        type: "prod",
        directory: normalizePath(tmpDir),
        stdout: process.stdout,
        stderr: process.stderr,
        addToRootDirectory: !1
      }), globalExpect(outputMock.success()).toMatchInlineSnapshot(`
        "Upgraded Shopify CLI to version ${currentCliVersion}"
      `);
    });
  });
});
//# sourceMappingURL=upgrade.test.js.map
