import {
  fetchNotifications,
  getNotifications,
  stringifyFilters
} from "./chunk-C4XAKIGB.js";
import {
  randomUUID
} from "./chunk-PUO72IWW.js";
import {
  CLI_KIT_VERSION
} from "./chunk-WRIQTRQE.js";
import {
  outputInfo,
  renderSelectPrompt,
  renderSuccess,
  renderTable,
  renderTextPrompt,
  writeFile
} from "./chunk-B36FYNEM.js";
import {
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/services/commands/notifications.ts
init_cjs_shims();
async function generate() {
  let today = /* @__PURE__ */ new Date(), formattedToday = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, "0")}-${today.getDate().toString().padStart(2, "0")}`, id = randomUUID(), type = await renderSelectPrompt({
    message: "Type of message?",
    choices: [
      { label: "Info", value: "info" },
      { label: "Warning", value: "warning" },
      { label: "Error", value: "error" }
    ]
  }), title = await renderTextPrompt({
    message: "Title"
  }), message = await renderTextPrompt({
    message: "Message"
  }), frequency = await renderSelectPrompt({
    message: "Frequency",
    choices: [
      { label: "Only once", value: "once" },
      { label: "Once a week", value: "once_a_week" },
      { label: "Once a day", value: "once_a_day" },
      { label: "Always", value: "always" }
    ]
  }), minVersion = await renderTextPrompt({
    message: "Minimum CLI version (optional)",
    initialAnswer: CLI_KIT_VERSION,
    allowEmpty: !0
  }), maxVersion = await renderTextPrompt({
    message: "Maximum CLI version (optional)",
    initialAnswer: CLI_KIT_VERSION,
    allowEmpty: !0
  }), minDate = await renderTextPrompt({
    message: "Minimum date in YYYY-MM-DD format (optional)",
    initialAnswer: formattedToday,
    allowEmpty: !0
  }), maxDate = await renderTextPrompt({
    message: "Maximum date in YYYY-MM-DD format (optional)",
    initialAnswer: formattedToday,
    allowEmpty: !0
  }), surface = await renderTextPrompt({
    message: "Surface. E.g.: app, theme, hydrogen, theme_app_extension... (optional)",
    allowEmpty: !0
  }), commands = await renderTextPrompt({
    message: "Comma separated list of commands. E.g.: app:generate:extension (optional)",
    allowEmpty: !0
  }), ownerChannel = await renderTextPrompt({
    message: "Slack channel of the team who will own this notification"
  }), notifications = await getNotifications(), notification = {
    id,
    type,
    title,
    frequency,
    message,
    minVersion: minVersion.length === 0 ? void 0 : minVersion,
    maxVersion: maxVersion.length === 0 ? void 0 : maxVersion,
    minDate: minDate.length === 0 ? void 0 : minDate,
    maxDate: maxDate.length === 0 ? void 0 : maxDate,
    surface: surface.length === 0 ? void 0 : surface,
    commands: commands.length === 0 ? void 0 : commands.split(",").map((command) => command.trim()),
    ownerChannel
  };
  notifications.notifications.push(notification), await writeFile("./notifications.json", JSON.stringify(notifications)), renderSuccess({ headline: "notifications.json file updated successfully." });
}
async function list() {
  let notifications = await fetchNotifications(), columns = {
    type: { header: "Type", color: "dim" },
    title: { header: "Title", color: "dim" },
    message: { header: "Message", color: "dim" },
    filters: { header: "Filters", color: "dim" }
  }, rows = notifications.notifications.map((notification) => ({
    type: notification.type,
    title: notification.title ?? "",
    message: notification.message,
    filters: stringifyFilters(notification)
  }));
  renderTable({ rows, columns }), outputInfo(`
`);
}

export {
  generate,
  list
};
//# sourceMappingURL=chunk-ZXKNRG53.js.map
