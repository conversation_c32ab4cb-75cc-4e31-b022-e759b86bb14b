import {
  Login,
  promptSessionSelect
} from "../../../chunk-KU6FTMKN.js";
import {
  mockAndCaptureOutput
} from "../../../chunk-DJVJNSKZ.js";
import {
  describe,
  globalExpect,
  test,
  vi
} from "../../../chunk-BQ3PZIHZ.js";
import "../../../chunk-2IA24ROR.js";
import "../../../chunk-VLSFD7SJ.js";
import "../../../chunk-SHWOPMLQ.js";
import "../../../chunk-K2VBTQSL.js";
import "../../../chunk-ZAVXS5HH.js";
import "../../../chunk-C4XAKIGB.js";
import "../../../chunk-PUO72IWW.js";
import "../../../chunk-25IMI7TH.js";
import "../../../chunk-G2VTHDI5.js";
import "../../../chunk-WRIQTRQE.js";
import "../../../chunk-B36FYNEM.js";
import "../../../chunk-F7F4BQYW.js";
import "../../../chunk-UMUTXITN.js";
import "../../../chunk-UATXMR5F.js";
import "../../../chunk-B5EXYCV3.js";
import "../../../chunk-G2ZZKGSV.js";
import "../../../chunk-75LV6AQS.js";
import "../../../chunk-UV5N2VL7.js";
import "../../../chunk-XE5EOEBL.js";
import "../../../chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "../../../chunk-PKR7KJ6P.js";

// src/cli/commands/auth/login.test.ts
init_cjs_shims();
vi.mock("@shopify/cli-kit/node/session-prompt");
describe("Login command", () => {
  test("runs login without alias flag", async () => {
    let outputMock = mockAndCaptureOutput();
    vi.mocked(promptSessionSelect).mockResolvedValue("test-account"), await Login.run([]), globalExpect(promptSessionSelect).toHaveBeenCalledWith(void 0), globalExpect(outputMock.output()).toMatch("Current account: test-account.");
  }), test("runs login with alias flag", async () => {
    let outputMock = mockAndCaptureOutput();
    vi.mocked(promptSessionSelect).mockResolvedValue("test-account"), await Login.run(["--alias", "my-work-account"]), globalExpect(promptSessionSelect).toHaveBeenCalledWith("my-work-account"), globalExpect(outputMock.output()).toMatch("Current account: test-account.");
  }), test("displays flags correctly in help", () => {
    let flags = Login.flags;
    globalExpect(flags.alias).toBeDefined(), globalExpect(flags.alias.description).toBe("Alias of the session you want to login to."), globalExpect(flags.alias.env).toBe("SHOPIFY_FLAG_AUTH_ALIAS");
  });
});
//# sourceMappingURL=login.test.js.map
