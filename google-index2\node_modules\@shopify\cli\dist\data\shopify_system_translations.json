{"shopify.sentence.words_connector": ", ", "shopify.sentence.two_words_connector": " and ", "shopify.sentence.last_word_connector": ", and ", "shopify.checkout.address_management.confirm_address_deletion": "Are you sure you want to delete the address {{address}}?", "shopify.checkout.general.page_title": "Checkout", "shopify.checkout.general.error_page_title": "Error", "shopify.checkout.general.skip_to_content": "Skip to content", "shopify.checkout.general.breadcrumb": "Breadcrumb", "shopify.checkout.general.continue_button_label": "Continue", "shopify.checkout.general.complete_purchase_button_label": "Complete order", "shopify.checkout.general.pay_now_button_label": "Pay now", "shopify.checkout.general.authenticate_purchase_button_label": "Authenticate payment", "shopify.checkout.general.edit_link_label": "Edit", "shopify.checkout.general.all_rights_reserved": "All rights reserved %{shop_name}", "shopify.checkout.general.print_policies_link_label": "Print", "shopify.checkout.general.cart": "<PERSON><PERSON>", "shopify.checkout.general.close_modal_label": "Close", "shopify.checkout.general.close_notice_label": "Close", "shopify.checkout.general.expand_notice_label": "View more", "shopify.checkout.general.continue_to_shipping_method": "Continue to shipping", "shopify.checkout.general.continue_to_payment_method": "Continue to payment", "shopify.checkout.general.continue_to_review": "Review order", "shopify.checkout.general.back_to_cart": "Return to cart", "shopify.checkout.general.back_to_contact_information": "Return to information", "shopify.checkout.general.back_to_shipping_method": "Return to shipping", "shopify.checkout.general.back_to_payment_method": "Return to payment", "shopify.checkout.general.choose_shipping_method": "Choose a shipping method", "shopify.checkout.general.choose_payment_method": "Choose a payment method", "shopify.checkout.general.choose_billing_address": "Choose a billing address", "shopify.checkout.general.contact_us_html": "Contact us for more information", "shopify.checkout.general.autocomplete_title": "Suggestions", "shopify.checkout.general.autocomplete_single_item": "1 item available", "shopify.checkout.general.autocomplete_multi_item": "{{number}} items available", "shopify.checkout.general.autocomplete_selection": "{{number}} of {{total}}", "shopify.checkout.general.autocomplete_close": "Close suggestions", "shopify.checkout.general.forwarding_indicator.new_window": "Opens in a new window.", "shopify.checkout.general.forwarding_indicator.external": "Opens external website.", "shopify.checkout.general.forwarding_indicator.external_and_new_window": "Opens external website in a new window.", "shopify.checkout.general.checkout_context.step_one": "%{shop_name} - Checkout", "shopify.checkout.general.tooltip_accessibility_label": "More information", "shopify.checkout.general.qr_code": "QR code", "shopify.checkout.general.errors.only_empty_terms_accepted": "This policy only accepts empty terms.", "shopify.checkout.general.errors.session_identifier_not_unique_error": "Something went wrong with your order, please try checking out again.", "shopify.checkout.general.errors.line_item_limit_reached": "The maximum number of line items has been reached.", "shopify.checkout.general.errors.session_is_already_finished": "This checkout session has already been finalized.", "shopify.checkout.general.errors.merchandise.only_value_constraint": "Only exact values are allowed in the merchandise input.", "shopify.checkout.general.errors.merchandise.merchandise_not_found": "Product not found.", "shopify.checkout.general.errors.merchandise.merchandise_variant_not_found": "Product variant not found.", "shopify.checkout.general.errors.merchandise.merchandise_not_applicable": "Product variant cannot be purchased.", "shopify.checkout.general.errors.merchandise.mixed_currencies": "Checkout contains line items with different currencies.", "shopify.checkout.general.errors.merchandise.bundle_products_not_supported_for_b2b": "Bundled items can't be added to B2B orders for this location.", "shopify.checkout.general.errors.merchandise.gift_card_quantity_mismatch": "When specifying gift card codes, the line item quantity and the number of gift card codes must match.", "shopify.checkout.general.errors.merchandise.gift_card_not_supported": "Gift cards are not supported on this checkout.", "shopify.checkout.general.errors.merchandise.gift_card_not_unique": "Gift card with code {{code}} occurs more than once in this checkout. Codes are formatted before comparison.", "shopify.checkout.general.errors.merchandise.gift_card_used": "Gift card code {{code}} is already assigned to a gift card. Codes are formatted before comparison.", "shopify.checkout.general.errors.merchandise.gift_card_invalid": "Gift card code {{code}} is invalid.", "shopify.checkout.general.errors.merchandise.gift_card_code_changed": "Gift card code {{proposed_code}} was changed to {{formatted_code}} to conform to formatting rules.", "shopify.checkout.general.errors.merchandise.gift_card_with_attributes_not_supported": "Gift cards with custom attributes are not supported on this checkout.", "shopify.checkout.general.errors.merchandise.gift_card_with_components_not_supported": "Gift cards with components are not supported on this checkout.", "shopify.checkout.general.errors.merchandise.selling_plan_mismatch": "The subscription has been updated.", "shopify.checkout.general.errors.merchandise.selling_plan_not_supported": "Subscriptions are not supported.", "shopify.checkout.general.errors.merchandise.selling_plan_not_supported_b2b": "Subscriptions are not supported for B2B checkout.", "shopify.checkout.general.errors.merchandise.price_mismatch": "The previously proposed price for this merchandise was invalid and has been updated.", "shopify.checkout.general.errors.merchandise.inventory_not_found": "No inventory found for this merchandise.", "shopify.checkout.general.errors.merchandise.product_not_published": "Product is not published.", "shopify.checkout.general.errors.merchandise.product_not_published_in_buyer_location": "Product is not published in buyer location.", "shopify.checkout.general.errors.merchandise.duplicated_stable_id": "This merchandise line has the same stable-ID as another line.", "shopify.checkout.general.errors.merchandise.minimum_quantity_not_met": "The quantity of some items doesn’t meet the minimum required.", "shopify.checkout.general.errors.merchandise.maximum_quantity_exceeded": "The quantity of some items exceeds the maximum allowed.", "shopify.checkout.general.errors.merchandise.quantity_increment_not_met": "The quantity of some items doesn't meet the order limits.", "shopify.checkout.general.errors.merchandise.quantity_rules_not_met": "The quantity of some items doesn't meet the order limits.", "shopify.checkout.general.errors.merchandise.bundle_requires_components": "This bundle merchandise has no components.", "shopify.checkout.general.errors.merchandise.title_too_long": "Merchandise title is too long.", "shopify.checkout.general.errors.merchandise.title_empty": "Merchandise title is empty.", "shopify.checkout.general.errors.buyer_identity.business_customer_does_not_match_company": "Company contact does not have access to company.", "shopify.checkout.general.errors.buyer_identity.customer_account_required": "You must sign in to continue.", "shopify.checkout.general.errors.buyer_identity.company_location_required": "Customer must choose a company location to continue.", "shopify.checkout.general.errors.buyer_identity.company_purchase_permission_required": "Customer does not have permission to place company orders.", "shopify.checkout.general.errors.buyer_identity.personal_order_not_allowed": "Personal orders are not allowed for this shop.", "shopify.checkout.general.errors.buyer_identity.lost_access_to_company": "Customer lost access to any company.", "shopify.checkout.general.errors.buyer_identity.contact_info_does_not_match_customer_account": "Contact info must match the account you're signed in to.", "shopify.checkout.general.errors.buyer_identity.phone_number_does_not_match_expected_pattern": "The specified phone number does not match the expected pattern.", "shopify.checkout.general.errors.buyer_identity.email_required": "An email is required when the merchandise is purchased using a subscription.", "shopify.checkout.general.errors.buyer_identity.email_domain_is_invalid": "This specified domain cannot be resolved.", "shopify.checkout.general.errors.buyer_identity.missing_contact_method": "Missing a valid contact method.", "shopify.checkout.general.errors.buyer_identity.checkout_completion_target_is_missing": "Checkout completion target is missing.", "shopify.checkout.general.errors.buyer_identity.invalid_customer_profile": "An invalid customer profile was provided.", "shopify.checkout.general.errors.buyer_identity.customer_profile_access_restricted": "Checkout does not have permission to access the customer profile.", "shopify.checkout.general.errors.buyer_identity.checkout_completion_target_is_invalid_proc": "Invalid checkout completion target.", "shopify.checkout.general.errors.buyer_identity.buyer_identity_presentment_currency_does_not_match": "There was a problem processing your order. Try again in a few minutes.", "shopify.checkout.general.errors.delivery.only_one_delivery_line_allowed": "Cart must have only one delivery line.", "shopify.checkout.general.errors.delivery.wrong_number_of_delivery_lines": "A delivery line must be specified for each group of merchandise.", "shopify.checkout.general.errors.delivery.external_delivery_promise_error": "An external delivery promise fetch error.", "shopify.checkout.general.errors.delivery.options_instructions_invalid": "The specified instructions does not match the expected pattern.", "shopify.checkout.general.errors.delivery.destination_address_required": "A destination address is required in order to continue.", "shopify.checkout.general.errors.delivery.pickup_points_unavailable": "Pickup points are currently unavailable.", "shopify.checkout.general.errors.delivery.local_pickup_delivery_line_detail_changed": "The current local pickup delivery strategy must be changed in order to continue.", "shopify.checkout.general.errors.delivery.selected_pickup_option_no_longer_available": "The previously selected pickup option is no longer available.", "shopify.checkout.general.errors.delivery.local_pickup_no_delivery_strategy_available": "No available local pickup delivery strategy found.", "shopify.checkout.general.errors.delivery.origin_location_id_required": "An origin location ID is required when the RETAIL delivery method is requested.", "shopify.checkout.general.errors.delivery.invalid_origin_location_id": "Could not find a Location matching the origin location ID.", "shopify.checkout.general.errors.delivery.origin_location_id_not_allowed": "The requested delivery methods don't support an origin location ID.", "shopify.checkout.general.errors.delivery.auto_fulfill_not_allowed": "This session is not authorized to override fulfillment behavior.", "shopify.checkout.general.errors.delivery.auto_fulfill_required": "Auto fulfill is required when the RETAIL delivery method is requested.", "shopify.checkout.general.errors.delivery.prefetch_shipping_rates_used": "Prefetched shipping rates should never return rates.", "shopify.checkout.general.errors.delivery.fulfillment_constraints_not_satisfied": "One or more fulfillment constraints were not satisfied.", "shopify.checkout.general.errors.delivery.must_fulfill_from_constraint_not_satisfied": "Fulfillment constraints are not satisfied because items cannot be fulfilled from required locations.", "shopify.checkout.general.errors.delivery.must_fulfill_from_same_location_constraint_not_satisfied": "Fulfillment constraints are not satisfied because items cannot be fulfilled from the same location.", "shopify.checkout.general.errors.delivery.delivery_strategy_conditions_not_satisfied": "Delivery strategies are available for this location, but their conditions are not satisfied.", "shopify.checkout.general.errors.delivery.out_of_stock_at_origin_location": "No stock at the origin location.", "shopify.checkout.general.errors.delivery.invalid_signature": "Invalid discount signature.", "shopify.checkout.general.errors.discount.discounts_not_allowed_for_b2b": "Discounts are not allowed in B2B stores.", "shopify.checkout.general.errors.discount.custom_discount_not_honoured": "The supplied custom discount was not honoured.", "shopify.checkout.general.errors.discount.unsupported_expectation": "The supplied discount line contained an expectation that the policy could not interpret.", "shopify.checkout.general.errors.discount.additional_requested_discount_code_discarded": "{{code}} is valid but not applicable. Check discount terms.", "shopify.checkout.general.errors.discount.additional_requested_discount_discarded": "Your discount is valid but not applicable. Check discount terms.", "shopify.checkout.general.errors.discount.allocations_mismatch": "Your discount code has changed. Review your cart and try again.", "shopify.checkout.general.errors.discount.discount_code_not_honoured": "<strong style=\"text-transform:uppercase\">%{discount_rejection_message}</strong>", "shopify.checkout.general.errors.discount.discount_title_too_long": "Discount title cannot be longer than 255 characters.", "shopify.checkout.general.errors.tax.new_tax_must_be_accepted": "Taxes associated to your order have changed. Review and try again.", "shopify.checkout.general.errors.tax.unresolvable_tax_terms": "The proposed terms are unresolvable given the current negotiation context.", "shopify.checkout.general.errors.tax.delivery_price_is_abstract": "The related delivery price is abstract.", "shopify.checkout.general.errors.tax.tax_inclusivity_mismatch": "Renegotiate with concrete totalTaxAndDutyAmount or totalAmountIncludedInTarget value.", "shopify.checkout.general.errors.tax.tax_exemption_not_supported": "Tax exemptions are not supported for the current negotiation session.", "shopify.checkout.general.errors.tax.delivery_tax_exemption_not_supported": "Tax exemptions for delivery lines are not currently supported.", "shopify.checkout.general.errors.tax.proposed_allocations_required": "Missing 'proposed allocations' field.", "shopify.checkout.general.errors.tax.merchandise_terms_must_be_defined": "Merchandise terms must be defined.", "shopify.checkout.general.errors.tax.delivery_terms_must_be_defined": "Delivery terms must be defined.", "shopify.checkout.general.errors.tax.merchandise_discount_terms_must_be_defined": "Merchandise discount terms must be defined.", "shopify.checkout.general.errors.tax.delivery_discount_terms_must_be_defined": "Delivery discount terms must be defined.", "shopify.checkout.general.errors.tax.allocated_required": "Missing 'allocated' field.", "shopify.checkout.general.errors.tax.value_required": "Missing 'value' field.", "shopify.checkout.general.errors.tax.line_splits_not_supported": "'merchandise lines' field must be split in such that each have a single line component in the seller proposal.", "shopify.checkout.general.errors.payment.manual_payments_not_allowed_for_b2b": "Manual payments are not allowed in B2B stores.", "shopify.checkout.general.errors.payment.positive_amount_expected": "Amount to pay must be greater than 0.", "shopify.checkout.general.errors.payment.total_amount_mismatch": "Total to pay does not match the sum of the payments amount.", "shopify.checkout.general.errors.payment.subscription_terms_not_accepted": "Subscription terms not accepted.", "shopify.checkout.general.errors.payment.missing_session_id": "Missing credit card session information.", "shopify.checkout.general.errors.payment.gateway_not_supported": "Payment gateway does not support storing the payment method.", "shopify.checkout.general.errors.payment.credit_card_last_name_required": "'last name' cannot be nil.", "shopify.checkout.general.errors.payment.credit_card_update_failed": "Credit card update failed.", "shopify.checkout.general.errors.payment.installments_amount_invalid": "Invalid amount for Installments transaction.", "shopify.checkout.general.errors.payment.installments_test_mode_not_allowed": "Test mode not allowed.", "shopify.checkout.general.errors.payment.installments_gift_card_not_allowed": "Installments does not allow the use of gift cards.", "shopify.checkout.general.errors.payment.installments_unsupported_currency": "Installments does not support this currency.", "shopify.checkout.general.errors.payment.installments_unsupported_shipping": "Installments does not support shipping to this address.", "shopify.checkout.general.errors.payment.installments_unsupported_billing": "Installments does not support this billing address.", "shopify.checkout.general.errors.payment.installments_unsupported_subscriptions": "Installments does not support subscriptions.", "shopify.checkout.general.errors.payment.installments_ineligible_line_item": "Installments is disabled for a line item in this purchase.", "shopify.checkout.general.errors.payment.shop_cash_not_found": "Shop Cash was not found.", "shopify.checkout.general.errors.payment.shop_cash_not_allowed": "Shop Cash not allowed without Shop Pay.", "shopify.checkout.general.errors.payment.shop_cash_unsupported_checkout_source": "Shop Cash does not support this checkout.", "shopify.checkout.general.errors.payment.shop_cash_gift_card_not_allowed": "Shop Cash does not allow the purchase of gift cards.", "shopify.checkout.general.errors.payment.shop_cash_not_enabled": "Shop Cash is not enabled for this shop.", "shopify.checkout.general.errors.payment.shop_cash_not_available": "Shop Cash is not supported in this checkout.", "shopify.checkout.general.errors.payment.shop_cash_unsupported_subscriptions": "Shop Cash does not support subscriptions.", "shopify.checkout.general.errors.payment.shop_cash_unsupported_shipping": "Shop Cash does not support merchandise with no shipping.", "shopify.checkout.general.errors.payment.invalid_address_type": "The provided address type is invalid for this payment method.", "shopify.checkout.general.errors.payment.missing_retail_attributions": "Retail attributions are required for manually-entered credit card payments.", "shopify.checkout.general.errors.payment.unexpected_retail_attributions": "Retail attributions unexpectedly provided for credit card payment.", "shopify.checkout.general.errors.payment.invalid_card_source": "This session does not have permission to use this card source.", "shopify.checkout.general.errors.payment.wallet_content_missing": "Wallet content is missing.", "shopify.checkout.general.errors.payment.payment_instrument_not_found": "Could not find payment method", "shopify.checkout.general.errors.payment.unauthorized_capture_method": "Configuring the payment capture method is not allowed.", "shopify.checkout.general.errors.payment.saved_payment_methods_not_allowed": "Saved payment methods is not allowed.", "shopify.checkout.general.errors.payment.saved_payment_methods_not_enabled": "Saved payment methods is not enabled for this shop.", "shopify.checkout.general.errors.payment.saved_payment_methods_cvv_verification_disabled": "The selected payment method requires CVV verification which is not enabled for this shop. Select a different payment method.", "shopify.checkout.general.errors.payment.store_credit_not_enabled": "Store credit is not enabled for this shop.", "shopify.checkout.general.errors.payment.store_credit_account_not_found": "Store credit account not found.", "shopify.checkout.general.errors.payment.store_credit_account_mismatch": "Store credit account does not match the buyer identity.", "shopify.checkout.general.errors.payment.store_credit_mismatched_currency": "The store credit account's currency does not match the checkout currency.", "shopify.checkout.general.errors.payment.store_credit_insufficient_funds": "There are insufficient funds in the store credit account.", "shopify.checkout.general.errors.payment.store_credit_buying_gift_card": "You can't use store credit to buy a gift card.", "shopify.checkout.general.errors.payment.gift_cards_not_allowed_for_b2b": "Gift cards are not allowed in B2B stores.", "shopify.checkout.general.errors.payment.method_required": "A payment method must be proposed.", "shopify.checkout.general.errors.payment.paypal_express_address_invalid": "PayPal returned an invalid address.", "shopify.checkout.general.errors.payment.shop_cash_unsupported_currency": "Shop Cash does not support this currency.", "shopify.checkout.general.errors.tip.not_accepted_by_shop": "Shop does not accept tips.", "shopify.checkout.general.errors.tip.only_one_tip_line_allowed": "Multiple tips lines are not supported.", "shopify.checkout.general.errors.tip.tips_not_allowed_for_b2b": "Tips are not allowed in B2B stores.", "shopify.checkout.general.errors.tip.currency_change_reset": "Tip has been reset due to currency change.", "shopify.checkout.general.errors.tip.tips_not_allowed_for_deferred_purchase_options": "Tips are not yet supported for deffered purchase options.", "shopify.checkout.general.errors.sale_attribution.draft_order_not_exist": "This draft order doesn't exist in the current shop.", "shopify.checkout.general.errors.sale_attribution.staff_member_not_exist": "This staff doesn't exist in the current shop.", "shopify.checkout.general.errors.sale_attribution.location_not_exist": "This location doesn't exist in the current shop.", "shopify.checkout.general.errors.sale_attribution.point_of_sale_device_not_exist": "This point of sale device doesn't exist in the current shop.", "shopify.checkout.general.errors.sale_attribution.unresolvable_target_merchandise_lines": "The proposed sale attributions are unresolvable given the current negotiation context.", "shopify.checkout.general.errors.non_negotiable.missing_non_negotiable_terms": "Your buyer's proposal should contain non-negotiable_terms.", "shopify.checkout.general.errors.non_negotiable.invalid_signature": "The signature for {{target_terms}} is invalid.", "shopify.checkout.general.errors.inventory.missing_quantity_value": "Quantity field is missing in the buyer's inventory lines.", "shopify.checkout.general.errors.inventory.changing_behavior_not_allowed": "This client does not have permission to change inventory behavior.", "shopify.checkout.general.errors.inventory.only_one_inventory_behavior_allowed": "Currently only one inventory behavior is allowed per checkout.", "shopify.checkout.general.errors.optional_duties.optional_duties_not_permitted": "Refusing duties is not allowed for this checkout.", "shopify.checkout.general.errors.attribution.retail_attribution_not_allowed": "Retail attribution is not allowed for this checkout.", "shopify.checkout.general.errors.attribution.retail_attribution_required": "Retail attribution is required for this checkout.", "shopify.checkout.general.errors.captcha.metadata_missing": "Completing a captcha is required for this checkout.", "shopify.checkout.general.errors.captcha.metadata_mismatch": "Provided captcha provider and type does not match the expected values.", "shopify.checkout.general.errors.captcha.token_missing": "A captcha token must be provided to complete this checkout.", "shopify.checkout.general.errors.captcha.job_enqueue_failed": "The captcha validation job failed to be enqueued.", "shopify.checkout.general.errors.captcha.job_already_queued": "Captcha validation has already been queued for this captcha token.", "shopify.checkout.general.errors.captcha.token_expired": "The provided captcha token has expired. Complete a new captcha to continue checkout.", "shopify.checkout.general.errors.captcha.token_invalid": "The provided captcha token was invalid. Complete a new captcha to continue checkout.", "shopify.checkout.general.errors.captcha.token_not_valid_for_session": "The provided captcha cannot be used with this checkout.", "shopify.checkout.general.errors.fx_reconciliation_override.shop_currency_mismatch": "The amount received must be in the store currency.", "shopify.checkout.general.errors.checkout_source.revoked": "This checkout is no longer available.", "shopify.checkout.general.full_price": "Full price:", "shopify.checkout.general.title": "Checkout - {{shopName}}", "shopify.checkout.general.full_title": "{{pageTitle}} - {{shopName}} - Checkout", "shopify.checkout.general.loading": "Loading…", "shopify.checkout.general.loading_title": "Checkout - {{shopName}}", "shopify.checkout.general.display_name": "{{firstName}} {{lastName}}", "shopify.checkout.general.google_map_title": "Google Map", "shopify.checkout.general.confirm_order_button_label": "Confirm order", "shopify.checkout.general.submit_order_button_label": "Submit order", "shopify.checkout.general.submit_for_review_button_label": "Submit for review", "shopify.checkout.general.submit_for_review_notice_label": "Payment won't be due until your order is reviewed", "shopify.checkout.general.continue_to_payment_plans_button_label": "Continue to payment plans", "shopify.checkout.general.back_button_label": "Return", "shopify.checkout.general.choose_delivery_strategy": "Choose a delivery method", "shopify.checkout.general.autocomplete_zero_item": "No items available", "shopify.checkout.general.autocomplete_powered_by_google": "Powered by {{brandName}}", "shopify.checkout.general.submit": "Submit", "shopify.checkout.general.processing": "Processing…", "shopify.checkout.general.stepper.increase": "Increase {{type}}", "shopify.checkout.general.stepper.decrease": "Decrease {{type}}", "shopify.checkout.general.stepper.minimum_reached": "Minimum of {{value}} reached", "shopify.checkout.general.stepper.maximum_reached": "Maximum of {{value}} reached", "shopify.checkout.general.remove_chip": "Remove item", "shopify.checkout.general.remove_item": "Remove %{label}", "shopify.checkout.general.remove_tag": "Remove tag", "shopify.checkout.general.dates.weekdays.monday": "Monday", "shopify.checkout.general.dates.weekdays.tuesday": "Tuesday", "shopify.checkout.general.dates.weekdays.wednesday": "Wednesday", "shopify.checkout.general.dates.weekdays.thursday": "Thursday", "shopify.checkout.general.dates.weekdays.friday": "Friday", "shopify.checkout.general.dates.weekdays.saturday": "Saturday", "shopify.checkout.general.dates.weekdays.sunday": "Sunday", "shopify.checkout.general.dates.weekdays_abbreviation.monday": "Mo", "shopify.checkout.general.dates.weekdays_abbreviation.tuesday": "Tu", "shopify.checkout.general.dates.weekdays_abbreviation.wednesday": "We", "shopify.checkout.general.dates.weekdays_abbreviation.thursday": "Th", "shopify.checkout.general.dates.weekdays_abbreviation.friday": "Fr", "shopify.checkout.general.dates.weekdays_abbreviation.saturday": "Sa", "shopify.checkout.general.dates.weekdays_abbreviation.sunday": "Su", "shopify.checkout.general.dates.months.january": "January", "shopify.checkout.general.dates.months.february": "February", "shopify.checkout.general.dates.months.march": "March", "shopify.checkout.general.dates.months.april": "April", "shopify.checkout.general.dates.months.may": "May", "shopify.checkout.general.dates.months.june": "June", "shopify.checkout.general.dates.months.july": "July", "shopify.checkout.general.dates.months.august": "August", "shopify.checkout.general.dates.months.september": "September", "shopify.checkout.general.dates.months.october": "October", "shopify.checkout.general.dates.months.november": "November", "shopify.checkout.general.dates.months.december": "December", "shopify.checkout.general.datepicker.show_previous_month": "Show previous month, {{month}} {{year}}", "shopify.checkout.general.datepicker.show_next_month": "Show next month, {{month}} {{year}}", "shopify.checkout.general.datepicker.range_start": "Range start", "shopify.checkout.general.datepicker.in_range": "In range", "shopify.checkout.general.datepicker.range_end": "Range end", "shopify.checkout.general.list_formatter.and.wordConnector": "{{previousWords}}, {{anotherWord}}", "shopify.checkout.general.list_formatter.and.lastWordConnector": "{{previousWords}}, and {{lastWord}}", "shopify.checkout.general.list_formatter.and.twoWordConnector": "{{firstWord}} and {{secondWord}}", "shopify.checkout.general.list_formatter.or.wordConnector": "{{previousWords}}, {{anotherWord}}", "shopify.checkout.general.list_formatter.or.lastWordConnector": "{{previousWords}}, or {{lastWord}}", "shopify.checkout.general.list_formatter.or.twoWordConnector": "{{firstWord}} or {{secondWord}}", "shopify.checkout.general.list_formatter.narrow.wordConnector": "{{previousWords}}, {{anotherWord}}", "shopify.checkout.general.list_formatter.narrow.lastWordConnector": "{{previousWords}}, {{lastWord}}", "shopify.checkout.general.list_formatter.narrow.twoWordConnector": "{{firstWord}}, {{secondWord}}", "shopify.checkout.general.map.cluster_title.one": "Cluster of {{number}} marker", "shopify.checkout.general.map.cluster_title.other": "Cluster of {{number}} markers", "shopify.checkout.general.sheet.expand": "Expand", "shopify.checkout.general.dropzone.add_file": "Add file", "shopify.checkout.general.dropzone.invalid_file_type": "File type not supported", "shopify.checkout.general.dropzone.dragged_over": "Drop file to upload", "shopify.checkout.contact.title": "Information", "shopify.checkout.contact.address_title": "Address", "shopify.checkout.contact.shipping_used_as_billing_notice": "This will also be used as your billing address for this order.", "shopify.checkout.contact.delivery_option_title": "Delivery method", "shopify.checkout.contact.contact_method_title": "Contact", "shopify.checkout.contact.email_label": "Email", "shopify.checkout.contact.email_placeholder": "Email", "shopify.checkout.contact.email_or_phone_label": "Email or mobile phone number", "shopify.checkout.contact.email_or_phone_placeholder": "Email or mobile phone number", "shopify.checkout.contact.stored_addresses_label": "Saved addresses", "shopify.checkout.contact.available_addresses_label": "Available addresses", "shopify.checkout.contact.new_address_label": "Use a new address", "shopify.checkout.contact.first_name_label": "First name", "shopify.checkout.contact.optional_first_name_label": "First name (optional)", "shopify.checkout.contact.last_name_label": "Last name", "shopify.checkout.contact.company_label": "Company", "shopify.checkout.contact.company_attention_label": "Company/Attention", "shopify.checkout.contact.optional_company_attention_label": "Company/Attention (optional)", "shopify.checkout.contact.optional_company_label": "Company (optional)", "shopify.checkout.contact.address1_label": "Address", "shopify.checkout.contact.address2_label": "Apartment, suite, etc.", "shopify.checkout.contact.optional_address2_label": "Apartment, suite, etc. (optional)", "shopify.checkout.contact.city_label": "City", "shopify.checkout.contact.country_label": "Country/Region", "shopify.checkout.contact.country_code": "Country/Region code", "shopify.checkout.contact.province_label": "Province", "shopify.checkout.contact.province_placeholder": "Province", "shopify.checkout.contact.county_label": "County", "shopify.checkout.contact.county_placeholder": "County", "shopify.checkout.contact.state_label": "State", "shopify.checkout.contact.state_placeholder": "State", "shopify.checkout.contact.region_label": "Region", "shopify.checkout.contact.region_placeholder": "Region", "shopify.checkout.contact.region_none": "None selected", "shopify.checkout.contact.prefecture_label": "Prefecture", "shopify.checkout.contact.prefecture_placeholder": "Prefecture", "shopify.checkout.contact.governorate_label": "Governorate", "shopify.checkout.contact.governorate_placeholder": "Governorate", "shopify.checkout.contact.emirate_label": "Emirate", "shopify.checkout.contact.emirate_placeholder": "Emirate", "shopify.checkout.contact.state_and_territory_label": "State/territory", "shopify.checkout.contact.state_and_territory_placeholder": "State/territory", "shopify.checkout.contact.phone_label": "Phone", "shopify.checkout.contact.optional_phone_label": "Phone (optional)", "shopify.checkout.contact.phone_placeholder": "Phone", "shopify.checkout.contact.phone_tooltip": "In case we need to contact you about your order", "shopify.checkout.contact.zip_code_label": "ZIP code", "shopify.checkout.contact.optional_zip_code_label": "ZIP code (optional)", "shopify.checkout.contact.zip_code_placeholder": "ZIP code", "shopify.checkout.contact.postal_code_label": "Postal code", "shopify.checkout.contact.optional_postal_code_label": "Postal code (optional)", "shopify.checkout.contact.postal_code_placeholder": "Postal code", "shopify.checkout.contact.postcode_label": "Postcode", "shopify.checkout.contact.optional_postcode_label": "Postcode (optional)", "shopify.checkout.contact.postcode_placeholder": "Postcode", "shopify.checkout.contact.pincode_label": "PIN code", "shopify.checkout.contact.optional_pincode_label": "PIN code (optional)", "shopify.checkout.contact.pincode_placeholder": "PIN code", "shopify.checkout.contact.civic_number_warning": "Add a house number if you have one", "shopify.checkout.contact.optional_last_name_label": "Last name (optional)", "shopify.checkout.contact.company_contact_info": "{{companyName}} · {{contact}}", "shopify.checkout.contact.different_shipping_address_label": "Ship to a different address", "shopify.checkout.contact.different_shipping_address_text": "Use a one-time address for this order", "shopify.checkout.contact.change_company_location_link_label": "Change company location", "shopify.checkout.contact.change_location_link_label": "Change location", "shopify.checkout.contact.ship_to_company": "Ship to {{companyName}}", "shopify.checkout.delivery_options.free_total_label": "Free", "shopify.checkout.delivery_options.ship": "Ship", "shopify.checkout.delivery_options.ship_to_address": "Ship to address", "shopify.checkout.delivery_options.shipping_address": "Shipping address", "shopify.checkout.delivery_options.pick_up": "Pick up", "shopify.checkout.delivery_options.pick_up_from_store": "Pickup in store", "shopify.checkout.delivery_options.pick_up_locations": "Pickup locations", "shopify.checkout.delivery_options.pick_up_in_one_hour": "Usually ready in 1 hour", "shopify.checkout.delivery_options.pick_up_in_two_hours": "Usually ready in 2 hours", "shopify.checkout.delivery_options.pick_up_in_four_hours": "Usually ready in 4 hours", "shopify.checkout.delivery_options.pick_up_in_twenty_four_hours": "Usually ready in 24 hours", "shopify.checkout.delivery_options.pick_up_in_two_to_four_days": "Usually ready in 2-4 days", "shopify.checkout.delivery_options.pick_up_in_five_or_more_days": "Usually ready in 5+ days", "shopify.checkout.delivery_options.pick_up_in_zero_to_two_hours": "Ready for pickup in 2 hours", "shopify.checkout.delivery_options.pick_up_in_two_to_four_hours": "Ready for pickup in 4 hours", "shopify.checkout.delivery_options.pick_up_immediately": "Ready for pickup now", "shopify.checkout.delivery_options.pick_up_next_day": "Ready for pickup next day", "shopify.checkout.delivery_options.pickup_point_method_definition": "Ship to pickup point", "shopify.checkout.delivery_options.retail_method_definition": "Retail shipping", "shopify.checkout.delivery_options.company_location_no_address_banner": "Orders shipping to locations with no address will be submitted for review.", "shopify.checkout.delivery_options.none_method_definition": "No shipping", "shopify.checkout.delivery_options.subscription_pickup_information": "This delivery method will apply to all items in this order and to all future orders for this subscription.", "shopify.checkout.customer_account.not_user_label": "Not %{first_name}?", "shopify.checkout.customer_account.have_an_account_label": "Have an account?", "shopify.checkout.customer_account.sign_in_link_label": "Sign in", "shopify.checkout.customer_account.sign_out_link_label": "Sign out", "shopify.checkout.customer_account.save_my_information_label": "Save this information for next time", "shopify.checkout.customer_account.invalid_address": "Selected address is invalid. %{update_address_link} or select a different address.", "shopify.checkout.customer_account.incomplete_address": "Selected address is incomplete. %{update_address_link} or select a different address.", "shopify.checkout.customer_account.rollup_label": "Account", "shopify.checkout.customer_account.invalid_shipping_method_summary": "Select a valid shipping address to view available shipping methods", "shopify.checkout.customer_account.saved_address_option_description.one": "You have {{count}} saved address.", "shopify.checkout.customer_account.saved_address_option_description.other": "You have {{count}} saved addresses.", "shopify.checkout.customer_account.saved_address_option_link": "Use a saved address", "shopify.checkout.customer_account.saved_address_modal_title": "Use my saved address", "shopify.checkout.stock.page_title": "Inventory issues", "shopify.checkout.stock.title": "Out of stock", "shopify.checkout.stock.items_unavailable_notice": "Some items are no longer available.", "shopify.checkout.stock.product_column_header": "Products", "shopify.checkout.stock.quantity_column_header": "Quantity", "shopify.checkout.stock.price_column_header": "Price", "shopify.checkout.stock.status_column_header": "Status", "shopify.checkout.stock.removed_from_cart_notice": "Removed from cart", "shopify.checkout.stock.sold_out_label": "Sold out", "shopify.checkout.stock.reduced_label": "Reduced", "shopify.checkout.stock.reduced_with_quantity_label": "Only %{quantity_available} left", "shopify.checkout.stock.remove_from_cart_button_label": "Remove from cart", "shopify.checkout.stock.continue_shopping_button_label": "Continue", "shopify.checkout.stock.go_back_to_cart_button_label": "Go back to my cart", "shopify.checkout.stock.remove": "Remove", "shopify.checkout.stock.return_to_store_label": "Return to store", "shopify.checkout.stock.continue_cta_label": "Continue checkout", "shopify.checkout.stock.item_label.one": "{{count}} item", "shopify.checkout.stock.item_label.other": "{{count}} items", "shopify.checkout.stock.empty_cart_label": "Your cart is empty", "shopify.checkout.stock.quantity_update.page_title": "Quantity update", "shopify.checkout.stock.quantity_update.title": "Quantity update", "shopify.checkout.stock.quantity_update.description": "Available quantities for these items have changed and are updated in your cart.", "shopify.checkout.stock.unpurchasable_product.title": "Unpurchasable product", "shopify.checkout.stock.unpurchasable_product.header": "Some items were removed from your cart", "shopify.checkout.stock.unpurchasable_product.message": "These items are not available for B2B orders.", "shopify.checkout.stock.unpurchasable_product.status_column_header": "Not available", "shopify.checkout.stock.unpurchasable_product_generic.title": "Unpurchasable product", "shopify.checkout.stock.unpurchasable_product_generic.header": "Some items were removed from your cart", "shopify.checkout.stock.unpurchasable_product_generic.message": "These items are not available.", "shopify.checkout.stock.unpurchasable_product_generic.status_column_header": "Not available", "shopify.checkout.stock.unfulfillable_product.title": "Delivery not available", "shopify.checkout.stock.unfulfillable_product.message.one": "%{delivery_method} is no longer available for this item.", "shopify.checkout.stock.unfulfillable_product.message.other": "%{delivery_method} is no longer available for these items.", "shopify.checkout.stock.unfulfillable_product.default_message.one": "The chosen delivery method is no longer available for this item.", "shopify.checkout.stock.unfulfillable_product.default_message.other": "The chosen delivery method is no longer available for these items.", "shopify.checkout.stock.unshippable_product.title": "No delivery available", "shopify.checkout.stock.unshippable_product.header.one": "No delivery available for %{count} item", "shopify.checkout.stock.unshippable_product.header.other": "No delivery available for %{count} items", "shopify.checkout.stock.unshippable_product.message.one": "This item will be removed from your cart because there are no delivery methods available for your address.", "shopify.checkout.stock.unshippable_product.message.other": "These items will be removed from your cart because there are no delivery methods available for your address.", "shopify.checkout.stock.unshippable_product.status_column_header": "No delivery", "shopify.checkout.stock.price_update.title": "Price update", "shopify.checkout.stock.price_update.description": "Prices for these items have changed and are updated in your cart.", "shopify.checkout.stock.price_and_quantity_update.title": "Price and quantity update", "shopify.checkout.stock.price_and_quantity_update.description": "Prices and available quantities for these items have changed and are updated in your cart.", "shopify.checkout.stock.out_of_stock.description": "These items are no longer available and have been removed from your cart.", "shopify.checkout.stock.out_of_stock.items_unavailable": "Some items are no longer available and have been removed from your cart.", "shopify.checkout.stock.out_of_stock_in_context_label": "This product is not available in your country/region.", "shopify.checkout.contextual_availability.title.one": "Unavailable product", "shopify.checkout.contextual_availability.title.other": "Unavailable products", "shopify.checkout.contextual_availability.products_not_available.one": "This product will be removed from your cart because it's not available in your country/region.", "shopify.checkout.contextual_availability.products_not_available.other": "These products will be removed from your cart because they're not available in your country/region.", "shopify.checkout.contextual_availability.all_products_in_cart_unavailable.one": "This product is not available in your country/region.", "shopify.checkout.contextual_availability.all_products_in_cart_unavailable.other": "These products are not available in your country/region.", "shopify.checkout.contextual_availability.unavailable_label": "Unavailable", "shopify.checkout.merchandise_unavailable_in_buyer_location.banner_title.some_products_one": "This product is not available for delivery to %{location_name}.", "shopify.checkout.merchandise_unavailable_in_buyer_location.banner_title.some_products_other": "These products are not available for delivery to %{location_name}.", "shopify.checkout.merchandise_unavailable_in_buyer_location.banner_title.all_products_one": "The product in your cart is not available for delivery to %{location_name}.", "shopify.checkout.merchandise_unavailable_in_buyer_location.banner_title.all_products_other": "The products in your cart are not available for delivery to %{location_name}.", "shopify.checkout.merchandise_unavailable_in_buyer_location.missing_country_fallback": "your country", "shopify.checkout.merchandise_unavailable_in_buyer_location.indeterminate_location_fallback": "your location", "shopify.checkout.merchandise_unavailable_in_buyer_location.cart_updated": "Your cart has been updated.", "shopify.checkout.merchandise_unavailable_in_buyer_location.change_address_only": "Change delivery address to continue.", "shopify.checkout.merchandise_unavailable_in_buyer_location.change_address_or_remove": "Change delivery address or %{remove_action}.", "shopify.checkout.merchandise_unavailable_in_buyer_location.remove_items_action.one": "remove unavailable item", "shopify.checkout.merchandise_unavailable_in_buyer_location.remove_items_action.other": "remove unavailable items", "shopify.checkout.merchandise_unavailable_in_buyer_location.empty_cart_action": "empty cart and return to store", "shopify.checkout.merchandise_unavailable_in_buyer_location.unavailable_line_item": "%{product_name} / %{variant_label}", "shopify.checkout.deliverability.not_deliverable.title.one": "No delivery available for %{count} item", "shopify.checkout.deliverability.not_deliverable.title.other": "No delivery available for %{count} items", "shopify.checkout.deliverability.not_deliverable.message.one": "This item will be removed from your cart because there are no delivery methods available for your address.", "shopify.checkout.deliverability.not_deliverable.message.other": "These items will be removed from your cart because there are no delivery methods available for your address.", "shopify.checkout.deliverability.not_deliverable.label": "No delivery", "shopify.checkout.order_summary.title": "Order summary", "shopify.checkout.order_summary.group_title_label": "Part of \"%{group_title}\"", "shopify.checkout.order_summary.for_add_on": "For: %{group_title}", "shopify.checkout.order_summary.from_shop_heading": "From %{shop_name}", "shopify.checkout.order_summary.from_other_stores_heading": "From other stores", "shopify.checkout.order_summary.number_items.one": "%{count} item", "shopify.checkout.order_summary.number_items.other": "%{count} items", "shopify.checkout.order_summary.view_all_items.one": "View %{count} item", "shopify.checkout.order_summary.view_all_items.other": "View all %{count} items", "shopify.checkout.order_summary.more_items_modal.search_item": "Search", "shopify.checkout.order_summary.more_items_modal.clear_search": "Clear search", "shopify.checkout.order_summary.more_items_modal.empty_results": "No results found for \"%{searchText}\". Check the spelling or use a different word or phrase.", "shopify.checkout.order_summary.more_items_modal.number_items_shown": "%{shownNumber} of %{totalNumber} items", "shopify.checkout.order_summary.order_name_label": "Order %{name}", "shopify.checkout.order_summary.shopping_cart_label": "Shopping cart", "shopify.checkout.order_summary.discount_title": "Discount", "shopify.checkout.order_summary.discount_title_stacking.one": "Discount", "shopify.checkout.order_summary.discount_title_stacking.other": "Discounts", "shopify.checkout.order_summary.discount_order_stacking.one": "Order discount", "shopify.checkout.order_summary.discount_order_stacking.other": "Order discounts", "shopify.checkout.order_summary.discount_label": "Discount code", "shopify.checkout.order_summary.discount_not_found": "Unable to find a valid discount matching the code entered", "shopify.checkout.order_summary.discount_placeholder": "Discount code", "shopify.checkout.order_summary.discount_code_applied": "Discount code applied", "shopify.checkout.order_summary.discount_code_allocations_mismatch": "Your <strong style=\"text-transform:uppercase\">%{code}</strong> discount code has changed. Review your cart and try again.", "shopify.checkout.order_summary.gift_card_applied": "Gift card applied", "shopify.checkout.order_summary.enter_shipping_address": "Enter shipping address", "shopify.checkout.order_summary.gift_card_title": "Gift card", "shopify.checkout.order_summary.gift_card_label": "Gift card", "shopify.checkout.order_summary.gift_card_placeholder": "Gift card", "shopify.checkout.order_summary.gift_card_and_discount_title": "Discount code or gift card", "shopify.checkout.order_summary.gift_card_and_discount_label": "Discount code or gift card", "shopify.checkout.order_summary.gift_card_and_discount_placeholder": "Discount code or gift card", "shopify.checkout.order_summary.remove_gift_card_label": "Clear gift card", "shopify.checkout.order_summary.remove_discount_label": "Remove discount", "shopify.checkout.order_summary.return_restocking_fee_label": "Restocking fee", "shopify.checkout.order_summary.return_shipping_fee_label": "Return shipping", "shopify.checkout.order_summary.free_shipping_discount_label": "Free shipping", "shopify.checkout.order_summary.applied_free_shipping_discount_label": "Applied", "shopify.checkout.order_summary.apply_discount_button_label": "Apply", "shopify.checkout.order_summary.applied_discount_message": "%{amount} off total order price", "shopify.checkout.order_summary.discount_has_been_applied": "Discount has been applied", "shopify.checkout.order_summary.discount_has_been_removed": "Discount has been removed", "shopify.checkout.order_summary.gift_card_has_been_applied": "Gift card has been applied", "shopify.checkout.order_summary.gift_card_has_been_removed": "Gift card has been removed", "shopify.checkout.order_summary.gift_card_subscriptions_notice": "Gift cards will only apply to your first payment", "shopify.checkout.order_summary.discount_already_applied": "A discount has been applied to this order. You can’t add another discount.", "shopify.checkout.order_summary.cart_does_not_meet_discount_requirements_notice": "<strong style=\"text-transform:uppercase\">%{code}</strong> discount code isn’t valid for the items in your cart", "shopify.checkout.order_summary.discount_requires_customer_notice": "Enter your shipping information to apply the <strong style=\"text-transform:uppercase\">%{code}</strong> discount code", "shopify.checkout.order_summary.customer_does_not_meet_discount_requirements_notice": "<strong style=\"text-transform:uppercase\">%{code}</strong> discount code isn’t available to you right now", "shopify.checkout.order_summary.eligible_customer_missing_notice": "<strong style=\"text-transform:uppercase\">%{code}</strong> discount code requires eligible customer", "shopify.checkout.order_summary.shipping_information_does_not_meet_discount_requirements_notice": "<strong style=\"text-transform:uppercase\">%{code}</strong> discount code isn’t available for your shipping address", "shopify.checkout.order_summary.customer_already_used_once_per_customer_discount_notice": "The <strong style=\"text-transform:uppercase\">%{code}</strong> discount code has already been used", "shopify.checkout.order_summary.subtotal_label": "Subtotal", "shopify.checkout.order_summary.subtotal_label_with_quantity.one": "Subtotal · %{count} item", "shopify.checkout.order_summary.subtotal_label_with_quantity.other": "Subtotal · %{count} items", "shopify.checkout.order_summary.shipping_label": "Shipping", "shopify.checkout.order_summary.pickup_label": "Pickup in store", "shopify.checkout.order_summary.no_pickup_location": "There are no pickup locations available.", "shopify.checkout.order_summary.pickup_unavailable_banner_title.one": "No stores available with your item", "shopify.checkout.order_summary.pickup_unavailable_banner_title.other": "No stores available with your items", "shopify.checkout.order_summary.pickup_unavailable_banner_body.ship_to_address": "Ship to address", "shopify.checkout.order_summary.pickup_unavailable_banner_body.ship_to_address_instead_html": "%{link} instead", "shopify.checkout.order_summary.tip_label": "Tip", "shopify.checkout.order_summary.unconfirmed_label": "Unconfirmed", "shopify.checkout.order_summary.calculating_shipping": "Calculating…", "shopify.checkout.order_summary.calculating": "Calculating…", "shopify.checkout.order_summary.shipping_default_value": "Calculated at next step", "shopify.checkout.order_summary.shipping_discount_title": "Shipping Discount", "shopify.checkout.order_summary.shipping_pending_value": "—", "shopify.checkout.order_summary.shipping_pending_message": "Not yet available", "shopify.checkout.order_summary.shipping_policy_link": "Shipping costs", "shopify.checkout.order_summary.taxes_label": "Taxes", "shopify.checkout.order_summary.estimated_taxes_label": "Estimated taxes", "shopify.checkout.order_summary.information_estimated_taxes_label": "Taxes (estimated)", "shopify.checkout.order_summary.estimated_taxes_finalization_tooltip_content": "The final tax and total will be confirmed by email or text after you place your order.", "shopify.checkout.order_summary.order_total_label": "Order Total", "shopify.checkout.order_summary.price_before_discount_aria_text": "Original price", "shopify.checkout.order_summary.price_after_discount_aria_text": "Discounted price", "shopify.checkout.order_summary.discount_code_aria_text": "Discount code", "shopify.checkout.order_summary.total_savings": "Total savings", "shopify.checkout.order_summary.total_label": "Total", "shopify.checkout.order_summary.total_due_label": "Total due", "shopify.checkout.order_summary.total_price_label": "Total price", "shopify.checkout.order_summary.total_outstanding_label": "Amount to pay", "shopify.checkout.order_summary.deposit_label": "<PERSON><PERSON><PERSON><PERSON>", "shopify.checkout.order_summary.total_due_today_label": "Total due today", "shopify.checkout.order_summary.total_due_later_label": "Total due later", "shopify.checkout.order_summary.partial_total_label": "Total", "shopify.checkout.order_summary.free_total_label": "Free", "shopify.checkout.order_summary.free_shipping_total_label": "Free", "shopify.checkout.order_summary.paid_label": "Paid", "shopify.checkout.order_summary.payment_due_label": "Payment due", "shopify.checkout.order_summary.vat_label_html": "Including %{amount} in taxes", "shopify.checkout.order_summary.duties_included_label": "Total includes taxes and duties. No extra charges on delivery.", "shopify.checkout.order_summary.discount_and_gift_card_savings_notice": "After %{discount_amount} in discounts and %{gift_card_amount} in gift cards", "shopify.checkout.order_summary.discount_savings_notice": "After %{discount_amount} in discounts", "shopify.checkout.order_summary.gift_card_savings_notice": "After %{gift_card_amount} in gift cards", "shopify.checkout.order_summary.cost_table_title": "Cost summary", "shopify.checkout.order_summary.cost_table_item_column_header": "<PERSON><PERSON>", "shopify.checkout.order_summary.cost_table_value_column_header": "Value", "shopify.checkout.order_summary.description_label": "Description", "shopify.checkout.order_summary.price_label": "Price", "shopify.checkout.order_summary.price_per_item.accessibility_label": "%{price} per item", "shopify.checkout.order_summary.price_per_item.label": "%{price}/ea", "shopify.checkout.order_summary.quantity_label": "Quantity", "shopify.checkout.order_summary.product_change_label": "Change", "shopify.checkout.order_summary.product_image_label": "Product image", "shopify.checkout.order_summary.product_image_quantity_label": "Quantity", "shopify.checkout.order_summary.scroll_order_summary": "Scroll for more items", "shopify.checkout.order_summary.expand_order_summary_short": "Show", "shopify.checkout.order_summary.collapse_order_summary_short": "<PERSON>de", "shopify.checkout.order_summary.expand_order_summary": "Show order summary", "shopify.checkout.order_summary.collapse_order_summary": "Hide order summary", "shopify.checkout.order_summary.original_price": "Original price", "shopify.checkout.order_summary.sale_price": "Sale price", "shopify.checkout.order_summary.total_updated_label": "Updated total price:", "shopify.checkout.order_summary.order_total_updated_label": "Updated total", "shopify.checkout.order_summary.total_already_paid_label": "Paid", "shopify.checkout.order_summary.unit_price.accessible_separator": "per", "shopify.checkout.order_summary.unit_price.content_html": "%{price}%{accessible_separator}%{per_unit}", "shopify.checkout.order_summary.unit_price.per_item.single": "%{price} each", "shopify.checkout.order_summary.unit_price.per_item.one": "%{price} per %{count} item", "shopify.checkout.order_summary.unit_price.per_item.other": "%{price} per %{count} items", "shopify.checkout.order_summary.unit_price.per_unit.single": "%{price}/%{unit}", "shopify.checkout.order_summary.unit_price.per_unit.plural": "%{price}/%{count}%{unit}", "shopify.checkout.order_summary.gift_card_ending": "Gift card ending with %{last_characters}", "shopify.checkout.order_summary.estimated_tax_label": "Estimated tax", "shopify.checkout.order_summary.multiple_shipping_lines_label": "Shipping total", "shopify.checkout.order_summary.recurring_subtotal_label": "Recurring subtotal", "shopify.checkout.order_summary.recurring_total_label": "Recurring total", "shopify.checkout.order_summary.recurring_total_tooltip": "Does not include shipping, tax, duties, or any applicable discounts.", "shopify.checkout.order_summary.confirmation_number_label": "Confirmation #%{confirmation_number}", "shopify.checkout.order_summary.higher_value_discount_applied": "<strong style=\"text-transform:uppercase\">%{code}</strong> couldn't be used with your existing discounts.", "shopify.checkout.order_summary.percent_off_shipping": "%{percent}% off shipping", "shopify.checkout.order_summary.amount_off_shipping": "%{amount} off shipping", "shopify.checkout.order_summary.deferred_payment_info.title": "Payment of %{amount_due} is due on %{due_at}", "shopify.checkout.order_summary.deferred_payment_info.description_html": "For questions about your order, %{contact_us}.", "shopify.checkout.order_summary.deferred_payment_info.paid_today": "Total paid today", "shopify.checkout.order_summary.deferred_payment_info.paid_today_next": "Paid today", "shopify.checkout.order_summary.deferred_payment_info.due_on_fulfillment_title": "Payment of %{amount_due} is due on fulfillment", "shopify.checkout.order_summary.deferred_payment_info.due_on_receipt_title": "Payment of %{amount_due} is due on receipt", "shopify.checkout.order_summary.deferred_balance_info.title": "Balance of {{amount_due}} is due on {{due_at}}", "shopify.checkout.order_summary.deferred_balance_info.due_on_fulfillment_title": "Balance of {{amount_due}} is due on fulfillment", "shopify.checkout.order_summary.payment_terms_totals.due_today": "Total due today", "shopify.checkout.order_summary.payment_terms_totals.due_today_next": "Due today", "shopify.checkout.order_summary.payment_terms_totals.due_later": "Total due %{due_at}", "shopify.checkout.order_summary.payment_terms_totals.due_on_fulfillment": "Total due on fulfillment", "shopify.checkout.order_summary.payment_terms_totals.due_on_fulfillment_next": "Due on fulfillment", "shopify.checkout.order_summary.payment_terms_totals.due_on_receipt": "Total due on receipt", "shopify.checkout.order_summary.payment_terms_totals.due_later_checkout_to_draft": "Total due later", "shopify.checkout.order_summary.payment_terms_totals.total_paid": "Total paid", "shopify.checkout.order_summary.maximum_discount_code_limit_reached": "You've reached the maximum number of discount codes. Remove an existing code to use <strong style=\"text-transform:uppercase\">%{code}</strong>.", "shopify.checkout.order_summary.discount_code_application_failed": "<strong style=\"text-transform:uppercase\">%{code}</strong> isn't working right now. Contact us for help.", "shopify.checkout.order_summary.due_on_fulfillment": "Due on fulfillment", "shopify.checkout.order_summary.gift_card_masked": "•••• %{last_characters}", "shopify.checkout.order_summary.subscriptions": "Subscriptions", "shopify.checkout.order_summary.duties_label": "Duties", "shopify.checkout.order_summary.duties_and_taxes_label": "Duties and taxes", "shopify.checkout.order_summary.error_duties_tooltip": "This order is being shipped from another country, so duties and taxes may be charged on delivery.", "shopify.checkout.order_summary.may_be_charged_on_delivery": "May be charged on delivery", "shopify.checkout.order_summary.total_updated_label_with_price": "Updated total price: %{totalPrice}", "shopify.checkout.order_summary.updated_shipping_method": "Updated shipping method: %{shipping_method}", "shopify.checkout.order_summary.recurring_payments": "Recurring Payments", "shopify.checkout.order_summary.recurring_total_multiple_label": "This order has a recurring charge for multiple items.", "shopify.checkout.order_summary.deferred_total_due_date_label": "Total due {{date}}", "shopify.checkout.order_summary.deferred_total_due_date_label_next": "Due %{date}", "shopify.checkout.order_summary.full_price": "Full price: %{price}", "shopify.checkout.order_summary.custom_discount": "Custom discount", "shopify.checkout.order_summary.expand_component_information.one": "Show %{count} item", "shopify.checkout.order_summary.expand_component_information.other": "Show %{count} items", "shopify.checkout.order_summary.collapse_component_information.one": "Hide %{count} item", "shopify.checkout.order_summary.collapse_component_information.other": "Hide %{count} items", "shopify.checkout.order_summary.component_quantity_title": "%{quantity} × %{product_title}", "shopify.checkout.order_summary.discount_discovery.add_code": "Add discount", "shopify.checkout.order_summary.discount_discovery.merchandise_quantity_label.one": "%{count} item", "shopify.checkout.order_summary.discount_discovery.merchandise_quantity_label.other": "%{count} items", "shopify.checkout.order_summary.alternative_payment_currency_total": "Charged as %{amount_due}", "shopify.checkout.shipping.title": "Shipping", "shopify.checkout.shipping.delivery_title": "Delivery", "shopify.checkout.shipping.shipping_method_notice": " ", "shopify.checkout.shipping.shipping_method_title": "Shipping method", "shopify.checkout.shipping.shipping_method_rollup_accessibility_label": "Shipping method options", "shopify.checkout.shipping.shipping_method_one_time_purchase_group_title": "One-time purchase", "shopify.checkout.shipping.shipping_method_first_shipment_group_title.one": "First shipment", "shopify.checkout.shipping.shipping_method_first_shipment_group_title.other": "First shipments", "shopify.checkout.shipping.shipping_method_subscription_group_title": "Subscription", "shopify.checkout.shipping.shipping_method_recurring_shipments_group_title.one": "Recurring shipment", "shopify.checkout.shipping.shipping_method_recurring_shipments_group_title.other": "Recurring shipments", "shopify.checkout.shipping.subscription_shipping": "Subscription shipping", "shopify.checkout.shipping.subscription_local_delivery": "Subscription local delivery", "shopify.checkout.shipping.subscription_pickup": "Subscription pickup", "shopify.checkout.shipping.waiting_on_rate_notice": "Getting available shipping rates…", "shopify.checkout.shipping.waiting_on_pickup_location_notice": "Getting available pick up locations…", "shopify.checkout.shipping.no_rates_for_cart_or_destination_notice": "There are no shipping methods available for your cart or address", "shopify.checkout.shipping.no_pickup_location_notice": "At least one of your products is not available for pick up", "shopify.checkout.shipping.no_rates_for_country_notice": "No shipping rates found for this address.", "shopify.checkout.shipping.no_rates_contact_notice": "Contact us for more information", "shopify.checkout.shipping.free_rate_label": "Free", "shopify.checkout.shipping.cheapest_rate_label": "Cheapest", "shopify.checkout.shipping.please_enter_your_shipping_information_notice": "Please enter your shipping information.", "shopify.checkout.shipping.estimated_delivery_date.one": "%{count} business day", "shopify.checkout.shipping.estimated_delivery_date.other": "%{count} business days", "shopify.checkout.shipping.estimated_delivery_date_range": "%{minimum} to %{maximum} business days", "shopify.checkout.shipping.delivery_date_range": "%{min_time}–%{max_time}", "shopify.checkout.shipping.estimated_delivery_time": "Estimated delivery %{time}", "shopify.checkout.shipping.estimated_delivery_time_range": "Estimated delivery %{min_time}–%{max_time}", "shopify.checkout.shipping.delivery_today": "Same-day delivery %{time}", "shopify.checkout.shipping.delivery_tomorrow": "Next-day delivery %{time}", "shopify.checkout.shipping.two_day_delivery": "2-day delivery %{time}", "shopify.checkout.shipping.delivery": "Delivery %{time}", "shopify.checkout.shipping.arrives_time": "Arrives %{time}", "shopify.checkout.shipping.arrives_time_range": "Arrives %{min_time}–%{max_time}", "shopify.checkout.shipping.estimated_time": "Estimated %{time}", "shopify.checkout.shipping.estimated_time_range": "Estimated %{min_time}–%{max_time}", "shopify.checkout.shipping.default_description": "Ships in 1 business day", "shopify.checkout.shipping.unbranded_default_description": "Ready to ship", "shopify.checkout.shipping.shipping_line_phone_label": "Mobile phone number (required)", "shopify.checkout.shipping.shipping_line_phone": "Your courier may use this number to contact you.", "shopify.checkout.shipping.shipping_line_delivery_phone_label": "Mobile phone number", "shopify.checkout.shipping.shipping_line_delivery_phone": "You may be contacted with updates on your delivery.", "shopify.checkout.shipping.optional_shipping_line_delivery_instructions_label": "Delivery instructions (optional)", "shopify.checkout.shipping.shipping_line_delivery_instructions": "Enter necessary information like door codes or drop-off instructions.", "shopify.checkout.shipping.other_method.zero": "No other methods available", "shopify.checkout.shipping.other_method.one": "%{count} other method available at", "shopify.checkout.shipping.other_method.other": "%{count} other methods available starting at", "shopify.checkout.shipping.shipping_rate_discounted_amount": "Was: %{original_amount}. Now: %{amount}", "shopify.checkout.shipping.local_delivery": "Local delivery", "shopify.checkout.shipping.prime_not_available.default": "Prime delivery isn't available for your order. Your shipping options have been updated.", "shopify.checkout.shipping.subscription_delivery_description.free_shipping_cycles.one": "Free shipping for %{count} more delivery", "shopify.checkout.shipping.subscription_delivery_description.free_shipping_cycles.other": "Free shipping for %{count} more deliveries", "shopify.checkout.shipping.subscription_delivery_description.free_shipping_cycles_delivery_not_included.one": "Free shipping for %{count} delivery", "shopify.checkout.shipping.subscription_delivery_description.free_shipping_cycles_delivery_not_included.other": "Free shipping for %{count} deliveries", "shopify.checkout.shipping.subscription_delivery_description.first_n_order_free_with_cycle.one": "Free shipping for %{count} more delivery, then %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.first_n_order_free_with_cycle.other": "Free shipping for %{count} more deliveries, then %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.first_n_order_free_with_cycle_delivery_not_included.one": "Free shipping for %{count} delivery, then %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.first_n_order_free_with_cycle_delivery_not_included.other": "Free shipping for %{count} deliveries, then %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.max_cycles_base_shipping.one": "%{count} more delivery for %{shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.max_cycles_base_shipping.other": "%{count} more deliveries for %{shipping_price} each", "shopify.checkout.shipping.subscription_delivery_description.max_cycles_base_shipping_delivery_not_included.one": "%{count} delivery for %{shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.max_cycles_base_shipping_delivery_not_included.other": "%{count} more deliveries for %{shipping_price} each", "shopify.checkout.shipping.subscription_delivery_description.prepaid_free_shipping_with_cycles.one": "Free shipping for the first %{cycle}, followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_free_shipping_with_cycles.other": "Free shipping for the first %{cycle}, followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_order_one_cycle.one": "%{shipping_price} for %{count} more delivery", "shopify.checkout.shipping.subscription_delivery_description.prepaid_order_one_cycle.other": "%{shipping_price} for %{count} more deliveries (%{per_delivery_cost})", "shopify.checkout.shipping.subscription_delivery_description.prepaid_order_one_cycle_delivery_not_included.one": "%{shipping_price} for %{count} delivery", "shopify.checkout.shipping.subscription_delivery_description.prepaid_order_one_cycle_delivery_not_included.other": "%{shipping_price} for %{count} deliveries (%{per_delivery_cost})", "shopify.checkout.shipping.subscription_delivery_description.prepaid_order_no_cycle.one": "%{shipping_price} for %{count} more delivery, followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_order_no_cycle.other": "%{shipping_price} for %{count} more deliveries (%{per_delivery_cost}), followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_order_no_cycle_delivery_not_included.one": "%{shipping_price} for %{count} delivery, followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_order_no_cycle_delivery_not_included.other": "%{shipping_price} for %{count} deliveries (%{per_delivery_cost}), followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.subscription_free_with_frequency_label": "Free shipping %{delivery_frequency}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_delivery_frequency.one": "for %{count} delivery", "shopify.checkout.shipping.subscription_delivery_description.prepaid_delivery_frequency.other": "for %{count} deliveries", "shopify.checkout.shipping.subscription_delivery_description.subscription_delivery_frequency.day.one": "every day", "shopify.checkout.shipping.subscription_delivery_description.subscription_delivery_frequency.day.other": "every %{count} days", "shopify.checkout.shipping.subscription_delivery_description.subscription_delivery_frequency.week.one": "every week", "shopify.checkout.shipping.subscription_delivery_description.subscription_delivery_frequency.week.other": "every %{count} weeks", "shopify.checkout.shipping.subscription_delivery_description.subscription_delivery_frequency.month.one": "every month", "shopify.checkout.shipping.subscription_delivery_description.subscription_delivery_frequency.month.other": "every %{count} months", "shopify.checkout.shipping.subscription_delivery_description.subscription_delivery_frequency.year.one": "every year", "shopify.checkout.shipping.subscription_delivery_description.subscription_delivery_frequency.year.other": "every %{count} years", "shopify.checkout.shipping.subscription_delivery_description.multiple_subscriptions_shipping_price_label": "%{shipping_price} per delivery", "shopify.checkout.shipping.subscription_delivery_description.prepaid_shipping_price_label": "%{shipping_price} per delivery x %{delivery_interval_count}", "shopify.checkout.shipping.subscription_delivery_description.initial_order_discounted": "First shipping cost %{discounted_shipping_cost}, then %{frequency_description}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_initial_order_discounted": "First shipping cost %{discounted_shipping_price} %{prepaid_delivery_frequency}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_initial_order_discounted_with_per_delivery_price": "First shipping cost %{discounted_shipping_price} %{prepaid_delivery_frequency} (%{discounted_cost_per_delivery})", "shopify.checkout.shipping.subscription_delivery_description.prepaid_initial_order_discounted_with_renewal": "First shipping cost %{discounted_shipping_price} %{prepaid_delivery_frequency}, then %{shipping_price} %{billing_cycle}", "shopify.checkout.shipping.subscription_delivery_description.prepaid_initial_order_discounted_with_per_delivery_price_and_renewal": "First shipping cost %{discounted_shipping_price} %{prepaid_delivery_frequency} (%{discounted_cost_per_delivery}), then %{shipping_price} %{billing_cycle}", "shopify.checkout.shipping.subscription_delivery_description.shipping_price_included": "Included", "shopify.checkout.shipping.subscription_delivery_description.recurring_order_shipping_price": "followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.first_order_shipping_price_included": "First order included, followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.first_order_different_shipping_price": "%{first_order_shipping_price} (%{per_delivery_price} per delivery), followed by %{recurring_order_shipping_price}", "shopify.checkout.shipping.subscription_delivery_description.first_n_order_free.one": "First order free", "shopify.checkout.shipping.subscription_delivery_description.first_n_order_free.other": "First %{count} orders free", "shopify.checkout.shipping.subscription_delivery_description.free_for_first_n_order.one": "For first order", "shopify.checkout.shipping.subscription_delivery_description.free_for_first_n_order.other": "For first %{count} orders", "shopify.checkout.shipping.one_time_purchase_shipping": "One-time shipping", "shopify.checkout.shipping.duties_and_taxes_title": "Duties and taxes", "shopify.checkout.shipping.shipping_information_required_notice": "Shipping information is required. {{link}}.", "shopify.checkout.shipping.shipping_information_redirect_link": "Enter your information here", "shopify.checkout.shipping.loading_progressive_rates": "Searching for more rates…", "shopify.checkout.shipping.no_other_methods_available": "No other methods available", "shopify.checkout.shipping.duties_and_taxes_options.ddu_title": "Pay on delivery", "shopify.checkout.shipping.duties_and_taxes_options.ddu_caption": "Additional fees may apply", "shopify.checkout.shipping.duties_and_taxes_options.ddp_title": "Pay now", "shopify.checkout.shipping.duties_and_taxes_options.ddp_caption": "No additional fees on delivery", "shopify.checkout.shipping.shipping_method_combinations.without_separator": "%{firstMethod}, %{secondMethod}", "shopify.checkout.shipping.shipping_method_combinations.with_separator": "%{rest}, %{current},", "shopify.checkout.shipping.shipping_method_combinations.combination": "%{rest} and %{current}", "shopify.checkout.shipping.shipping_used_as_billing_notice": "This will also be used as your billing address for this order.", "shopify.checkout.shipping.address_update": "Update address", "shopify.checkout.shipping.shipping_address_title": "Ship to", "shopify.checkout.shipping.shipping_address_rollup_accessibility_label": "Ship to address options", "shopify.checkout.shipping.add_address": "Use a different address", "shopify.checkout.shipping.add_delivery_instructions": "Add delivery instructions", "shopify.checkout.shipping.edit": "Edit", "shopify.checkout.shipping.delete": "Delete", "shopify.checkout.shipping.address_more": "More actions for this address", "shopify.checkout.shipping.address_edit": "Edit address", "shopify.checkout.shipping.address_delete": "Delete address", "shopify.checkout.shipping.address_confirm": "Are you sure you want to delete this address from your account?", "shopify.checkout.shipping.address_default": "Set as default", "shopify.checkout.shipping.address_form.add_address": "Add address", "shopify.checkout.shipping.address_form.save": "Save", "shopify.checkout.shipping.address_form.cancel": "Cancel", "shopify.checkout.shipping.address_form.generic_error": "There was a problem saving your address. Please try again.", "shopify.checkout.shipping.address_form.one_time_address_subtitle": "Use a one-time address for this order", "shopify.checkout.shipping.different_shipping_method_summary": "Enter a different shipping address to view available shipping methods", "shopify.checkout.shipping.invalid_shipping_method_summary": "Enter a valid shipping address to view available shipping methods", "shopify.checkout.shipping.method_summary.incomplete_address_entered": "Enter a complete shipping address to view available shipping methods", "shopify.checkout.shipping.method_summary.incomplete_address_selected": "Select a complete shipping address to view available shipping methods", "shopify.checkout.shipping.local_pickup.search_address": "Search for address", "shopify.checkout.shipping.local_pickup.rollup_title": "Pickup location", "shopify.checkout.shipping.local_pickup.rollup_accessibility_label": "Pickup location options", "shopify.checkout.shipping.missing_shipping_address": "Shipping address is missing", "shopify.checkout.shipping.split_shipping_merchandise_quantity_label_standard": "%{quantity} items", "shopify.checkout.shipping.split_shipping_merchandise_quantity_label_additional": "+%{quantity} more", "shopify.checkout.shipping.split_shipping_merchandise_shipment_number": "Shipment %{number} of %{total}", "shopify.checkout.shipping.pre_order_ships_now": "Ships now", "shopify.checkout.shipping.pre_order_ships_later": "Ships later", "shopify.checkout.shipping.pre_order_ships_date": "Ships %{date}", "shopify.checkout.shipping.split_shipping_shipped_by_shop": "Shipped by %{shop_name}", "shopify.checkout.shipping.split_shipping_multiple_shipment_label": "Multiple shipments", "shopify.checkout.shipping.split_shipping_shipping_label": "Method", "shopify.checkout.shipping.split_shipping_shipment_notice": "Your order will arrive in %{quantity_shipments}", "shopify.checkout.shipping.split_shipping_first_shipment_notice": "Your first order will arrive in %{quantity_shipments}", "shopify.checkout.shipping.split_shipping_multiple_shipment_quantity_label": "%{quantity} shipments", "shopify.checkout.shipping.split_shipping_multiple_shipment_notice": "Your order will arrive in multiple shipments", "shopify.checkout.shipping.split_shipping_multiple_shipment_notice_one_time_purchase": "Your order will arrive in %{quantity} shipments", "shopify.checkout.shipping.split_shipping_more_shipping_options": "More shipping options", "shopify.checkout.shipping.split_shipping_shipment_number": "Shipment %{number}", "shopify.checkout.shipping.split_shipping_merchandise_quantity_label_inline_primary": "Quantity: %{quantity}", "shopify.checkout.shipping.split_shipping_merchandise_quantity_label_inline": "%{merchandise_title} ×%{quantity}", "shopify.checkout.shipping.split_shipping_option_free": "Free shipping", "shopify.checkout.shipping.split_shipping_option_lowest_price": "Lowest price", "shopify.checkout.shipping.split_shipping_option_fastest": "Fastest", "shopify.checkout.shipping.split_shipping_option_recommended": "Recommended", "shopify.checkout.shipping.split_shipping_option_custom": "Custom", "shopify.checkout.localized_fields.additional_information.title": "Additional information", "shopify.checkout.localized_fields.additional_information.tax_credential_br": "CPF/CNPJ", "shopify.checkout.localized_fields.additional_information.shipping_credential_br": "CPF/CNPJ", "shopify.checkout.localized_fields.additional_information.shipping_credential_kr": "Personal Customs Code", "shopify.checkout.localized_fields.additional_information.shipping_credential_cn": "Resident ID number", "shopify.checkout.localized_fields.additional_information.tax_credential_it": "Codice Fisca<PERSON> (optional)", "shopify.checkout.localized_fields.additional_information.tax_email_it": "PEC (optional)", "shopify.checkout.localized_fields.additional_information.tax_credential_cl": "RUT", "shopify.checkout.localized_fields.additional_information.shipping_credential_cl": "RUT", "shopify.checkout.localized_fields.additional_information.tax_credential_co": "Cédula/IVA", "shopify.checkout.localized_fields.additional_information.tax_credential_type_co": "<PERSON><PERSON><PERSON>", "shopify.checkout.localized_fields.additional_information.shipping_credential_co": "Cédula/IVA", "shopify.checkout.localized_fields.additional_information.shipping_credential_type_co": "<PERSON><PERSON><PERSON>", "shopify.checkout.localized_fields.additional_information.tax_credential_cr": "Identification number/RUT", "shopify.checkout.localized_fields.additional_information.shipping_credential_cr": "Identification number/RUT", "shopify.checkout.localized_fields.additional_information.tax_credential_ec": "CI/RUC/IVA", "shopify.checkout.localized_fields.additional_information.shipping_credential_ec": "CI/RUC/IVA", "shopify.checkout.localized_fields.additional_information.tax_credential_gt": "NIT/IVA", "shopify.checkout.localized_fields.additional_information.shipping_credential_gt": "NIT/IVA", "shopify.checkout.localized_fields.additional_information.tax_credential_id": "NPWP", "shopify.checkout.localized_fields.additional_information.shipping_credential_id": "NPWP", "shopify.checkout.localized_fields.additional_information.tax_credential_my": "TIN/Registration Number", "shopify.checkout.localized_fields.additional_information.shipping_credential_my": "TIN/Registration Number", "shopify.checkout.localized_fields.additional_information.shipping_credential_mx": "RFC", "shopify.checkout.localized_fields.additional_information.tax_credential_mx": "RFC", "shopify.checkout.localized_fields.additional_information.tax_credential_type_mx": "Régi<PERSON>", "shopify.checkout.localized_fields.additional_information.tax_credential_use_mx": "Uso de CFDI", "shopify.checkout.localized_fields.additional_information.tax_credential_py": "CI/RUC/IVA", "shopify.checkout.localized_fields.additional_information.shipping_credential_py": "CI/RUC/IVA", "shopify.checkout.localized_fields.additional_information.tax_credential_pe": "DNI/RUC/CE", "shopify.checkout.localized_fields.additional_information.shipping_credential_pe": "DNI/RUC/CE", "shopify.checkout.localized_fields.additional_information.tax_credential_pt": "NIF/IVA", "shopify.checkout.localized_fields.additional_information.shipping_credential_pt": "NIF/IVA", "shopify.checkout.localized_fields.additional_information.tax_credential_es": "DNI/NIF", "shopify.checkout.localized_fields.additional_information.shipping_credential_es": "DNI/NIF", "shopify.checkout.localized_fields.additional_information.shipping_credential_tw": "National ID Number/VAT", "shopify.checkout.localized_fields.additional_information.tax_credential_tr": "Türkiye Cumhuriyeti Kimlik Numarası/KDV", "shopify.checkout.localized_fields.additional_information.shipping_credential_tr": "Türkiye Cumhuriyeti Kimlik Numarası/KDV", "shopify.checkout.payment.network_card_payment_options": "Choose a card network", "shopify.checkout.payment.network_card_persistence_description": "The selected network will be saved with your payment method for future transactions.", "shopify.checkout.payment.amazon_payments_label": "Amazon Pay", "shopify.checkout.payment.amazon_payments_login_hint": "You'll be asked to sign in with Amazon.", "shopify.checkout.payment.amazon_payments_logout": "Sign out from Amazon Pay", "shopify.checkout.payment.amazon_payments_failed_mfa_challenge": "The selected payment method could not be used. Select a different method or contact your bank or card issuer.", "shopify.checkout.payment.amazon_payments_abandoned_mfa_challenge": "Your payment couldn't be completed. Try a different payment method or contact your bank or card issuer.", "shopify.checkout.payment.amazon_payments_could_not_enqueue_authorize": "There was an issue processing your payment. Try again or use a different payment method.", "shopify.checkout.payment.title": "Payment", "shopify.checkout.payment.amount_left_to_pay_label": "Payment due:", "shopify.checkout.payment.discount_button_action_label": "Apply Discount Code", "shopify.checkout.payment.gift_card_code_label": "Gift card code", "shopify.checkout.payment.apply_gift_card_button_label": "Apply", "shopify.checkout.payment.gift_card_balance_label": "Balance:", "shopify.checkout.payment.supported_card_brands_more_label": "and more…", "shopify.checkout.payment.detected_card_brand": "Detected card brand: {brand}", "shopify.checkout.payment.credit_card_vaulting_sign_in_banner_title": "Save your credit card", "shopify.checkout.payment.credit_card_vaulting_sign_in_banner_info": "Log in or create an account to save your credit card for future purchases.", "shopify.checkout.payment.credit_card_vaulting_sign_in_banner_button_label": "Log in", "shopify.checkout.payment.ftd_vault_credit_card_label": "Save this credit card to my account for a faster checkout", "shopify.checkout.payment.card_number_label": "Card number", "shopify.checkout.payment.card_number_placeholder": "Card number", "shopify.checkout.payment.card_pay_with": "Pay with:", "shopify.checkout.payment.card_security_notice": "All transactions are secure and encrypted.", "shopify.checkout.payment.card_security_vault_notice": "We need your details for future payments. All transactions are secure and encrypted.", "shopify.checkout.payment.card_information_notice": "All transactions are secure and encrypted. Credit card information is never stored.", "shopify.checkout.payment.name_on_card_label": "Name on card", "shopify.checkout.payment.name_on_card_placeholder": "Name on card", "shopify.checkout.payment.card_nickname_label": "Nickname (optional)", "shopify.checkout.payment.card_expired": "Expired", "shopify.checkout.payment.card_expiry_label": "Expiration date (MM / YY)", "shopify.checkout.payment.card_expiry_placeholder": "Expiration date (MM / YY)", "shopify.checkout.payment.credit_card_start_month": "Start month", "shopify.checkout.payment.credit_card_start_year": "Start year", "shopify.checkout.payment.delete_customer_mandate_modal_title": "Remove this payment card?", "shopify.checkout.payment.delete_customer_mandate_modal_body": "If you remove this card, you'll need to add it again before using it for payments.", "shopify.checkout.payment.delete_customer_mandate_modal_confirm": "Remove card", "shopify.checkout.payment.delete_customer_mandate_modal_cancel": "Cancel", "shopify.checkout.payment.card_verification_value_label": "Security code", "shopify.checkout.payment.card_verification_value_placeholder": "Security code", "shopify.checkout.payment.card_verification_confirm": "For verification purposes, enter your card's security code.", "shopify.checkout.payment.card_expiry_date_explanation": "Card expiration date in format: month, month, year, year", "shopify.checkout.payment.card_expiration_month_label": "Expiration month", "shopify.checkout.payment.card_expiration_year_label": "Expiration year", "shopify.checkout.payment.card_verification_value_explanation": "3-digit security code usually found on the back of your card. American Express cards have a 4-digit code located on the front.", "shopify.checkout.payment.card_verification_value_explanation_amex": "4-digit security code on the front of your card", "shopify.checkout.payment.card_verification_value_explanation_other": "3-digit security code on the back of your card", "shopify.checkout.payment.card_start_label": "Issue date (MM / YY)", "shopify.checkout.payment.card_start_placeholder": "Issue date (MM / YY)", "shopify.checkout.payment.card_issue_number_label": "Issue number", "shopify.checkout.payment.card_issue_number_placeholder": "Issue number", "shopify.checkout.payment.card_change_label": "Change", "shopify.checkout.payment.card_update_label": "Update card", "shopify.checkout.payment.paypal_disconnect": "Disconnect", "shopify.checkout.payment.ends_with_label": "ending with %{last_digits}", "shopify.checkout.payment.masked_digits_label": "•••• %{last_digits}", "shopify.checkout.payment.payment_method_saved": "Payment method saved", "shopify.checkout.payment.card_vaulted_company_location": "Card saved for this location", "shopify.checkout.payment.expiry_long": "(Expires %{month}/%{year})", "shopify.checkout.payment.expiry_short": "(Exp %{month}/%{year})", "shopify.checkout.payment.crypto_payment_method.description": "Securely connect to your wallet on the Base network", "shopify.checkout.payment.crypto_payment_method.tooltip": "Your wallet balance and address will not be shared and only the requested amount can be deducted", "shopify.checkout.payment.crypto_modal.header": "Pay %{shop_name} %{total_price} USDC", "shopify.checkout.payment.crypto_modal.error.refresh": "Refresh", "shopify.checkout.payment.crypto_modal.error.close": "close", "shopify.checkout.payment.crypto_modal.error.sandbox_error": "Something went wrong", "shopify.checkout.payment.crypto_modal.error.web_component_error": "There was a problem loading this page", "shopify.checkout.payment.saved_by_payment_method": "Saved by %{payment_method}", "shopify.checkout.payment.pay_with_payment_method": "Pay with %{payment_method}", "shopify.checkout.payment.different_credit_card_label": "Use a different credit card", "shopify.checkout.payment.same_billing_address_label": "Same as shipping address", "shopify.checkout.payment.different_billing_address_label": "Use a different billing address", "shopify.checkout.payment.billing_address_associated_with_mandate": "Use address associated with payment method", "shopify.checkout.payment.billing_address_associated_with_mandate_info": "If you wish to change your billing address, please remove and re-enter your credit card details", "shopify.checkout.payment.free_order_notice_html": "Your order is <strong>free</strong>. No payment is required.", "shopify.checkout.payment.express_checkout_free_order": "You won't be charged for a free order", "shopify.checkout.payment.order_covered_by_gift_cards_notice.one": "Your order is covered by your gift card.", "shopify.checkout.payment.order_covered_by_gift_cards_notice.other": "Your order is covered by your gift cards.", "shopify.checkout.payment.offsite_gateway_redirect_hint": "After clicking “%{button_label}”, you will be redirected to %{gateway_label} to complete your purchase securely.", "shopify.checkout.payment.offsite_gateway_review_page_redirect_hint": "After you review your order, you will be redirected to %{gateway_label} to complete your purchase securely.", "shopify.checkout.payment.offsite_gateway_redirect_hint_multi_currency_html": "%{gateway_label} total %{total_shop_price}", "shopify.checkout.payment.offsite_gateway_redirect_hint_no_button_label": "After continuing, you will be redirected to %{gateway_label} to complete your purchase securely.", "shopify.checkout.payment.receiver_payment_experience_hint": "After placing your order, you will receive payment details. Make the payment at Multibanco or online within %{due_days} days.", "shopify.checkout.payment.billing_address_title": "Billing address", "shopify.checkout.payment.billing_address_rollup_accessibility_label": "Billing address options", "shopify.checkout.payment.billing_address_description": "Select the address that matches your card or payment method.", "shopify.checkout.payment.billing_address_description_no_shipping_address": "Enter the address that matches your card or payment method.", "shopify.checkout.payment.no_js_credit_card_fields_redirect_hint": "You will be redirected to add your payment information.", "shopify.checkout.payment.card_fields_container_prefix": "Field container for:", "shopify.checkout.payment.card_fields_unavailable_html.one": "Credit and debit card payments aren’t available right now.<br><a href=\"%{current_url}\">Refresh this page</a> or try again later.", "shopify.checkout.payment.card_fields_unavailable_html.other": "Credit and debit card payments aren’t available right now.<br><a href=\"%{current_url}\">Refresh this page</a> or try a different payment method.", "shopify.checkout.payment.card_fields_processing_error": "There was a problem processing the payment. Try refreshing this page or check your internet connection.", "shopify.checkout.payment.technical_error": "Payments can’t be processed right now because of a technical issue. Try again in a few minutes.", "shopify.checkout.payment.no_method_error": "This store can’t accept payments right now.", "shopify.checkout.payment.not_configured_error": "This store can’t accept payments right now.", "shopify.checkout.payment.missing_credit_card_error": "Enter your card details to complete the payment", "shopify.checkout.payment.shop_pay_ideal_unavailable_error": "iDEAL is not available for this order. Choose another payment method.", "shopify.checkout.payment.payment_method_not_available_for_shipping_address_error": "Payment method not available for selected shipping address. Choose another payment method.", "shopify.checkout.payment.billing_address_not_supported_for_payment_method_error": "This payment method isn't available for your billing address and currency. Please try a different payment method.", "shopify.checkout.payment.generic_incorrect_card_info": "Your payment details couldn’t be verified. Check your card details and try again.", "shopify.checkout.payment.sofort_label": "Sofort bank transfer", "shopify.checkout.payment.paypal_label": "PayPal", "shopify.checkout.payment.paypal_wallet_label": "PayPal Wallet", "shopify.checkout.payment.shop_pay_installments_label": "Shop Pay Installments", "shopify.checkout.payment.paypal_modal.order_contains_subscriptions": "Subscription", "shopify.checkout.payment.paypal_modal.order_contains_fixed_selling_plans": "Total may not include taxes, shipping, deferred charges, and some discounts. Continue to checkout for the amount due today.", "shopify.checkout.payment.klarna_label": "<PERSON><PERSON><PERSON>", "shopify.checkout.payment.klarna_pay_later_label": "Pay later with K<PERSON>na", "shopify.checkout.payment.klarna_pay_now_label": "Pay now with <PERSON><PERSON><PERSON>", "shopify.checkout.payment.bancontact_label": "Bancontact", "shopify.checkout.payment.giropay_label": "Giropay", "shopify.checkout.payment.multibanco_label": "Multibanco", "shopify.checkout.payment.eps_label": "eps-Überweisung", "shopify.checkout.payment.klarna_slice_it_label": "Finance with Klarna", "shopify.checkout.payment.ideal_label": "iDEAL", "shopify.checkout.payment.bank_picker_label": "Bank", "shopify.checkout.payment.afterpay_label": "Afterpay", "shopify.checkout.payment.cashapppay_label": "Cash App Pay", "shopify.checkout.payment.przelewy24_label": "Przelewy24", "shopify.checkout.payment.blik_label": "Blik", "shopify.checkout.payment.twint_label": "TWINT", "shopify.checkout.payment.mobilepay_label": "MobilePay", "shopify.checkout.payment.swish_label": "Swish", "shopify.checkout.payment.vipps_label": "<PERSON><PERSON><PERSON>", "shopify.checkout.payment.grabpay_label": "GrabPay", "shopify.checkout.payment.kcp_creditcard_label": "KCP Credit Card", "shopify.checkout.payment.usdc_label": "USDC", "shopify.checkout.payment.purchase_options_card_update_agreement_label_html": "By updating your payment method, you authorize payments for any recurring or deferred purchase according to this store's <a class=\"link--small\" data-modal=\"true\" data-title-text=\"%{cancellation_policy}\" href=\"%{policy_href}\">cancellation policy</a>.", "shopify.checkout.payment.subscription_card_update_agreement_label": "Changing the payment method will update your subscription and future recurring payments.", "shopify.checkout.payment.subscription_agreement_label": "One or more items in your cart is a deferred or subscription purchase. By continuing with your payment, you agree that your payment method will automatically be charged at the price and frequency listed on this page until it ends or you cancel. All cancellations are subject to the %{cancelled}.", "shopify.checkout.payment.subscription_agreement_cancelled": "cancellation policy", "shopify.checkout.payment.subscription_heading": "Subscription agreement", "shopify.checkout.payment.subscription_agreement_label_html": "I understand that I'm agreeing to a subscription. It will renew at the price and frequency listed until it ends or is\n<a class=\"link--small\" data-modal=\"true\" data-title-text=\"%{subscription_policy}\" href=\"%{policy_href}\">cancelled</a>.\n", "shopify.checkout.payment.purchase_options_agreement_label": "One or more items in your cart is a deferred or recurring purchase. By continuing with your payment, you agree that your payment method will automatically be charged at the price and frequency listed on this page until it ends or you cancel. All cancellations are subject to the  %{cancellation_policy_label}.", "shopify.checkout.payment.purchase_options_agreement_label_html": "I understand that one or more items in my cart is a deferred or recurring purchase. By continuing, I agree to the\n<a class=\"link--small\" data-modal=\"true\" data-title-text=\"%{subscription_policy}\" href=\"%{policy_href}\">cancellation policy</a>\nand authorize you to charge my payment method at the prices, frequency and dates listed on this page until my\norder is fulfilled or I cancel, if permitted.\n", "shopify.checkout.payment.purchase_options_agreement_label_wallets": "By continuing with your payment, you agree to the future charges listed on this page and the cancellation policy.", "shopify.checkout.payment.change_currency_section_description": "Select a currency option", "shopify.checkout.payment.no_real_orders_dev_store": "This store can't accept real orders or real payments.", "shopify.checkout.payment.waiting_on_payment_methods": "Getting available payment methods...", "shopify.checkout.payment.apple_pay_payment_error": "Your payment couldn't be completed. Try again or use a different payment method.", "shopify.checkout.payment.taxes_not_included": "Taxes not included", "shopify.checkout.payment.no_taxes_banner_content": "Taxes won’t be charged on this order. Once you’ve collected payment, taxes can no longer be added.", "shopify.checkout.payment.paypal.connect": "Click to connect your PayPal account", "shopify.checkout.payment.apple.connect": "Click to connect your Apple Pay account", "shopify.checkout.payment.apple.not_available": "Apple Pay is not available in this browser. To connect your Apple Pay account you'll need to use Safari on an Apple device.", "shopify.checkout.payment.google.connect": "Click to connect with your Google Pay account", "shopify.checkout.payment.google.error_authorizing": "Error authorizing payment", "shopify.checkout.payment.account_connected": "Account connected.", "shopify.checkout.payment.mor_disclaimer_html": "By clicking below and completing your order, you agree to purchase your item(s) from Global-e as merchant of record\nfor this transaction, on Global-e's <a class=\"link--small\" data-modal=\"true\" data-title-text=\"%{mor_terms}\" href=\"%{terms_href}\">Terms and Conditions</a>\nand <a class=\"link--small\" data-modal=\"true\" data-title-text=\"%{privacy_policy}\" href=\"%{policy_href}\">Privacy Policy</a>. Global-e is an international\nfulfilment service provider to %{shop_name}\n", "shopify.checkout.payment.pending_payment.due_notice": "Your payment of %{amount_due} is due on %{due_date}.", "shopify.checkout.payment.pending_payment.multibanco.buyer_action_info": "Your payment of %{amount_due} is due.", "shopify.checkout.payment.pending_payment.multibanco.instruction_html": "Make your payment at Multibanco or online using the details below. For questions or to update payment details, %{contact_us}.", "shopify.checkout.payment.pending_payment.multibanco.entity": "Entity", "shopify.checkout.payment.pending_payment.multibanco.reference": "Reference", "shopify.checkout.payment.pending_payment.multibanco.amount": "Amount", "shopify.checkout.payment.pending_payment.multibanco.find_nearest_atm": "Find Nearest ATM", "shopify.checkout.payment.update_payment_method": "Update payment method", "shopify.checkout.payment.order_covered_by_store_credit_notice": "Your order is covered by your store credit.", "shopify.checkout.payment.order_covered_by_redeemables_notice": "Your order is covered.", "shopify.checkout.payment.store_credit.label": "Store credit", "shopify.checkout.payment.store_credit.apply_label": "Apply store credit", "shopify.checkout.payment.store_credit.add_label": "Add store credit", "shopify.checkout.payment.store_credit.manage_label": "Manage store credit", "shopify.checkout.payment.store_credit.buying_gift_card": "You can’t use store credit to buy a gift card", "shopify.checkout.payment.store_credit.no_fixed_selling_plans": "You can’t use store credit with deferred purchase options", "shopify.checkout.payment.clear": "Clear", "shopify.checkout.payment.billing_must_match_shipping_notice": "Your payment method’s billing address must match the shipping address.", "shopify.checkout.payment.same_billing_address_checkbox_label": "Use shipping address as billing address", "shopify.checkout.payment.location_billing_address_label": "Location billing address", "shopify.checkout.payment.fact_billing_address_label": "Billing address", "shopify.checkout.payment.location_shipping_address_label": "Location shipping address", "shopify.checkout.payment.use_order_billing_address_label": "Use order billing address", "shopify.checkout.payment.use_location_billing_address_label": "Use location billing address", "shopify.checkout.payment.use_location_shipping_address_label": "Use location shipping address", "shopify.checkout.payment.no_working_method_error": "This store can’t accept payments right now. Refresh this page or come back later.", "shopify.checkout.payment.brand_not_available": "This store doesn't accept {{brand}}. Please use a different card to pay.", "shopify.checkout.payment.shipping_rate_updated": "Available shipping methods have been updated. Confirm your choice before checking out.", "shopify.checkout.payment.billing_address_invalid_phone": "Update the phone number in your billing address for {{label}} {{last_digits}} to continue.", "shopify.checkout.payment.billing_address_invalid_phone_no_card": "Update the phone number in your billing address to continue.", "shopify.checkout.payment.preview_not_available.title": "Preview not available", "shopify.checkout.payment.preview_not_available.content": "Don’t worry, this express checkout works for your customers.", "shopify.checkout.payment.preview_not_available.button": "Ok", "shopify.checkout.payment.card_fields.errors.required.issue_date": "Enter a valid issue date", "shopify.checkout.payment.card_fields.errors.required.issue_number": "Enter a valid issue number", "shopify.checkout.payment.card_fields.errors.invalid.verification_value": "Enter a valid CVV or security code", "shopify.checkout.payment.simulated_checkout": "Preview coming soon", "shopify.checkout.payment.simulated_express_checkout": "Wallets are unavailable in the editor preview due to your region or browser", "shopify.checkout.payment.vaulting_agreement_label": "Save my payment information with %{company} for a faster checkout.", "shopify.checkout.payment.business_customer_vaulting_payment_label": "Save this card at checkout and authorize %{company} to charge it for future purchases for this location. All contacts assigned to this location can use the card.", "shopify.checkout.payment.purchase_options_and_vaulting_agreement_label": "One or more items in your cart is a deferred or subscription purchase. By continuing with your payment, you agree that your payment method will automatically be charged at the price and frequency listed on this page until it ends or you cancel. All cancellations are subject to the %{cancellation_policy_label}.", "shopify.checkout.payment.purchase_options_cancellation_policy_label": "cancellation policy", "shopify.checkout.payment.use_different_credit_card_label": "Use a different card", "shopify.checkout.payment.saved_credit_card": "Your saved credit card", "shopify.checkout.payment.saved_credit_card_info": "Last four digits", "shopify.checkout.payment.saved_credit_card_cvv_verification": "For verification purposes, please enter your card's security code.", "shopify.checkout.payment.saved_credit_card_actions.edit": "Edit", "shopify.checkout.payment.saved_credit_card_actions.delete": "Delete", "shopify.checkout.payment.saved_credit_cards_modal.view_all_label": "View all saved cards", "shopify.checkout.payment.saved_credit_cards_modal.done": "Done", "shopify.checkout.payment.saved_credit_cards_modal.cancel": "Cancel", "shopify.checkout.payment.add_credit_card_modal.title": "Add card", "shopify.checkout.payment.add_credit_card_modal.save": "Save", "shopify.checkout.payment.add_credit_card_modal.cancel": "Cancel", "shopify.checkout.payment.delete_credit_card_modal.title": "Delete card", "shopify.checkout.payment.delete_credit_card_modal.confirmation": "Are you sure you want to delete the {{brandName}} ending in {{lastDigits}}?", "shopify.checkout.payment.delete_credit_card_modal.cancel": "Cancel", "shopify.checkout.payment.delete_credit_card_modal.delete": "Delete", "shopify.checkout.payment.edit_credit_card_modal.title": "Edit card", "shopify.checkout.payment.edit_credit_card_modal.edit_credit_card_rollup_accessibility_label": "Show card options", "shopify.checkout.payment.edit_credit_card_modal.done": "Done", "shopify.checkout.payment.edit_credit_card_modal.cancel": "Cancel", "shopify.checkout.payment.edit_credit_card_modal.description": "You can only modify the billing address of your card.", "shopify.checkout.payment.edit_credit_card_modal.fields.default": "Set as default", "shopify.checkout.payment.edit_credit_card_modal.fields.card_number": "Card number", "shopify.checkout.payment.edit_credit_card_modal.fields.expiry": "Expiration date", "shopify.checkout.payment.edit_credit_card_modal.fields.security_code": "Security code", "shopify.checkout.payment.edit_credit_card_modal.fields.name": "Name on card", "shopify.checkout.payment.edit_credit_card_modal.fields.billing": "Bill to", "shopify.checkout.payment.expired_cards_info.one": "There is %{count} expired card, which you can view and edit in your account.", "shopify.checkout.payment.expired_cards_info.other": "There are %{count} expired cards, which you can view and edit in your account.", "shopify.checkout.payment.local_payment_method_receiver_notice": "After placing your order, you will receive payment details. Make the payment at an ATM or online within {{dueDays}} days.", "shopify.checkout.payment.order_edit_banner_title": "Payment of {{amount_owed}} is needed", "shopify.checkout.payment.order_edit_banner_subtitle": "Your order {{order_number}} was updated. Review these changes, then pay to confirm your order.", "shopify.checkout.payment.order_edit_banner_items_added.one": "Added", "shopify.checkout.payment.order_edit_banner_items_added.other": "Added %{count}", "shopify.checkout.payment.order_edit_banner_items_removed.in_full": "Removed", "shopify.checkout.payment.order_edit_banner_items_removed.with_quantities": "Removed {{delta}} of {{quantity}}", "shopify.checkout.payment.order_edit_banner_items_returned.in_full": "Returned {{quantity}}", "shopify.checkout.payment.order_edit_banner_items_returned.with_quantities": "Returned {{delta}} of {{quantity}}", "shopify.checkout.payment.order_edit_banner_items_return_line_item_removed.in_full": "Removed from return", "shopify.checkout.payment.order_edit_banner_items_return_line_item_removed.with_quantities": "Removed {{delta}} from return", "shopify.checkout.payment.order_edit_banner_single_item_added": "Added", "shopify.checkout.payment.order_edit_banner_multiple_items_added": "Added {{amount}}", "shopify.checkout.payment.order_edit_banner_single_item_removed": "Removed", "shopify.checkout.payment.order_edit_banner_multiple_items_removed": "Removed {{amount}}", "shopify.checkout.payment.order_edit_banner_single_item_returned": "Returned", "shopify.checkout.payment.order_edit_banner_multiple_items_returned": "Returned {{amount}}", "shopify.checkout.payment.order_edit_banner_single_item_return_line_item_removed": "Removed from return", "shopify.checkout.payment.order_edit_banner_multiple_items_return_line_item_removed": "Removed {{amount}} from return", "shopify.checkout.payment.order_edit_banner_updated_order_label": "Updated order", "shopify.checkout.payment.gift_card_wrapper.heading": "Gift card", "shopify.checkout.payment.gift_card_wrapper.apply_gift_card_label": "Apply a gift card", "shopify.checkout.payment.gift_card_wrapper.apply_another_gift_card_label": "Apply another gift card", "shopify.checkout.payment.gift_card_wrapper.gift_card_label": "Gift card %{identifier}", "shopify.checkout.payment.gift_card_wrapper.payment_tag": "Gift card", "shopify.checkout.payment.gift_card_wrapper.add_label": "Add a gift card", "shopify.checkout.payment.gift_card_wrapper.manage_label": "Manage gift cards", "shopify.checkout.payment.gift_card_store_credit.add_label": "Add a gift card or store credit", "shopify.checkout.payment.gift_card_store_credit.manage_label": "Manage gift cards or store credit", "shopify.checkout.payment.gift_card_checkbox_label": "Apply gift card", "shopify.checkout.payment.custom_onsite_payment_method_modal.title": "Custom Onsite Payment Confirmation", "shopify.checkout.payment.markets_pro_disclaimer_label": "By clicking below and completing your order, you agree to purchase your item(s) from Global-e as merchant of record for this transaction, on Global-e's %{markets_pro_terms} and %{privacy_policy}. Global-e is an international fulfilment service provider to %{shop_name}.", "shopify.checkout.payment.markets_pro_disclaimer_terms_and_conditions": "Terms and Conditions", "shopify.checkout.payment.markets_pro_disclaimer_privacy_policy": "Privacy Policy", "shopify.checkout.payment.markets_pro_disclaimer_terms_and_conditions_title": "Merchant of Record Terms and Conditions", "shopify.checkout.payment.markets_pro_disclaimer_privacy_policy_title": "Merchant of Record Privacy Policy", "shopify.checkout.payment.purchase_order_number.title": "PO number", "shopify.checkout.payment.purchase_order_number.label": "PO number (optional)", "shopify.checkout.payment.alternative_payment_currency_total_notice": "Note: you will be charged %{amount_due}.", "shopify.checkout.payment.alternative_payment_currency_total_manual_payment_notice": "Note: converted total is %{amount_due}.", "shopify.checkout.payment.waiting_on_taxes_message": "If your taxes don't calculate within the next few seconds, refresh the page.", "shopify.checkout.payment.waiting_on_taxes_notice": "Calculating taxes", "shopify.checkout.payment.payment_method": "Payment method", "shopify.checkout.payment.payment_method_rollup_accessibility_label": "Payment method options", "shopify.checkout.payment.expired_payment_method": "Selected payment method has expired", "shopify.checkout.payment.payment_method_update_failed": "Payment method update failed.", "shopify.checkout.payment.payment_method_incompatible_with_payment_terms": "Payment method is incompatible with payment terms.", "shopify.checkout.payment.proposed_gateway_unavailable": "The proposed payment gateway is unavailable", "shopify.checkout.payment.pay_in_installments_split_pay_short": "4 payments of %{amount}", "shopify.checkout.payment.gift_cards_unavailable": "Your gift card can no longer be applied to this order and has been removed", "shopify.checkout.payment.payment_flexibility_terms_id_mismatch": "Payment flexibility terms ID mismatch", "shopify.checkout.payment.payment_option.accessibility_label": "Choose a payment option", "shopify.checkout.payment.bank_account_type.checking": "Checking", "shopify.checkout.payment.bank_account_type.savings": "Savings", "shopify.checkout.payment.bank_account_choose": "Choose an account to securely complete your purchase", "shopify.checkout.payment.bank_account_connect_another": "Connect another bank account", "shopify.checkout.payment.bank_account_connect": "Connect bank account", "shopify.checkout.payment.bank_account_missing": "Bank account must be connected", "shopify.checkout.billing.billing_address_title": "Bill to", "shopify.checkout.billing.same_as_shipping": "Same as shipping address", "shopify.checkout.billing.add_address": "Use a different address", "shopify.checkout.billing.edit": "Edit", "shopify.checkout.billing.delete": "Delete", "shopify.checkout.billing.address_more": "More actions for this address", "shopify.checkout.billing.address_edit": "Edit address", "shopify.checkout.billing.address_delete": "Delete address", "shopify.checkout.billing.address_confirm": "Are you sure you want to delete this address from your account?", "shopify.checkout.billing.address_form.add_address": "Add address", "shopify.checkout.billing.address_form.save": "Save", "shopify.checkout.billing.address_form.cancel": "Cancel", "shopify.checkout.company_location.location_title": "Company location", "shopify.checkout.company_location.location_rollup_accessibility_label": "Company location options", "shopify.checkout.payment_summary.gift_card_current_balance_notice": "Current balance %{amount}", "shopify.checkout.payment_summary.credit_card_expires_on_notice": "Expires on %{month}/%{year}", "shopify.checkout.payment_summary.express_payment_gateway_label": "Express", "shopify.checkout.payment_summary.manual_payment_gateway_label": "Manual", "shopify.checkout.payment_summary.billing_address_title": "Billing address", "shopify.checkout.payment_summary.free_label": "Free", "shopify.checkout.field_errors.handle_phone_invalid": "Enter a valid phone number", "shopify.checkout.field_errors.handle_email_invalid": "Enter a valid email", "shopify.checkout.field_errors.email_invalid": "Enter a valid email", "shopify.checkout.field_errors.email_or_phone_blank": "Enter an email or phone number", "shopify.checkout.field_errors.email_or_phone_invalid": "Enter a valid email or a mobile phone number", "shopify.checkout.field_errors.address_first_name_blank": "Enter a first name", "shopify.checkout.field_errors.address_first_name_too_long": "First name is too long (maximum is 255 characters)", "shopify.checkout.field_errors.address_first_name_contains_emojis": "First name cannot contain emojis", "shopify.checkout.field_errors.address_first_name_contains_html_tags": "First name cannot contain HTML tags.", "shopify.checkout.field_errors.address_first_name_contains_url": "First name cannot contain a URL", "shopify.checkout.field_errors.address_first_name_contains_mathematical_symbols": "First name cannot contain mathematical symbols", "shopify.checkout.field_errors.address_last_name_blank": "Enter a last name", "shopify.checkout.field_errors.address_last_name_too_long": "Last name is too long (maximum is 255 characters)", "shopify.checkout.field_errors.address_last_name_contains_emojis": "Last name cannot contain emojis", "shopify.checkout.field_errors.address_last_name_contains_html_tags": "Last name cannot contain HTML tags.", "shopify.checkout.field_errors.address_last_name_contains_url": "Last name cannot contain a URL", "shopify.checkout.field_errors.address_last_name_contains_mathematical_symbols": "Last name cannot contain mathematical symbols", "shopify.checkout.field_errors.address_address1_blank": "Enter an address", "shopify.checkout.field_errors.address_address1_too_long": "The first address line is too long (maximum is 255 characters)", "shopify.checkout.field_errors.address_address1_contains_emojis": "Address1 cannot contain emojis", "shopify.checkout.field_errors.address_address1_contains_html_tags": "Address1 cannot contain HTML tags.", "shopify.checkout.field_errors.address_address1_contains_mathematical_symbols": "The first address line cannot contain mathematical symbols", "shopify.checkout.field_errors.address_address2_blank": "Enter an apartment, suite, etc.", "shopify.checkout.field_errors.address_address2_too_long": "The second address line is too long (maximum is 255 characters)", "shopify.checkout.field_errors.address_address2_contains_emojis": "Address2 cannot contain emojis", "shopify.checkout.field_errors.address_address2_contains_html_tags": "Address2 cannot contain HTML tags.", "shopify.checkout.field_errors.address_address2_contains_mathematical_symbols": "The second address line cannot contain mathematical symbols", "shopify.checkout.field_errors.address_city_blank": "Enter a city", "shopify.checkout.field_errors.address_city_too_long": "City is too long (maximum is 255 characters)", "shopify.checkout.field_errors.address_city_contains_emojis": "City cannot contain emojis", "shopify.checkout.field_errors.address_city_contains_html_tags": "City cannot contain HTML tags.", "shopify.checkout.field_errors.address_city_contains_mathematical_symbols": "City cannot contain mathematical symbols", "shopify.checkout.field_errors.address_country_blank": "Select a country/region", "shopify.checkout.field_errors.address_country_not_supported": "We don't ship to this country or region. Enter a new shipping address and try again.", "shopify.checkout.field_errors.address_province_blank": "Select a state / province", "shopify.checkout.field_errors.address_province_invalid": "Select a valid state / province", "shopify.checkout.field_errors.address_company_blank": "Enter a company name", "shopify.checkout.field_errors.address_company_too_long": "Company name is too long (maximum is 255 characters)", "shopify.checkout.field_errors.address_company_contains_emojis": "Company cannot contain emojis", "shopify.checkout.field_errors.address_company_contains_html_tags": "Company cannot contain HTML tags.", "shopify.checkout.field_errors.address_company_contains_mathematical_symbols": "Company cannot contain mathematical symbols", "shopify.checkout.field_errors.address_phone_blank": "Enter a valid phone number", "shopify.checkout.field_errors.address_phone_contains_emojis": "Phone number cannot contain emojis", "shopify.checkout.field_errors.address_phone_invalid": "Enter a valid phone number", "shopify.checkout.field_errors.address_zip_blank": "Enter a ZIP / postal code", "shopify.checkout.field_errors.address_zip_blocked_address": "This location isn't supported", "shopify.checkout.field_errors.address_zip_too_long": "ZIP / postal code is too long (maximum is 255 characters)", "shopify.checkout.field_errors.address_zip_not_supported": "This location isn't supported", "shopify.checkout.field_errors.address_zip_invalid_for_country": "Enter a valid ZIP / postal code for %{country}", "shopify.checkout.field_errors.address_zip_invalid_for_province": "Enter a valid ZIP / postal code for %{province}", "shopify.checkout.field_errors.address_zip_invalid_for_country_and_province": "Enter a valid ZIP / postal code for %{province}, %{country}", "shopify.checkout.field_errors.address_zip_contains_emojis": "ZIP / postal code cannot contain emojis", "shopify.checkout.field_errors.address_zip_contains_mathematical_symbols": "ZIP / postal code cannot contain mathematical symbols", "shopify.checkout.field_errors.address_generic_blank": "Required field", "shopify.checkout.field_errors.address_generic_invalid": "Invalid entry", "shopify.checkout.field_errors.address_generic_error": "Field error", "shopify.checkout.field_errors.credit_card_name_blank": "Enter your name exactly as it’s written on your card", "shopify.checkout.field_errors.credit_card_name_invalid": "Enter your name exactly as it’s written on your card", "shopify.checkout.field_errors.credit_card_number_invalid": "Enter a valid card number", "shopify.checkout.field_errors.credit_card_expiry_invalid": "Enter a valid expiration date", "shopify.checkout.field_errors.credit_card_month_invalid": "Enter a valid expiration month", "shopify.checkout.field_errors.credit_card_year_invalid": "Enter a valid expiration year", "shopify.checkout.field_errors.credit_card_start_invalid": "Enter a valid card start date", "shopify.checkout.field_errors.credit_card_start_month_invalid": "Enter a valid card start month", "shopify.checkout.field_errors.credit_card_start_year_invalid": "Enter a valid card start year", "shopify.checkout.field_errors.credit_card_number_blank": "Enter a card number", "shopify.checkout.field_errors.credit_card_verification_value_blank": "Enter the CVV or security code on your card", "shopify.checkout.field_errors.delivery_line_detail_changed": "Some delivery details may have changed. Verify your shipping method and try again.", "shopify.checkout.field_errors.delivery_zone_not_found": "The specified zone was not found in the specified country.", "shopify.checkout.field_errors.reduction_code_code_not_found": "Enter a valid discount code or gift card", "shopify.checkout.field_errors.reduction_code_discount_not_found": "Enter a valid discount code", "shopify.checkout.field_errors.reduction_code_gift_card_code_invalid": "Enter a valid gift card", "shopify.checkout.field_errors.reduction_code_gift_card_unusable": "You can’t use a gift card to buy another gift card", "shopify.checkout.field_errors.reduction_code_gift_cards_not_supported_with_deferred_purchase_options": "Gift cards are not supported with deferred purchase options.", "shopify.checkout.field_errors.shipping_line_phone_blank": "Enter a phone number to use this delivery method", "shopify.checkout.field_errors.shipping_line_phone_invalid": "Enter a valid phone number", "shopify.checkout.field_errors.tips_invalid_tip_amount": "Enter a tip less than %{amount}", "shopify.checkout.field_errors.gift_card_already_applied": "This gift card has already been applied to your order", "shopify.checkout.field_errors.gift_card_disabled": "This gift card is no longer available", "shopify.checkout.field_errors.gift_card_expired": "This gift card has expired", "shopify.checkout.field_errors.gift_card_depleted": "This gift card has no funds left", "shopify.checkout.field_errors.gift_card_currency_mismatch": "Only gift cards in %{checkout_currency} can be used with this order", "shopify.checkout.field_errors.gift_cards_not_supported_with_deferred_purchase_options": "Gift cards are not supported with deferred purchase options.", "shopify.checkout.field_errors.gift_card_buying_gift_card": "You can't use a gift card to buy another gift card", "shopify.checkout.field_errors.payment_gateway_missing": "Choose a payment method.", "shopify.checkout.field_errors.payment_gateway_needs_paid_plan_error": "This payment method isn’t available on the store right now", "shopify.checkout.field_errors.payment_gateway_not_in_test_mode": "isn't setup to test orders. This store can't accept real orders or real payments.", "shopify.checkout.field_errors.payment_gateway_incompatible_with_multi_currency": "To complete this checkout in your local currency, you need to pay by credit card.", "shopify.checkout.field_errors.payment_gateway_unsupported_for_subscriptions": "This payment method isn't available for subscription orders.", "shopify.checkout.field_errors.payment_gateway_unsupported_for_deferred_purchase_options": "This payment method isn't available for deferred purchase options.", "shopify.checkout.field_errors.payment_gateway_unsupported_for_country": "This payment method isn't available for your shipping country.", "shopify.checkout.field_errors.payment_gateway_unsupported_for_development_store_html": "To place a test order, you’ll need to use a test payment gateway. Learn more about <a href=\"%{docs_link}\">testing orders in development stores</a>.", "shopify.checkout.field_errors.payment_gateway_unsupported_for_order_above_maximum_orders_html": "You’ve reached the limit for development store orders with this payment processor. To place another order, you’ll need to use a test payment gateway. Learn more about <a href=\"%{docs_link}\">testing orders in development stores</a>.", "shopify.checkout.field_errors.purchase_options_agreement_blank": "To continue with your purchase, agree to the deferred or recurring purchase terms.", "shopify.checkout.field_errors.physical_gift_card_invalid_quantity": "Physical gift card line items must have a quantity that matches the number of gift card codes.", "shopify.checkout.field_errors.physical_gift_card_duplicate_code": "Physical gift card line item contains duplicate code.", "shopify.checkout.field_errors.physical_gift_card_invalid_code": "Physical gift card code is invalid.", "shopify.checkout.field_errors.email_blank": "Enter an email", "shopify.checkout.field_errors.phone_blank": "Enter a phone number", "shopify.checkout.field_errors.address_country_invalid": "Select a country/region from the list", "shopify.checkout.field_errors.address_zip_invalid": "Enter a valid ZIP / postal code", "shopify.checkout.field_errors.gift_card_invalid": "Gift card \"••••%{last_four_characters}\" is no longer valid", "shopify.checkout.field_errors.vaulting_agreement_blank": "Your card must be saved because payment for the balance of this order is due later.", "shopify.checkout.field_errors.sms_marketing_phone_invalid": "Enter a valid phone number", "shopify.checkout.field_errors.address_suggestion_did_you_mean_html": "Did you mean %{html}?", "shopify.checkout.field_errors.split_cart_international_shipping_not_supported": "All items must use the same delivery method when shipping internationally. Update the delivery method or create multiple orders.", "shopify.checkout.field_errors.invalid_delivery_address_reference_handle": "Delivery address reference handle could not be matched to any available delivery addresses.", "shopify.checkout.field_errors.must_use_available_delivery_address": "Delivery address must be selected from the available delivery addresses.", "shopify.checkout.field_errors.must_use_available_shipping_rate": "Shipping rate must be selected from the available shipping rates.", "shopify.checkout.store_credit_errors.generic_error": "There was a problem with your store credit. Try again or reload the page.", "shopify.checkout.redeemable_errors.shop_cash.currency_mismatch": "The currency has changed. Please review your Shop Cash and offer.", "shopify.checkout.redeemable_errors.gift_card.duplicate": "The gift card has already been applied to your order", "shopify.checkout.redeemable_errors.gift_card.insufficient_funds": "This gift card has no funds left", "shopify.checkout.redeemable_errors.gift_card.expired": "This gift card has expired", "shopify.checkout.redeemable_errors.gift_card.not_available": "This gift card is no longer available", "shopify.checkout.redeemable_errors.gift_card.invalid": "Enter a valid gift card", "shopify.checkout.redeemable_errors.gift_card.currency_mismatch": "Only gift cards in %{shop_currency} are accepted", "shopify.checkout.redeemable_errors.gift_card.generic": "The gift card could not be added to checkout", "shopify.checkout.redeemable_errors.gift_card.throttle_limit_exceeded": "You have exceeded the maximum number of attempts. Try again later.", "shopify.checkout.redeemable_errors.gift_card.no_longer_available": "This gift card can no longer be used with this checkout", "shopify.checkout.merchant_instruction_errors.empty_stable_id": "The %{position} %{name} merchandise instruction contains an empty stable ID.", "shopify.checkout.merchant_instruction_errors.invalid_stable_id_target": "The %{position} %{name} merchandise instruction references stable ID %{stable_id}, but that stable ID is not in merchandise lines.", "shopify.checkout.merchant_instruction_errors.duplicate_variant_id": "The %{position} %{name} merchandise instruction contains a duplicate variant ID %{variant_id}.", "shopify.checkout.merchant_instruction_errors.invalid_variant_id_target": "The %{name} merchandise instruction references variant ID %{variant_id}, but that variant is not in merchandise lines.", "shopify.checkout.merchant_instruction_errors.invalid_price_override_target": "The price override merchandise instruction for line item %{stable_id} is invalid since it is a bundle component.", "shopify.checkout.payment_errors.amount_too_small": "The minimum payment amount must be higher than $0.50 USD. Please try again with a higher amount.", "shopify.checkout.payment_errors.method_not_available": "The requested payment method is not available. Choose another payment method and try again.", "shopify.checkout.payment_errors.available_methods_updated": "Available payment methods have been updated.", "shopify.checkout.payment_errors.generic_error": "There was an issue processing your payment. Try again or use a different payment method.", "shopify.checkout.payment_errors.rejected_transaction": "Your payment was declined. Try again or use a different payment method.", "shopify.checkout.payment_errors.credit_card_processing": "Your payment can’t be processed for technical reasons. Try again in a few minutes or use a different payment method.", "shopify.checkout.payment_errors.checkout_amount_changed": "Your payment couldn't be processed because the amount due at checkout has changed. Please check your order and try again.", "shopify.checkout.payment_errors.deferred_payment_not_allowed": "Deferred payments are not allowed. Please check your order and try again.", "shopify.checkout.payment_errors.deferred_payment_required": "This order requires a deferred payment. Please check your order and try again.", "shopify.checkout.payment_errors.deferred_amount_changed": "Your payment couldn't be processed because the deferred payment amount has changed. Please check your order and try again.", "shopify.checkout.payment_errors.deferred_date_changed": "Your payment couldn't be processed because the deferred payment date has changed. Please check your order and try again.", "shopify.checkout.payment_errors.order_total_changed": "Your order total has changed. Review and try again.", "shopify.checkout.payment_errors.payment_processing": "Your payment can’t be processed for technical reasons. Try again or use a different payment method.", "shopify.checkout.payment_errors.payment_processing_no_retry": "Your payment can’t be processed for technical reasons. Please try a different payment method.", "shopify.checkout.payment_errors.paypal_zero_amount": "You can’t use PayPal for %{amount} orders. Confirm your order is correct and check out without PayPal.", "shopify.checkout.payment_errors.paypal_invalid_token": "PayPal wasn't available. Try again or use a different checkout option.", "shopify.checkout.payment_errors.pay_later_failed": "There was an error processing your payment with Pay later. Choose another payment method and try again.", "shopify.checkout.payment_errors.three_d_secure_failed": "Your payment couldn’t be verified. Try again or use a different payment method.", "shopify.checkout.payment_errors.three_d_secure_failed_duplicate": "The credit card's 3D Secure authentication failed because another is already in progress.", "shopify.checkout.payment_errors.three_d_secure_failed_payment_method": "Your card couldn’t be verified. Try again or add a different card.", "shopify.checkout.payment_errors.three_d_secure_throttled": "Too many attempts; please wait before trying again", "shopify.checkout.payment_errors.subscription_agreement_blank": "To buy a subscription your payment details need to be saved. To continue, confirm that you agree or remove subscription items from you order.", "shopify.checkout.payment_errors.insufficient_funds": "Your payment was declined due to insufficient funds. Try again or use a different payment method.", "shopify.checkout.payment_errors.multiple_deferred_payment_not_allowed": "Only one deferred payment allowed per request. Please check your request and try again.", "shopify.checkout.payment_errors.shipping_must_match_billing": "Billing address must match shipping address. Please use a different payment method.", "shopify.checkout.payment_errors.decision_rule_block": "There was an issue processing your payment. Please contact the store owner.", "shopify.checkout.payment_errors.pick_up_card": "Your card was declined. Try again or use a different payment method.", "shopify.checkout.payment_errors.cannot_verify": "Your payment details couldn’t be verified. Check your card details and try again.", "shopify.checkout.payment_errors.invalid_number": "Enter a valid card number", "shopify.checkout.payment_errors.invalid_expiry_date": "Enter a valid expiration date", "shopify.checkout.payment_errors.invalid_cvc": "Enter a valid security code", "shopify.checkout.payment_errors.expired_card": "Your card has expired. Use a different payment method.", "shopify.checkout.payment_errors.payment_method_not_applicable": "This payment method is not applicable. Use a different payment method.", "shopify.checkout.payment_errors.card_declined": "Your card was declined. Try again or use a different payment method.", "shopify.checkout.payment_errors.processing_error": "There was an issue processing your payment. Try again or use a different payment method.", "shopify.checkout.payment_errors.processing_error_checkout_as_guest": "There was an issue processing your payment. Try again with a different payment method or {{checkout_as_guest_link}}.", "shopify.checkout.payment_errors.selected_payment_method": "There was an issue with your selected payment method and you haven't been charged. Try again or use a different payment method.", "shopify.checkout.payment_errors.payment_method_billing_address": "There was an issue with your selected payment method's billing address and you haven't been charged. Check the address and try again or use a different payment method.", "shopify.checkout.payment_errors.call_issuer": "Your card was declined. Use a different payment method or contact your bank for more information.", "shopify.checkout.payment_errors.test_mode_live_card": "This store can’t accept real orders or real payments.", "shopify.checkout.payment_errors.transient_error": "There was a problem processing your payment. Try refreshing this page or check your internet connection.", "shopify.checkout.payment_errors.missing_shipping_address": "Your payment couldn’t be processed because your shipping address is missing. Add a shipping address and try again.", "shopify.checkout.payment_errors.invalid_shipping_address": "Your payment couldn’t be processed because we couldn’t find your shipping address. Check your shipping address and try again.", "shopify.checkout.payment_errors.successful_offsite_with_gift_card_error": "There was an issue processing your gift card payment. Try again or use a different payment method.", "shopify.checkout.payment_errors.development_store_order_limit_reached": "You’ve reached the limit for development store orders with this payment processor. To place another order, you’ll need to use a test payment gateway. Learn more about %{development_store_order_testing_docs_link}.", "shopify.checkout.payment_errors.development_store_with_order_limit": "To place a test order, you’ll need to use a test payment gateway. Learn more about %{development_store_order_testing_docs_link}.", "shopify.checkout.payment_errors.development_store_order_testing_docs_link": "testing orders in development stores", "shopify.checkout.payment_errors.payment_above_threshold": "The payment amount is above the monetary limit for this region. Reduce the total and try again", "shopify.checkout.payment_errors.risky": "There was an issue processing your payment. Try again or use a different payment method.", "shopify.checkout.payment_errors.expired_buyer_action": "The payment method timed out. Please try again.", "shopify.checkout.payment_errors.name_mismatch": "Please check your card details. The name on your card and billing address don't match.", "shopify.checkout.payment_errors.shop_pay_wallet_not_available": "Shop Pay is not available for this checkout.", "shopify.checkout.payment_errors.market_manager_blocks_wallet_payments": "Payment method is not supported in your country.", "shopify.checkout.payment_errors.paypal_over_capture": "Your order total was updated. Pay with {{walletName}} to complete your order.", "shopify.checkout.shipping_errors.shipping_method": "The shipping options have changed for your order. Please review your selection.", "shopify.checkout.shipping_errors.shipping_method_not_available": "Your cart has been updated and the items you added can’t be shipped to your address. Remove the items to complete your order.", "shopify.checkout.shipping_errors.shipping_method_not_available_generic.one": "Your order cannot be shipped to the selected address. Review your address to ensure it's correct and try again, or select a different address.", "shopify.checkout.shipping_errors.shipping_method_not_available_generic.other": "Some items in your cart can’t be shipped to the address you entered. Review your address to ensure it's correct and try again, or select a different address.", "shopify.checkout.shipping_errors.shipping_method_not_available_due_to_unsatisfied_conditions": "Items in the cart do not meet price or weight requirements to qualify for shipping. Update your cart and try again.", "shopify.checkout.shipping_errors.shipping_method_not_available_updated.one": "The item you added doesn’t ship to your location. Remove it to check out, or contact the store.", "shopify.checkout.shipping_errors.shipping_method_not_available_updated.other": "One or more items you added don’t ship to your location. Remove them to check out, or contact the store.", "shopify.checkout.shipping_errors.payment_method_not_supported": "You can't use this payment method for %{country}. Please use a different payment method to ship to this country.", "shopify.checkout.shipping_errors.empty_state": "Enter your shipping address to view available shipping methods.", "shopify.checkout.shipping_errors.title": "Shipping not available", "shopify.checkout.shipping_errors.invalid_selected_address": "Selected shipping address is not valid", "shopify.checkout.shipping_errors.invalid_selected_address_with_update_link": "Selected shipping address is not valid. %{update_address_link} or select a different address", "shopify.checkout.shipping_errors.unavailable_addresses_only_countries": "Your order can only be shipped to {{countriesList}}. Add a new address to continue.", "shopify.checkout.shipping_errors.unavailable_selected_address": "Shipping not available for selected address", "shopify.checkout.shipping_errors.unavailable_selected_address_for_region": "Shipping isn’t available for {{region}}. Select a different address to continue", "shopify.checkout.alternative_payment_method_banner.express_checkout": "Express checkout", "shopify.checkout.alternative_payment_method_banner.or": "OR", "shopify.checkout.alternative_payment_method_banner.or_pay_another_way": "Or pay another way", "shopify.checkout.alternative_payment_method_banner.or_check_out_another_way": "Or check out another way", "shopify.checkout.alternative_payment_method_banner.or_pay_with_credit_card": "Or pay with credit card", "shopify.checkout.alternative_payment_method_banner.show_more_options": "Show more options", "shopify.checkout.alternative_payment_method_banner.hide_more_options": "Hide more options", "shopify.checkout.post_purchase.page_title": "Post-purchase", "shopify.checkout.post_purchase.title": "Post-purchase", "shopify.checkout.post_purchase.escape_confirmation": "You’ve paid for your order.", "shopify.checkout.post_purchase.escape_action": "View order confirmation ›", "shopify.checkout.post_purchase.components.close": "Close", "shopify.checkout.post_purchase.components.expand": "View more", "shopify.checkout.post_purchase.components.processing": "Processing…", "shopify.checkout.post_purchase.components.submit": "Submit", "shopify.checkout.processing.redirecting_page_title": "Redirecting…", "shopify.checkout.processing.redirecting_title": "Redirecting…", "shopify.checkout.processing.redirecting_notice": "Wait while we redirect you.", "shopify.checkout.processing.redirecting_to_bank": "Redirecting to {{bank}}", "shopify.checkout.processing.complete_your_purchase_title": "Complete your purchase", "shopify.checkout.processing.continue_to_payment_gateway_notice": "Continue to payment to add your payment information.", "shopify.checkout.processing.continue_to_payment_gateway_button_label": "Continue to payment", "shopify.checkout.processing.complete_your_purchase_title_error": "Correct your payment information", "shopify.checkout.processing.continue_to_payment_gateway_notice_error": "Your payment information couldn’t be verified.", "shopify.checkout.processing.continue_to_payment_gateway_button_label_error": "Update payment information", "shopify.checkout.processing.page_title": "Processing order", "shopify.checkout.processing.title": "Processing order", "shopify.checkout.processing.your_order_has_been_received_title": "Thanks for your order", "shopify.checkout.processing.you_will_receive_confirmation": "We’re getting a lot of orders right now. You should receive confirmation by email or text soon. If you don't receive it in the next hour, let us know.", "shopify.checkout.processing.you_will_not_be_charged": "You won't be charged yet", "shopify.checkout.processing.wait.short": "Your order’s being processed.", "shopify.checkout.processing.wait.medium.one": "Your order’s being processed. Estimated wait time is %{count} minute.", "shopify.checkout.processing.wait.medium.other": "Your order’s being processed. Estimated wait time is %{count} minutes.", "shopify.checkout.processing.wait.long": "You’ll receive an email as soon as your order’s processed. There’s currently a high volume of orders.", "shopify.checkout.processing.wait.no_auto_refresh_html": "If you’re not automatically redirected, %{link}.", "shopify.checkout.processing.wait.refresh_this_page": "refresh this page", "shopify.checkout.processing.remote_shops_payments_processing": "Processing payments", "shopify.checkout.processing.action_required": "Action required", "shopify.checkout.processing.modal_notice": "We are contacting {{<PERSON><PERSON><PERSON><PERSON>}}", "shopify.checkout.processing.remote_checkout_count": "%{current} of %{total}", "shopify.checkout.payment_challenge_modal.instructions": "Complete your purchase using your {{paymentMethod}} code", "shopify.checkout.payment_challenge_modal.timer_message": "You have {{timeLeft}} to pay", "shopify.checkout.payment_challenge_modal.copy_text": "Copy {{payment<PERSON>ethod}} code", "shopify.checkout.payment_challenge_modal.open_app_text": "Open {{paymentMethod}} app", "shopify.checkout.payment_challenge_modal.header": "Pay with {{payment<PERSON>eth<PERSON>}}", "shopify.checkout.payment_challenge_modal.qr_code.instructions": "Complete your purchase using your {{paymentMethod}} code", "shopify.checkout.payment_challenge_modal.external.instructions": "Confirm payment on your app to complete this purchase", "shopify.checkout.payment_challenge_modal.close": "Close the challenge", "shopify.checkout.failed_payment.page_title": "Payment couldn’t be processed", "shopify.checkout.failed_payment.title": "Payment couldn’t be processed", "shopify.checkout.failed_payment.payment_not_processed_title": "Your payment couldn’t be processed", "shopify.checkout.failed_payment.payment_not_processed_text": "You haven’t been charged. Return to your cart to complete your purchase.", "shopify.checkout.failed_payment.return_to_cart_button_label": "Return to cart", "shopify.checkout.payment_gateway.credit_card_label": "Credit card", "shopify.checkout.payment_gateway.bank_deposit_label": "Bank Deposit", "shopify.checkout.payment_gateway.cash_on_delivery_label": "Cash on Delivery (COD)", "shopify.checkout.payment_gateway.money_order_label": "Money Order", "shopify.checkout.payment_gateway.debit_card_label": "Debit card", "shopify.checkout.payment_gateway.deferred_payment_label": "Choose payment method later", "shopify.checkout.payment_gateway.fixed_payment_label": "Your payment will be due on {{dueDate}}.", "shopify.checkout.payment_gateway.generic_payment_terms": "You’re on {{translatedName}} terms. Your payment will be due on {{dueDate}}.", "shopify.checkout.payment_gateway.login_prompt.no_access.message": "{{logIn}} to an account with access to this location to add or use saved payment methods.", "shopify.checkout.payment_gateway.login_prompt.no_access.link": "Log in", "shopify.checkout.payment_gateway.direct_payment_terms": "You’re on {{translatedName}} terms. Your card will be charged on {{dueDate}}.", "shopify.checkout.payment_gateway.fixed_direct_payment_terms": "Your card will be charged on {{dueDate}}.", "shopify.checkout.payment_gateway.order_due_on_fulfillment_choose_later": "Your payment will be due once your order is fulfilled.", "shopify.checkout.payment_gateway.order_due_on_fulfillment_payment": "Your card will be charged once your order is fulfilled.", "shopify.checkout.payment_gateway.order_due_on_receipt_choose_later": "Your payment will be due on receipt.", "shopify.checkout.payment_gateway.order_deposit_due_now": "{{depositAmount}} is due today. The balance is on {{translatedName}} terms and is due on {{dueDate}}.", "shopify.checkout.payment_gateway.order_deposit_due_on_fulfillment": "{{depositAmount}} is due today. The balance will be due once your order is fulfilled.", "shopify.checkout.payment_gateway.order_deposit_due_on_receipt": "{{depositAmount}} is due today. The balance will be due later.", "shopify.checkout.payment_gateway.order_deposit_due_on_fixed_date": "{{depositAmount}} is due today. The balance will be due on {{dueDate}}.", "shopify.checkout.payment_gateway.draft_order_payment_due_later_no_terms": "Your payment will be due once your order is confirmed.", "shopify.checkout.payment_gateway.draft_order_payment_due_later_no_terms_payment_selected": "Your card will be charged once your order is confirmed.", "shopify.checkout.payment_gateway.draft_order_payment_due_later_terms": "You’ll be on {{translatedName}} terms once your order is confirmed.", "shopify.checkout.payment_gateway.draft_order_payment_due_on_fulfillment": "Your payment will be due once your order has been confirmed and fulfilled.", "shopify.checkout.payment_gateway.draft_order_deposit_due_later": "{{depositAmount}} will be due when your order is confirmed. The balance will be on {{translatedName}} terms.", "shopify.checkout.payment_gateway.draft_order_deposit_due_on_fulfillment": "{{depositAmount}} will be due when your order is confirmed. The balance will be once your order is fulfilled.", "shopify.checkout.thank_you.title": "Thank you for your purchase!", "shopify.checkout.thank_you.confirmation_email_sent_text": "A confirmation email has been sent to %{email}", "shopify.checkout.thank_you.return_to_store_link_label": "Continue shopping", "shopify.checkout.thank_you.print_link_label": "Print receipt", "shopify.checkout.thank_you.payment_information_title": "Payment information", "shopify.checkout.thank_you.shipping_information_title": "Shipping information", "shopify.checkout.thank_you.page_title": "Thank you, %{display_name}!", "shopify.checkout.thank_you.page_title_no_name": "Thank you!", "shopify.checkout.thank_you.purchase_order_number_title": "PO number #%{purchase_order_number}", "shopify.checkout.thank_you.cancelled_page_title": "Order canceled", "shopify.checkout.thank_you.failed_pending_payment_page_title": "There’s a problem with your payment", "shopify.checkout.thank_you.customer_information_title": "Order details", "shopify.checkout.thank_you.contact_information_title": "Contact information", "shopify.checkout.thank_you.billing_address_title": "Billing address", "shopify.checkout.thank_you.company_location_title": "Location", "shopify.checkout.thank_you.shipping_address_title": "Shipping address", "shopify.checkout.thank_you.shipping_address_map_title": "Google map displaying pin point of shipping address: %{address}", "shopify.checkout.thank_you.payment_method_title": "Payment method", "shopify.checkout.thank_you.payment_method_not_charged": "Not charged", "shopify.checkout.thank_you.shipping_method_title": "Shipping method", "shopify.checkout.thank_you.tracking_number": "Tracking number:", "shopify.checkout.thank_you.company_tracking_number": "%{company} tracking number:", "shopify.checkout.thank_you.estimated_arrival": "Estimated delivery date: ", "shopify.checkout.thank_you.estimated_delivery": "Current delivery estimate: ", "shopify.checkout.thank_you.re-order": "Re-order the same items", "shopify.checkout.thank_you.confirmed": "Confirmed", "shopify.checkout.thank_you.confirmed_items_count": "%{confirmed_count} of %{total_count} items are confirmed", "shopify.checkout.thank_you.gift_card_confirmation_by_text": "If you didn’t receive it, resend the text or contact us.", "shopify.checkout.thank_you.gift_card_confirmation_by_email": "If you didn’t receive it, resend the email or contact us.", "shopify.checkout.thank_you.gift_card_title": "Gift cards", "shopify.checkout.thank_you.resend_gift_cards": "Resend gift cards", "shopify.checkout.thank_you.thank_you_title": "Your order is confirmed", "shopify.checkout.thank_you.thank_you_title_payment": "Your payment is confirmed", "shopify.checkout.thank_you.thank_you_title_review": "Your order has been submitted for review", "shopify.checkout.thank_you.thank_you_title_payments_app_pending_default": "Your payment is being processed", "shopify.checkout.thank_you.thank_you_title_payments_app_pending_buyer_action_required": "Your order was received", "shopify.checkout.thank_you.thank_you_title_payments_app_failed_pending_payment": "Your payment couldn’t be processed", "shopify.checkout.thank_you.thank_you_confirmed_order_with_email": "You’ll receive a confirmation email with your order number shortly.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_phone": "You’ll receive a confirmation text with your order number shortly.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_email_and_phone": "You’ll receive a confirmation email with your order number shortly.", "shopify.checkout.thank_you.thank_you_confirmed_orders_with_email": "You'll get separate confirmation emails with your order numbers soon", "shopify.checkout.thank_you.thank_you_confirmed_orders_with_phone": "You'll get separate confirmation texts with your order numbers soon", "shopify.checkout.thank_you.thank_you_confirmed_orders_with_email_and_phone": "You'll get separate confirmation emails with your order numbers soon", "shopify.checkout.thank_you.thank_you_confirmed_order_with_email_payment": "You made a payment on your order %{date}.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_phone_payment": "You made a payment on your order %{date}.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_email_and_phone_payment": "You made a payment on your order %{date}.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_email_pick_up": "You’ll receive an email when your order is ready for pickup.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_phone_pick_up": "You’ll receive a text when your order is ready for pickup.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_email_and_phone_pick_up": "You’ll receive an email when your order is ready for pickup.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_missing": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_missing_payment": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_missing_pick_up": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_draft": "We’ll let you know if we need more information or when your order is confirmed.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_draft_payment": "We’ll let you know if we need more information or when your order is confirmed.", "shopify.checkout.thank_you.thank_you_confirmed_order_with_draft_pick_up": "We’ll let you know if we need more information or when your order is confirmed.", "shopify.checkout.thank_you.thank_you_unconfirmed_items_heading.one": "An item is not confirmed", "shopify.checkout.thank_you.thank_you_unconfirmed_items_heading.other": "Some items are not confirmed", "shopify.checkout.thank_you.thank_you_unconfirmed_items_description.one": "You haven't been charged for this", "shopify.checkout.thank_you.thank_you_unconfirmed_items_description.other": "You haven't been charged for these", "shopify.checkout.thank_you.thank_you_confirmed_items_with_email.one": "You’ll receive a confirmation email with your order number soon", "shopify.checkout.thank_you.thank_you_confirmed_items_with_email.other": "You’ll receive confirmation emails with your order numbers soon", "shopify.checkout.thank_you.thank_you_confirmed_items_with_phone.one": "You’ll receive a confirmation text with your order number soon", "shopify.checkout.thank_you.thank_you_confirmed_items_with_phone.other": "You’ll receive confirmation texts with your order numbers soon", "shopify.checkout.thank_you.thank_you_item_label.one": "%{count} item", "shopify.checkout.thank_you.thank_you_item_label.other": "%{count} items", "shopify.checkout.thank_you.thank_you_from_shop": "From %{shop_name}", "shopify.checkout.thank_you.thank_you_payments_app_failed_pending_payment": "You weren’t charged for your order. Return to your cart to review your order and pay again.", "shopify.checkout.thank_you.thank_you_payments_app_failed_pending_payment_payment": "You weren’t charged for your order. Return to your cart to review your order and pay again.", "shopify.checkout.thank_you.thank_you_payments_app_failed_pending_payment_pick_up": "You weren’t charged for your order. Return to your cart to review your order and pay again.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_missing": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_missing_payment": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_missing_pick_up": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_email_and_phone": "You’ll receive a confirmation email with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_email_and_phone_payment": "You’ll receive a confirmation email with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_email_and_phone_pick_up": "You’ll receive a confirmation email with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_email": "You’ll receive a confirmation email with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_email_payment": "You’ll receive a confirmation email with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_email_pick_up": "You’ll receive a confirmation email with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_phone": "You’ll receive a confirmation text with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_phone_payment": "You’ll receive a confirmation text with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_default_with_phone_pick_up": "You’ll receive a confirmation text with your order number shortly.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_missing": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_missing_payment": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_missing_pick_up": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_email_and_phone": "After completing your payment, you’ll receive a confirmation email with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_email_and_phone_payment": "After completing your payment, you’ll receive a confirmation email with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_email_and_phone_pick_up": "After completing your payment, you’ll receive a confirmation email with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_email": "After completing your payment, you’ll receive a confirmation email with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_email_payment": "After completing your payment, you’ll receive a confirmation email with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_email_pick_up": "After completing your payment, you’ll receive a confirmation email with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_phone": "After completing your payment, you’ll receive a confirmation text with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_phone_payment": "After completing your payment, you’ll receive a confirmation text with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_buyer_action_required_with_phone_pick_up": "After completing your payment, you’ll receive a confirmation text with your order number.", "shopify.checkout.thank_you.thank_you_payments_app_pending_payment_description_provider_action_required_default": "Your payment is being processed. You'll get an email when your payment is confirmed.", "shopify.checkout.thank_you.track_your_order_link_text": "Track your order", "shopify.checkout.thank_you.checkout_on_remote_shop_link_text": "Checkout on %{remoteShopName}", "shopify.checkout.thank_you.order_status_confirmed_order_with_email": "You’ll receive an email when your order is ready.", "shopify.checkout.thank_you.order_status_confirmed_order_with_phone": "You may receive a text when your order is ready.", "shopify.checkout.thank_you.order_status_confirmed_order_with_email_and_phone": "You’ll receive an email when your order is ready.", "shopify.checkout.thank_you.order_status_confirmed_order_with_email_payment": "You made a payment on your order %{date}.", "shopify.checkout.thank_you.order_status_confirmed_order_with_phone_payment": "You made a payment on your order %{date}.", "shopify.checkout.thank_you.order_status_confirmed_order_with_email_and_phone_payment": "You made a payment on your order %{date}.", "shopify.checkout.thank_you.order_status_confirmed_order_with_email_pick_up": "You’ll receive an email when your order is ready for pickup.", "shopify.checkout.thank_you.order_status_confirmed_order_with_phone_pick_up": "You’ll receive a text when your order is ready for pickup.", "shopify.checkout.thank_you.order_status_confirmed_order_with_email_and_phone_pick_up": "You’ll receive an email and text when your order is ready for pickup.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_missing": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_missing_payment": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_missing_pick_up": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_email_and_phone": "You’ll get a confirmation email soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_email_and_phone_payment": "You’ll get a confirmation email soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_email_and_phone_pick_up": "You’ll get a confirmation email soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_email": "You’ll get a confirmation email soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_email_payment": "You’ll get a confirmation email soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_email_pick_up": "You’ll get a confirmation email soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_phone": "You’ll get a confirmation text soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_phone_payment": "You’ll get a confirmation text soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_default_with_phone_pick_up": "You’ll get a confirmation text soon. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_missing": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_missing_payment": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_missing_pick_up": "Contact us by email or phone to receive an order confirmation.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_email_and_phone": "After completing your payment, you’ll get a confirmation email. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_email_and_phone_payment": "After completing your payment, you’ll get a confirmation email. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_email_and_phone_pick_up": "After completing your payment, you’ll get a confirmation email. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_email": "After completing your payment, you’ll get a confirmation email. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_email_payment": "After completing your payment, you’ll get a confirmation email. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_email_pick_up": "After completing your payment, you’ll get a confirmation email. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_phone": "After completing your payment, you’ll get a confirmation text. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_phone_payment": "After completing your payment, you’ll get a confirmation text. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_pending_payment_description_buyer_action_required_with_phone_pick_up": "After completing your payment, you’ll get a confirmation text. Track your order using the Shop app.", "shopify.checkout.thank_you.order_status_payments_app_failed_pending_payment": "You weren’t charged for your order. Return to your cart to review your order and pay again.", "shopify.checkout.thank_you.order_status_payments_app_failed_pending_payment_payment": "You weren’t charged for your order. Return to your cart to review your order and pay again.", "shopify.checkout.thank_you.order_status_payments_app_failed_pending_payment_pick_up": "You weren’t charged for your order. Return to your cart to review your order and pay again.", "shopify.checkout.thank_you.order_status_confirmed_order_local": "We’re getting your order ready for delivery.", "shopify.checkout.thank_you.order_status_confirmed_order_local_pick_up": "We’re getting your order ready.", "shopify.checkout.thank_you.order_status_confirmed_order_local_payment": "We're getting your order ready.", "shopify.checkout.thank_you.order_status_updates_with_email": "You’ll get shipping and delivery updates by email.", "shopify.checkout.thank_you.order_status_updates_with_phone": "You may get shipping and delivery updates by text.", "shopify.checkout.thank_you.order_status_updates_with_email_and_phone": "You’ll get shipping and delivery updates by email.", "shopify.checkout.thank_you.order_status_updates_with_missing": "Choose how you want to get shipping updates.", "shopify.checkout.thank_you.order_status_confirmed_with_installments_payment_return": "Please return your attention back to the sales associate.", "shopify.checkout.thank_you.order_status_confirmed_with_installments_payment_confirm": "You’ll receive a confirmation email with your order number and details of your installment schedule shortly.", "shopify.checkout.thank_you.confirmed_title": "Your order is confirmed", "shopify.checkout.thank_you.confirmed_description": "We’ve accepted your order, and we’re getting it ready. Come back to this page for updates on your shipment status.", "shopify.checkout.thank_you.confirmed_orders_title": "Your orders are confirmed", "shopify.checkout.thank_you.ready_for_pickup_title": "Your order is ready for pickup", "shopify.checkout.thank_you.ready_for_pickup_description": "Your order is ready for pickup. Please bring your order confirmation when you pick up your order.", "shopify.checkout.thank_you.ready_for_delivery_title": "Your order is ready for delivery", "shopify.checkout.thank_you.ready_for_delivery_description": "We’ve prepared your order.", "shopify.checkout.thank_you.order_updates_title": "Order updates", "shopify.checkout.thank_you.shop_phone_description": "You'll receive a text with a link to the Shop app to help track your order.", "shopify.checkout.thank_you.order_updates_description_html": "Updates will be sent to %{customer_contact}", "shopify.checkout.thank_you.order_updates_current_step_prefix": "Current step: ", "shopify.checkout.thank_you.order_updates_past_step_prefix": "Past step: ", "shopify.checkout.thank_you.order_updates_upcoming_step_prefix": "Upcoming step: ", "shopify.checkout.thank_you.in_transit": "On its way", "shopify.checkout.thank_you.in_transit_title": "Your order is on its way", "shopify.checkout.thank_you.in_transit_description": "You'll receive updates on its progress.", "shopify.checkout.thank_you.in_transit_description_days": "You should receive your order in the next %{number} days.", "shopify.checkout.thank_you.in_transit_description_today": "You should receive your order today.", "shopify.checkout.thank_you.in_transit_description_tomorrow": "You should receive your order in the next day.", "shopify.checkout.thank_you.no_tracking_number": "There is no tracking number for this order.", "shopify.checkout.thank_you.out_for_delivery": "Out for delivery", "shopify.checkout.thank_you.out_for_delivery_title": "Your order is out for delivery", "shopify.checkout.thank_you.out_for_delivery_description_html": "Your order is on its way and should arrive today. If it’s not delivered in the next 2 days, %{contact_us}.", "shopify.checkout.thank_you.out_for_delivery_local_title": "Your order is out for delivery", "shopify.checkout.thank_you.out_for_delivery_local_description_html": "Your order will arrive soon.", "shopify.checkout.thank_you.attempted_delivery": "Attempted delivery", "shopify.checkout.thank_you.attempted_delivery_title": "An attempt was made to deliver your order", "shopify.checkout.thank_you.attempted_delivery_description_html": "An attempt was made to deliver your order but it was unsuccessful. %{contact_us} to arrange another delivery attempt.", "shopify.checkout.thank_you.attempted_delivery_local_title": "Missed delivery", "shopify.checkout.thank_you.attempted_delivery_local_description_html": "We weren’t able to deliver your order. To schedule another delivery, %{contact_us}.", "shopify.checkout.thank_you.delivered": "Delivered", "shopify.checkout.thank_you.delivered_title": "Your order has been delivered", "shopify.checkout.thank_you.delivered_description_html": "Your order has been delivered to the address you provided. If you haven’t received it, or if you have any other problems, %{contact_us}.", "shopify.checkout.thank_you.delivered_local_title": "Your order has been delivered", "shopify.checkout.thank_you.delivered_local_description_html": "Haven’t received your order? %{let_us_know}.", "shopify.checkout.thank_you.choose_payment_method_later": "Choose payment method later", "shopify.checkout.thank_you.picked_up_title": "Your order has been picked up", "shopify.checkout.thank_you.picked_up_description_html": "Your order was picked up at %{location_name} on %{date}.", "shopify.checkout.thank_you.contact_us": "Contact us", "shopify.checkout.thank_you.contact_us_lowercase": "contact us", "shopify.checkout.thank_you.contact_us_html": "Need help? %{contact_us}", "shopify.checkout.thank_you.updated_time": "Updated %{time_ago} ago", "shopify.checkout.thank_you.failure": "Not delivered", "shopify.checkout.thank_you.failed_title": "Your order couldn’t be delivered", "shopify.checkout.thank_you.failed_contact_merchant": "Contact %{company} to arrange delivery.", "shopify.checkout.thank_you.failed_contact_merchant_no_company": "Contact us to arrange delivery.", "shopify.checkout.thank_you.non_shippable_title": "Your order is complete", "shopify.checkout.thank_you.let_us_know": "Let us know", "shopify.checkout.thank_you.login_title": "Sign in to view all order details", "shopify.checkout.thank_you.login_description": "You can find your order number in the receipt you received via email.", "shopify.checkout.thank_you.login_description_phone": "You can find your order number in the receipt you received via text message.", "shopify.checkout.thank_you.login_not_customer_html": "Not %{name}? You can %{link}", "shopify.checkout.thank_you.login_reorder_link_text.one": "buy this product too", "shopify.checkout.thank_you.login_reorder_link_text.other": "buy these products too", "shopify.checkout.thank_you.customer_information_hidden_fields_message": "Sign in to view all customer information.", "shopify.checkout.thank_you.cancelled_title": "Your order has been canceled", "shopify.checkout.thank_you.cancelled_description": "Your order was canceled on %{date}. Contact us if you have any questions.", "shopify.checkout.thank_you.order_number_label": "Order number", "shopify.checkout.thank_you.customer_validation_error": "Enter a valid email or order number.", "shopify.checkout.thank_you.customer_validation_error_phone": "Enter a valid phone or order number.", "shopify.checkout.thank_you.login": "Sign in", "shopify.checkout.thank_you.unfulfilled_items_title": "Preparing for shipping", "shopify.checkout.thank_you.unfulfilled_items_description": "We are currently preparing these items for shipping.", "shopify.checkout.thank_you.marker.current": "Current shipment location", "shopify.checkout.thank_you.marker.shipping": "Shipping address", "shopify.checkout.thank_you.pick_up_in_store_marker.current": "Current shipment location", "shopify.checkout.thank_you.pick_up_in_store_marker.shipping": "Pickup location", "shopify.checkout.thank_you.fulfillment.fulfilled_table_title": "Items in this shipment", "shopify.checkout.thank_you.fulfillment.unfulfilled_table_title": "Items preparing for shipping", "shopify.checkout.thank_you.fulfillment.product_image_label": "Product image", "shopify.checkout.thank_you.fulfillment.description_label": "Description", "shopify.checkout.thank_you.fulfillment.quantity_label": "Quantity", "shopify.checkout.thank_you.order_updates_description_missing": "You are not receiving shipping updates.", "shopify.checkout.thank_you.order_updates_subscribe_to_email_notifications": "Get shipping updates by email", "shopify.checkout.thank_you.order_updates_subscribe_to_email_or_phone_notifications": "Get shipping updates by email or text", "shopify.checkout.thank_you.order_updates_subscribe_to_phone_notifications": "Get shipping updates by text", "shopify.checkout.thank_you.order_updates_submit_subscription": "Submit", "shopify.checkout.thank_you.track_shipment": "Track shipment", "shopify.checkout.thank_you.other_options": "Other options", "shopify.checkout.thank_you.delivery_information_label": "Delivery information:", "shopify.checkout.thank_you.gift_card_sent_confirmation": "Gift cards sent", "shopify.checkout.thank_you.qr_code.send_link_to_phone": "Or send a link to your phone", "shopify.checkout.thank_you.qr_code.title": "Track on your phone", "shopify.checkout.thank_you.shop_qr_with_sms_upsell.qr_code.title": "Scan with your phone", "shopify.checkout.thank_you.shop_qr_with_sms_upsell.sms.title": "Or send a link to your phone", "shopify.checkout.thank_you.view_business_customer_account": "View account", "shopify.checkout.thank_you.breadcrumbs_submitted": "Submitted", "shopify.checkout.thank_you.breadcrumbs_in_review": "In review", "shopify.checkout.thank_you.breadcrumbs_confirmed": "Confirmed", "shopify.checkout.thank_you.missing_shipping_address_banner.title": "Order doesn’t have a shipping address", "shopify.checkout.thank_you.missing_shipping_address_banner.text_html": "%{contact_us} to add your address", "shopify.checkout.thank_you.missing_shipping_address_banner.contact_us": "Contact us", "shopify.checkout.thank_you.first_payment_complete": "First payment complete", "shopify.checkout.thank_you.in_store_purchase": "In-store purchase", "shopify.checkout.thank_you.company_location_section_title": "Company and location", "shopify.checkout.thank_you.thank_you_ready_for_picked_up": "Your order was picked up at %{address}.", "shopify.checkout.thank_you.ready_for_delivery": "Ready for delivery", "shopify.checkout.thank_you.order_updates_subscribe_to_email_notifications_failed": "We can not store this email for updates. Please try again or use another email.", "shopify.checkout.thank_you.order_updates_subscribe_to_email_notifications_email_already_exists": "The email you entered already exists.", "shopify.checkout.thank_you.subscriptions_management.title": "Subscription", "shopify.checkout.thank_you.subscriptions_management.description": "Continue to your account to view and manage your subscriptions.", "shopify.checkout.thank_you.subscriptions_management.button_label": "Manage your subscription", "shopify.checkout.thank_you.manage_order_label": "Manage your order", "shopify.checkout.thank_you.gift_card_confirmation_by_text_typ.one": "You’ll receive a text with your gift card in a few minutes.", "shopify.checkout.thank_you.gift_card_confirmation_by_text_typ.other": "You’ll receive a text with your gift cards in a few minutes.", "shopify.checkout.thank_you.gift_card_confirmation_by_email_typ.one": "You’ll receive an email with your gift card in a few minutes.", "shopify.checkout.thank_you.gift_card_confirmation_by_email_typ.other": "You’ll receive an email with your gift cards in a few minutes.", "shopify.checkout.thank_you.gift_card_email_title.one": "Gift card was sent by email", "shopify.checkout.thank_you.gift_card_email_title.other": "Gift cards were sent by email", "shopify.checkout.thank_you.gift_card_text_title.one": "Gift card was sent by text message", "shopify.checkout.thank_you.gift_card_text_title.other": "Gift cards were sent by text message", "shopify.checkout.thank_you.resend_gift_cards_by_email": "Resend email", "shopify.checkout.thank_you.resend_gift_cards_by_text": "Resend text message", "shopify.checkout.thank_you.gift_card_resent_confirmation.one": "Gift card was resent.", "shopify.checkout.thank_you.gift_card_resent_confirmation.other": "Gift cards were resent.", "shopify.checkout.thank_you.gift_card_resend_failure.one": "There was a problem resending your gift card. Try again or contact us.", "shopify.checkout.thank_you.gift_card_resend_failure.other": "There was a problem resending your gift cards. Try again or contact us.", "shopify.checkout.thank_you.vat_invoices.title": "VAT invoices", "shopify.checkout.thank_you.verify_shop_account_title": "Verify your Shop account", "shopify.checkout.thank_you.verify_shop_account_message": "Your information was saved with Shop, verify your account to automatically use it on millions of stores.", "shopify.checkout.thank_you.verify_shop_account_cta": "Verify your account", "shopify.checkout.thank_you.verify_shop_account_confirmation": "Checkout details saved", "shopify.checkout.thank_you.announcement.close": "Close", "shopify.checkout.thank_you.announcement.expand": "Expand", "shopify.checkout.thank_you.announcement.collapse": "Collapse", "shopify.checkout.vaulted.change_button": "Change", "shopify.checkout.review.title": "Review", "shopify.checkout.review.review_title": "Complete your order", "shopify.checkout.review.review_and_confirm_title": "Review and confirm", "shopify.checkout.review.checkout_as_guest": "Check out as guest", "shopify.checkout.review.account_more_actions": "More actions for this account", "shopify.checkout.review.checkout_a_different_way": "Check out a different way", "shopify.checkout.review.review_notice_html": " ", "shopify.checkout.review.blocks.contact_method_title": "Contact", "shopify.checkout.review.blocks.billing_address_title": "Billing", "shopify.checkout.review.blocks.shipping_address_title": "Ship to", "shopify.checkout.review.blocks.shipping_method_title": "Shipping method", "shopify.checkout.review.blocks.payment_method_title": "Payment method", "shopify.checkout.review.blocks.change_link_label": "Change", "shopify.checkout.review.blocks.change_contact_method_link_label": "Change contact information", "shopify.checkout.review.blocks.change_billing_address_link_label": "Change billing address", "shopify.checkout.review.blocks.change_shipping_address_link_label": "Change shipping address", "shopify.checkout.review.blocks.change_shipping_method_link_label": "Change shipping method", "shopify.checkout.review.blocks.change_payment_method_link_label": "Change payment method", "shopify.checkout.review.blocks.pick_up_in_store_shipping_method_html": "Pickup in store · %{location_name}", "shopify.checkout.review.blocks.purchase_order_number_title": "PO number", "shopify.checkout.review.blocks.change_purchase_order_number_label": "Change PO number", "shopify.checkout.review.blocks.change_delivery_method_link_label": "Change delivery method", "shopify.checkout.review.blocks.no_address": "(No address)", "shopify.checkout.review.blocks.no_purchase_order_number": "(No PO number)", "shopify.checkout.review.blocks.due_after_order_reviewed_html": "Due after order is reviewed", "shopify.checkout.review.blocks.review_title_free_order": "Review and complete your order", "shopify.checkout.review.blocks.payment_method_saved_company_location": "Save card for this location", "shopify.checkout.review.blocks.company_location": "Company location", "shopify.checkout.review.block_title": "Review your information", "shopify.checkout.review.item_header.change_value": "Change value", "shopify.checkout.review.item_header.section": "Section", "shopify.checkout.review.item_header.value": "Value", "shopify.checkout.marketing.accept_marketing_checkbox_label": "Email me with news and offers", "shopify.checkout.marketing.post_accept_marketing_checkbox_label": "You are subscribed to receive email news and offers", "shopify.checkout.marketing.pending_accept_marketing_checkbox_label": "Check your email to confirm your subscription to news and offers", "shopify.checkout.marketing.accept_sms_checkbox_label": "Text me with news and offers", "shopify.checkout.marketing.disclaimer.description_html": " ", "shopify.checkout.marketing.disclaimer.privacy_policy": "Privacy Policy", "shopify.checkout.marketing.disclaimer.terms_of_service": "Terms of Service", "shopify.checkout.marketing.sms.disclaimer.privacy_policy": "Privacy Policy", "shopify.checkout.marketing.sms.disclaimer.terms_of_service": "Terms of Service", "shopify.checkout.marketing.sms.disclaimer.description_html": "By signing up via text, you agree to receive recurring automated marketing messages, including cart reminders, at the phone number provided. Consent is not a condition of purchase. Reply STOP to unsubscribe. Reply HELP for help. Message frequency varies. Msg & data rates may apply. View our %{privacy_policy_link} and %{terms_of_service_link}.", "shopify.checkout.marketing.sms.disclaimer.description_transactional_html": "You may receive text messages related to order confirmation and shipping updates. Reply STOP to unsubscribe. Reply HELP for help. Message frequency varies. Msg & data rates may apply. View our %{privacy_policy_modal_link} and %{terms_of_service_modal_link}.", "shopify.checkout.marketing.sms.mobile_phone_number": "Mobile phone number", "shopify.checkout.marketing.sms.get_offers_by_text": "Get offers by text", "shopify.checkout.marketing.unsubscribe.title": "You’ve unsubscribed", "shopify.checkout.marketing.unsubscribe.description": "You won’t receive any more cart reminders from us.", "shopify.checkout.marketing.accept_shop_pay_marketing_checkbox_label": "Sign me up for news and offers from this store", "shopify.checkout.shop_policies.policy_label": "Policy", "shopify.checkout.shop_policies.policies_label": "Policies", "shopify.checkout.shop_policies.select_policy_label": "Select a policy", "shopify.checkout.shop_policies.refund_policy": "Refund policy", "shopify.checkout.shop_policies.privacy_policy": "Privacy policy", "shopify.checkout.shop_policies.terms_of_service": "Terms of service", "shopify.checkout.shop_policies.legal_notice": "Legal notice", "shopify.checkout.shop_policies.shipping_policy": "Shipping", "shopify.checkout.shop_policies.terms_of_sale": "Terms of sale", "shopify.checkout.shop_policies.subscription_policy": "Cancellations", "shopify.checkout.shop_policies.purchase_options_policy": "Purchase options policy", "shopify.checkout.shop_policies.contact_information": "Contact", "shopify.checkout.shop_policies.purchase_options_cancellation_policy": "Cancellations", "shopify.checkout.shop_policies.cookie_preferences": "Cookies", "shopify.checkout.mor_policies.terms_and_conditions": "Merchant of Record Terms and Conditions", "shopify.checkout.mor_policies.privacy_policy": "Merchant of Record Privacy Policy", "shopify.checkout.markets.invalid_market_manager_error": "Review your contact information before completing payment", "shopify.checkout.notifications.title": "Notifications", "shopify.checkout.order_payment_collection.custom_line_item_description": "Amount to pay for order %{order_name}", "shopify.checkout.order_payment_collection.changes_summary.added.one": "Added", "shopify.checkout.order_payment_collection.changes_summary.added.other": "Added %{count}", "shopify.checkout.order_payment_collection.changes_summary.removed": "Removed", "shopify.checkout.order_payment_collection.changes_summary.subtracted": "Removed %{count}", "shopify.checkout.order_payment_collection.changes_summary.returned": "Returned %{count}", "shopify.checkout.order_payment_collection.changes_summary.return_line_item_removed": "Removed %{count} from return", "shopify.checkout.order_payment_collection.additional_payment_requested": "Payment of %{total_outstanding} is needed", "shopify.checkout.order_payment_collection.checkout_review_and_pay": "Your order %{order_name} was updated. Review these changes, then pay to confirm your order.", "shopify.checkout.order_payment_collection.order_updated_on": "Your order was updated on %{last_edited_at}.", "shopify.checkout.order_payment_collection.order_review_and_pay": "Your order was updated. Review and pay to confirm your order.", "shopify.checkout.order_payment_collection.pay_now": "Pay now", "shopify.checkout.change_currency.currency_button_label": "%{currency} (%{currency_symbol})", "shopify.checkout.change_currency.credit_card_will_be_charged_html": "Your credit card will be charged %{amount}", "shopify.checkout.change_currency.payment_total_label": "Payment total", "shopify.checkout.change_currency.conversion_rate": "1 %{from_currency_code} = %{rate} %{to_currency_code}", "shopify.checkout.change_currency.change_currency_link": "Change to %{currency_name}", "shopify.checkout.change_currency.card_provider_fx_charges_warning": "Your card provider may charge fees on %{currency_code} transactions.", "shopify.checkout.change_currency.currency_not_supported_by_shop": "The currency you selected is no longer available. All prices are now in %{currency_code}.", "shopify.checkout.change_country.cart_updated_based_on_country": "Your cart has been updated based on your shipping country.", "shopify.checkout.change_country.cart_and_currency_updated_based_on_country": "Your cart and currency has been updated based on your shipping country.", "shopify.checkout.change_cart_localization.verify_shipping_address": "Your country/region has been updated. Enter your shipping address to continue checkout.", "shopify.checkout.tips.title": "Add tip", "shopify.checkout.tips.description": "Show your support for the team at %{shop_name}", "shopify.checkout.tips.presets_description": "Select a tip percentage", "shopify.checkout.tips.custom_label": "Custom tip", "shopify.checkout.tips.add_tip": "Add tip", "shopify.checkout.tips.update_tip": "Update tip", "shopify.checkout.tips.message": "Thank you, we appreciate it.", "shopify.checkout.tips.preset_label": "%{tip_percent}%", "shopify.checkout.tips.no_tip_label": "None", "shopify.checkout.open_graph_meta_tag.title_with_products.zero": "Purchase %{product_name}", "shopify.checkout.open_graph_meta_tag.title_with_products.one": "Purchase %{product_name} and %{count} other item", "shopify.checkout.open_graph_meta_tag.title_with_products.other": "Purchase %{product_name} and %{count} other items", "shopify.checkout.subscriptions.recurring_total_intervals.day.one": "day", "shopify.checkout.subscriptions.recurring_total_intervals.day.other": "%{count} days", "shopify.checkout.subscriptions.recurring_total_intervals.week.one": "week", "shopify.checkout.subscriptions.recurring_total_intervals.week.other": "%{count} weeks", "shopify.checkout.subscriptions.recurring_total_intervals.month.one": "month", "shopify.checkout.subscriptions.recurring_total_intervals.month.other": "%{count} months", "shopify.checkout.subscriptions.recurring_total_intervals.year.one": "year", "shopify.checkout.subscriptions.recurring_total_intervals.year.other": "%{count} years", "shopify.checkout.subscriptions.recurring_totals": "%{total_price} every %{interval}", "shopify.checkout.subscriptions.recurring_totals_with_initial_order_shipping_discount": "First payment %{total_price_with_discount}, then %{recurring_total}", "shopify.checkout.subscriptions.recurring_totals_with_policies.first_cycles.one": "First payment %{total_price}", "shopify.checkout.subscriptions.recurring_totals_with_policies.first_cycles.other": "First %{count} payments %{total_price} each", "shopify.checkout.subscriptions.recurring_totals_with_policies.following_cycles": ", then %{total_price} every %{interval}", "shopify.checkout.subscriptions.recurring_totals_with_initial_order_shipping_discount_with_policies.first_cycles.one": ", then %{count} payment at %{total_price}", "shopify.checkout.subscriptions.recurring_totals_with_initial_order_shipping_discount_with_policies.first_cycles.other": ", then %{count} payments at %{total_price} each", "shopify.checkout.customer_payment_methods.page_title": "Payment methods", "shopify.checkout.customer_payment_methods.title": "Payment methods", "shopify.checkout.customer_payment_methods.update_card_button_label": "Update card", "shopify.checkout.customer_payment_methods.delete_customer_mandate.generic_error": "There was an issue processing your request.", "shopify.checkout.customer_payment_methods.delete_customer_mandate.success_message": "Your payment method has been successfully deleted", "shopify.checkout.throttle.too_many_attempts.message": "Too many attempts", "shopify.checkout.throttle.too_many_attempts.description": "Please try again in a few minutes", "shopify.checkout.throttle.line_to_check_out": "You’re in line to check out", "shopify.checkout.throttle.cart_summary_heading": "Your cart", "shopify.checkout.throttle.merchandise.available": "Available", "shopify.checkout.throttle.merchandise.sold_out": "Sold Out", "shopify.checkout.throttle.inventory.partial_inventory_banner": "Part of your cart is sold out and will not be available to purchase at checkout.", "shopify.checkout.throttle.inventory.sold_out.heading": "Your cart is sold out", "shopify.checkout.throttle.inventory.sold_out.storefront_body": "Go back to the store to continue shopping", "shopify.checkout.throttle.inventory.sold_out.no_storefront_body": "Unfortunately the item you want to purchase is no longer available.", "shopify.checkout.throttle.inventory.sold_out.link": "Back to store", "shopify.checkout.throttle.title": "Queue", "shopify.checkout.throttle.time_to_checkout_title": "Time to check out - {{shopName}}", "shopify.checkout.throttle.estimated_wait_time.calculating": "Calculating estimated wait time", "shopify.checkout.throttle.estimated_wait_time.less_than_one": "Estimated wait time: Less than a minute", "shopify.checkout.throttle.estimated_wait_time.one": "Estimated wait time: {{count}} minute", "shopify.checkout.throttle.estimated_wait_time.other": "Estimated wait time: {{count}} minutes", "shopify.checkout.throttle.last_checked": "(Last checked: {{time}})", "shopify.checkout.throttle.footer.heading": "Don’t refresh this page, it will update automatically.", "shopify.checkout.throttle.footer.body": "Items in your cart are not reserved until you complete checkout.", "shopify.checkout.cart_checkout_validation.runtime_error": "There was an issue processing your request, please contact the store owner.", "shopify.checkout.no_address_location.title": "Location with no address", "shopify.checkout.no_address_location.header": "You don’t have a shipping address for this location", "shopify.checkout.no_address_location.alt_header": "Missing shipping address", "shopify.checkout.no_address_location.message": "Contact us to add an address for this location to complete checkout.", "shopify.checkout.no_address_location.email_us_button_label": "Send us an email", "shopify.checkout.order_errors.creation_failure": "There was an issue creating your order. Try again, or contact us for more details.", "shopify.checkout.order_errors.network_failure": "Your order couldn't be submitted because of a network error. Check your connection and try again.", "shopify.checkout.order_errors.inventory_reservation_failure": "Some items are no longer available.", "shopify.checkout.session_errors.session_not_unique": "There was a problem processing your order. Refresh this page to return to the store.", "shopify.checkout.session_errors.session_already_finished": "This order has already been submitted. You can return to the store to start a new order.", "shopify.checkout.artifact_errors.artifact_dissatisfaction_without_violation": "There was a problem processing your order. Try again in a few minutes.", "shopify.checkout.pickup_point.location_is_currently_closed": "Closed", "shopify.checkout.pickup_point.business_hours": "Opening hours", "shopify.checkout.pickup_point.no_business_hours": "No opening hours available", "shopify.checkout.pickup_point.open_twenty_four_hours": "Open 24/7", "shopify.checkout.pickup_point.title": "Pickup point", "shopify.checkout.pickup_point.or": "or", "shopify.checkout.pickup_point.search_button": "Search", "shopify.checkout.pickup_point.address_label": "Address", "shopify.checkout.pickup_point.selector_tabs": "Pickup point selector", "shopify.checkout.pickup_point.selector_tab_map": "Map", "shopify.checkout.pickup_point.selector_tab_list": "List", "shopify.checkout.pickup_point.could_not_resolve_address": "Your address couldn't be located. Try again or use a different address", "shopify.checkout.pickup_point.no_pickup_points_near_you": "No pickup points found near the selected address", "shopify.checkout.pickup_point.no_pickup_points_near_location": "No pickup points found near your location", "shopify.checkout.pickup_point.pickup_unavailable_banner_title.one": "No pickup points available for your item", "shopify.checkout.pickup_point.pickup_unavailable_banner_title.other": "No pickup points available for your items", "shopify.checkout.pickup_point.no_pickup_points_found": "No pickup points found", "shopify.checkout.pickup_point.paginator.next_accessibility_label": "Next page", "shopify.checkout.pickup_point.paginator.previous_accessibility_label": "Previous page", "shopify.checkout.pickup_point.paginator.header.one": "Showing %{first} - %{last} of %{count} point", "shopify.checkout.pickup_point.paginator.header.other": "Showing %{first} - %{last} of %{count} points", "shopify.checkout.pickup_point.paginator.selected_pickup_point_label.one": "%{pickup_point_index} of %{count} point", "shopify.checkout.pickup_point.paginator.selected_pickup_point_label.other": "%{pickup_point_index} of %{count} points", "shopify.checkout.pickup_point.paginator.selected_page.one": "%{first} - %{last} of %{count} point", "shopify.checkout.pickup_point.paginator.selected_page.other": "%{first} - %{last} of %{count} points", "shopify.checkout.pickup_point.number_of_pickup_points_near_location.one": "%{count} pickup point near your location", "shopify.checkout.pickup_point.number_of_pickup_points_near_location.other": "%{count} pickup points near your location", "shopify.checkout.pickup_point.change": "Change location", "shopify.checkout.pickup_point.waiting_on_pickup_point_location_notice": "Searching for pickup points...", "shopify.checkout.pickup_point.geolocation.shared_location_button": "Use my location", "shopify.checkout.pickup_point.geolocation.errors.unsuported_by_browser": "Getting geolocation is not supported by your browser. Try again or use a different browser.", "shopify.checkout.pickup_point.geolocation.errors.permission_not_allowed": "Allow sharing your location.", "shopify.checkout.pickup_point.geolocation.errors.permission_denied": "Couldn't get your location. Try again.", "shopify.checkout.error_page.terminal.title": "There was a problem with our checkout", "shopify.checkout.error_page.terminal.message": "Refresh this page or try again in a few minutes", "shopify.checkout.error_page.terminal.action.cta": "Refresh Page", "shopify.checkout.error_page.terminal.request_id": "Request ID: {{requestId}}", "shopify.checkout.notice_banner.return_to_cart_link_text": "Return to cart", "shopify.checkout.notice_banner.return_to_cart_to_update_quantities": "%{return_to_cart_link_text} to update the quantities", "shopify.checkout.notice_banner.subscription_changed": "One of your subscriptions has changed.", "shopify.checkout.notice_banner.company_location_changed": "Your cart has been updated. Review your order before submitting it.", "shopify.checkout.notice_banner.company_location_changed_product_removed": "Your cart has been updated because some products are not available at this location. Review your order before submitting it.", "shopify.checkout.notice_banner.violations_title": "{{violation}} is missing or invalid", "shopify.checkout.notice_banner.individual_violations_title.DeliveryViolations": "Shipping address", "shopify.checkout.notice_banner.individual_violations_title.PaymentViolations": "Billing address", "shopify.checkout.notice_banner.individual_violations_title.OtherViolations": "Order Information", "shopify.checkout.notice_banner.individual_violations_title.UnhandledViolations": "Order Information", "shopify.checkout.notice_banner.all_violations_content": "Call or {{emailLink}} us to update this information and complete your order:", "shopify.checkout.notice_banner.all_violations_email_link": "email", "shopify.checkout.notice_banner.editable_shipping_address_violations_message_title": "Add this information to complete your order:", "shopify.checkout.notice_banner.editable_shipping_address_violations_message_footer": "To permanently update the address for this location, {{emailLink}}.", "shopify.checkout.notice_banner.editable_shipping_address_violations_email_link": "contact us", "shopify.checkout.notice_banner.shipping_address_incomplete_title": "Shipping address is incomplete", "shopify.checkout.notice_banner.calculating_shipping": "Shipping is still being calculated. Try again in a few seconds. You haven’t been charged.", "shopify.checkout.notice_banner.calculating_taxes": "Taxes are still being calculated. Try again in a few seconds. You haven't been charged.", "shopify.checkout.notice_banner.generic_processing_error.title": "There was a problem processing your order", "shopify.checkout.notice_banner.generic_processing_error.body": "You haven't been charged. Try again or use different payment method.", "shopify.checkout.notice_banner.payment_method_not_available_with_payment_terms": "The payment method you selected is no longer available because payment for this order is now on {{paymentTerms}} terms", "shopify.checkout.notice_banner.payment_method_not_available_with_fulfillment_terms": "The payment method you selected is no longer available because payment for this order is now due on fulfillment", "shopify.checkout.notice_banner.payment_method_not_available_with_fixed_terms": "The payment method you selected is no longer available because payment for this order is due later", "shopify.checkout.notice_banner.contact_information.title": "Contact information is invalid", "shopify.checkout.notice_banner.contact_information.header": "To update your information, {{emailLink}}.", "shopify.checkout.notice_banner.contact_information.email_link": "contact us", "shopify.checkout.review_address_modal.title": "Review address", "shopify.checkout.review_address_modal.subtitle.one": "This order will be shipped to the address provided by {{merchant}}.", "shopify.checkout.review_address_modal.subtitle.other": "This order will be shipped to an address provided by {{merchant}}. Select a shipping address.", "shopify.checkout.review_address_modal.subtitle_no_match": "This address doesn't match any of your saved addresses.", "shopify.checkout.review_address_modal.subtitle_must_use": "Your order will be shipped to this address.", "shopify.checkout.review_address_modal.continue": "Continue with this address", "shopify.checkout.review_address_modal.use_saved_address": "Use a different address", "shopify.checkout.review_address_modal.return_to_cart": "Return to cart", "shopify.checkout.marketplaces.legal_notice.legal_text": "Review", "shopify.checkout.marketplaces.legal_notice.separator": " ", "shopify.checkout.marketplaces.legal_notice.link_label": "{{partnerDisplayName}} Terms and Conditions", "shopify.checkout.online_store_chat.title": "Questions? Chat with us", "shopify.checkout.local_pickup.location_address_search_label": "Address", "shopify.checkout.local_pickup.location_information.one": "There is %{count} location with stock close to you", "shopify.checkout.local_pickup.location_information.other": "There are %{count} locations with stock close to you", "shopify.checkout.local_pickup.toggle_location": "Change location", "shopify.checkout.local_pickup.or": "OR", "shopify.checkout.local_pickup.cancel": "Cancel", "shopify.checkout.local_pickup.your_location": "your location", "shopify.checkout.local_pickup.search_button": "Find stores", "shopify.checkout.local_pickup.no_stores_found": "No stores found", "shopify.checkout.local_pickup.all_stores_far_away.title": "The closest locations are more than %{distance} away", "shopify.checkout.local_pickup.all_stores_far_away.description": "Select a location or %{ship_to_address_link}", "shopify.checkout.local_pickup.all_stores_far_away.ship_to_address_link": "ship to address", "shopify.checkout.local_pickup.selected_pickup_option_no_longer_available.title": "Your previously selected location is no longer available", "shopify.checkout.local_pickup.selected_pickup_option_no_longer_available.description": "Select a new location or %{ship_to_address_link}", "shopify.checkout.local_pickup.selected_pickup_option_no_longer_available.ship_to_address_link": "ship to address", "shopify.checkout.local_pickup.could_not_resolve_address": "Your address couldn't be located. Try again or use a different address", "shopify.checkout.local_pickup.paginator.accessibility_label": "Local pickup pagination", "shopify.checkout.local_pickup.paginator.previous": "Previous page", "shopify.checkout.local_pickup.paginator.next": "Next page", "shopify.checkout.local_pickup.paginator.label.one": "%{first} - %{last} of %{count} store", "shopify.checkout.local_pickup.paginator.label.other": "%{first} - %{last} of %{count} stores", "shopify.checkout.local_pickup.reveal_more_label.one": "Show %{count} more location", "shopify.checkout.local_pickup.reveal_more_label.other": "Show %{count} more locations", "shopify.checkout.captcha.title": "Complete the CAPTCHA to continue", "shopify.checkout.captcha.errors.not_solved": "Complete the CAPTCHA to continue", "shopify.checkout.clipboard.copied": "<PERSON>pied", "shopify.checkout.clipboard.failed_to_copy": "Failed to copy", "shopify.checkout.discount_errors.generic_error": "Your order couldn't be processed because your discount code is no longer available. Review your order total and try again.", "shopify.checkout.b2b_errors.lost_b2b_purchasing_permissions": "You no longer have permission to place B2B orders. %{logout_url} to place a personal order instead.", "shopify.checkout.b2b_errors.log_out_link_text": "Sign out", "shopify.checkout.b2b_errors.lost_permissions_subtitle": "You no longer have permission to place B2B orders. Sign out to place a personal order.", "shopify.checkout.b2b_errors.lost_permissions_title": "No location permissions", "shopify.checkout.shop_pay_external.title": "Shop Pay Checkout", "shopify.checkout.shop_pay_external.errors.problem_loading": "There's a problem loading Shop Pay", "shopify.checkout.shop_pay_external.errors.try_again_later": "Check your connection or close this window and try again in a few moments.", "shopify.checkout.shop_pay_external.errors.checkout_has_expired": "Your checkout has expired. Close this window and try checking out again from {{shopName}}.", "shopify.checkout.shop_pay_external.return_to_store": "Return to {{shopName}}", "shopify.checkout.shop_pay_external.return_to_store_simple": "Return to store", "shopify.checkout.wallets.return_to_store": "Return to store", "shopify.checkout.wallets.return_to_checkout": "Return to checkout", "shopify.checkout.wallets.redirect_hint": "You will be asked to sign in with {{walletName}}.", "shopify.checkout.wallets.logged_in": "Connected to {{email}}", "shopify.checkout.wallets.shipping_line_label": "Shipping", "shopify.checkout.wallets.connect_shipping_methods": "{{method1}} and {{method2}}", "shopify.checkout.wallets.payment_in_progress_title": "Payment in progress", "shopify.checkout.wallets.payment_in_progress_body": "Your payment is being processed. Your order will be completed soon.", "shopify.checkout.wallets.call_to_action": "Pay with {{walletName}}", "shopify.checkout.wallets.redirection_notice": "After clicking \"{{button<PERSON><PERSON><PERSON>}}\", you will be redirected to {{walletName}} to complete your purchase securely.", "shopify.checkout.wallets.errors.unavailable.title": "{{walletName}} not available", "shopify.checkout.wallets.errors.unavailable.description": "{{walletName}} wasn’t available. Use a different payment method or try again later.", "shopify.checkout.wallets.errors.unavailable.instructions": "There was an issue with {{walletName}}. Return to store to try again or check out a different way.", "shopify.checkout.wallets.errors.unavailable.unavailable_for_country": "{{walletName}} is not available in {{countryName}}. Select a different payment method to complete your purchase", "shopify.checkout.wallets.errors.unavailable_terminal.instructions": "{{walletName}} is currently unavailable for your order. To continue your purchase, choose another checkout method.", "shopify.checkout.wallets.errors.generic": "There was an issue with {{walletName}}. Try again or use a different payment method.", "shopify.checkout.wallets.errors.discount_code": "Unable to apply this discount code. Please review your code and try again.", "shopify.checkout.wallets.errors.terminal": "There was an issue with {{walletName}}. Please use a different payment method.", "shopify.checkout.wallets.errors.detour.stock_problems": "Some items are no longer available. Close {{walletName}} and try again.", "shopify.checkout.wallets.errors.detour.price_change": "Some item prices have changed. Close {{walletName}} to review your order and try again.", "shopify.checkout.wallets.errors.detour.queue": "You have been placed in a queue to checkout. Close {{walletName}} to continue.", "shopify.checkout.wallets.errors.detour.currency_change": "You can't use a different address with {{walletName}}. Use the original address, or try a different payment method.", "shopify.checkout.wallets.errors.unsupported.address": "The merchant can’t deliver to this address", "shopify.checkout.wallets.errors.unsupported.wallet_is_unavailable": "{{walletName}} cannot be used with this address. Select a different address or close {{walletName}} to complete your purchase", "shopify.checkout.wallets.errors.unsupported.postal_code": "This location isn't supported", "shopify.checkout.wallets.errors.outdated.shipping_method": "The shipping options have changed for your order. Review your selection.", "shopify.checkout.wallets.errors.outdated.payment_terms": "The payment terms have changed. Please review your order", "shopify.checkout.wallets.errors.invalid.shipping_address": "Enter a valid shipping address", "shopify.checkout.wallets.errors.invalid.billing_address": "Enter a valid billing address", "shopify.checkout.wallets.errors.invalid.shipping_option": "Choose a valid shipping option", "shopify.checkout.wallets.errors.invalid.email": "Enter a valid email", "shopify.checkout.wallets.errors.invalid.zone": "Enter a valid state / province", "shopify.checkout.wallets.errors.invalid.billing_zone": "Enter a valid billing address state / province", "shopify.checkout.wallets.errors.invalid.emirate": "Enter a valid emirate", "shopify.checkout.wallets.errors.invalid.postal_code": "Enter a valid ZIP / postal code", "shopify.checkout.wallets.errors.invalid.phone": "Enter a valid phone number", "shopify.checkout.wallets.errors.invalid.billing_phone": "Enter a valid billing address phone number", "shopify.checkout.wallets.errors.invalid.reduction_code": "Enter a valid discount code or gift card", "shopify.checkout.wallets.errors.invalid.billing_must_match_shipping": "Must match shipping address", "shopify.checkout.wallets.errors.invalid.billing_zip": "Enter a valid billing address ZIP / postal code", "shopify.checkout.wallets.errors.missing.first_name": "Enter a first name", "shopify.checkout.wallets.errors.missing.billing_first_name": "Enter a billing address first name", "shopify.checkout.wallets.errors.missing.last_name": "Enter a last name", "shopify.checkout.wallets.errors.missing.address1": "Enter an address", "shopify.checkout.wallets.errors.missing.billing_address1": "Enter a billing address", "shopify.checkout.wallets.errors.missing.address2": "Enter an apartment, suite, etc.", "shopify.checkout.wallets.errors.missing.billing_address2": "Enter a billing address apartment, suite, etc.", "shopify.checkout.wallets.errors.missing.city": "Enter a city", "shopify.checkout.wallets.errors.missing.billing_city": "Enter a billing address city", "shopify.checkout.wallets.errors.missing.province": "Select a state / province", "shopify.checkout.wallets.errors.missing.billing_province": "Select a billing address state / province", "shopify.checkout.wallets.errors.missing.country": "Select a country/region", "shopify.checkout.wallets.errors.missing.zip": "Enter a ZIP / postal code", "shopify.checkout.wallets.errors.missing.billing_zip": "Enter a billing address ZIP / postal code", "shopify.checkout.wallets.errors.missing.phone": "Enter a phone number", "shopify.checkout.wallets.errors.missing.contact_info": "Enter contact information", "shopify.checkout.wallets.errors.formatting.name_contains_emojis": "Delete emoji from your name", "shopify.checkout.wallets.errors.formatting.postal_code_contains_emoji": "Delete emoji from your postal code", "shopify.checkout.wallets.errors.formatting.first_name_too_long": "First name contains too many letters", "shopify.checkout.wallets.errors.formatting.last_name_too_long": "Last name contains too many letters", "shopify.checkout.wallets.errors.formatting.address_too_long": "Address line is too long", "shopify.checkout.wallets.errors.select_different_shipping_address_to_resolve_errors": "Your shipping address is invalid. Select a different address or use a new one.", "shopify.checkout.wallets.scrim.continue_purchase_in_window": "Continue your purchase in the {{walletName}} window", "shopify.checkout.wallets.scrim.and_return_to_checkout": "and return to Checkout", "shopify.checkout.wallets.scrim.cancel": "Cancel", "shopify.checkout.note.length_exceeds_maximum": "Reduce the size of the note to 5000 or fewer characters.", "shopify.checkout.one.errors.merchandise.gift_card_price_must_be_greater_than_zero": "Gift card price must be greater than 0.", "shopify.checkout.one.errors.merchandise.gift_card_price_must_not_exceed_limit": "Gift card price must not exceed %{gift_card_limit}.", "shopify.checkout.one.errors.merchandise.subtotal_limit_reached": "The maximum value of line items has been reached.", "shopify.checkout.one.errors.merchandise.signature_mismatch": "Merchandise signature mismatch for line %{stable_id}.", "shopify.checkout.units.milligram": "mg", "shopify.checkout.units.gram": "g", "shopify.checkout.units.kilogram": "kg", "shopify.checkout.units.milliliter": "ml", "shopify.checkout.units.centiliter": "cl", "shopify.checkout.units.liter": "L", "shopify.checkout.units.cubic_meter": "m³", "shopify.checkout.units.millimeter": "mm", "shopify.checkout.units.centimeter": "cm", "shopify.checkout.units.meter": "m", "shopify.checkout.units.square_meter": "m²", "shopify.checkout.units.ounce": "oz", "shopify.checkout.units.pound": "lb", "shopify.checkout.units.fluid_ounce": "fl oz", "shopify.checkout.units.pint": "pt", "shopify.checkout.units.quart": "qt", "shopify.checkout.units.gallon": "gal", "shopify.checkout.units.inch": "in", "shopify.checkout.units.foot": "ft", "shopify.checkout.units.yard": "yd", "shopify.checkout.units.square_foot": "ft²", "shopify.checkout.units.item": "item", "shopify.checkout.memberships.delivery_promise_unfulfillable": "The address is not supported for the current membership.", "shopify.checkout.memberships.items_split_across_locations_unsupported": "Prime delivery isn't available for your order. Your shipping options have been updated.", "shopify.checkout.editor.navigation.store_logo_accessibility_label": "Navigate to Online Store", "shopify.checkout.tooltip.accessibility_label_context": "More information about %{context}", "shopify.checkout.tooltip.additional_payment_methods": "Additional payment methods", "shopify.pagination.previous": "Previous", "shopify.pagination.next": "Next", "shopify.links.powered_by_shopify": "Powered by Shopify", "shopify.links.learn_more": "Learn more", "shopify.feed.more": "More", "shopify.attributes.email": "email", "shopify.attributes.password": "password", "shopify.attributes.password_confirmation": "The password confirmation", "shopify.attributes.first_name": "first name", "shopify.attributes.last_name": "last name", "shopify.attributes.body": "body", "shopify.attributes.signature": "Address", "shopify.addresses.zip_code": "ZIP code", "shopify.addresses.postal_code": "Postal code", "shopify.addresses.postcode": "Postcode", "shopify.addresses.pincode": "Pincode", "shopify.addresses.region": "Region", "shopify.addresses.prefecture": "Prefecture", "shopify.addresses.province": "Province", "shopify.addresses.state": "State", "shopify.addresses.state_and_territory": "State/territory", "shopify.addresses.county": "County", "shopify.addresses.emirate": "Emirate", "shopify.addresses.governorate": "Governorate", "shopify.addresses.confirm": "Are you sure you wish to delete this address?", "shopify.collections.sorting.manual": "Featured", "shopify.collections.sorting.best_selling": "Best selling", "shopify.collections.sorting.az": "Alphabetically, A-Z", "shopify.collections.sorting.za": "Alphabetically, Z-A", "shopify.collections.sorting.price_ascending": "Price, low to high", "shopify.collections.sorting.price_descending": "Price, high to low", "shopify.collections.sorting.date_ascending": "Date, old to new", "shopify.collections.sorting.date_descending": "Date, new to old", "shopify.errors.blank": "can't be blank", "shopify.errors.blocked_address": "This location isn't supported", "shopify.errors.credit_card_session_expired": "Credit card authorization has expired, please enter your payment information again. Your card has not been charged.", "shopify.errors.empty": "can't be empty", "shopify.errors.invalid_email": "must be a valid email address", "shopify.errors.discount_disabled": "This discount has been disabled", "shopify.errors.discount_expired": "This discount is not valid anymore", "shopify.errors.discount_limit_reached": "This discount has reached its usage limit", "shopify.errors.discount_not_found": "Unable to find a valid discount matching the code entered", "shopify.errors.customer_already_used_once_per_customer_discount_notice": "This discount has reached its usage limit", "shopify.errors.gift_card_already_applied": "Code has already been applied to your checkout", "shopify.errors.gift_card_code_invalid": "Code is invalid", "shopify.errors.gift_card_currency_mismatch": "Your gift card is in %{gift_card_currency}. Contact us to have it reissued in %{checkout_currency}.", "shopify.errors.gift_card_depleted": "There are no funds left on this gift card", "shopify.errors.gift_card_disabled": "Gift card is disabled", "shopify.errors.gift_card_expired": "Gift card is expired", "shopify.errors.invalid": "is invalid", "shopify.errors.bad_domain": "contains an invalid domain name", "shopify.errors.taken": "has already been taken", "shopify.errors.contains_html_tags": "cannot contain HTML tags", "shopify.errors.too_short": "is too short (minimum is %{count} characters)", "shopify.errors.too_long": "is too long (maximum is %{count} characters)", "shopify.errors.password_mismatch": "must match the provided password", "shopify.errors.contains_spaces": "starts or ends with spaces.", "shopify.errors.email_domain_invalid": "provider is not supported", "shopify.errors.invalid_for_country": "is not valid for %{country}", "shopify.errors.invalid_for_country_and_province": "is not valid for %{province} and %{country}", "shopify.errors.invalid_province_in_country": "is not a valid province in %{country}", "shopify.errors.invalid_state_in_country": "is not a valid state in %{country}", "shopify.errors.invalid_region_in_country": "is not a valid region in %{country}", "shopify.errors.less_than_or_equal_to": "must be less than or equal to %{count}", "shopify.errors.not_supported": "is not supported", "shopify.errors.full_name_required": "Enter a first name and last name", "shopify.errors.invalid_for_card_type": "is not valid", "shopify.errors.invalid_type": "sorry, we do not accept cards of this type", "shopify.errors.invalid_format": "format is not valid", "shopify.errors.expired": "has expired", "shopify.errors.invalid_start_date_or_issue_number_for_debit": "a valid start date or issue number is required", "shopify.errors.invalid_expiry_year": "is not a valid expiration year", "shopify.errors.reset_password_html": "This email address is already associated with an account. If this account is yours, you can <a href=\"/account/login#recover\">reset your password</a>", "shopify.errors.verify_email": "We have sent an email to %{customer_email}, please click the link included to verify your email address.", "shopify.errors.product_not_available": "product is not published for this customer.", "shopify.errors.shop_404.title": "Create an Ecommerce Website and Sell Online! Ecommerce Software by Shopify", "shopify.errors.shop_404.heros.store_unavailable": "Sorry, this store is currently unavailable.", "shopify.errors.shop_404.heros.store_not_exist": "This store does not exist.", "shopify.errors.shop_404.heros.no_storefront": "This store doesn't have a website yet.", "shopify.errors.shop_404.links.explore_other_stores": "Explore other stores", "shopify.errors.shop_404.links.start_free_trial": "Start a free trial", "shopify.errors.shop_404.owner_ctas.header": "Are you the store owner?", "shopify.errors.shop_404.owner_ctas.fix_domain": "Finish setting up your new web address, go to %{domain_settings_link}. Click “Connect domain” and enter: %{domain}.<br>If you're having trouble getting into your store, try the %{forgot_your_store_link} page.", "shopify.errors.shop_404.owner_ctas.fix_domain_link_text": "your domain settings", "shopify.errors.shop_404.owner_ctas.forgot_your_store_link_text": "forgot your store", "shopify.errors.shop_404.owner_ctas.add_storefront": "You can set up your website by adding the Online Store channel in your %{admin_link}. If you're having trouble getting into your store, try the %{forgot_your_store_link} page.", "shopify.errors.shop_404.owner_ctas.admin_link_text": "admin", "shopify.errors.shop_404.ctas.sell_in_person.header": "Sell in person", "shopify.errors.shop_404.ctas.sell_in_person.caption": "Get the features you need to run your retail store whether you're starting out or scaling up.", "shopify.errors.shop_404.ctas.sell_in_person.link_text": "Start free trial", "shopify.errors.shop_404.ctas.open_a_store.header": "Open a new Shopify store", "shopify.errors.shop_404.ctas.open_a_store.caption": "Start a 3-day free trial, then continue for $1/month for the next 3 months.", "shopify.errors.shop_404.ctas.open_a_store.link_text": "Sign up now", "shopify.errors.shop_404.ctas.editions.header": "Check out Shopify Editions", "shopify.errors.shop_404.ctas.editions.caption": "With 100+ product updates, you can be more productive and creative, and powerful in commerce than you ever imagined.", "shopify.errors.shop_404.ctas.editions.link_text": "Explore latest Editions", "shopify.errors.shop_404.ctas.store_owner.header": "Are you the store owner?", "shopify.errors.shop_404.ctas.store_owner.caption": "Are you having trouble getting into your store? Try the %{forgot_your_store_link} feature. If you would like to reactivate your store contact %{shopify_support_link}.", "shopify.errors.shop_404.ctas.store_owner.shopify_support_link_text": "Shopify support", "shopify.notices.customer.password_reset_error": "Password reset error", "shopify.notices.customer.subscribe_error": "Subscribe error", "shopify.notices.customer.unsubscribe_error": "Unsubscribe error", "shopify.notices.customer.no_account_found": "No account found with that email.", "shopify.notices.customer.invalid_credentials": "Incorrect email or password.", "shopify.notices.customer.denylisted_reset_password": "Your account couldn't be accessed because the current password is not secure. You will receive an email to update your password.", "shopify.notices.customer.signup_disabled": "Customer account creation has been disabled.", "shopify.notices.address.updated": "Successfully updated address.", "shopify.notices.address.error_updating": "Error updating address", "shopify.notices.address.created": "Successfully created address", "shopify.notices.address.error_creating": "Error creating address.", "shopify.notices.address.deleted": "Successfully deleted address", "shopify.notices.address.error_deleting": "Error deleting address.", "shopify.notices.line_item.item_status.add_variant": "Added %{delta}", "shopify.notices.line_item.item_status.increment_item": "Added %{delta}", "shopify.notices.line_item.item_status.decrement_item": "Removed %{delta} of %{total}", "shopify.notices.line_item.item_status.decrement_fulfilled_line_item": "Returned %{delta} of %{total}", "shopify.notices.line_item.item_status.decrement_return_line_item": "Removed %{delta} from return", "shopify.notices.order.not_available": "This order is not available", "shopify.notices.order.financial_status.authorized": "Authorized", "shopify.notices.order.financial_status.pending": "Pending", "shopify.notices.order.financial_status.paid": "Paid", "shopify.notices.order.financial_status.unpaid": "Unpaid", "shopify.notices.order.financial_status.voided": "Voided", "shopify.notices.order.financial_status.partially_paid": "Partially paid", "shopify.notices.order.financial_status.partially_refunded": "Partially refunded", "shopify.notices.order.financial_status.refunded": "Refunded", "shopify.notices.order.financial_status.expired": "Expired", "shopify.notices.order.fulfillment_status.fulfilled": "Fulfilled", "shopify.notices.order.fulfillment_status.complete": "Complete", "shopify.notices.order.fulfillment_status.partial": "Partial", "shopify.notices.order.fulfillment_status.unfulfilled": "Unfulfilled", "shopify.notices.order.fulfillment_status.restocked": "Restocked", "shopify.notices.order.transaction_status.success": "Success", "shopify.notices.order.transaction_status.pending": "Pending", "shopify.notices.order.transaction_status.failure": "Failure", "shopify.notices.order.transaction_status.error": "Error", "shopify.notices.order.cancel_reason.declined": "Payment declined", "shopify.notices.order.cancel_reason.inventory": "Items unavailable", "shopify.notices.order.cancel_reason.fraud": "Fraudulent order", "shopify.notices.order.cancel_reason.customer": "Customer changed/cancelled order", "shopify.notices.order.cancel_reason.staff": "Staff error", "shopify.notices.order.cancel_reason.other": "Other", "shopify.notices.order.cash_on_delivery": "Cash on Delivery (COD)", "shopify.notices.cart.only_n_items_available": "You can only add %{count} %{name} to the cart.", "shopify.notices.cart.too_many_items_in_cart": "You can't add more %{name} to the cart.", "shopify.notices.cart.only_one_added_to_cart": "Only 1 item was added to your cart due to availability.", "shopify.notices.cart.only_n_added_to_cart": "Only %{quantity} items were added to your cart due to availability.", "shopify.notices.cart.maximum_available_quantity_reached": "The maximum quantity of this item is already in your cart.", "shopify.notices.cart.less_than_minimum": "This item has a minimum of %{min}.", "shopify.notices.cart.more_than_maximum": "This item has a maximum of %{max}.", "shopify.notices.cart.not_respect_step": "You can only add this item in increments of %{step}.", "shopify.notices.cart.all_items_in_cart": "All %{count} %{name} are in your cart.", "shopify.notices.cart.empty_update": "cannot update empty cart", "shopify.notices.cart.missing_parameters": "no valid id or line parameter", "shopify.notices.cart.generic_error": "<PERSON><PERSON>", "shopify.notices.cart.invalid_input": "%{parameter} parameter is invalid.", "shopify.notices.cart.product_not_available": "Product is not available", "shopify.notices.cart.too_many_line_items_error": "Your cart can't contain more than %{max} items.", "shopify.notices.cart.link_expired": "Link expired", "shopify.notices.cart.link_no_longer_exists": "Link no longer exists.", "shopify.notices.cart.stock_problems_html": "One or more items are no longer available. We've provided an <a href='%{link}'>updated cart</a>.", "shopify.notices.cart.changed": "Cart changed", "shopify.notices.cart.items_changed": "One or more items have changed.", "shopify.notices.cart.product_sold_out": "The product '%{name}' is already sold out.", "shopify.notices.cart.variant_not_found": "Cannot find variant", "shopify.notices.cart.variant_requires_selling_plan": "Variant can only be purchased with a selling plan.", "shopify.notices.cart.selling_plan_not_available_for_company_locations": "Only one-time purchase is available for B2B orders", "shopify.notices.cart.digital_product_not_available_for_company_locations": "This item can't be added to a B2B order", "shopify.notices.cart.buyer_cannot_purchase_for_company_locations": "You can't purchase for this location", "shopify.notices.cart.selling_plan_not_applicable": "Cannot apply selling plan to variant", "shopify.notices.cart.shipping_address_not_required": "This cart does not require shipping", "shopify.notices.cart.shipping_address_invalid": "There was a problem calculating your shipping rates. Continue to checkout to choose a shipping rate before you complete your order.", "shopify.notices.cart.bundle_requires_components": "The bundle product '%{name}' cannot be added to the cart.", "shopify.notices.cart.gift_card_with_components_not_supported": "The bundle product '%{name}' cannot be added to the cart.", "shopify.notices.cart.gift_card_price_must_be_greater_than_zero": "The bundle product '%{name}' cannot be added to the cart.", "shopify.notices.cart.gift_card_recipient_validation_error": "The specified gift card recipient is invalid", "shopify.notices.cart.view_lines_limit_reached": "Lines must be less than %{limit}", "shopify.notices.cart.cart_too_large": "Cart is too large.", "shopify.notices.cart.cart_attributes_error": "Cart attributes contain invalid data.", "shopify.notices.cart.merchandise_line_transformers.run_error": "An error occurred in your cart.", "shopify.notices.cart.merchandise_not_applicable": "Item cannot be purchased as configured.", "shopify.notices.storefront.invalid_password": "Password incorrect, please try again.", "shopify.notices.tags.add_articles": "Narrow search to articles also having tag %{tag}", "shopify.notices.tags.add_products": "Narrow selection to products matching tag %{tag}", "shopify.notices.tags.remove_articles": "Widen search to articles that aren't tagged %{tag}", "shopify.notices.tags.remove_products": "Remove tag %{tag}", "shopify.email_marketing.subscribed.confirmation": "Thank you for subscribing to our mailing list.", "shopify.email_marketing.subscribed.disclaimer": "You can unsubscribe at any time.", "shopify.email_marketing.subscribed.unsubscribe": "Unsubscribe", "shopify.email_marketing.unsubscribed.confirmation": "You’ve unsubscribed from our mailing list.", "shopify.email_marketing.unsubscribed.disclaimer": "You won’t receive any more marketing updates from us. You can subscribe again at any time.", "shopify.email_marketing.unsubscribed.preview": "You're previewing what customers will see when they unsubscribe.", "shopify.email_marketing.open_tracking.opt_in.opted_in_html": "<b>You’ve opted in to email open tracking.</b>", "shopify.email_marketing.open_tracking.opt_in.visibility": "Email open rates will be reported anonymously. We will only see if you’ve opened an email as part of an aggregated email open rate.", "shopify.email_marketing.open_tracking.opt_in.opt_out_html": "You can <a style='text-decoration:underline;' href='%{link}'>opt out</a> of email open tracking if you do not want to share this information.", "shopify.email_marketing.open_tracking.opt_in.preview_bar": "You're previewing what customers will see when they opt in to email open tracking.", "shopify.email_marketing.open_tracking.opt_out.opted_out_html": "<b>You’ve opted out of email open tracking.</b>", "shopify.email_marketing.open_tracking.opt_out.visibility": "We will not see if you’ve opened our emails.", "shopify.email_marketing.open_tracking.opt_out.opt_in_html": "<a style='text-decoration:underline;' href='%{link}'>Opt in</a> to email open tracking.", "shopify.email_marketing.open_tracking.opt_out.preview_bar": "You're previewing what customers will see when they opt out of email open tracking.", "shopify.email_marketing.open_tracking.no_longer_track.status_html": "<b>We no longer track any email opens.</b>", "shopify.email_marketing.open_tracking.no_longer_track.privacy_policy": "After this email was sent, we updated our privacy policy and we no longer track opens.", "shopify.email_marketing.open_tracking.track.status_html": "<b>Email open tracking is reported.</b>", "shopify.email_marketing.open_tracking.track.visibility": "We will only see if you’ve opened the email, as part of the total email open rate.", "shopify.email_marketing.open_tracking.updated_policy.status_html": "<b>Email open tracking is reported.</b>", "shopify.email_marketing.open_tracking.updated_policy.visibility": "We have updated our policy, and now track emails opens. Email open rates will be reported anonymously. We will only see if you’ve opened an email as part of the total email open rate.", "shopify.email_marketing.open_tracking.updated_policy.opt_out_html": "If you do not want to share this information, you can <a style='text-decoration:underline;' href='%{link}'>unsubscribe.</a>", "shopify.filters.availability.value.in_stock": "In stock", "shopify.filters.availability.value.out_of_stock": "Out of stock", "shopify.filters.boolean.true_label": "Yes", "shopify.filters.boolean.false_label": "No", "shopify.search.error": "There was a problem loading your search results. Please refresh the page or try again later.", "shopify.search.sorting.relevance": "Relevance", "shopify.search.sorting.price_ascending": "Price, low to high", "shopify.search.sorting.price_descending": "Price, high to low", "shopify.page_titles.blog_or_article_with_error": "Error - %{title}", "shopify.page_titles.products": "Products", "shopify.page_titles.shopping_cart": "Your Shopping Cart", "shopify.page_titles.account": "Account", "shopify.page_titles.create_account": "Create Account", "shopify.page_titles.reset_account": "Reset Account", "shopify.page_titles.addresses": "Addresses", "shopify.page_titles.order": "Order %{name}", "shopify.page_titles.search": "Search", "shopify.page_titles.search_results.one": "Search: %{count} result found for \"%{terms}\"", "shopify.page_titles.search_results.other": "Search: %{count} results found for \"%{terms}\"", "shopify.page_titles.collections": "Collections", "shopify.page_titles.not_found": "404 Not Found", "shopify.page_titles.challenge": "Challenge", "shopify.page_titles.checkpoint": "Checkpoint", "shopify.cart.discounts_with_count": "%{count} discounts have been applied", "shopify.challenge.message": "To continue, let us know you're not a robot.", "shopify.challenge.error": "Your answer wasn't correct, please try again.", "shopify.challenge.submit_button_text": "Submit", "shopify.checkpoint.message": "Solve <PERSON> to continue", "shopify.checkpoint.error": "Captcha validation failed", "shopify.checkpoint.submit_button_text": "Submit", "shopify.store_availability.pick_up_time.one_hour": "Usually ready in 1 hour", "shopify.store_availability.pick_up_time.two_hours": "Usually ready in 2 hours", "shopify.store_availability.pick_up_time.four_hours": "Usually ready in 4 hours", "shopify.store_availability.pick_up_time.twenty_four_hours": "Usually ready in 24 hours", "shopify.store_availability.pick_up_time.two_to_four_days": "Usually ready in 2-4 days", "shopify.store_availability.pick_up_time.five_or_more_days": "Usually ready in 5+ days", "shopify.store_availability.pick_up_time.zero_to_two_hours": "Usually ready in 2 hours", "shopify.store_availability.pick_up_time.two_to_four_hours": "Usually ready in 4 hours", "shopify.store_availability.pick_up_time.immediately": "Ready for pickup now", "shopify.store_availability.pick_up_time.next_day": "Ready for pickup next day", "shopify.subscriptions.buyer_consent_product": "This item is a recurring or deferred purchase. By continuing, I agree to the %{cancellationPolicyLink} and authorize you to charge my payment method at the prices, frequency and dates listed on this page until my order is fulfilled or I cancel, if permitted.", "shopify.subscriptions.buyer_consent_cart": "One or more of the items in your cart is a recurring or deferred purchase. By continuing, I agree to the %{cancellationPolicyLink} and authorize you to charge my payment method at the prices, frequency and dates listed on this page until my order is fulfilled or I cancel, if permitted.", "shopify.subscriptions.cancellation_policy": "cancellation policy", "shopify.online_store.hcaptcha_badge.badge_text": "Protected by hCaptcha", "shopify.online_store.hcaptcha_badge.terms_link_text": "Terms", "shopify.online_store.hcaptcha_badge.privacy_link_text": "Privacy", "shopify.online_store.spam_detection.disclaimer_html": "<p data-spam-detection-disclaimer=\"\">This site is protected by hCaptcha and the hCaptcha <a href=\"https://hcaptcha.com/privacy\">Privacy Policy</a> and <a href=\"https://hcaptcha.com/terms\">Terms of Service</a> apply.</p>\n"}