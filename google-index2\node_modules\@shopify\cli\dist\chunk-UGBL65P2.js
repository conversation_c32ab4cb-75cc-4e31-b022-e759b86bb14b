import {
  cliInstallCommand
} from "./chunk-XO7GQ2WG.js";
import {
  base_command_default
} from "./chunk-2IA24ROR.js";
import {
  renderInfo
} from "./chunk-B36FYNEM.js";
import {
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/commands/upgrade.ts
init_cjs_shims();
var Upgrade = class extends base_command_default {
  static {
    this.summary = "Shows details on how to upgrade Shopify CLI.";
  }
  static {
    this.descriptionWithMarkdown = "Shows details on how to upgrade Shopify CLI.";
  }
  static {
    this.description = this.descriptionWithoutMarkdown();
  }
  async run() {
    renderInfo({
      body: [`To upgrade Shopify CLI use your package manager.
`, "Example:", { command: cliInstallCommand() }]
    });
  }
};

export {
  Upgrade
};
//# sourceMappingURL=chunk-UGBL65P2.js.map
