import {
  KitchenSinkAll
} from "../../../chunk-76S5M67J.js";
import {
  asyncTasks
} from "../../../chunk-AJYT5DKD.js";
import {
  prompts
} from "../../../chunk-Z7TXQNKI.js";
import {
  staticService
} from "../../../chunk-DKG2TMNN.js";
import {
  describe,
  globalExpect,
  test,
  vi
} from "../../../chunk-BQ3PZIHZ.js";
import "../../../chunk-2IA24ROR.js";
import "../../../chunk-VLSFD7SJ.js";
import "../../../chunk-SHWOPMLQ.js";
import "../../../chunk-K2VBTQSL.js";
import "../../../chunk-ZAVXS5HH.js";
import "../../../chunk-C4XAKIGB.js";
import "../../../chunk-PUO72IWW.js";
import "../../../chunk-25IMI7TH.js";
import "../../../chunk-G2VTHDI5.js";
import "../../../chunk-WRIQTRQE.js";
import "../../../chunk-B36FYNEM.js";
import "../../../chunk-F7F4BQYW.js";
import "../../../chunk-UMUTXITN.js";
import "../../../chunk-UATXMR5F.js";
import "../../../chunk-B5EXYCV3.js";
import "../../../chunk-G2ZZKGSV.js";
import "../../../chunk-75LV6AQS.js";
import "../../../chunk-UV5N2VL7.js";
import "../../../chunk-XE5EOEBL.js";
import "../../../chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "../../../chunk-PKR7KJ6P.js";

// src/cli/commands/kitchen-sink/index.test.ts
init_cjs_shims();
vi.mock("../../services/kitchen-sink/prompts.js");
vi.mock("../../services/kitchen-sink/static.js");
vi.mock("../../services/kitchen-sink/async.js");
describe("kitchen-sink all command", () => {
  test("launches service", async () => {
    vi.mocked(asyncTasks).mockResolvedValue(), vi.mocked(staticService).mockResolvedValue(), vi.mocked(prompts).mockResolvedValue(), await KitchenSinkAll.run([], import.meta.url), globalExpect(asyncTasks).toHaveBeenCalled(), globalExpect(staticService).toHaveBeenCalled(), globalExpect(prompts).toHaveBeenCalled();
  });
});
//# sourceMappingURL=index.test.js.map
