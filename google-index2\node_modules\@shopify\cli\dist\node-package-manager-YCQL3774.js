import {
  FindUpAndReadPackageJsonNotFoundError,
  PackageJsonNotFoundError,
  UnknownPackageManagerError,
  addNPMDependencies,
  addNPMDependenciesIfNeeded,
  addNPMDependenciesWithoutVersionIfNeeded,
  addResolutionOrOverride,
  bunLockfile,
  checkForCachedNewVersion,
  checkForNewVersion,
  findUpAndReadPackageJson,
  getDependencies,
  getPackageManager,
  getPackageName,
  getPackageVersion,
  inferPackageManager,
  installNPMDependenciesRecursively,
  installNodeModules,
  lockfiles,
  lockfilesByManager,
  npmLockfile,
  packageManager,
  packageManagerFromUserAgent,
  pnpmLockfile,
  pnpmWorkspaceFile,
  readAndParsePackageJson,
  usesWorkspaces,
  versionSatisfies,
  writePackageJSON,
  yarnLockfile
} from "./chunk-G2VTHDI5.js";
import "./chunk-B36FYNEM.js";
import "./chunk-F7F4BQYW.js";
import "./chunk-UMUTXITN.js";
import "./chunk-UATXMR5F.js";
import "./chunk-B5EXYCV3.js";
import "./chunk-G2ZZKGSV.js";
import "./chunk-75LV6AQS.js";
import "./chunk-UV5N2VL7.js";
import "./chunk-XE5EOEBL.js";
import "./chunk-EG6MBBEN.js";
import "./chunk-PKR7KJ6P.js";
export {
  FindUpAndReadPackageJsonNotFoundError,
  PackageJsonNotFoundError,
  UnknownPackageManagerError,
  addNPMDependencies,
  addNPMDependenciesIfNeeded,
  addNPMDependenciesWithoutVersionIfNeeded,
  addResolutionOrOverride,
  bunLockfile,
  checkForCachedNewVersion,
  checkForNewVersion,
  findUpAndReadPackageJson,
  getDependencies,
  getPackageManager,
  getPackageName,
  getPackageVersion,
  inferPackageManager,
  installNPMDependenciesRecursively,
  installNodeModules,
  lockfiles,
  lockfilesByManager,
  npmLockfile,
  packageManager,
  packageManagerFromUserAgent,
  pnpmLockfile,
  pnpmWorkspaceFile,
  readAndParsePackageJson,
  usesWorkspaces,
  versionSatisfies,
  writePackageJSON,
  yarnLockfile
};
//# sourceMappingURL=node-package-manager-YCQL3774.js.map
