import {
  error<PERSON><PERSON><PERSON>,
  registerCleanBugsnagErrorsFromWithinPlugins
} from "./chunk-VLSFD7SJ.js";
import {
  setCurrentCommandId,
  showNotificationsIfNeeded
} from "./chunk-C4XAKIGB.js";
import {
  hashString
} from "./chunk-PUO72IWW.js";
import {
  AbortError,
  addPublicMetadata,
  addSensitiveMetadata,
  findPathUp,
  isDevelopment,
  isTruthy,
  outputContent,
  outputResult,
  outputToken,
  readFile,
  renderInfo,
  renderWarning,
  terminalSupportsPrompting,
  underscore
} from "./chunk-B36FYNEM.js";
import {
  require_lib
} from "./chunk-F7F4BQYW.js";
import {
  cwd
} from "./chunk-EG6MBBEN.js";
import {
  __commonJS,
  __require,
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/parser.js
var require_parser = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/parser.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var ParserError = class _ParserError extends Error {
      /* istanbul ignore next */
      constructor(msg, filename, linenumber) {
        super("[ParserError] " + msg, filename, linenumber), this.name = "ParserError", this.code = "ParserError", Error.captureStackTrace && Error.captureStackTrace(this, _ParserError);
      }
    }, State = class {
      constructor(parser) {
        this.parser = parser, this.buf = "", this.returned = null, this.result = null, this.resultTable = null, this.resultArr = null;
      }
    }, Parser = class {
      constructor() {
        this.pos = 0, this.col = 0, this.line = 0, this.obj = {}, this.ctx = this.obj, this.stack = [], this._buf = "", this.char = null, this.ii = 0, this.state = new State(this.parseStart);
      }
      parse(str) {
        if (str.length === 0 || str.length == null) return;
        this._buf = String(str), this.ii = -1, this.char = -1;
        let getNext;
        for (; getNext === !1 || this.nextChar(); )
          getNext = this.runOne();
        this._buf = null;
      }
      nextChar() {
        return this.char === 10 && (++this.line, this.col = -1), ++this.ii, this.char = this._buf.codePointAt(this.ii), ++this.pos, ++this.col, this.haveBuffer();
      }
      haveBuffer() {
        return this.ii < this._buf.length;
      }
      runOne() {
        return this.state.parser.call(this, this.state.returned);
      }
      finish() {
        this.char = 1114112;
        let last;
        do
          last = this.state.parser, this.runOne();
        while (this.state.parser !== last);
        return this.ctx = null, this.state = null, this._buf = null, this.obj;
      }
      next(fn) {
        if (typeof fn != "function") throw new ParserError("Tried to set state to non-existent state: " + JSON.stringify(fn));
        this.state.parser = fn;
      }
      goto(fn) {
        return this.next(fn), this.runOne();
      }
      call(fn, returnWith) {
        returnWith && this.next(returnWith), this.stack.push(this.state), this.state = new State(fn);
      }
      callNow(fn, returnWith) {
        return this.call(fn, returnWith), this.runOne();
      }
      return(value) {
        if (this.stack.length === 0) throw this.error(new ParserError("Stack underflow"));
        value === void 0 && (value = this.state.buf), this.state = this.stack.pop(), this.state.returned = value;
      }
      returnNow(value) {
        return this.return(value), this.runOne();
      }
      consume() {
        if (this.char === 1114112) throw this.error(new ParserError("Unexpected end-of-buffer"));
        this.state.buf += this._buf[this.ii];
      }
      error(err) {
        return err.line = this.line, err.col = this.col, err.pos = this.pos, err;
      }
      /* istanbul ignore next */
      parseStart() {
        throw new ParserError("Must declare a parseStart method");
      }
    };
    Parser.END = 1114112;
    Parser.Error = ParserError;
    module2.exports = Parser;
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/create-datetime.js
var require_create_datetime = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/create-datetime.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = (value) => {
      let date = new Date(value);
      if (isNaN(date))
        throw new TypeError("Invalid Datetime");
      return date;
    };
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/format-num.js
var require_format_num = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/format-num.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = (d, num) => {
      for (num = String(num); num.length < d; ) num = "0" + num;
      return num;
    };
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/create-datetime-float.js
var require_create_datetime_float = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/create-datetime-float.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var f = require_format_num(), FloatingDateTime = class extends Date {
      constructor(value) {
        super(value + "Z"), this.isFloating = !0;
      }
      toISOString() {
        let date = `${this.getUTCFullYear()}-${f(2, this.getUTCMonth() + 1)}-${f(2, this.getUTCDate())}`, time = `${f(2, this.getUTCHours())}:${f(2, this.getUTCMinutes())}:${f(2, this.getUTCSeconds())}.${f(3, this.getUTCMilliseconds())}`;
        return `${date}T${time}`;
      }
    };
    module2.exports = (value) => {
      let date = new FloatingDateTime(value);
      if (isNaN(date))
        throw new TypeError("Invalid Datetime");
      return date;
    };
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/create-date.js
var require_create_date = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/create-date.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var f = require_format_num(), DateTime = global.Date, Date2 = class extends DateTime {
      constructor(value) {
        super(value), this.isDate = !0;
      }
      toISOString() {
        return `${this.getUTCFullYear()}-${f(2, this.getUTCMonth() + 1)}-${f(2, this.getUTCDate())}`;
      }
    };
    module2.exports = (value) => {
      let date = new Date2(value);
      if (isNaN(date))
        throw new TypeError("Invalid Datetime");
      return date;
    };
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/create-time.js
var require_create_time = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/create-time.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var f = require_format_num(), Time = class extends Date {
      constructor(value) {
        super(`0000-01-01T${value}Z`), this.isTime = !0;
      }
      toISOString() {
        return `${f(2, this.getUTCHours())}:${f(2, this.getUTCMinutes())}:${f(2, this.getUTCSeconds())}.${f(3, this.getUTCMilliseconds())}`;
      }
    };
    module2.exports = (value) => {
      let date = new Time(value);
      if (isNaN(date))
        throw new TypeError("Invalid Datetime");
      return date;
    };
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/toml-parser.js
var require_toml_parser = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/lib/toml-parser.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    module.exports = makeParserClass(require_parser());
    module.exports.makeParserClass = makeParserClass;
    var TomlError = class _TomlError extends Error {
      constructor(msg) {
        super(msg), this.name = "TomlError", Error.captureStackTrace && Error.captureStackTrace(this, _TomlError), this.fromTOML = !0, this.wrapped = null;
      }
    };
    TomlError.wrap = (err) => {
      let terr = new TomlError(err.message);
      return terr.code = err.code, terr.wrapped = err, terr;
    };
    module.exports.TomlError = TomlError;
    var createDateTime = require_create_datetime(), createDateTimeFloat = require_create_datetime_float(), createDate = require_create_date(), createTime = require_create_time(), CTRL_I = 9, CTRL_J = 10, CTRL_M = 13, CTRL_CHAR_BOUNDARY = 31, CHAR_SP = 32, CHAR_QUOT = 34, CHAR_NUM = 35, CHAR_APOS = 39, CHAR_PLUS = 43, CHAR_COMMA = 44, CHAR_HYPHEN = 45, CHAR_PERIOD = 46, CHAR_0 = 48, CHAR_1 = 49, CHAR_7 = 55, CHAR_9 = 57, CHAR_COLON = 58, CHAR_EQUALS = 61, CHAR_A = 65, CHAR_E = 69, CHAR_F = 70, CHAR_T = 84, CHAR_U = 85, CHAR_Z = 90, CHAR_LOWBAR = 95, CHAR_a = 97, CHAR_b = 98, CHAR_e = 101, CHAR_f = 102, CHAR_i = 105, CHAR_l = 108, CHAR_n = 110, CHAR_o = 111, CHAR_r = 114, CHAR_s = 115, CHAR_t = 116, CHAR_u = 117, CHAR_x = 120, CHAR_z = 122, CHAR_LCUB = 123, CHAR_RCUB = 125, CHAR_LSQB = 91, CHAR_BSOL = 92, CHAR_RSQB = 93, CHAR_DEL = 127, SURROGATE_FIRST = 55296, SURROGATE_LAST = 57343, escapes = {
      [CHAR_b]: "\b",
      [CHAR_t]: "	",
      [CHAR_n]: `
`,
      [CHAR_f]: "\f",
      [CHAR_r]: "\r",
      [CHAR_QUOT]: '"',
      [CHAR_BSOL]: "\\"
    };
    function isDigit(cp) {
      return cp >= CHAR_0 && cp <= CHAR_9;
    }
    function isHexit(cp) {
      return cp >= CHAR_A && cp <= CHAR_F || cp >= CHAR_a && cp <= CHAR_f || cp >= CHAR_0 && cp <= CHAR_9;
    }
    function isBit(cp) {
      return cp === CHAR_1 || cp === CHAR_0;
    }
    function isOctit(cp) {
      return cp >= CHAR_0 && cp <= CHAR_7;
    }
    function isAlphaNumQuoteHyphen(cp) {
      return cp >= CHAR_A && cp <= CHAR_Z || cp >= CHAR_a && cp <= CHAR_z || cp >= CHAR_0 && cp <= CHAR_9 || cp === CHAR_APOS || cp === CHAR_QUOT || cp === CHAR_LOWBAR || cp === CHAR_HYPHEN;
    }
    function isAlphaNumHyphen(cp) {
      return cp >= CHAR_A && cp <= CHAR_Z || cp >= CHAR_a && cp <= CHAR_z || cp >= CHAR_0 && cp <= CHAR_9 || cp === CHAR_LOWBAR || cp === CHAR_HYPHEN;
    }
    var _type = Symbol("type"), _declared = Symbol("declared"), hasOwnProperty = Object.prototype.hasOwnProperty, defineProperty = Object.defineProperty, descriptor = { configurable: !0, enumerable: !0, writable: !0, value: void 0 };
    function hasKey(obj, key) {
      return hasOwnProperty.call(obj, key) ? !0 : (key === "__proto__" && defineProperty(obj, "__proto__", descriptor), !1);
    }
    var INLINE_TABLE = Symbol("inline-table");
    function InlineTable() {
      return Object.defineProperties({}, {
        [_type]: { value: INLINE_TABLE }
      });
    }
    function isInlineTable(obj) {
      return obj === null || typeof obj != "object" ? !1 : obj[_type] === INLINE_TABLE;
    }
    var TABLE = Symbol("table");
    function Table() {
      return Object.defineProperties({}, {
        [_type]: { value: TABLE },
        [_declared]: { value: !1, writable: !0 }
      });
    }
    function isTable(obj) {
      return obj === null || typeof obj != "object" ? !1 : obj[_type] === TABLE;
    }
    var _contentType = Symbol("content-type"), INLINE_LIST = Symbol("inline-list");
    function InlineList(type) {
      return Object.defineProperties([], {
        [_type]: { value: INLINE_LIST },
        [_contentType]: { value: type }
      });
    }
    function isInlineList(obj) {
      return obj === null || typeof obj != "object" ? !1 : obj[_type] === INLINE_LIST;
    }
    var LIST = Symbol("list");
    function List() {
      return Object.defineProperties([], {
        [_type]: { value: LIST }
      });
    }
    function isList(obj) {
      return obj === null || typeof obj != "object" ? !1 : obj[_type] === LIST;
    }
    var _custom;
    try {
      let utilInspect = eval("require('util').inspect");
      _custom = utilInspect.custom;
    } catch (_) {
    }
    var _inspect = _custom || "inspect", BoxedBigInt = class {
      constructor(value) {
        try {
          this.value = global.BigInt.asIntN(64, value);
        } catch {
          this.value = null;
        }
        Object.defineProperty(this, _type, { value: INTEGER });
      }
      isNaN() {
        return this.value === null;
      }
      /* istanbul ignore next */
      toString() {
        return String(this.value);
      }
      /* istanbul ignore next */
      [_inspect]() {
        return `[BigInt: ${this.toString()}]}`;
      }
      valueOf() {
        return this.value;
      }
    }, INTEGER = Symbol("integer");
    function Integer(value) {
      let num = Number(value);
      return Object.is(num, -0) && (num = 0), global.BigInt && !Number.isSafeInteger(num) ? new BoxedBigInt(value) : Object.defineProperties(new Number(num), {
        isNaN: { value: function() {
          return isNaN(this);
        } },
        [_type]: { value: INTEGER },
        [_inspect]: { value: () => `[Integer: ${value}]` }
      });
    }
    function isInteger(obj) {
      return obj === null || typeof obj != "object" ? !1 : obj[_type] === INTEGER;
    }
    var FLOAT = Symbol("float");
    function Float(value) {
      return Object.defineProperties(new Number(value), {
        [_type]: { value: FLOAT },
        [_inspect]: { value: () => `[Float: ${value}]` }
      });
    }
    function isFloat(obj) {
      return obj === null || typeof obj != "object" ? !1 : obj[_type] === FLOAT;
    }
    function tomlType(value) {
      let type = typeof value;
      if (type === "object") {
        if (value === null) return "null";
        if (value instanceof Date) return "datetime";
        if (_type in value)
          switch (value[_type]) {
            case INLINE_TABLE:
              return "inline-table";
            case INLINE_LIST:
              return "inline-list";
            /* istanbul ignore next */
            case TABLE:
              return "table";
            /* istanbul ignore next */
            case LIST:
              return "list";
            case FLOAT:
              return "float";
            case INTEGER:
              return "integer";
          }
      }
      return type;
    }
    function makeParserClass(Parser) {
      class TOMLParser extends Parser {
        constructor() {
          super(), this.ctx = this.obj = Table();
        }
        /* MATCH HELPER */
        atEndOfWord() {
          return this.char === CHAR_NUM || this.char === CTRL_I || this.char === CHAR_SP || this.atEndOfLine();
        }
        atEndOfLine() {
          return this.char === Parser.END || this.char === CTRL_J || this.char === CTRL_M;
        }
        parseStart() {
          if (this.char === Parser.END)
            return null;
          if (this.char === CHAR_LSQB)
            return this.call(this.parseTableOrList);
          if (this.char === CHAR_NUM)
            return this.call(this.parseComment);
          if (this.char === CTRL_J || this.char === CHAR_SP || this.char === CTRL_I || this.char === CTRL_M)
            return null;
          if (isAlphaNumQuoteHyphen(this.char))
            return this.callNow(this.parseAssignStatement);
          throw this.error(new TomlError(`Unknown character "${this.char}"`));
        }
        // HELPER, this strips any whitespace and comments to the end of the line
        // then RETURNS. Last state in a production.
        parseWhitespaceToEOL() {
          if (this.char === CHAR_SP || this.char === CTRL_I || this.char === CTRL_M)
            return null;
          if (this.char === CHAR_NUM)
            return this.goto(this.parseComment);
          if (this.char === Parser.END || this.char === CTRL_J)
            return this.return();
          throw this.error(new TomlError("Unexpected character, expected only whitespace or comments till end of line"));
        }
        /* ASSIGNMENT: key = value */
        parseAssignStatement() {
          return this.callNow(this.parseAssign, this.recordAssignStatement);
        }
        recordAssignStatement(kv) {
          let target = this.ctx, finalKey = kv.key.pop();
          for (let kw of kv.key) {
            if (hasKey(target, kw) && (!isTable(target[kw]) || target[kw][_declared]))
              throw this.error(new TomlError("Can't redefine existing key"));
            target = target[kw] = target[kw] || Table();
          }
          if (hasKey(target, finalKey))
            throw this.error(new TomlError("Can't redefine existing key"));
          return isInteger(kv.value) || isFloat(kv.value) ? target[finalKey] = kv.value.valueOf() : target[finalKey] = kv.value, this.goto(this.parseWhitespaceToEOL);
        }
        /* ASSSIGNMENT expression, key = value possibly inside an inline table */
        parseAssign() {
          return this.callNow(this.parseKeyword, this.recordAssignKeyword);
        }
        recordAssignKeyword(key) {
          return this.state.resultTable ? this.state.resultTable.push(key) : this.state.resultTable = [key], this.goto(this.parseAssignKeywordPreDot);
        }
        parseAssignKeywordPreDot() {
          if (this.char === CHAR_PERIOD)
            return this.next(this.parseAssignKeywordPostDot);
          if (this.char !== CHAR_SP && this.char !== CTRL_I)
            return this.goto(this.parseAssignEqual);
        }
        parseAssignKeywordPostDot() {
          if (this.char !== CHAR_SP && this.char !== CTRL_I)
            return this.callNow(this.parseKeyword, this.recordAssignKeyword);
        }
        parseAssignEqual() {
          if (this.char === CHAR_EQUALS)
            return this.next(this.parseAssignPreValue);
          throw this.error(new TomlError('Invalid character, expected "="'));
        }
        parseAssignPreValue() {
          return this.char === CHAR_SP || this.char === CTRL_I ? null : this.callNow(this.parseValue, this.recordAssignValue);
        }
        recordAssignValue(value) {
          return this.returnNow({ key: this.state.resultTable, value });
        }
        /* COMMENTS: #...eol */
        parseComment() {
          do
            if (this.char === Parser.END || this.char === CTRL_J)
              return this.return();
          while (this.nextChar());
        }
        /* TABLES AND LISTS, [foo] and [[foo]] */
        parseTableOrList() {
          if (this.char === CHAR_LSQB)
            this.next(this.parseList);
          else
            return this.goto(this.parseTable);
        }
        /* TABLE [foo.bar.baz] */
        parseTable() {
          return this.ctx = this.obj, this.goto(this.parseTableNext);
        }
        parseTableNext() {
          return this.char === CHAR_SP || this.char === CTRL_I ? null : this.callNow(this.parseKeyword, this.parseTableMore);
        }
        parseTableMore(keyword) {
          if (this.char === CHAR_SP || this.char === CTRL_I)
            return null;
          if (this.char === CHAR_RSQB) {
            if (hasKey(this.ctx, keyword) && (!isTable(this.ctx[keyword]) || this.ctx[keyword][_declared]))
              throw this.error(new TomlError("Can't redefine existing key"));
            return this.ctx = this.ctx[keyword] = this.ctx[keyword] || Table(), this.ctx[_declared] = !0, this.next(this.parseWhitespaceToEOL);
          } else if (this.char === CHAR_PERIOD) {
            if (!hasKey(this.ctx, keyword))
              this.ctx = this.ctx[keyword] = Table();
            else if (isTable(this.ctx[keyword]))
              this.ctx = this.ctx[keyword];
            else if (isList(this.ctx[keyword]))
              this.ctx = this.ctx[keyword][this.ctx[keyword].length - 1];
            else
              throw this.error(new TomlError("Can't redefine existing key"));
            return this.next(this.parseTableNext);
          } else
            throw this.error(new TomlError("Unexpected character, expected whitespace, . or ]"));
        }
        /* LIST [[a.b.c]] */
        parseList() {
          return this.ctx = this.obj, this.goto(this.parseListNext);
        }
        parseListNext() {
          return this.char === CHAR_SP || this.char === CTRL_I ? null : this.callNow(this.parseKeyword, this.parseListMore);
        }
        parseListMore(keyword) {
          if (this.char === CHAR_SP || this.char === CTRL_I)
            return null;
          if (this.char === CHAR_RSQB) {
            if (hasKey(this.ctx, keyword) || (this.ctx[keyword] = List()), isInlineList(this.ctx[keyword]))
              throw this.error(new TomlError("Can't extend an inline array"));
            if (isList(this.ctx[keyword])) {
              let next = Table();
              this.ctx[keyword].push(next), this.ctx = next;
            } else
              throw this.error(new TomlError("Can't redefine an existing key"));
            return this.next(this.parseListEnd);
          } else if (this.char === CHAR_PERIOD) {
            if (!hasKey(this.ctx, keyword))
              this.ctx = this.ctx[keyword] = Table();
            else {
              if (isInlineList(this.ctx[keyword]))
                throw this.error(new TomlError("Can't extend an inline array"));
              if (isInlineTable(this.ctx[keyword]))
                throw this.error(new TomlError("Can't extend an inline table"));
              if (isList(this.ctx[keyword]))
                this.ctx = this.ctx[keyword][this.ctx[keyword].length - 1];
              else if (isTable(this.ctx[keyword]))
                this.ctx = this.ctx[keyword];
              else
                throw this.error(new TomlError("Can't redefine an existing key"));
            }
            return this.next(this.parseListNext);
          } else
            throw this.error(new TomlError("Unexpected character, expected whitespace, . or ]"));
        }
        parseListEnd(keyword) {
          if (this.char === CHAR_RSQB)
            return this.next(this.parseWhitespaceToEOL);
          throw this.error(new TomlError("Unexpected character, expected whitespace, . or ]"));
        }
        /* VALUE string, number, boolean, inline list, inline object */
        parseValue() {
          if (this.char === Parser.END)
            throw this.error(new TomlError("Key without value"));
          if (this.char === CHAR_QUOT)
            return this.next(this.parseDoubleString);
          if (this.char === CHAR_APOS)
            return this.next(this.parseSingleString);
          if (this.char === CHAR_HYPHEN || this.char === CHAR_PLUS)
            return this.goto(this.parseNumberSign);
          if (this.char === CHAR_i)
            return this.next(this.parseInf);
          if (this.char === CHAR_n)
            return this.next(this.parseNan);
          if (isDigit(this.char))
            return this.goto(this.parseNumberOrDateTime);
          if (this.char === CHAR_t || this.char === CHAR_f)
            return this.goto(this.parseBoolean);
          if (this.char === CHAR_LSQB)
            return this.call(this.parseInlineList, this.recordValue);
          if (this.char === CHAR_LCUB)
            return this.call(this.parseInlineTable, this.recordValue);
          throw this.error(new TomlError("Unexpected character, expecting string, number, datetime, boolean, inline array or inline table"));
        }
        recordValue(value) {
          return this.returnNow(value);
        }
        parseInf() {
          if (this.char === CHAR_n)
            return this.next(this.parseInf2);
          throw this.error(new TomlError('Unexpected character, expected "inf", "+inf" or "-inf"'));
        }
        parseInf2() {
          if (this.char === CHAR_f)
            return this.state.buf === "-" ? this.return(-1 / 0) : this.return(1 / 0);
          throw this.error(new TomlError('Unexpected character, expected "inf", "+inf" or "-inf"'));
        }
        parseNan() {
          if (this.char === CHAR_a)
            return this.next(this.parseNan2);
          throw this.error(new TomlError('Unexpected character, expected "nan"'));
        }
        parseNan2() {
          if (this.char === CHAR_n)
            return this.return(NaN);
          throw this.error(new TomlError('Unexpected character, expected "nan"'));
        }
        /* KEYS, barewords or basic, literal, or dotted */
        parseKeyword() {
          return this.char === CHAR_QUOT ? this.next(this.parseBasicString) : this.char === CHAR_APOS ? this.next(this.parseLiteralString) : this.goto(this.parseBareKey);
        }
        /* KEYS: barewords */
        parseBareKey() {
          do {
            if (this.char === Parser.END)
              throw this.error(new TomlError("Key ended without value"));
            if (isAlphaNumHyphen(this.char))
              this.consume();
            else {
              if (this.state.buf.length === 0)
                throw this.error(new TomlError("Empty bare keys are not allowed"));
              return this.returnNow();
            }
          } while (this.nextChar());
        }
        /* STRINGS, single quoted (literal) */
        parseSingleString() {
          return this.char === CHAR_APOS ? this.next(this.parseLiteralMultiStringMaybe) : this.goto(this.parseLiteralString);
        }
        parseLiteralString() {
          do {
            if (this.char === CHAR_APOS)
              return this.return();
            if (this.atEndOfLine())
              throw this.error(new TomlError("Unterminated string"));
            if (this.char === CHAR_DEL || this.char <= CTRL_CHAR_BOUNDARY && this.char !== CTRL_I)
              throw this.errorControlCharInString();
            this.consume();
          } while (this.nextChar());
        }
        parseLiteralMultiStringMaybe() {
          return this.char === CHAR_APOS ? this.next(this.parseLiteralMultiString) : this.returnNow();
        }
        parseLiteralMultiString() {
          return this.char === CTRL_M ? null : this.char === CTRL_J ? this.next(this.parseLiteralMultiStringContent) : this.goto(this.parseLiteralMultiStringContent);
        }
        parseLiteralMultiStringContent() {
          do {
            if (this.char === CHAR_APOS)
              return this.next(this.parseLiteralMultiEnd);
            if (this.char === Parser.END)
              throw this.error(new TomlError("Unterminated multi-line string"));
            if (this.char === CHAR_DEL || this.char <= CTRL_CHAR_BOUNDARY && this.char !== CTRL_I && this.char !== CTRL_J && this.char !== CTRL_M)
              throw this.errorControlCharInString();
            this.consume();
          } while (this.nextChar());
        }
        parseLiteralMultiEnd() {
          return this.char === CHAR_APOS ? this.next(this.parseLiteralMultiEnd2) : (this.state.buf += "'", this.goto(this.parseLiteralMultiStringContent));
        }
        parseLiteralMultiEnd2() {
          return this.char === CHAR_APOS ? this.return() : (this.state.buf += "''", this.goto(this.parseLiteralMultiStringContent));
        }
        /* STRINGS double quoted */
        parseDoubleString() {
          return this.char === CHAR_QUOT ? this.next(this.parseMultiStringMaybe) : this.goto(this.parseBasicString);
        }
        parseBasicString() {
          do {
            if (this.char === CHAR_BSOL)
              return this.call(this.parseEscape, this.recordEscapeReplacement);
            if (this.char === CHAR_QUOT)
              return this.return();
            if (this.atEndOfLine())
              throw this.error(new TomlError("Unterminated string"));
            if (this.char === CHAR_DEL || this.char <= CTRL_CHAR_BOUNDARY && this.char !== CTRL_I)
              throw this.errorControlCharInString();
            this.consume();
          } while (this.nextChar());
        }
        recordEscapeReplacement(replacement) {
          return this.state.buf += replacement, this.goto(this.parseBasicString);
        }
        parseMultiStringMaybe() {
          return this.char === CHAR_QUOT ? this.next(this.parseMultiString) : this.returnNow();
        }
        parseMultiString() {
          return this.char === CTRL_M ? null : this.char === CTRL_J ? this.next(this.parseMultiStringContent) : this.goto(this.parseMultiStringContent);
        }
        parseMultiStringContent() {
          do {
            if (this.char === CHAR_BSOL)
              return this.call(this.parseMultiEscape, this.recordMultiEscapeReplacement);
            if (this.char === CHAR_QUOT)
              return this.next(this.parseMultiEnd);
            if (this.char === Parser.END)
              throw this.error(new TomlError("Unterminated multi-line string"));
            if (this.char === CHAR_DEL || this.char <= CTRL_CHAR_BOUNDARY && this.char !== CTRL_I && this.char !== CTRL_J && this.char !== CTRL_M)
              throw this.errorControlCharInString();
            this.consume();
          } while (this.nextChar());
        }
        errorControlCharInString() {
          let displayCode = "\\u00";
          return this.char < 16 && (displayCode += "0"), displayCode += this.char.toString(16), this.error(new TomlError(`Control characters (codes < 0x1f and 0x7f) are not allowed in strings, use ${displayCode} instead`));
        }
        recordMultiEscapeReplacement(replacement) {
          return this.state.buf += replacement, this.goto(this.parseMultiStringContent);
        }
        parseMultiEnd() {
          return this.char === CHAR_QUOT ? this.next(this.parseMultiEnd2) : (this.state.buf += '"', this.goto(this.parseMultiStringContent));
        }
        parseMultiEnd2() {
          return this.char === CHAR_QUOT ? this.return() : (this.state.buf += '""', this.goto(this.parseMultiStringContent));
        }
        parseMultiEscape() {
          return this.char === CTRL_M || this.char === CTRL_J ? this.next(this.parseMultiTrim) : this.char === CHAR_SP || this.char === CTRL_I ? this.next(this.parsePreMultiTrim) : this.goto(this.parseEscape);
        }
        parsePreMultiTrim() {
          if (this.char === CHAR_SP || this.char === CTRL_I)
            return null;
          if (this.char === CTRL_M || this.char === CTRL_J)
            return this.next(this.parseMultiTrim);
          throw this.error(new TomlError("Can't escape whitespace"));
        }
        parseMultiTrim() {
          return this.char === CTRL_J || this.char === CHAR_SP || this.char === CTRL_I || this.char === CTRL_M ? null : this.returnNow();
        }
        parseEscape() {
          if (this.char in escapes)
            return this.return(escapes[this.char]);
          if (this.char === CHAR_u)
            return this.call(this.parseSmallUnicode, this.parseUnicodeReturn);
          if (this.char === CHAR_U)
            return this.call(this.parseLargeUnicode, this.parseUnicodeReturn);
          throw this.error(new TomlError("Unknown escape character: " + this.char));
        }
        parseUnicodeReturn(char) {
          try {
            let codePoint = parseInt(char, 16);
            if (codePoint >= SURROGATE_FIRST && codePoint <= SURROGATE_LAST)
              throw this.error(new TomlError("Invalid unicode, character in range 0xD800 - 0xDFFF is reserved"));
            return this.returnNow(String.fromCodePoint(codePoint));
          } catch (err) {
            throw this.error(TomlError.wrap(err));
          }
        }
        parseSmallUnicode() {
          if (isHexit(this.char)) {
            if (this.consume(), this.state.buf.length >= 4) return this.return();
          } else
            throw this.error(new TomlError("Invalid character in unicode sequence, expected hex"));
        }
        parseLargeUnicode() {
          if (isHexit(this.char)) {
            if (this.consume(), this.state.buf.length >= 8) return this.return();
          } else
            throw this.error(new TomlError("Invalid character in unicode sequence, expected hex"));
        }
        /* NUMBERS */
        parseNumberSign() {
          return this.consume(), this.next(this.parseMaybeSignedInfOrNan);
        }
        parseMaybeSignedInfOrNan() {
          return this.char === CHAR_i ? this.next(this.parseInf) : this.char === CHAR_n ? this.next(this.parseNan) : this.callNow(this.parseNoUnder, this.parseNumberIntegerStart);
        }
        parseNumberIntegerStart() {
          return this.char === CHAR_0 ? (this.consume(), this.next(this.parseNumberIntegerExponentOrDecimal)) : this.goto(this.parseNumberInteger);
        }
        parseNumberIntegerExponentOrDecimal() {
          return this.char === CHAR_PERIOD ? (this.consume(), this.call(this.parseNoUnder, this.parseNumberFloat)) : this.char === CHAR_E || this.char === CHAR_e ? (this.consume(), this.next(this.parseNumberExponentSign)) : this.returnNow(Integer(this.state.buf));
        }
        parseNumberInteger() {
          if (isDigit(this.char))
            this.consume();
          else {
            if (this.char === CHAR_LOWBAR)
              return this.call(this.parseNoUnder);
            if (this.char === CHAR_E || this.char === CHAR_e)
              return this.consume(), this.next(this.parseNumberExponentSign);
            if (this.char === CHAR_PERIOD)
              return this.consume(), this.call(this.parseNoUnder, this.parseNumberFloat);
            {
              let result = Integer(this.state.buf);
              if (result.isNaN())
                throw this.error(new TomlError("Invalid number"));
              return this.returnNow(result);
            }
          }
        }
        parseNoUnder() {
          if (this.char === CHAR_LOWBAR || this.char === CHAR_PERIOD || this.char === CHAR_E || this.char === CHAR_e)
            throw this.error(new TomlError("Unexpected character, expected digit"));
          if (this.atEndOfWord())
            throw this.error(new TomlError("Incomplete number"));
          return this.returnNow();
        }
        parseNoUnderHexOctBinLiteral() {
          if (this.char === CHAR_LOWBAR || this.char === CHAR_PERIOD)
            throw this.error(new TomlError("Unexpected character, expected digit"));
          if (this.atEndOfWord())
            throw this.error(new TomlError("Incomplete number"));
          return this.returnNow();
        }
        parseNumberFloat() {
          if (this.char === CHAR_LOWBAR)
            return this.call(this.parseNoUnder, this.parseNumberFloat);
          if (isDigit(this.char))
            this.consume();
          else return this.char === CHAR_E || this.char === CHAR_e ? (this.consume(), this.next(this.parseNumberExponentSign)) : this.returnNow(Float(this.state.buf));
        }
        parseNumberExponentSign() {
          if (isDigit(this.char))
            return this.goto(this.parseNumberExponent);
          if (this.char === CHAR_HYPHEN || this.char === CHAR_PLUS)
            this.consume(), this.call(this.parseNoUnder, this.parseNumberExponent);
          else
            throw this.error(new TomlError("Unexpected character, expected -, + or digit"));
        }
        parseNumberExponent() {
          if (isDigit(this.char))
            this.consume();
          else return this.char === CHAR_LOWBAR ? this.call(this.parseNoUnder) : this.returnNow(Float(this.state.buf));
        }
        /* NUMBERS or DATETIMES  */
        parseNumberOrDateTime() {
          return this.char === CHAR_0 ? (this.consume(), this.next(this.parseNumberBaseOrDateTime)) : this.goto(this.parseNumberOrDateTimeOnly);
        }
        parseNumberOrDateTimeOnly() {
          if (this.char === CHAR_LOWBAR)
            return this.call(this.parseNoUnder, this.parseNumberInteger);
          if (isDigit(this.char))
            this.consume(), this.state.buf.length > 4 && this.next(this.parseNumberInteger);
          else return this.char === CHAR_E || this.char === CHAR_e ? (this.consume(), this.next(this.parseNumberExponentSign)) : this.char === CHAR_PERIOD ? (this.consume(), this.call(this.parseNoUnder, this.parseNumberFloat)) : this.char === CHAR_HYPHEN ? this.goto(this.parseDateTime) : this.char === CHAR_COLON ? this.goto(this.parseOnlyTimeHour) : this.returnNow(Integer(this.state.buf));
        }
        parseDateTimeOnly() {
          if (this.state.buf.length < 4) {
            if (isDigit(this.char))
              return this.consume();
            if (this.char === CHAR_COLON)
              return this.goto(this.parseOnlyTimeHour);
            throw this.error(new TomlError("Expected digit while parsing year part of a date"));
          } else {
            if (this.char === CHAR_HYPHEN)
              return this.goto(this.parseDateTime);
            throw this.error(new TomlError("Expected hyphen (-) while parsing year part of date"));
          }
        }
        parseNumberBaseOrDateTime() {
          return this.char === CHAR_b ? (this.consume(), this.call(this.parseNoUnderHexOctBinLiteral, this.parseIntegerBin)) : this.char === CHAR_o ? (this.consume(), this.call(this.parseNoUnderHexOctBinLiteral, this.parseIntegerOct)) : this.char === CHAR_x ? (this.consume(), this.call(this.parseNoUnderHexOctBinLiteral, this.parseIntegerHex)) : this.char === CHAR_PERIOD ? this.goto(this.parseNumberInteger) : isDigit(this.char) ? this.goto(this.parseDateTimeOnly) : this.returnNow(Integer(this.state.buf));
        }
        parseIntegerHex() {
          if (isHexit(this.char))
            this.consume();
          else {
            if (this.char === CHAR_LOWBAR)
              return this.call(this.parseNoUnderHexOctBinLiteral);
            {
              let result = Integer(this.state.buf);
              if (result.isNaN())
                throw this.error(new TomlError("Invalid number"));
              return this.returnNow(result);
            }
          }
        }
        parseIntegerOct() {
          if (isOctit(this.char))
            this.consume();
          else {
            if (this.char === CHAR_LOWBAR)
              return this.call(this.parseNoUnderHexOctBinLiteral);
            {
              let result = Integer(this.state.buf);
              if (result.isNaN())
                throw this.error(new TomlError("Invalid number"));
              return this.returnNow(result);
            }
          }
        }
        parseIntegerBin() {
          if (isBit(this.char))
            this.consume();
          else {
            if (this.char === CHAR_LOWBAR)
              return this.call(this.parseNoUnderHexOctBinLiteral);
            {
              let result = Integer(this.state.buf);
              if (result.isNaN())
                throw this.error(new TomlError("Invalid number"));
              return this.returnNow(result);
            }
          }
        }
        /* DATETIME */
        parseDateTime() {
          if (this.state.buf.length < 4)
            throw this.error(new TomlError("Years less than 1000 must be zero padded to four characters"));
          return this.state.result = this.state.buf, this.state.buf = "", this.next(this.parseDateMonth);
        }
        parseDateMonth() {
          if (this.char === CHAR_HYPHEN) {
            if (this.state.buf.length < 2)
              throw this.error(new TomlError("Months less than 10 must be zero padded to two characters"));
            return this.state.result += "-" + this.state.buf, this.state.buf = "", this.next(this.parseDateDay);
          } else if (isDigit(this.char))
            this.consume();
          else
            throw this.error(new TomlError("Incomplete datetime"));
        }
        parseDateDay() {
          if (this.char === CHAR_T || this.char === CHAR_SP) {
            if (this.state.buf.length < 2)
              throw this.error(new TomlError("Days less than 10 must be zero padded to two characters"));
            return this.state.result += "-" + this.state.buf, this.state.buf = "", this.next(this.parseStartTimeHour);
          } else {
            if (this.atEndOfWord())
              return this.returnNow(createDate(this.state.result + "-" + this.state.buf));
            if (isDigit(this.char))
              this.consume();
            else
              throw this.error(new TomlError("Incomplete datetime"));
          }
        }
        parseStartTimeHour() {
          return this.atEndOfWord() ? this.returnNow(createDate(this.state.result)) : this.goto(this.parseTimeHour);
        }
        parseTimeHour() {
          if (this.char === CHAR_COLON) {
            if (this.state.buf.length < 2)
              throw this.error(new TomlError("Hours less than 10 must be zero padded to two characters"));
            return this.state.result += "T" + this.state.buf, this.state.buf = "", this.next(this.parseTimeMin);
          } else if (isDigit(this.char))
            this.consume();
          else
            throw this.error(new TomlError("Incomplete datetime"));
        }
        parseTimeMin() {
          if (this.state.buf.length < 2 && isDigit(this.char))
            this.consume();
          else {
            if (this.state.buf.length === 2 && this.char === CHAR_COLON)
              return this.state.result += ":" + this.state.buf, this.state.buf = "", this.next(this.parseTimeSec);
            throw this.error(new TomlError("Incomplete datetime"));
          }
        }
        parseTimeSec() {
          if (isDigit(this.char)) {
            if (this.consume(), this.state.buf.length === 2)
              return this.state.result += ":" + this.state.buf, this.state.buf = "", this.next(this.parseTimeZoneOrFraction);
          } else
            throw this.error(new TomlError("Incomplete datetime"));
        }
        parseOnlyTimeHour() {
          if (this.char === CHAR_COLON) {
            if (this.state.buf.length < 2)
              throw this.error(new TomlError("Hours less than 10 must be zero padded to two characters"));
            return this.state.result = this.state.buf, this.state.buf = "", this.next(this.parseOnlyTimeMin);
          } else
            throw this.error(new TomlError("Incomplete time"));
        }
        parseOnlyTimeMin() {
          if (this.state.buf.length < 2 && isDigit(this.char))
            this.consume();
          else {
            if (this.state.buf.length === 2 && this.char === CHAR_COLON)
              return this.state.result += ":" + this.state.buf, this.state.buf = "", this.next(this.parseOnlyTimeSec);
            throw this.error(new TomlError("Incomplete time"));
          }
        }
        parseOnlyTimeSec() {
          if (isDigit(this.char)) {
            if (this.consume(), this.state.buf.length === 2)
              return this.next(this.parseOnlyTimeFractionMaybe);
          } else
            throw this.error(new TomlError("Incomplete time"));
        }
        parseOnlyTimeFractionMaybe() {
          if (this.state.result += ":" + this.state.buf, this.char === CHAR_PERIOD)
            this.state.buf = "", this.next(this.parseOnlyTimeFraction);
          else
            return this.return(createTime(this.state.result));
        }
        parseOnlyTimeFraction() {
          if (isDigit(this.char))
            this.consume();
          else if (this.atEndOfWord()) {
            if (this.state.buf.length === 0) throw this.error(new TomlError("Expected digit in milliseconds"));
            return this.returnNow(createTime(this.state.result + "." + this.state.buf));
          } else
            throw this.error(new TomlError("Unexpected character in datetime, expected period (.), minus (-), plus (+) or Z"));
        }
        parseTimeZoneOrFraction() {
          if (this.char === CHAR_PERIOD)
            this.consume(), this.next(this.parseDateTimeFraction);
          else if (this.char === CHAR_HYPHEN || this.char === CHAR_PLUS)
            this.consume(), this.next(this.parseTimeZoneHour);
          else {
            if (this.char === CHAR_Z)
              return this.consume(), this.return(createDateTime(this.state.result + this.state.buf));
            if (this.atEndOfWord())
              return this.returnNow(createDateTimeFloat(this.state.result + this.state.buf));
            throw this.error(new TomlError("Unexpected character in datetime, expected period (.), minus (-), plus (+) or Z"));
          }
        }
        parseDateTimeFraction() {
          if (isDigit(this.char))
            this.consume();
          else {
            if (this.state.buf.length === 1)
              throw this.error(new TomlError("Expected digit in milliseconds"));
            if (this.char === CHAR_HYPHEN || this.char === CHAR_PLUS)
              this.consume(), this.next(this.parseTimeZoneHour);
            else {
              if (this.char === CHAR_Z)
                return this.consume(), this.return(createDateTime(this.state.result + this.state.buf));
              if (this.atEndOfWord())
                return this.returnNow(createDateTimeFloat(this.state.result + this.state.buf));
              throw this.error(new TomlError("Unexpected character in datetime, expected period (.), minus (-), plus (+) or Z"));
            }
          }
        }
        parseTimeZoneHour() {
          if (isDigit(this.char)) {
            if (this.consume(), /\d\d$/.test(this.state.buf)) return this.next(this.parseTimeZoneSep);
          } else
            throw this.error(new TomlError("Unexpected character in datetime, expected digit"));
        }
        parseTimeZoneSep() {
          if (this.char === CHAR_COLON)
            this.consume(), this.next(this.parseTimeZoneMin);
          else
            throw this.error(new TomlError("Unexpected character in datetime, expected colon"));
        }
        parseTimeZoneMin() {
          if (isDigit(this.char)) {
            if (this.consume(), /\d\d$/.test(this.state.buf)) return this.return(createDateTime(this.state.result + this.state.buf));
          } else
            throw this.error(new TomlError("Unexpected character in datetime, expected digit"));
        }
        /* BOOLEAN */
        parseBoolean() {
          if (this.char === CHAR_t)
            return this.consume(), this.next(this.parseTrue_r);
          if (this.char === CHAR_f)
            return this.consume(), this.next(this.parseFalse_a);
        }
        parseTrue_r() {
          if (this.char === CHAR_r)
            return this.consume(), this.next(this.parseTrue_u);
          throw this.error(new TomlError("Invalid boolean, expected true or false"));
        }
        parseTrue_u() {
          if (this.char === CHAR_u)
            return this.consume(), this.next(this.parseTrue_e);
          throw this.error(new TomlError("Invalid boolean, expected true or false"));
        }
        parseTrue_e() {
          if (this.char === CHAR_e)
            return this.return(!0);
          throw this.error(new TomlError("Invalid boolean, expected true or false"));
        }
        parseFalse_a() {
          if (this.char === CHAR_a)
            return this.consume(), this.next(this.parseFalse_l);
          throw this.error(new TomlError("Invalid boolean, expected true or false"));
        }
        parseFalse_l() {
          if (this.char === CHAR_l)
            return this.consume(), this.next(this.parseFalse_s);
          throw this.error(new TomlError("Invalid boolean, expected true or false"));
        }
        parseFalse_s() {
          if (this.char === CHAR_s)
            return this.consume(), this.next(this.parseFalse_e);
          throw this.error(new TomlError("Invalid boolean, expected true or false"));
        }
        parseFalse_e() {
          if (this.char === CHAR_e)
            return this.return(!1);
          throw this.error(new TomlError("Invalid boolean, expected true or false"));
        }
        /* INLINE LISTS */
        parseInlineList() {
          if (this.char === CHAR_SP || this.char === CTRL_I || this.char === CTRL_M || this.char === CTRL_J)
            return null;
          if (this.char === Parser.END)
            throw this.error(new TomlError("Unterminated inline array"));
          return this.char === CHAR_NUM ? this.call(this.parseComment) : this.char === CHAR_RSQB ? this.return(this.state.resultArr || InlineList()) : this.callNow(this.parseValue, this.recordInlineListValue);
        }
        recordInlineListValue(value) {
          if (this.state.resultArr) {
            let listType = this.state.resultArr[_contentType], valueType = tomlType(value);
            if (listType !== valueType)
              throw this.error(new TomlError(`Inline lists must be a single type, not a mix of ${listType} and ${valueType}`));
          } else
            this.state.resultArr = InlineList(tomlType(value));
          return isFloat(value) || isInteger(value) ? this.state.resultArr.push(value.valueOf()) : this.state.resultArr.push(value), this.goto(this.parseInlineListNext);
        }
        parseInlineListNext() {
          if (this.char === CHAR_SP || this.char === CTRL_I || this.char === CTRL_M || this.char === CTRL_J)
            return null;
          if (this.char === CHAR_NUM)
            return this.call(this.parseComment);
          if (this.char === CHAR_COMMA)
            return this.next(this.parseInlineList);
          if (this.char === CHAR_RSQB)
            return this.goto(this.parseInlineList);
          throw this.error(new TomlError("Invalid character, expected whitespace, comma (,) or close bracket (])"));
        }
        /* INLINE TABLE */
        parseInlineTable() {
          if (this.char === CHAR_SP || this.char === CTRL_I)
            return null;
          if (this.char === Parser.END || this.char === CHAR_NUM || this.char === CTRL_J || this.char === CTRL_M)
            throw this.error(new TomlError("Unterminated inline array"));
          return this.char === CHAR_RCUB ? this.return(this.state.resultTable || InlineTable()) : (this.state.resultTable || (this.state.resultTable = InlineTable()), this.callNow(this.parseAssign, this.recordInlineTableValue));
        }
        recordInlineTableValue(kv) {
          let target = this.state.resultTable, finalKey = kv.key.pop();
          for (let kw of kv.key) {
            if (hasKey(target, kw) && (!isTable(target[kw]) || target[kw][_declared]))
              throw this.error(new TomlError("Can't redefine existing key"));
            target = target[kw] = target[kw] || Table();
          }
          if (hasKey(target, finalKey))
            throw this.error(new TomlError("Can't redefine existing key"));
          return isInteger(kv.value) || isFloat(kv.value) ? target[finalKey] = kv.value.valueOf() : target[finalKey] = kv.value, this.goto(this.parseInlineTableNext);
        }
        parseInlineTableNext() {
          if (this.char === CHAR_SP || this.char === CTRL_I)
            return null;
          if (this.char === Parser.END || this.char === CHAR_NUM || this.char === CTRL_J || this.char === CTRL_M)
            throw this.error(new TomlError("Unterminated inline array"));
          if (this.char === CHAR_COMMA)
            return this.next(this.parseInlineTable);
          if (this.char === CHAR_RCUB)
            return this.goto(this.parseInlineTable);
          throw this.error(new TomlError("Invalid character, expected whitespace, comma (,) or close bracket (])"));
        }
      }
      return TOMLParser;
    }
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse-pretty-error.js
var require_parse_pretty_error = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse-pretty-error.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = prettyError;
    function prettyError(err, buf) {
      if (err.pos == null || err.line == null) return err;
      let msg = err.message;
      if (msg += ` at row ${err.line + 1}, col ${err.col + 1}, pos ${err.pos}:
`, buf && buf.split) {
        let lines = buf.split(/\n/), lineNumWidth = String(Math.min(lines.length, err.line + 3)).length, linePadding = " ";
        for (; linePadding.length < lineNumWidth; ) linePadding += " ";
        for (let ii = Math.max(0, err.line - 1); ii < Math.min(lines.length, err.line + 2); ++ii) {
          let lineNum = String(ii + 1);
          if (lineNum.length < lineNumWidth && (lineNum = " " + lineNum), err.line === ii) {
            msg += lineNum + "> " + lines[ii] + `
`, msg += linePadding + "  ";
            for (let hh = 0; hh < err.col; ++hh)
              msg += " ";
            msg += `^
`;
          } else
            msg += lineNum + ": " + lines[ii] + `
`;
        }
      }
      return err.message = msg + `
`, err;
    }
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse-string.js
var require_parse_string = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse-string.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = parseString;
    var TOMLParser = require_toml_parser(), prettyError = require_parse_pretty_error();
    function parseString(str) {
      global.Buffer && global.Buffer.isBuffer(str) && (str = str.toString("utf8"));
      let parser = new TOMLParser();
      try {
        return parser.parse(str), parser.finish();
      } catch (err) {
        throw prettyError(err, str);
      }
    }
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse-async.js
var require_parse_async = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse-async.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = parseAsync;
    var TOMLParser = require_toml_parser(), prettyError = require_parse_pretty_error();
    function parseAsync(str, opts) {
      opts || (opts = {});
      let index = 0, blocksize = opts.blocksize || 40960, parser = new TOMLParser();
      return new Promise((resolve, reject) => {
        setImmediate(parseAsyncNext, index, blocksize, resolve, reject);
      });
      function parseAsyncNext(index2, blocksize2, resolve, reject) {
        if (index2 >= str.length)
          try {
            return resolve(parser.finish());
          } catch (err) {
            return reject(prettyError(err, str));
          }
        try {
          parser.parse(str.slice(index2, index2 + blocksize2)), setImmediate(parseAsyncNext, index2 + blocksize2, blocksize2, resolve, reject);
        } catch (err) {
          reject(prettyError(err, str));
        }
      }
    }
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse-stream.js
var require_parse_stream = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse-stream.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = parseStream;
    var stream = __require("stream"), TOMLParser = require_toml_parser();
    function parseStream(stm) {
      return stm ? parseReadable(stm) : parseTransform(stm);
    }
    function parseReadable(stm) {
      let parser = new TOMLParser();
      return stm.setEncoding("utf8"), new Promise((resolve, reject) => {
        let readable, ended = !1, errored = !1;
        function finish() {
          if (ended = !0, !readable)
            try {
              resolve(parser.finish());
            } catch (err) {
              reject(err);
            }
        }
        function error(err) {
          errored = !0, reject(err);
        }
        stm.once("end", finish), stm.once("error", error), readNext();
        function readNext() {
          readable = !0;
          let data;
          for (; (data = stm.read()) !== null; )
            try {
              parser.parse(data);
            } catch (err) {
              return error(err);
            }
          if (readable = !1, ended) return finish();
          errored || stm.once("readable", readNext);
        }
      });
    }
    function parseTransform() {
      let parser = new TOMLParser();
      return new stream.Transform({
        objectMode: !0,
        transform(chunk, encoding, cb) {
          try {
            parser.parse(chunk.toString(encoding));
          } catch (err) {
            this.emit("error", err);
          }
          cb();
        },
        flush(cb) {
          try {
            this.push(parser.finish());
          } catch (err) {
            this.emit("error", err);
          }
          cb();
        }
      });
    }
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse.js
var require_parse = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/parse.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = require_parse_string();
    module2.exports.async = require_parse_async();
    module2.exports.stream = require_parse_stream();
    module2.exports.prettyError = require_parse_pretty_error();
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/stringify.js
var require_stringify = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/stringify.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = stringify2;
    module2.exports.value = stringifyInline;
    function stringify2(obj) {
      if (obj === null) throw typeError("null");
      if (obj === void 0) throw typeError("undefined");
      if (typeof obj != "object") throw typeError(typeof obj);
      if (typeof obj.toJSON == "function" && (obj = obj.toJSON()), obj == null) return null;
      let type = tomlType2(obj);
      if (type !== "table") throw typeError(type);
      return stringifyObject("", "", obj);
    }
    function typeError(type) {
      return new Error("Can only stringify objects, not " + type);
    }
    function arrayOneTypeError() {
      return new Error("Array values can't have mixed types");
    }
    function getInlineKeys(obj) {
      return Object.keys(obj).filter((key) => isInline(obj[key]));
    }
    function getComplexKeys(obj) {
      return Object.keys(obj).filter((key) => !isInline(obj[key]));
    }
    function toJSON(obj) {
      let nobj = Array.isArray(obj) ? [] : Object.prototype.hasOwnProperty.call(obj, "__proto__") ? { ["__proto__"]: void 0 } : {};
      for (let prop of Object.keys(obj))
        obj[prop] && typeof obj[prop].toJSON == "function" && !("toISOString" in obj[prop]) ? nobj[prop] = obj[prop].toJSON() : nobj[prop] = obj[prop];
      return nobj;
    }
    function stringifyObject(prefix, indent, obj) {
      obj = toJSON(obj);
      var inlineKeys, complexKeys;
      inlineKeys = getInlineKeys(obj), complexKeys = getComplexKeys(obj);
      var result = [], inlineIndent = indent || "";
      inlineKeys.forEach((key) => {
        var type = tomlType2(obj[key]);
        type !== "undefined" && type !== "null" && result.push(inlineIndent + stringifyKey(key) + " = " + stringifyAnyInline(obj[key], !0));
      }), result.length > 0 && result.push("");
      var complexIndent = prefix && inlineKeys.length > 0 ? indent + "  " : "";
      return complexKeys.forEach((key) => {
        result.push(stringifyComplex(prefix, complexIndent, key, obj[key]));
      }), result.join(`
`);
    }
    function isInline(value) {
      switch (tomlType2(value)) {
        case "undefined":
        case "null":
        case "integer":
        case "nan":
        case "float":
        case "boolean":
        case "string":
        case "datetime":
          return !0;
        case "array":
          return value.length === 0 || tomlType2(value[0]) !== "table";
        case "table":
          return Object.keys(value).length === 0;
        /* istanbul ignore next */
        default:
          return !1;
      }
    }
    function tomlType2(value) {
      return value === void 0 ? "undefined" : value === null ? "null" : typeof value == "bigint" || Number.isInteger(value) && !Object.is(value, -0) ? "integer" : typeof value == "number" ? "float" : typeof value == "boolean" ? "boolean" : typeof value == "string" ? "string" : "toISOString" in value ? isNaN(value) ? "undefined" : "datetime" : Array.isArray(value) ? "array" : "table";
    }
    function stringifyKey(key) {
      var keyStr = String(key);
      return /^[-A-Za-z0-9_]+$/.test(keyStr) ? keyStr : stringifyBasicString(keyStr);
    }
    function stringifyBasicString(str) {
      return '"' + escapeString(str).replace(/"/g, '\\"') + '"';
    }
    function stringifyLiteralString(str) {
      return "'" + str + "'";
    }
    function numpad(num, str) {
      for (; str.length < num; ) str = "0" + str;
      return str;
    }
    function escapeString(str) {
      return str.replace(/\\/g, "\\\\").replace(/[\b]/g, "\\b").replace(/\t/g, "\\t").replace(/\n/g, "\\n").replace(/\f/g, "\\f").replace(/\r/g, "\\r").replace(/([\u0000-\u001f\u007f])/, (c) => "\\u" + numpad(4, c.codePointAt(0).toString(16)));
    }
    function stringifyMultilineString(str) {
      let escaped = str.split(/\n/).map((str2) => escapeString(str2).replace(/"(?="")/g, '\\"')).join(`
`);
      return escaped.slice(-1) === '"' && (escaped += `\\
`), `"""
` + escaped + '"""';
    }
    function stringifyAnyInline(value, multilineOk) {
      let type = tomlType2(value);
      return type === "string" && (multilineOk && /\n/.test(value) ? type = "string-multiline" : !/[\b\t\n\f\r']/.test(value) && /"/.test(value) && (type = "string-literal")), stringifyInline(value, type);
    }
    function stringifyInline(value, type) {
      switch (type || (type = tomlType2(value)), type) {
        case "string-multiline":
          return stringifyMultilineString(value);
        case "string":
          return stringifyBasicString(value);
        case "string-literal":
          return stringifyLiteralString(value);
        case "integer":
          return stringifyInteger(value);
        case "float":
          return stringifyFloat(value);
        case "boolean":
          return stringifyBoolean(value);
        case "datetime":
          return stringifyDatetime(value);
        case "array":
          return stringifyInlineArray(value.filter((_) => tomlType2(_) !== "null" && tomlType2(_) !== "undefined" && tomlType2(_) !== "nan"));
        case "table":
          return stringifyInlineTable(value);
        /* istanbul ignore next */
        default:
          throw typeError(type);
      }
    }
    function stringifyInteger(value) {
      return String(value).replace(/\B(?=(\d{3})+(?!\d))/g, "_");
    }
    function stringifyFloat(value) {
      if (value === 1 / 0)
        return "inf";
      if (value === -1 / 0)
        return "-inf";
      if (Object.is(value, NaN))
        return "nan";
      if (Object.is(value, -0))
        return "-0.0";
      var chunks = String(value).split("."), int = chunks[0], dec = chunks[1] || 0;
      return stringifyInteger(int) + "." + dec;
    }
    function stringifyBoolean(value) {
      return String(value);
    }
    function stringifyDatetime(value) {
      return value.toISOString();
    }
    function isNumber(type) {
      return type === "float" || type === "integer";
    }
    function arrayType(values) {
      var contentType = tomlType2(values[0]);
      return values.every((_) => tomlType2(_) === contentType) ? contentType : values.every((_) => isNumber(tomlType2(_))) ? "float" : "mixed";
    }
    function validateArray(values) {
      let type = arrayType(values);
      if (type === "mixed")
        throw arrayOneTypeError();
      return type;
    }
    function stringifyInlineArray(values) {
      values = toJSON(values);
      let type = validateArray(values);
      var result = "[", stringified = values.map((_) => stringifyInline(_, type));
      return stringified.join(", ").length > 60 || /\n/.test(stringified) ? result += `
  ` + stringified.join(`,
  `) + `
` : result += " " + stringified.join(", ") + (stringified.length > 0 ? " " : ""), result + "]";
    }
    function stringifyInlineTable(value) {
      value = toJSON(value);
      var result = [];
      return Object.keys(value).forEach((key) => {
        result.push(stringifyKey(key) + " = " + stringifyAnyInline(value[key], !1));
      }), "{ " + result.join(", ") + (result.length > 0 ? " " : "") + "}";
    }
    function stringifyComplex(prefix, indent, key, value) {
      var valueType = tomlType2(value);
      if (valueType === "array")
        return stringifyArrayOfTables(prefix, indent, key, value);
      if (valueType === "table")
        return stringifyComplexTable(prefix, indent, key, value);
      throw typeError(valueType);
    }
    function stringifyArrayOfTables(prefix, indent, key, values) {
      values = toJSON(values), validateArray(values);
      var firstValueType = tomlType2(values[0]);
      if (firstValueType !== "table") throw typeError(firstValueType);
      var fullKey = prefix + stringifyKey(key), result = "";
      return values.forEach((table) => {
        result.length > 0 && (result += `
`), result += indent + "[[" + fullKey + `]]
`, result += stringifyObject(fullKey + ".", indent, table);
      }), result;
    }
    function stringifyComplexTable(prefix, indent, key, value) {
      var fullKey = prefix + stringifyKey(key), result = "";
      return getInlineKeys(value).length > 0 && (result += indent + "[" + fullKey + `]
`), result + stringifyObject(fullKey + ".", indent, value);
    }
  }
});

// ../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/toml.js
var require_toml = __commonJS({
  "../../node_modules/.pnpm/@iarna+toml@2.2.5/node_modules/@iarna/toml/toml.js"(exports2) {
    "use strict";
    init_cjs_shims();
    exports2.parse = require_parse();
    exports2.stringify = require_stringify();
  }
});

// ../cli-kit/dist/public/node/base-command.js
init_cjs_shims();

// ../cli-kit/dist/public/node/environments.js
init_cjs_shims();

// ../cli-kit/dist/public/node/toml.js
init_cjs_shims();
var toml = __toESM(require_toml(), 1);
function decodeToml(input) {
  let normalizedInput = input.replace(/\r\n$/g, `
`);
  return toml.parse(normalizedInput);
}
function encodeToml(content) {
  return toml.stringify(content);
}

// ../cli-kit/dist/public/node/environments.js
function renderWarningIfNeeded(message, silent) {
  silent || renderWarning(message);
}
async function loadEnvironment(environmentName, fileName, options) {
  let filePath = await environmentFilePath(fileName, options);
  if (!filePath) {
    renderWarningIfNeeded({ body: "Environment file not found." }, options?.silent);
    return;
  }
  let environments = decodeToml(await readFile(filePath)).environments;
  if (!environments) {
    renderWarningIfNeeded({
      body: ["No environments found in", { command: filePath }, { char: "." }]
    }, options?.silent);
    return;
  }
  let environment = environments[environmentName];
  if (!environment) {
    renderWarningIfNeeded({
      body: ["Environment", { command: environmentName }, "not found."]
    }, options?.silent);
    return;
  }
  return await addSensitiveMetadata(() => ({
    environmentFlags: JSON.stringify(environment)
  })), environment;
}
async function environmentFilePath(fileName, options) {
  let basePath = options?.from && options.from !== "." ? options.from : cwd();
  return findPathUp(fileName, {
    cwd: basePath,
    type: "file"
  });
}

// ../cli-kit/dist/public/node/base-command.js
var import_core = __toESM(require_lib()), BaseCommand = class extends import_core.Command {
  // Replace markdown links to plain text like: "link label" (url)
  static descriptionWithoutMarkdown() {
    return (this.descriptionWithMarkdown ?? "").replace(/(\[)(.*?)(])(\()(.*?)(\))/gm, '"$2" ($5)');
  }
  static analyticsNameOverride() {
  }
  static analyticsStopCommand() {
  }
  async catch(error) {
    return error.skipOclifErrorHandling = !0, await errorHandler(error, this.config), import_core.Errors.handle(error);
  }
  async init() {
    return this.exitWithTimestampWhenEnvVariablePresent(), setCurrentCommandId(this.id ?? ""), isDevelopment() || await registerCleanBugsnagErrorsFromWithinPlugins(this.config), this.showNpmFlagWarning(), await showNotificationsIfNeeded(), super.init();
  }
  // NPM creates an environment variable for every flag passed to a script.
  // This function checks for the presence of any of the available CLI flags
  // and warns the user to use the `--` separator.
  showNpmFlagWarning() {
    let commandVariables = this.constructor;
    Object.keys(commandVariables.flags || {}).map((key) => `npm_config_${underscore(key).replace(/^no_/, "")}`).some((flag) => process.env[flag] !== void 0) && renderWarning({
      body: [
        "NPM scripts require an extra",
        { command: "--" },
        "separator to pass the flags. Example:",
        { command: "npm run dev -- --reset" }
      ]
    });
  }
  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
  exitWithTimestampWhenEnvVariablePresent() {
    isTruthy(process.env.SHOPIFY_CLI_ENV_STARTUP_PERFORMANCE_RUN) && (outputResult(`
      SHOPIFY_CLI_TIMESTAMP_START
      { "timestamp": ${Date.now()} }
      SHOPIFY_CLI_TIMESTAMP_END
      `), process.exit(0));
  }
  async parse(options, argv) {
    let result = await super.parse(options, argv);
    return result = await this.resultWithEnvironment(result, options, argv), await addFromParsedFlags(result.flags), { ...result, argv: result.argv };
  }
  environmentsFilename() {
  }
  failMissingNonTTYFlags(flags, requiredFlags) {
    terminalSupportsPrompting() || requiredFlags.forEach((name) => {
      if (!(name in flags))
        throw new AbortError(outputContent`Flag not specified:

${outputToken.cyan(name)}

This flag is required in non-interactive terminal environments, such as a CI environment, or when piping input from another process.`, "To resolve this, specify the option in the command, or run the command in an interactive environment such as your local terminal.");
    });
  }
  async resultWithEnvironment(originalResult, options, argv) {
    let flags = originalResult.flags, environmentsFileName = this.environmentsFilename();
    if (!environmentsFileName)
      return originalResult;
    let environmentFileExists = await environmentFilePath(environmentsFileName, { from: flags.path }), environments = [];
    flags.environment && (environments = Array.isArray(flags.environment) ? flags.environment : [flags.environment]);
    let environmentSpecified = environments.length > 0;
    if (!environmentFileExists && !environmentSpecified || environmentSpecified && environments.length > 1)
      return originalResult;
    let { environment, isDefaultEnvironment } = await this.loadEnvironmentForCommand(flags.path, environmentsFileName, environments[0]);
    if (!environment || isDefaultEnvironment && !commandSupportsFlag(options?.flags, "environment"))
      return originalResult;
    let noDefaultsResult = await super.parse(noDefaultsOptions(options), argv), result = await super.parse(options, [
      // Need to specify argv default because we're merging with argsFromEnvironment.
      ...argv ?? this.argv,
      ...argsFromEnvironment(environment, options, noDefaultsResult),
      ...isDefaultEnvironment ? ["--environment", "default"] : []
    ]);
    return reportEnvironmentApplication(noDefaultsResult.flags, result.flags, isDefaultEnvironment ? "default" : environments[0], environment), result;
  }
  /**
   * Tries to load an environment to forward to the command. If no environment
   * is specified it will try to load a default environment.
   */
  async loadEnvironmentForCommand(path, environmentsFileName, specifiedEnvironment) {
    return specifiedEnvironment ? { environment: await loadEnvironment(specifiedEnvironment, environmentsFileName, { from: path }), isDefaultEnvironment: !1 } : { environment: await loadEnvironment("default", environmentsFileName, { from: path, silent: !0 }), isDefaultEnvironment: !0 };
  }
};
BaseCommand.baseFlags = {};
async function addFromParsedFlags(flags) {
  await addPublicMetadata(() => ({
    cmd_all_verbose: flags.verbose,
    cmd_all_path_override: flags.path !== void 0,
    cmd_all_path_override_hash: flags.path === void 0 ? void 0 : hashString(flags.path)
  }));
}
function reportEnvironmentApplication(noDefaultsFlags, flagsWithEnvironments, environmentName, environment) {
  let changes = {};
  for (let [name, value] of Object.entries(flagsWithEnvironments)) {
    let userSpecifiedThisFlag = Object.prototype.hasOwnProperty.call(noDefaultsFlags, name), environmentContainsFlag = Object.prototype.hasOwnProperty.call(environment, name);
    if (!userSpecifiedThisFlag && environmentContainsFlag) {
      let valueToReport = name === "password" ? `********${value.substr(-4)}` : value;
      changes[name] = valueToReport;
    }
  }
  if (Object.keys(changes).length === 0)
    return;
  let items = Object.entries(changes).map(([name, value]) => `${name}: ${value}`);
  renderInfo({
    headline: ["Using applicable flags from", { userInput: environmentName }, "environment:"],
    body: [{ list: { items } }]
  });
}
function noDefaultsOptions(options) {
  return options?.flags ? {
    ...options,
    flags: Object.fromEntries(Object.entries(options.flags).map(([label, settings]) => {
      let copiedSettings = { ...settings };
      return delete copiedSettings.default, [label, copiedSettings];
    }))
  } : options;
}
function argsFromEnvironment(environment, options, noDefaultsResult) {
  let args = [];
  for (let [label, value] of Object.entries(environment)) {
    let flagIsRelevantToCommand = commandSupportsFlag(options?.flags, label), userSpecifiedThisFlag = noDefaultsResult.flags && Object.prototype.hasOwnProperty.call(noDefaultsResult.flags, label);
    if (flagIsRelevantToCommand && !userSpecifiedThisFlag)
      if (typeof value == "boolean")
        if (value)
          args.push(`--${label}`);
        else
          throw new AbortError(outputContent`Environments can only specify true for boolean flags. Attempted to set ${outputToken.yellow(label)} to false.`);
      else Array.isArray(value) ? value.forEach((element) => args.push(`--${label}`, `${element}`)) : args.push(`--${label}`, `${value}`);
  }
  return args;
}
function commandSupportsFlag(flags, flagName) {
  return !!flags && Object.prototype.hasOwnProperty.call(flags, flagName);
}
var base_command_default = BaseCommand;

export {
  decodeToml,
  encodeToml,
  loadEnvironment,
  noDefaultsOptions,
  base_command_default
};
//# sourceMappingURL=chunk-2IA24ROR.js.map
