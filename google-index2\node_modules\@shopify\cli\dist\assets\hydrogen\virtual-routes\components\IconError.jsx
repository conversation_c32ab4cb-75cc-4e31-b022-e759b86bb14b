import { jsx, jsxs } from "react/jsx-runtime";
const IconError = (props) => /* @__PURE__ */ jsxs(
  "svg",
  {
    width: 32,
    height: 32,
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...props,
    children: [
      /* @__PURE__ */ jsx("path", { fill: "#000", d: "M6 18h20v2H6zM30 8h2v16h-2zM0 8h2v16H0z" }),
      /* @__PURE__ */ jsx(
        "path",
        {
          fill: "#F63310",
          d: "M2 8h2v16H2zM4 6h2v4H4zM6 4h2v4H6zM8 2h2v4H8zM24 4h2v2h-2zM26 6h2v2h-2z"
        }
      ),
      /* @__PURE__ */ jsx(
        "path",
        {
          fill: "#74170B",
          d: "M26 24h2v2h-2zM28 22h2v2h-2zM24 26h2v2h-2zM22 28h2v2h-2z"
        }
      ),
      /* @__PURE__ */ jsx(
        "path",
        {
          fill: "#971C06",
          d: "M6 26h2v2H6zM4 24h2v2H4zM4 18h2v2H4zM26 18h2v2h-2zM26 12h2v2h-2z"
        }
      ),
      /* @__PURE__ */ jsx(
        "path",
        {
          fill: "#D62C0D",
          d: "M26 8h2v4h-2zM4 10h2v2H4zM8 6h18v2H8zM10 4h14v2H10zM6 8h20v2H6z"
        }
      ),
      /* @__PURE__ */ jsx("path", { fill: "#971C06", d: "M28 10h2v12h-2zM8 28h14v2H8z" }),
      /* @__PURE__ */ jsx("path", { fill: "#D62C0D", d: "M26 20h2v2h-2zM4 20h2v2H4z" }),
      /* @__PURE__ */ jsx("path", { fill: "#F63310", d: "M4 12h2v2H4z" }),
      /* @__PURE__ */ jsx("path", { fill: "#971C06", d: "M6 20h20v2H6z" }),
      /* @__PURE__ */ jsx("path", { fill: "#F63310", d: "M28 8h2v2h-2zM10 2h14v2H10zM6 10h20v2H6z" }),
      /* @__PURE__ */ jsx("path", { fill: "#D62C0D", d: "M4 22h24v2H4zM6 24h20v2H6zM8 26h16v2H8z" }),
      /* @__PURE__ */ jsx("path", { fill: "#000", d: "M6 12h20v2H6z" }),
      /* @__PURE__ */ jsx("path", { fill: "#fff", d: "M6 14h20v2H6zM6 16h2v2H6z" }),
      /* @__PURE__ */ jsx("path", { fill: "#ECEDEE", d: "M8 16h18v2H8z" }),
      /* @__PURE__ */ jsx(
        "path",
        {
          fill: "#000",
          d: "M4 14h2v4H4zM26 14h2v4h-2zM2 6h2v2H2zM4 4h2v2H4zM6 2h2v2H6zM24 2h2v2h-2zM26 4h2v2h-2zM26 26h2v2h-2zM24 28h2v2h-2zM6 28h2v2H6zM4 26h2v2H4zM2 24h2v2H2zM8 30h16v2H8zM28 6h2v2h-2zM28 24h2v2h-2zM8 0h16v2H8z"
        }
      )
    ]
  }
);
export {
  IconError
};
