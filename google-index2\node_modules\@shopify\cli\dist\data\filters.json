[{"category": "cart", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/cart", "raw_liquid": "{{ cart | item_count_for_variant: 39888235757633 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the total item count for a specified variant in the [`cart`](https://shopify.dev/docs/api/liquid/objects/cart) object.", "syntax": "cart | item_count_for_variant: {variant_id}", "name": "item_count_for_variant"}, {"category": "cart", "deprecated": false, "deprecation_reason": "", "description": "Accepts the following object types:\n\n- `product`\n- `variant`", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "line_item"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/bloodroot-whole", "raw_liquid": "{% assign product = all_products['bloodroot-whole'] %}\n{% assign line_items = cart | line_items_for: product %}\n\nTotal cart quantity for product: {{ line_items | sum: 'quantity' }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "", "description": "", "syntax": "", "path": "/products/bloodroot-whole", "raw_liquid": "{% assign product = all_products['bloodroot-whole'] %}\n{% assign variant = product.variants.first %}\n{% assign line_items = cart | line_items_for: variant %}\n\nTotal cart quantity for variant: {{ line_items | sum: 'quantity' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the subset of [`cart`](https://shopify.dev/docs/api/liquid/objects/cart) line items that include a specified product or variant.", "syntax": "cart | line_items_for: object", "name": "line_items_for"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "&lt;aside class=\"note beta\"&gt;\n  &lt;h4&gt;Developer preview&lt;/h4&gt;\n  &lt;p&gt;Style settings are currently only available as part of the &lt;a href=\"/storefronts/themes/architecture/blocks/theme-blocks/developer-preview\"&gt;Theme blocks developer preview&lt;/a&gt;.&lt;/p&gt;\n&lt;/aside&gt;", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ settings.layout | class_list }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates the list of style classes for a [style setting](/storefronts/themes/architecture/settings/style-settings) or a collection of settings.", "syntax": "settings.layout | class_list", "name": "class_list"}, {"category": "collection", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "attribute [string] You can specify the value of supported [HTML attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes).", "name": "HTML", "positional": true, "required": false, "types": []}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'Health' | link_to_type }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "HTML attributes", "description": "You can specify [HTML attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#attributes) by including a parameter that matches the attribute name, and the desired value.\n", "syntax": "string | link_to_type: attribute: string", "path": "/", "raw_liquid": "{{ 'Health' | link_to_type: class: 'link-class' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;a&gt;` tag with an `href` attribute linking to a [collection page](https://shopify.dev/docs/storefronts/themes/architecture/templates/collection) that lists all products of the given\nproduct type.", "syntax": "string | link_to_type", "name": "link_to_type"}, {"category": "collection", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "attribute [string] [HTML attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes) for the link, passed as `attribute: value`.", "name": "HTML", "positional": true, "required": false, "types": []}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ \"<PERSON><PERSON>'s Potent Potions\" | link_to_vendor }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "HTML attributes", "description": "You can specify [HTML attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#attributes) by including a parameter that matches the attribute name, and the desired value.\n", "syntax": "string | link_to_vendor: attribute: string", "path": "/", "raw_liquid": "{{ \"<PERSON><PERSON>'s Potent Potions\" | link_to_vendor: class: 'link-class' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;a&gt;` tag with an `href` attribute linking to a [collection page](https://shopify.dev/docs/storefronts/themes/architecture/templates/collection) that lists all products of a given\nproduct vendor.", "syntax": "string | link_to_vendor", "name": "link_to_vendor"}, {"category": "collection", "deprecated": false, "deprecation_reason": "", "description": "Accepts the following values:\n\n- `manual` (as defined in the [collection settings](https://help.shopify.com/manual/products/collections/collection-layout#change-the-sort-order-for-the-products-in-a-collection))\n- `best-selling`\n- `title-ascending`\n- `title-descending`\n- `price-ascending`\n- `price-descending`\n- `created-ascending`\n- `created-descending`\n\n&gt; Tip:\n&gt; You can append the `sort_by` filter to the [`url_for_type`](/docs/api/liquid/filters/url_for_type)\n&gt; and [`url_for_vendor`](/docs/api/liquid/filters/url_for_vendor) filters.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{{ collection.url | sort_by: 'best-selling' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates a collection URL with the provided `sort_by` parameter appended.\nThis filter must be applied to the object property [`collection.url`](https://shopify.dev/docs/api/liquid/objects/collection#collection-url).", "syntax": "string | sort_by: string", "name": "sort_by"}, {"category": "collection", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'health' | url_for_type }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates a URL for a [collection page](https://shopify.dev/docs/storefronts/themes/architecture/templates/collection) that lists all products of the given product type.", "syntax": "string | url_for_type", "name": "url_for_type"}, {"category": "collection", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ \"<PERSON><PERSON>'s Potent Potions\" | url_for_vendor }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates a URL for a [collection page](https://shopify.dev/docs/storefronts/themes/architecture/templates/collection) that lists all products from the given product vendor.", "syntax": "string | url_for_vendor", "name": "url_for_vendor"}, {"category": "collection", "deprecated": false, "deprecation_reason": "", "description": "When the collection context is included, you can access the associated [`collection` object](/docs/api/liquid/objects/collection)\nin the [product template](/themes/architecture/templates/product).\n\n&gt; Caution:\n&gt; Because a standard product page and a product page in the context of a collection have the same content on separate\n&gt; URLs, you should consider the SEO implications of using the `within` filter.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{%- assign collection_product = collection.products.first -%}\n\n{{ collection_product.url | within: collection }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates a product URL within the context of the provided collection.", "syntax": "string | within: collection", "name": "within"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "&gt; Tip:\n&gt; For accessibility best practices, it's recommended to have a minimum brightness difference of 125.", "parameters": [{"description": "A color to compare with the provided color.", "name": "color", "positional": true, "required": true, "types": ["string"]}], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#E800B0' | brightness_difference: '#FECEE9' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Calculates the [perceived brightness difference](https://www.w3.org/WAI/ER/WD-AERT/#color-contrast) between two colors.", "syntax": "string | brightness_difference: string", "name": "brightness_difference"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_brightness }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Calculates the [perceived brightness](https://www.w3.org/WAI/ER/WD-AERT/#color-contrast) of a given color.", "syntax": "string | color_brightness", "name": "color_brightness"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "The order in which you specify the colors doesn't matter.\n\n&gt; Tip:\n&gt; For accessibility best practices, the\n&gt; [WCAG 2.0 level AA](https://www.w3.org/WAI/WCAG21/quickref/?versions=2.0#qr-visual-audio-contrast-contrast) requires a\n&gt; minimum contrast ratio of 4.5:1 for normal text, and 3:1 for large text. [Level AAA](https://www.w3.org/WAI/WCAG21/quickref/?versions=2.0#qr-visual-audio-contrast7)\n&gt; requires a minimum contrast ratio of 7:1 for normal text, and 4.5:1 for large text.", "parameters": [{"description": "A color to compare with the provided color.", "name": "color", "positional": true, "required": true, "types": ["string"]}], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#E800B0' | color_contrast: '#D9D8FF' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Calculates the contrast ratio between two colors and returns the ratio's numerator. The ratio's denominator, which isn't\nreturned, is always 1. For example, with a contrast ratio of 3.5:1, this filter returns 3.5.", "syntax": "string | color_contrast: string", "name": "color_contrast"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The amount to darken the provided color by.", "name": "percent", "positional": true, "required": true, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_darken: 30 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Darkens a given color by a specific percentage. The percentage must be between 0 and 100.", "syntax": "string | color_darken: number", "name": "color_darken"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The amount to desaturate the provided color by.", "name": "percent", "positional": true, "required": true, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_desaturate: 30 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Desaturates a given color by a specific percentage. The percentage must be between 0 and 100.", "syntax": "string | color_desaturate: number", "name": "color_desaturate"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "&gt; Tip:\n&gt; For accessibility best practices, it's recommended to have a minimum color difference of 500.", "parameters": [{"description": "A color to compare with the provided color.", "name": "color", "positional": true, "required": true, "types": ["string"]}], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#720955' | color_difference: '#FFF3F9' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Calculates the [color difference](https://www.w3.org/WAI/ER/WD-AERT/#color-contrast) between two colors.", "syntax": "string | color_difference: string", "name": "color_difference"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "Accepts the following color components:\n\n- `alpha`\n- `red`\n- `green`\n- `blue`\n- `hue`\n- `saturation`\n- `lightness`", "parameters": [{"description": "The color component that you want to extract.", "name": "color_component", "positional": true, "required": true, "types": ["string"]}], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_extract: 'red' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Extracts a specific color component from a given color.", "syntax": "string | color_extract: string", "name": "color_extract"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The amount to lighten the provided color by.", "name": "percent", "positional": true, "required": true, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_lighten: 30 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Lightens a given color by a specific percentage. The percentage must be between 0 and 100.", "syntax": "string | color_lighten: number", "name": "color_lighten"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "&gt; Tip:\n&gt; A percentage factor of 100 returns the color being filtered. A percentage factor of 0 returns the color\n&gt; supplied to the filter.", "parameters": [{"description": "A color to mix with the provided color.", "name": "color", "positional": true, "required": true, "types": ["string"]}, {"description": "The percentage amount to mix the colors by.", "name": "percent", "positional": true, "required": true, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#E800B0' | color_mix: '#00936F', 50 }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "", "description": "If one input has an alpha component, but the other does not, an alpha component of 1.0 will be assumed for the input without an alpha component.\n", "syntax": "", "path": "/", "raw_liquid": "{{ 'rgba(232, 0, 176, 0.75)' | color_mix: '#00936F', 50 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Blends two colors together by a specific percentage factor. The percentage must be between 0 and 100.", "syntax": "string | color_mix: string, number", "name": "color_mix"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "The following table outlines valid color components, and the value range for their modifications:\n\n| Component | Value range |\n| --- | --- |\n| &lt;ul&gt;&lt;li&gt;`red`&lt;/li&gt;&lt;li&gt;`green`&lt;/li&gt;&lt;li&gt;`blue`&lt;/li&gt;&lt;/ul&gt; | An integer between 0 and 255 |\n| `alpha` | A decimal between 0 and 1 |\n| `hue` | An integer between 0 and 360 |\n| &lt;ul&gt;&lt;li&gt;`saturation`&lt;li&gt;`lightness`&lt;/li&gt;&lt;/ul&gt; | An integer between 0 and 100 |", "parameters": [{"description": "The color component that you want to modify.", "name": "color_component", "positional": true, "required": true, "types": ["string"]}, {"description": "The amount to modify the supplied color component by.", "name": "modification_value", "positional": true, "required": true, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_modify: 'red', 255 }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "", "description": "The format of the modified color depends on the component being modified. For example, if you modify the `alpha` component of a color in hexadecimal format, then the modified color will be in `rgba()` format.\n", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_modify: 'alpha', 0.85 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Modifies a specific color component of a given color by a specific amount.", "syntax": "string | color_modify: string, number", "name": "color_modify"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The amount to saturate the provided color by.", "name": "percent", "positional": true, "required": true, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_saturate: 30 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Saturates a given color by a specific percentage. The percentage must be between 0 and 100.", "syntax": "string | color_saturate: number", "name": "color_saturate"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "Because colors are converted to `hex6` format, if a color with an alpha component is provided, then the alpha component\nis excluded from the output.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'rgb(234, 90, 185)' | color_to_hex }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a CSS color string to hexadecimal format (`hex6`).", "syntax": "string | color_to_hex", "name": "color_to_hex"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "If a color with an alpha component is provided, the color is converted to `HSLA` format.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_to_hsl }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a CSS color string to `HSL` format.", "syntax": "string | color_to_hsl", "name": "color_to_hsl"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_to_oklch }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a CSS color string to `OKLCH` format.", "syntax": "string | color_to_oklch", "name": "color_to_oklch"}, {"category": "color", "deprecated": false, "deprecation_reason": "", "description": "If a color with an alpha component is provided, then the color is converted to `RGBA` format.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | color_to_rgb }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a CSS color string to `RGB` format.", "syntax": "string | color_to_rgb", "name": "color_to_rgb"}, {"category": "color", "deprecated": true, "deprecation_reason": "The `hex_to_rgba` filter has been replaced by [`color_to_rgb`](/docs/api/liquid/filters/color_to_rgb) and\n[`color_modify`](/docs/api/liquid/filters/color_modify).", "description": "", "parameters": [{"description": "The desired alpha value.", "name": "alpha", "positional": true, "required": false, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '#EA5AB9' | hex_to_rgba }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "alpha", "description": "The default `alpha` value is 1.0. However, you can specify a decimal value between 0.0 and 1.0.", "syntax": "string | hex_to_rgba: number", "path": "/", "raw_liquid": "{{ '#EA5AB9' | hex_to_rgba: 0.5 }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Converts a CSS color string from  hexadecimal format to `RGBA` format. Shorthand hexadecimal formatting (`hex3`) is also accepted.", "syntax": "string | hex_to_rgba", "name": "hex_to_rgba"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "The secret key for the message is supplied as a parameter to the filter.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{%- assign secret_potion = 'Polyjuice' | hmac_sha1: '<PERSON><PERSON>' -%}\n\nMy secret potion: {{ secret_potion }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string into an SHA-1 hash using a hash message authentication code (HMAC).", "syntax": "string | hmac_sha1: string", "name": "hmac_sha1"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "The secret key for the message is supplied as a parameter to the filter.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{%- assign secret_potion = 'Polyjuice' | hmac_sha256: '<PERSON>ina' -%}\n\nMy secret potion: {{ secret_potion }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string into an SHA-256 hash using a hash message authentication code (HMAC).", "syntax": "string | hmac_sha256: string", "name": "hmac_sha256"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '' | md5 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string into an MD5 hash.", "syntax": "string | md5", "name": "md5"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{%- assign secret_potion = 'Polyjuice' | sha1 -%}\n\nMy secret potion: {{ secret_potion }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string into an SHA-1 hash.", "syntax": "string | sha1: string", "name": "sha1"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{%- assign secret_potion = 'Polyjuice' | sha256 -%}\n\nMy secret potion: {{ secret_potion }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string into an SHA-256 hash.", "syntax": "string | sha256: string", "name": "sha256"}, {"category": "localization", "deprecated": true, "deprecation_reason": "Deprecated without a direct replacement because the [currency form](/docs/api/liquid/tags/form#form-currency) has also been\ndeprecated. The currency form was replaced by the [localization form](/docs/api/liquid/tags/form#form-localization). Refer to\nthis guide which explains [how to create a country selector](/docs/themes/markets/multiple-currencies-languages#implementing-country-and-language-selectors)\nusing the localization form.", "description": "The `currency_selector` filter must be applied to the [`form` object](/docs/api/liquid/objects/form) within a\n[currency form](/docs/api/liquid/tags/form#form-currency).", "parameters": [{"description": "The desired `class` attribute.", "name": "class", "positional": false, "required": false, "types": ["string"]}, {"description": "The desired `id` attribute.", "name": "id", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{% form 'currency' %}\n  {{ form | currency_selector }}\n{% endform %}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "class", "description": "Specify the `class` attribute of the `&lt;select&gt;` element.", "syntax": "form | currency_selector: class: string", "path": "/", "raw_liquid": "{% form 'currency' %}\n  {{ form | currency_selector: class: 'custom-class' }}\n{% endform %}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "id", "description": "Specify the `id` attribute of the `&lt;select&gt;` element.", "syntax": "form | currency_selector: id: string", "path": "/", "raw_liquid": "{% form 'currency' %}\n  {{ form | currency_selector: id: 'custom-id' }}\n{% endform %}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;select&gt;` element with an option for each currency available on the store.", "syntax": "form | currency_selector", "name": "currency_selector"}, {"category": "customer", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'Log in' | customer_login_link }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML link to the customer login page.", "syntax": "string | customer_login_link", "name": "customer_login_link"}, {"category": "customer", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'Log out' | customer_logout_link }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML link to log the customer out of their account and redirect to the homepage.", "syntax": "string | customer_logout_link", "name": "customer_logout_link"}, {"category": "customer", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'Create an account' | customer_register_link }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML link to the customer registration page.", "syntax": "string | customer_register_link", "name": "customer_register_link"}, {"category": "format", "deprecated": false, "deprecation_reason": "", "description": "The `date` filter accepts the same parameters as Ruby's strftime method for formatting the date. For a list of shorthand\nformats, refer to the [Ruby documentation](https://ruby-doc.org/core-3.1.1/Time.html#method-i-strftime) or\n[strftime reference and sandbox](http://www.strfti.me/).", "parameters": [{"description": "The desired date format.", "name": "format", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.created_at | date: '%B %d, %Y' }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "The current date", "description": "You can apply the `date` filter to the keywords `'now'` and `'today'` to output the current timestamp.\n\n&gt; Note:\n&gt; The timestamp will reflect the time that the Liquid was last rendered. Because of this, the timestamp might not be updated for every page view, depending on the context and caching.\n", "syntax": "", "path": "/", "raw_liquid": "{{ 'now' | date: '%B %d, %Y' }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "format", "description": "Specify a locale-aware date format. You can use the following formats:\n\n- `abbreviated_date`\n- `basic`\n- `date`\n- `date_at_time`\n- `default`\n- `on_date`\n- `short` (deprecated)\n- `long` (deprecated)\n\n&gt; Note:\n&gt; You can also [define custom formats](/docs/api/liquid/filters/date-setting-format-options-in-locale-files) in your theme's locale files.\n", "syntax": "string | date: format: string", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.created_at | date: format: 'abbreviated_date' }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "Setting format options in locale files", "description": "You can define custom date formats in your [theme's storefront locale files](/themes/architecture/locales/storefront-locale-files). These custom formats should be included in a `date_formats` category:\n\n```json\n\"date_formats\": {\n  \"month_day_year\": \"%B %d, %Y\"\n}\n```\n", "syntax": "", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.created_at | date: format: 'month_day_year' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Converts a timestamp into another date format.", "syntax": "string | date: string", "name": "date"}, {"category": "font", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "[string] Customize the [`font_display` property](https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display) of the `@font-face` declaration.", "name": "font_display", "positional": false, "required": false, "types": []}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ settings.type_header_font | font_face }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "font_display", "description": "You can include an optional parameter to specify the [`font_display` property](https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display) of the `@font_face` declaration.\n", "syntax": "font | font_face: font_display: string", "path": "/", "raw_liquid": "{{ settings.type_header_font | font_face: font_display: 'swap' }}", "parameter": true, "display_type": "text", "show_data_tab": false}], "summary": "Generates a CSS [`@font_face` declaration](https://developer.mozilla.org/en-US/docs/Web/CSS/%40font-face) to load the provided font.", "syntax": "font | font_face", "name": "font_face"}, {"category": "font", "deprecated": false, "deprecation_reason": "", "description": "The `font_modify` filter requires two parameters. The first indicates which property should be modified and the second is\neither the new value, or modification amount, for that property.\n\n&gt; Tip:\n&gt; You can access every variant of the chosen font's family by using [`font.variants`](/docs/api/liquid/objects/font#font-variants).\n&gt; However, you can more easily access specific styles and weights by using the `font_modify` filter.\n\nThe following table outlines the valid font properties and modification values:\n\n&lt;table&gt;\n  &lt;thead&gt;\n    &lt;th&gt;Property&lt;/th&gt;\n    &lt;th&gt;Modification value&lt;/th&gt;\n    &lt;th&gt;Output&lt;/th&gt;\n  &lt;/thead&gt;\n  &lt;tbody&gt;\n    &lt;tr&gt;\n      &lt;td rowspan=3&gt;&lt;code&gt;style&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;&lt;code&gt;normal&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;Returns the normal variant of the same weight, if it exists.&lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td&gt;&lt;code&gt;italic&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;Returns the italic variant of the same weight, if it exists.&lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td&gt;&lt;code&gt;oblique&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;\n        &lt;p&gt;Returns the oblique variant of the same weight, if it exists.&lt;/p&gt;\n        &lt;p&gt;Oblique variants are similar to italic variants in appearance. All Shopify fonts have only oblique or italic variants, not both.&lt;/p&gt;\n      &lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td rowspan=7&gt;&lt;code&gt;weight&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;&lt;code&gt;100&lt;/code&gt; &amp;rarr; &lt;code&gt;900&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;Returns a variant of the same style with the given weight, if it exists.&lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td&gt;&lt;code&gt;normal&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;Returns a variant of the same style with a weight of &lt;code&gt;400&lt;/code&gt;, if it exists.&lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td&gt;&lt;code&gt;bold&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;Returns a variant of the same style with a weight of &lt;code&gt;700&lt;/code&gt;, if it exists.&lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td&gt;&lt;code&gt;+100&lt;/code&gt; &amp;rarr; &lt;code&gt;+900&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;\n        &lt;p&gt;Returns a variant of the same style with a weight incremented by the given value, if it exists.&lt;/p&gt;\n        &lt;p&gt;For example, if a font has a weight of &lt;code&gt;400&lt;/code&gt;, then using &lt;code&gt;+100&lt;/code&gt; would return the font with a weight of &lt;code&gt;500&lt;/code&gt;.&lt;/p&gt;\n      &lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td&gt;&lt;code&gt;-100&lt;/code&gt; &amp;rarr; &lt;code&gt;-900&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;\n        &lt;p&gt;Returns a variant of the same style with a weight decremented by the given value, if it exists.&lt;/p&gt;\n        &lt;p&gt;For example, if a font has a weight of &lt;code&gt;400&lt;/code&gt;, then using &lt;code&gt;-100&lt;/code&gt; would return the font with a weight of &lt;code&gt;300&lt;/code&gt;.&lt;/p&gt;\n      &lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td&gt;&lt;code&gt;lighter&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;Returns a lighter variant of the same style by applying the rules used by the &lt;a href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight#Meaning_of_relative_weights\"&gt;CSS &lt;code&gt;font-weight&lt;/code&gt; property&lt;/a&gt; and browser &lt;a href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight#Fallback_weights\"&gt;fallback weights&lt;/a&gt;, if it exists.&lt;/td&gt;\n    &lt;/tr&gt;\n    &lt;tr&gt;\n      &lt;td&gt;&lt;code&gt;bolder&lt;/code&gt;&lt;/td&gt;\n      &lt;td&gt;Returns a bolder variant of the same style by applying the rules used by the &lt;a href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight#Meaning_of_relative_weights\"&gt;CSS &lt;code&gt;font-weight&lt;/code&gt; property&lt;/a&gt; and browser &lt;a href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight#Fallback_weights\"&gt;fallback weights&lt;/a&gt;, if it exists.&lt;/td&gt;\n    &lt;/tr&gt;\n  &lt;/tbody&gt;\n&lt;/table&gt;", "parameters": [{"description": "Font property to modify", "name": "property", "positional": true, "required": true, "types": ["string"]}, {"description": "The new value for the associated property.", "name": "value", "positional": true, "required": true, "types": ["string"]}], "return_type": [{"type": "font", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{%- assign bold_font = settings.type_body_font | font_modify: 'weight', 'bold' -%}\n\nh2 {\n  font-weight: {{ bold_font.weight }};\n}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "Non-existent font variants", "description": "If the `font_modify` filter tries to create a font variant that doesn't exist, then it returns `nil`. To handle this, you can either assign a fallback value with the [`default` filter](/docs/api/liquid/filters/default), or check for `nil` before using the variant.\n", "syntax": "", "path": "/", "raw_liquid": "{%- assign bold_font = settings.type_body_font | font_modify: 'weight', 'bold' -%}\n{%- assign italic_font = settings.type_body_font | font_modify: 'style', 'italic' -%}\n{%- assign heavy_font = settings.type_body_font | font_modify: 'weight', '900' | default: bold_font -%}\n{%- assign oblique_font = settings.type_body_font | font_modify: 'style', 'oblique' | default: italic_font -%}\n\nh2 {\n  font-style: {{ heavy_font.weight }};\n}\n\n.italic {\n  {% if oblique_font -%}\n    font-style: {{ oblique_font.style }};\n  {%- else -%}\n    font-style: {{ italic_font.style }};\n  {%- endif %}\n}", "parameter": false, "display_type": "text", "show_data_tab": false}], "summary": "Modifies a specific property of a given font.", "syntax": "font | font_modify: string, string", "name": "font_modify"}, {"category": "font", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "Choose to return the URL for the font in `woff` format, instead of `woff2`.", "name": "font_type", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ settings.type_header_font | font_url }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "woff format", "description": "By default, the `font_url` filter returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for the font in `woff2` format. However, you can also choose `woff` format.\n", "syntax": "font | font_url: string", "path": "/", "raw_liquid": "{{ settings.type_header_font | font_url: 'woff' }}", "parameter": true, "display_type": "text", "show_data_tab": false}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for the provided font in `woff2` format.", "syntax": "font | font_url", "name": "font_url"}, {"category": "default", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [], "summary": "Generates default error messages for each possible value of [`form.errors`](/docs/themes/liquid/reference/objects/form#form-errors).", "syntax": "string | default_errors", "name": "default_errors"}, {"category": "payment", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; You can't render accelerated checkout buttons through AJAX requests, including those through the\n&gt; [Section Rendering API](/api/section-rendering). The accelerated checkout buttons are added by JavaScript included\n&gt; by Shopify through the [`content_for_header`](/docs/api/liquid/objects/content_for_header) object, which only runs on\n&gt; the initial page load.\n&gt;\n&gt; For guidance on styling or event tracking of accelerated checkout buttons, refer to [accelerated checkout](/docs/storefronts/themes/pricing-payments/accelerated-checkout/).", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{% form 'product', product %}\n  {{ form | payment_button }}\n{% endform %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML container to host [accelerated checkout buttons](https://help.shopify.com/manual/online-store/dynamic-checkout)\nfor a product. The `payment_button` filter must be used on the `form` object within a [product form](/docs/api/liquid/tags/form#form-product).", "syntax": "form | payment_button", "name": "payment_button"}, {"category": "payment", "deprecated": false, "deprecation_reason": "", "description": "The `payment_terms` filter must be used on the `form` object within a [product form](/docs/api/liquid/tags/form#form-product) or\n[cart form](/docs/api/liquid/tags/form#form-cart).\n\n```liquid\n{% form 'product', product %}\n  {{ form | payment_terms }}\n{% endform %}\n```\n\n```liquid\n{% form 'cart', cart %}\n  {{ form | payment_terms }}\n{% endform %}\n```", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [], "summary": "Generates the HTML for the [Shop Pay Installments banner](/themes/pricing-payments/installments).", "syntax": "form | payment_terms", "name": "payment_terms"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "The `time_tag` filter accepts the same parameters as Ruby's strftime method for formatting the date. For a list of shorthand\nformats, refer to the [Ruby documentation](https://ruby-doc.org/core-3.1.1/Time.html#method-i-strftime) or\n[strftime reference and sandbox](http://www.strfti.me/).", "parameters": [{"description": "The desired date format.", "name": "format", "positional": false, "required": false, "types": ["string"]}, {"description": "The format for the datetime attribute of the time element.", "name": "datetime", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.created_at | time_tag: '%B %d, %Y' }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "format", "description": "Specify a locale-aware date format. Accepts the following values:\n\n- `abbreviated_date`\n- `basic`\n- `date`\n- `date_at_time`\n- `default`\n- `on_date`\n- `short` (deprecated)\n- `long` (deprecated)\n\n&gt; Note:\n&gt; You can also [define custom formats](/docs/api/liquid/filters/date-setting-format-options-in-locale-files) in your theme's locale files.\n", "syntax": "string | time_tag: format: string", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.created_at | time_tag: format: 'abbreviated_date' }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "datetime", "description": "By default, the value of the `datetime` attribute of the `&lt;time&gt;` tag is formatted as `YYYY-MM-DDThh:mm:ssTZD`. However, you can specify a custom format with [strftime shorthand formats](https://ruby-doc.org/core-3.1.2/Time.html#method-i-strftime).\n", "syntax": "string | time_tag: datetime: string", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.created_at | time_tag: '%B %d, %Y', datetime: '%Y-%m-%d' }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "Setting format options in locale files", "description": "You can define custom date formats in your [theme's storefront locale files](/themes/architecture/locales/storefront-locale-files). These custom formats should be included in a `date_formats` category:\n\n```json\n\"date_formats\": {\n  \"month_day_year\": \"%B %d, %Y\"\n}\n```\n", "syntax": "", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.created_at | time_tag: format: 'month_day_year' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Converts a timestamp into an HTML `&lt;time&gt;` tag.", "syntax": "string | time_tag: string", "name": "time_tag"}, {"category": "localization", "deprecated": false, "deprecation_reason": "", "description": "The `translate` filter has an alias of `t`, which is more commonly used.\n\n&gt; Tip:\n&gt; To learn more about using the `t` filter, refer to [storefront locale file usage](/themes/architecture/locales/storefront-locale-files#usage)\n&gt; or [schema locale file usage](/themes/architecture/locales/schema-locale-files#usage).", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "", "raw_liquid": "", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Section locales vs. theme locales", "description": "The `t` filter can also reference keys defined in the [`locales` object](/themes/architecture/sections/section-schema#locales) of section file's `schema` tag. Content that you put in the `schema` under the `locales` object is only accessible to that section. This is useful if you need to make a standalone section that you want to share between themes.\n\nContent that is global to a theme should be placed in the theme's `locales` directory. For example, you could include the expression \"See more\" in your `locales` directory to create a single translation. You could then use the translation in a blog post and on the product details page.\n\n&gt; Note:\n&gt; Translations in the section's `schema` tag that aren't part of the `locales` object are used for merchant-facing text shown in the theme editor. These translations don't use the `t` filter.\n", "syntax": "", "path": "", "raw_liquid": "", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns a string of translated text for a given translation key from a [locale file](/themes/architecture/locales).", "syntax": "string | t", "name": "translate"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; The asset size must be less than 15KB to be inlined.\n&gt;", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'icon.svg' | inline_asset_content }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Outputs the content of an asset inline in the template. The asset must be either a SVG, JS, or CSS file.", "syntax": "asset_name | inline_asset_content", "name": "inline_asset_content"}, {"category": "format", "deprecated": false, "deprecation_reason": "", "description": "&gt; Tip:\n&gt; When using the JSON output in JavaScript, you don't need to wrap it in quotes because the `json` filter includes them.\n&gt; The `json` filter also escapes any quotes inside the output.\n\n#### Product inventory\n\nWhen applied to a [`product` object](/docs/api/liquid/objects/product) on any Shopify store created after December 5, 2017, the\n`json` filter doesn't output values for the `inventory_quantity` and `inventory_policy` properties of any associated\n[variants](/docs/api/liquid/objects/variant). These properties are excluded to help prevent bots and crawlers from retrieving\ninventory quantities for stores to which they aren't granted access.\n\nIf you need inventory information, you can access it through individual variants.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/crocodile-tears", "raw_liquid": "{{ product | json }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string, or object, into JSON format.", "syntax": "variable | json", "name": "json"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ -3 | abs }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the absolute value of a number.", "syntax": "number | abs", "name": "abs"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{%-  assign path = product.url -%}\n\n{{ request.origin | append: path }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Adds a given string to the end of a string.", "syntax": "string | append: string", "name": "append"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 4 | at_least: 5 }}\n{{ 4 | at_least: 3 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Limits a number to a minimum value.", "syntax": "number | at_least", "name": "at_least"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 6 | at_most: 5 }}\n{{ 4 | at_most: 5 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Limits a number to a maximum value.", "syntax": "number | at_most", "name": "at_most"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'b25lIHR3byB0aHJlZQ==' | base64_decode }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Decodes a string in [Base64 format](https://developer.mozilla.org/en-US/docs/Glossary/Base64).", "syntax": "string | base64_decode", "name": "base64_decode"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'one two three' | base64_encode }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Encodes a string to [Base64 format](https://developer.mozilla.org/en-US/docs/Glossary/Base64).", "syntax": "string | base64_encode", "name": "base64_encode"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'b25lIHR3byB0aHJlZQ==' | base64_url_safe_decode }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Decodes a string in URL-safe [Base64 format](https://developer.mozilla.org/en-US/docs/Glossary/Base64).", "syntax": "string | base64_url_safe_decode", "name": "base64_url_safe_decode"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "To produce URL-safe Base64, this filter uses `-` and `_` in place of `+` and `/`.", "syntax": "", "path": "/", "raw_liquid": "{{ 'one two three' | base64_url_safe_encode }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Encodes a string to URL-safe [Base64 format](https://developer.mozilla.org/en-US/docs/Glossary/Base64).", "syntax": "string | base64_url_safe_encode", "name": "base64_url_safe_encode"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'this sentence should start with a capitalized word.' | capitalize }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Capitalizes the first word in a string and downcases the remaining characters.", "syntax": "string | capitalize", "name": "capitalize"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 1.2 | ceil }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Rounds a number up to the nearest integer.", "syntax": "number | ceil", "name": "ceil"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{%- assign original_prices = collection.products | map: 'compare_at_price' -%}\n\nOriginal prices:\n\n{% for price in original_prices -%}\n  - {{ price }}\n{%- endfor %}\n\n{%- assign compacted_original_prices = original_prices | compact -%}\n\nOriginal prices - compacted:\n\n{% for price in compacted_original_prices -%}\n  - {{ price }}\n{%- endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Removes any `nil` items from an array.", "syntax": "array | compact", "name": "compact"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; The `concat` filter won't filter out duplicates. If you want to remove duplicates, then you need to use the\n&gt; [`uniq` filter](/docs/api/liquid/filters/uniq).", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{%- assign types_and_vendors = collection.all_types | concat: collection.all_vendors -%}\n\nTypes and vendors:\n\n{% for item in types_and_vendors -%}\n  {%- if item != blank -%}\n    - {{ item }}\n  {%- endif -%}\n{%- endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Concatenates (combines) two arrays.", "syntax": "array | concat: array", "name": "concat"}, {"category": "date", "deprecated": false, "deprecation_reason": "", "description": "This filter formats a date using various format specifiers. If the format string is empty,\nthe original input is returned. If the input cannot be converted to a date, the original input is returned.\n\nThe following format specifiers can be used:\n\n%a - The abbreviated weekday name (``Sun'')\n%A - The  full  weekday  name (``Sunday'')\n%b - The abbreviated month name (``Jan'')\n%B - The  full  month  name (``January'')\n%c - The preferred local date and time representation\n%d - Day of the month (01..31)\n%H - Hour of the day, 24-hour clock (00..23)\n%I - Hour of the day, 12-hour clock (01..12)\n%j - Day of the year (001..366)\n%m - Month of the year (01..12)\n%M - Minute of the hour (00..59)\n%p - Meridian indicator (``AM''  or  ``PM'')\n%s - Number of seconds since 1970-01-01 00:00:00 UTC.\n%S - Second of the minute (00..60)\n%U - Week  number  of the current year,\n        starting with the first Sunday as the first\n        day of the first week (00..53)\n%W - Week  number  of the current year,\n        starting with the first Monday as the first\n        day of the first week (00..53)\n%w - Day of the week (Sunday is 0, 0..6)\n%x - Preferred representation for the date alone, no time\n%X - Preferred representation for the time alone, no date\n%y - Year without a century (00..99)\n%Y - Year with century\n%Z - Time zone name\n%% - Literal ``%'' character", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [], "summary": "Formats a date according to a specified format string.", "syntax": "date | date: string", "name": "date"}, {"category": "default", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "Whether to use false values instead of the default.", "name": "allow_false", "positional": false, "required": false, "types": ["boolean"]}], "return_type": [{"type": "untyped", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.selected_variant.url | default: product.url }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "allow_false", "description": "By default, the  `default` filter's value will be used in place of `false` values. You can use the `allow_false` parameter to allow variables to return `false` instead of the default value.\n", "syntax": "variable | default: variable, allow_false: boolean", "path": "/products/health-potion", "raw_liquid": "{%- assign display_price = false -%}\n\n{{ display_price | default: true, allow_false: true }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Sets a default value for any variable whose value is one of the following:\n\n- [`empty`](/docs/api/liquid/basics#empty)\n- [`false`](/docs/api/liquid/basics#truthy-and-falsy)\n- [`nil`](/docs/api/liquid/basics#nil)", "syntax": "variable | default: variable", "name": "default"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 4 | divided_by: 2 }}\n\n# divisor is an integer\n{{ 20 | divided_by: 7 }}\n\n# divisor is a float \n{{ 20 | divided_by: 7.0 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Divides a number by a given number. The `divided_by` filter produces a result of the same type as the divisor. This means if you divide by an integer, the result will be an integer, and if you divide by a float, the result will be a float.", "syntax": "number | divided_by: number", "name": "divided_by"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.title | downcase }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string to all lowercase characters.", "syntax": "string | downcase", "name": "downcase"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '&lt;p&gt;Text to be escaped.&lt;/p&gt;' | escape }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Escapes special characters in HTML, such as `&lt;&gt;`, `'`, and `&amp;`, and converts characters into escape sequences. The filter doesn't effect characters within the string that don’t have a corresponding escape sequence.\".", "syntax": "string | escape", "name": "escape"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "# applying the escape filter to already escaped text escapes characters in HTML entities:\n\n{{ \"&amp;lt;p&amp;gt;Text to be escaped.&amp;lt;/p&amp;gt;\" | escape }}\n\n# applying the escape_once filter to already escaped text skips characters in HTML entities:\n\n{{ \"&amp;lt;p&amp;gt;Text to be escaped.&amp;lt;/p&amp;gt;\" | escape_once }}\n\n# use escape_once to escape strings where a combination of HTML entities and non-escaped characters might be present:\n\n{{ \"&amp;lt;p&amp;gt;Text to be escaped.&amp;lt;/p&amp;gt; &amp; some additional text\" | escape_once }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Escapes a string without changing characters that have already been escaped.", "syntax": "string | escape_once", "name": "escape_once"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "This requires you to provide both the property name and the associated value.", "parameters": [], "return_type": [{"type": "untyped", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign product = collection.products | find: 'vendor', \"Polina's Potent Potions\" %}\n\n{{ product.title }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Returns `nil` when no items match the specified property value.", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign product = collection.products | find: 'vendor', \"Polina's Potions\" %}\n\n{{ product.title | default: \"No product found\" }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the first item in an array with a specific property value.", "syntax": "array | find: string, string", "name": "find"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "This requires you to provide both the property name and the associated value.", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign index = collection.products | find_index: 'vendor', \"Polina's Potent Potions\" %}\n\n{{ index }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Returns `nil` when no items match the specified property value.", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign index = collection.products | find_index: 'vendor', \"Polina's Potions\" %}\n\n{{ index | default: \"No index found\" }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the index of the first item in an array with a specific property value.", "syntax": "array | find_index: string, string", "name": "find_index"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "untyped", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{%- assign first_product = collection.products | first -%}\n\n{{ first_product.title }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Dot notation", "description": "You can use the `first` filter with dot notation when you need to use it inside a tag or object output.\n", "syntax": "", "path": "/collections/all", "raw_liquid": "{{ collection.products.first.title }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the first item in an array.", "syntax": "array | first", "name": "first"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 1.2 | floor }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Rounds a number down to the nearest integer.", "syntax": "number | floor", "name": "floor"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "This requires you to provide both the property name and the associated value.", "parameters": [], "return_type": [{"type": "boolean", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign has_potent_potions = collection.products | has: 'vendor', \"Polina's Potent Potions\" %}\n\n{{ has_potent_potions }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Returns `false` when no items match the specified property value.", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign has_potent_potions = collection.products | has: 'vendor', \"Polina's Potions\" %}\n\n{{ has_potent_potions }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Tests if any item in an array has a specific property value.", "syntax": "array | has: string, string", "name": "has"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{{ collection.all_tags | join }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Custom separator", "description": "You can specify a custom separator for the joined items.\n", "syntax": "array | join: string", "path": "/collections/sale-potions", "raw_liquid": "{{ collection.all_tags | join: ', ' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Combines all of the items in an array into a single string, separated by a space.", "syntax": "array | join", "name": "join"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "untyped", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{%- assign last_product = collection.products | last -%}\n\n{{ last_product.title }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Dot notation", "description": "You can use the `last` filter with dot notation when you need to use it inside a tag or object output.\n", "syntax": "", "path": "/collections/all", "raw_liquid": "{{ collection.products.last.title }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the last item in an array.", "syntax": "array | last", "name": "last"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{%- assign text = '  Some potions create whitespace.      ' -%}\n\n\"{{ text }}\"\n\"{{ text | lstrip }}\"", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Strips all whitespace from the left of a string.", "syntax": "string | lstrip", "name": "lstrip"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{%- assign product_titles = collection.products | map: 'title' -%}\n\n{{ product_titles | join: ', ' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Creates an array of values from a specific property of the items in an array.", "syntax": "array | map: string", "name": "map"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 4 | minus: 2 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Subtracts a given number from another number.", "syntax": "number | minus: number", "name": "minus"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 12 | modulo: 5 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the remainder of dividing a number by a given number.", "syntax": "number | modulo: number", "name": "modulo"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.description | newline_to_br }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts newlines (`\\n`) in a string to HTML line breaks (`&lt;br&gt;`).", "syntax": "string | newline_to_br", "name": "newline_to_br"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 2 | plus: 2 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Adds two numbers.", "syntax": "number | plus: number", "name": "plus"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{%- assign origin = request.origin -%}\n\n{{ product.url | prepend: origin }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Adds a given string to the beginning of a string.", "syntax": "string | prepend: string", "name": "prepend"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "This requires you to provide both the property name and the associated value.", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign polina_products = collection.products | reject: 'vendor', \"Polina's Potent Potions\" %}\n\nProducts from other vendors than Polina's Potent Potions:\n\n{% for product in polina_products -%}\n  - {{ product.title }}\n{%- endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Filters an array to exclude items with a specific property value.", "syntax": "array | reject: string, string", "name": "reject"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ \"I can't do it!\" | remove: \"'t\" }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Removes any instance of a substring inside a string.", "syntax": "string | remove: string", "name": "remove"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ \"I hate it when I accidentally spill my duplication potion accidentally!\" | remove_first: ' accidentally' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Removes the first instance of a substring inside a string.", "syntax": "string | remove_first: string", "name": "remove_first"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ \"I hate it when I accidentally spill my duplication potion accidentally!\" | remove_last: ' accidentally' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Removes the last instance of a substring inside a string.", "syntax": "string | remove_last: string", "name": "remove_last"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/komodo-dragon-scale", "raw_liquid": "{{ product.handle | replace: '-', ' ' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Replaces any instance of a substring inside a string with a given string.", "syntax": "string | replace: string, string", "name": "replace"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/komodo-dragon-scale", "raw_liquid": "{{ product.handle | replace_first: '-', ' ' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Replaces the first instance of a substring inside a string with a given string.", "syntax": "string | replace_first: string, string", "name": "replace_first"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/komodo-dragon-scale", "raw_liquid": "{{ product.handle | replace_last: '-', ' ' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Replaces the last instance of a substring inside a string with a given string.", "syntax": "string | replace_last: string, string", "name": "replace_last"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "Original order:\n{{ collection.products | map: 'title' | join: ', ' }}\n\nReverse order:\n{{ collection.products | reverse | map: 'title' | join: ', ' }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Reversing strings", "description": "You can't use the `reverse` filter on strings directly. However, you can use the [`split` filter](/docs/api/liquid/filters/split) to create an array of characters in the string, reverse that array, and then use the [`join` filter](/docs/api/liquid/filters/join) to combine them again.\n", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{{ collection.title | split: '' | reverse | join: '' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Reverses the order of the items in an array.", "syntax": "array | reverse", "name": "reverse"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 2.7 | round }}\n{{ 1.3 | round }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Round to a specific number of decimal places", "description": "You can specify a number of decimal places to round to. If you don't specify a number, then the `round` filter rounds to the nearest integer.\n", "syntax": "", "path": "/", "raw_liquid": "{{ 3.14159 | round: 2 }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Rounds a number to the nearest integer.", "syntax": "number | round", "name": "round"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{%- assign text = '  Some potions create whitespace.      ' -%}\n\n\"{{ text }}\"\n\"{{ text | rstrip }}\"", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Strips all whitespace from the right of a string.", "syntax": "string | rstrip", "name": "rstrip"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "The size of a string is the number of characters that the string includes. The size of an array is the number of items\nin the array.", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{{ collection.title | size }}\n{{ collection.products | size }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "Dot notation", "description": "You can use the `size` filter with dot notation when you need to use it inside a tag or object output.\n", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{% if collection.products.size &gt;= 10 %}\n  There are 10 or more products in this collection.\n{% else %}\n  There are less than 10 products in this collection.\n{% endif %}", "parameter": false, "display_type": "text", "show_data_tab": false}], "summary": "Returns the size of a string or array.", "syntax": "variable | size", "name": "size"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "By default, the substring has a length of one character, and the array series has one array item. However, you can\nprovide a second parameter to specify the number of characters or array items.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{{ collection.title | slice: 0 }}\n{{ collection.title | slice: 0, 5 }}\n\n{{ collection.all_tags | slice: 1, 2 | join: ', ' }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Negative index", "description": "You can supply a negative index which will count from the end of the string.\n", "syntax": "", "path": "/collections/all", "raw_liquid": "{{ collection.title | slice: -3, 3 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns a substring or series of array items, starting at a given 0-based index.", "syntax": "string | slice", "name": "slice"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign tags = collection.all_tags | sort %}\n\n{% for tag in tags -%}\n  {{ tag }}\n{%- endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Sort by an array item property", "description": "You can specify an array item property to sort the array items by. You can sort by any property of the object that you're sorting.\n", "syntax": "array | sort: string", "path": "/collections/all", "raw_liquid": "{% assign products = collection.products | sort: 'price' %}\n\n{% for product in products -%}\n  {{ product.title }}\n{%- endfor %}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Sorts the items in an array in case-sensitive alphabetical, or numerical, order.", "syntax": "array | sort", "name": "sort"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "&gt; Caution:\n&gt; You shouldn't use the `sort_natural` filter to sort numerical values. When comparing items an array, each item is converted to a\n&gt; string, so sorting on numerical values can lead to unexpected results.", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign tags = collection.all_tags | sort_natural %}\n\n{% for tag in tags -%}\n  {{ tag }}\n{%- endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Sort by an array item property", "description": "You can specify an array item property to sort the array items by.\n", "syntax": "array | sort_natural: string", "path": "/collections/all", "raw_liquid": "{% assign products = collection.products | sort_natural: 'title' %}\n\n{% for product in products -%}\n  {{ product.title }}\n{%- endfor %}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Sorts the items in an array in case-insensitive alphabetical order.", "syntax": "array | sort_natural", "name": "sort_natural"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "string"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{%- assign title_words = product.handle | split: '-' -%}\n\n{% for word in title_words -%}\n  {{ word }}\n{%- endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Splits a string into an array of substrings based on a given separator.", "syntax": "string | split: string", "name": "split"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{%- assign text = '  Some potions create whitespace.      ' -%}\n\n\"{{ text }}\"\n\"{{ text | strip }}\"", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Strips all whitespace from the left and right of a string.", "syntax": "string | strip", "name": "strip"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "&lt;!-- With HTML --&gt;\n{{ product.description }}\n\n&lt;!-- HTML stripped --&gt;\n{{ product.description | strip_html }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Strips all HTML tags from a string.", "syntax": "string | strip_html", "name": "strip_html"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "&lt;!-- With newlines --&gt;\n{{ product.description }}\n\n&lt;!-- Newlines stripped --&gt;\n{{ product.description | strip_newlines }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Strips all newline characters (line breaks) from a string.", "syntax": "string | strip_newlines", "name": "strip_newlines"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{% assign fibonacci = '0, 1, 1, 2, 3, 5' | split: ', ' %}\n\n{{ fibonacci | sum }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Sum object property values", "description": "For an array of Liquid objects, you can specify a property to sum.", "syntax": "array | sum: string", "path": "/cart", "raw_liquid": "Total quantity of all items in cart:\n{{ cart.items | sum: 'quantity' }}\n\nSubtotal price for all items in cart:\n{{ cart.items | sum: 'final_line_price' | money }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Returns the sum of all elements in an array.", "syntax": "array | sum", "name": "sum"}, {"category": "math", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "number", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 2 | times: 2 }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Multiplies a number by a given number.", "syntax": "number | times: number", "name": "times"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "If the specified number of characters is less than the length of the string, then an ellipsis (`...`) is appended to\nthe truncated string. The ellipsis is included in the character count of the truncated string.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.title | truncate: 15 }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Specify a custom ellipsis", "description": "You can provide a second parameter to specify a custom ellipsis. If you don't want an ellipsis, then you can supply an empty string.\n", "syntax": "string | truncate: number, string", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.title | truncate: 15, '--' }}\n{{ article.title | truncate: 15, '' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Truncates a string down to a given number of characters.", "syntax": "string | truncate: number", "name": "truncate"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "If the specified number of words is less than the number of words in the string, then an ellipsis (`...`) is appended to\nthe truncated string.\n\n&gt; Caution:\n&gt; HTML tags are treated as words, so you should strip any HTML from truncated content. If you don't strip HTML, then\n&gt; closing HTML tags can be removed, which can result in unexpected behavior.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.content | strip_html | truncatewords: 15 }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Specify a custom ellipsis", "description": "You can provide a second parameter to specify a custom ellipsis. If you don't want an ellipsis, then you can supply an empty string.\n", "syntax": "string | truncatewords: number, string", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.content | strip_html | truncatewords: 15, '--' }}\n\n{{ article.content | strip_html | truncatewords: 15, '' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Truncates a string down to a given number of words.", "syntax": "string | truncatewords: number", "name": "truncatewords"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{% assign potion_array = 'invisibility, health, love, health, invisibility' | split: ', ' %}\n\n{{ potion_array | uniq | join: ', ' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Removes any duplicate items in an array.", "syntax": "array | uniq", "name": "uniq"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.title | upcase }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string to all uppercase characters.", "syntax": "string | upcase", "name": "upcase"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'test%40test.com' | url_decode }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Decodes any [percent-encoded](https://developer.mozilla.org/en-US/docs/Glossary/percent-encoding) characters\nin a string.", "syntax": "string | url_decode", "name": "url_decode"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; Spaces are converted to a `+` character, instead of a percent-encoded character.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '<EMAIL>' | url_encode }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts any URL-unsafe characters in a string to the\n[percent-encoded](https://developer.mozilla.org/en-US/docs/Glossary/percent-encoding) equivalent.", "syntax": "string | url_encode", "name": "url_encode"}, {"category": "array", "deprecated": false, "deprecation_reason": "", "description": "This requires you to provide both the property name and the associated value.", "parameters": [], "return_type": [{"type": "array", "name": "", "description": "", "array_value": "untyped"}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign polina_products = collection.products | where: 'vendor', \"Polina's Potent Potions\" %}\n\nProducts from Polina's Potent Potions:\n\n{% for product in polina_products -%}\n  - {{ product.title }}\n{%- endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Filter for boolean properties with a `true` value", "description": "You can filter for items that have a `true` value for a boolean property. This requires you to provide only the property name.\n", "syntax": "", "path": "/collections/all", "raw_liquid": "{% assign available_products = collection.products | where: 'available' %}\n\nAvailable products:\n\n{% for product in available_products -%}\n  - {{ product.title }}\n{%- endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Filters an array to include only items with a specific property value.", "syntax": "array | where: string, string", "name": "where"}, {"category": "media", "deprecated": false, "deprecation_reason": "", "description": "If [alt text is set on the video](https://help.shopify.com/en/manual/products/product-media/add-alt-text), then it's\nincluded in the `title` attribute of the `&lt;iframe&gt;`. If no alt text is set, then the `title` attribute is set to the\nproduct title.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/potion-beats", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'external_video' %}\n    {% if media.host == 'youtube' %}\n      {{ media | external_video_url: color: 'white' | external_video_tag }}\n    {% elsif media.host == 'vimeo' %}\n      {{ media | external_video_url: loop: '1', muted: '1' | external_video_tag }}\n    {% endif %}\n  {% endif %}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "HTML attributes", "description": "You can specify [HTML attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/iframe#attributes) by adding a parameter that matches the attribute name, and the desired value.\n", "syntax": "variable | external_video_tag: attribute: string", "path": "/products/potion-beats", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'external_video' %}\n    {% if media.host == 'youtube' %}\n      {{ media | external_video_url: color: 'white' | external_video_tag: class:'youtube-video' }}\n    {% endif %}\n  {% endif %}\n{% endfor %}", "parameter": true, "display_type": "text", "show_data_tab": false}], "summary": "Generates an HTML `&lt;iframe&gt;` tag containing the player for a given external video. The input for the `external_video_tag`\nfilter can be either a [`media` object](/docs/api/liquid/objects/media) or [`external_video_url`](/docs/api/liquid/filters/external_video_url).", "syntax": "variable | external_video_tag", "name": "external_video_tag"}, {"category": "media", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "You can specify [YouTube](https://developers.google.com/youtube/player_parameters#Parameters) and [Vimeo](https://vimeo.zendesk.com/hc/en-us/articles/************-Using-Player-Parameters) video parameters by adding a parameter that matches the parameter name, and the desired value.\n", "syntax": "", "path": "/products/potion-beats", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'external_video' %}\n    {% if media.host == 'youtube' %}\n      {{ media | external_video_url: color: 'white' | external_video_tag }}\n    {% elsif media.host == 'vimeo' %}\n      {{ media | external_video_url: loop: '1', muted: '1' | external_video_tag }}\n    {% endif %}\n  {% endif %}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": false}], "summary": "Returns the URL for a given external video. Use this filter to specify parameters for the external video player generated\nby the [`external_video_tag` filter](/docs/api/liquid/filters/external_video_tag).", "syntax": "media | external_video_url: attribute: string", "name": "external_video_url"}, {"category": "media", "deprecated": false, "deprecation_reason": "", "description": "By default, `width` and `height` attributes are added to the `&lt;img&gt;` tag based on the dimensions and aspect ratio from\nthe image URL. However, you can override these attributes with the [width](/docs/api/liquid/filters/image_tag#image_tag-width) and [height](/docs/api/liquid/filters/image_tag#image_tag-height)\nparameters. If only one parameter is provided, then only that attribute is added.", "parameters": [{"description": "The width of the image.", "name": "width", "positional": false, "required": false, "types": ["number"]}, {"description": "The height of the image.", "name": "height", "positional": false, "required": false, "types": ["number"]}, {"description": "The source sizes for responsiveness.", "name": "sizes", "positional": false, "required": false, "types": ["number"]}, {"description": "The widths to associate with custom `srcset` values.", "name": "widths", "positional": false, "required": false, "types": ["number"]}, {"description": "The `srcset` for responsiveness.", "name": "srcset", "positional": false, "required": false, "types": ["number"]}, {"description": "Whether the resource should be preloaded.", "name": "preload", "positional": false, "required": false, "types": ["boolean"]}, {"description": "The image's alt text.", "name": "alt", "positional": false, "required": false, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 200 | image_tag }}", "parameter": false, "display_type": "html", "show_data_tab": true}, {"name": "Lazy loading", "description": "If you don't apply the `preload` attribute to `image_tag`, then `loading` is automatically set to `lazy` for images in sections further down the page.\nYou shouldn't lazy load images above the fold. If the default value doesn't work for your theme, then consider writing your own logic using the `section.index` and `section.location` properties. For more information, refer to the [`section` object](/docs/api/liquid/objects/section).\n", "syntax": "", "path": "/", "raw_liquid": "", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "`image_tag` and focal points", "description": "This filter automatically applies a focal point to the image using the `object-position` CSS style, if focal point coordinates are available. You can also access an image's focal point coordinates directly through the [`focal_point`](/docs/api/liquid/objects/focal_point) object. [Learn how to set a focal point](https://help.shopify.com/manual/online-store/images/theme-images#set-a-focal-point-on-a-theme-image).\n", "syntax": "", "path": "/", "raw_liquid": "{{ images['potions-header.png'] | image_url: width: 300 | image_tag }}", "parameter": false, "display_type": "html", "show_data_tab": true}, {"name": "width", "description": "Specify the `width` attribute of the `&lt;img&gt;` tag. You can set the parameter to `nil` to prevent the attribute from being added.\n", "syntax": "image_url | image_tag: width: number", "path": "/products/health-potion", "raw_liquid": "&lt;!-- With a width --&gt;\n{{ product | image_url: width: 400 | image_tag: width: 300 }}\n\n&lt;!-- With the width set to nil --&gt;\n{{ product | image_url: width: 400 | image_tag: width: nil }}", "parameter": true, "display_type": "html", "show_data_tab": true}, {"name": "height", "description": "Specify the `height` attribute of the `&lt;img&gt;` tag. You can set the parameter to `nil` to prevent the attribute from being added.\n", "syntax": "image_url | image_tag: height: number", "path": "/products/health-potion", "raw_liquid": "&lt;!-- With a height --&gt;\n{{ product | image_url: width: 400 | image_tag: height: 300 }}\n\n&lt;!-- With the height set to nil --&gt;\n{{ product | image_url: width: 400 | image_tag: height: nil }}", "parameter": true, "display_type": "html", "show_data_tab": true}, {"name": "sizes", "description": "Specify source sizes with the [HTML `sizes` attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attr-sizes).\n", "syntax": "image_url | image_tag: sizes: string", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 200 | image_tag: sizes: '(min-width:1600px) 960px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)' }}", "parameter": true, "display_type": "html", "show_data_tab": true}, {"name": "widths", "description": "By default, Shopify generates a `srcset` with a smart set of default widths up to the maximum defined in the image URL. However, you can create your own set of widths.\n", "syntax": "image_url | image_tag: widths: string", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 600 | image_tag: widths: '200, 300, 400' }}", "parameter": true, "display_type": "html", "show_data_tab": true}, {"name": "srcset", "description": "By default, Shopify generates a `srcset`. However, you can create your own `srcset`.\nThe `srcset` parameter takes precedence over the [`width` parameter](/docs/api/liquid/filters/image_tag#image_tag-width).\nYou shouldn't to use the `srcset` parameter unless you want to remove the attribute by setting the parameter to `nil`.\n", "syntax": "image_url | image_tag: srcset: string", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 200 | image_tag: srcset: nil }}", "parameter": true, "display_type": "html", "show_data_tab": true}, {"name": "preload", "description": "Specify whether the image should be preloaded.\n\nWhen `preload` is set to `true`, a resource hint is sent as a [Link HTTP header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Link)\nwith a `rel` value of [`preload`](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types/preload).\nThe Link header also includes `imagesrcset` and `imagesizes` that match the `srcset` and `sizes` attribute of the tag,\nwhere present:\n\n```liquid\nLink: &lt;IMAGE_URL&gt;; rel=preload; as=image\nLink: &lt;IMAGE_URL&gt;; rel=preload; as=image; imagesrcset=ADDITIONAL_IMAGE_URL 352w; imagesizes=40vw\n```\n\nThis option doesn't affect the HTML img tag directly.\n\nYou should use the preload parameter sparingly. For example, consider preloading only above-the-fold images.\nTo learn more about resource hints in Shopify themes, refer to [Performance best practices for Shopify themes](/themes/best-practices/performance#preload-key-resources-defer-or-avoid-loading-others).\n", "syntax": "image_url | image_tag: preload: boolean", "path": "", "raw_liquid": "", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "alt", "description": "By default, the `alt` attribute of the `&lt;img&gt;` tag is set to the [media alt text](https://help.shopify.com/manual/products/product-media/add-alt-text), or the resource title for article, collection, line item, product, and variant images. However, you can override this default, or set the value if there's no default.\n", "syntax": "image_url | image_tag: alt: string", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 200 | image_tag: alt: \"My image's alt text\" }}", "parameter": true, "display_type": "html", "show_data_tab": true}, {"name": "HTML attributes", "description": "You can specify [HTML attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attributes) by adding a parameter that matches the attribute name, and the desired value.\n", "syntax": "image_url | image_tag: attribute: string", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 200 | image_tag: class: 'custom-class', loading: 'lazy' }}", "parameter": true, "display_type": "html", "show_data_tab": true}], "summary": "Generates an HTML `&lt;img&gt;` tag for a given [`image_url`](/docs/api/liquid/filters/image_url).", "syntax": "string | image_tag", "name": "image_tag"}, {"category": "media", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The dimensions of the media's poster image.", "name": "image_size", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/potion-beats", "raw_liquid": "{% for media in product.media %}\n  {{- media | media_tag }}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "image_size", "description": "Specify the dimensions of the media's poster image in pixels.", "syntax": "media | media_tag: image_size: string", "path": "/products/potion-beats", "raw_liquid": "{% for media in product.media %}\n  {{- media | media_tag: image_size: '400x' }}\n{% endfor %}", "parameter": true, "display_type": "text", "show_data_tab": false}], "summary": "Generates an appropriate HTML tag for a given media object.", "syntax": "media | media_tag", "name": "media_tag"}, {"category": "media", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The dimensions of the model viewer's poster image.", "name": "image_size", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/potion-bottle", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'model' %}\n    {{ media | model_viewer_tag }}\n  {% endif %}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "Model viewer attributes", "description": "By default, the model viewer component has the following attributes:\n\n| Attribute | Value |\n| --- | --- |\n| `alt` | `[alt-text]` - The media's alt text. |\n| `poster` | `[preview-image-url]` - The media's preview image URL. |\n| `camera-controls` | N/A - Allows for controls via mouse/touch. |\n\nYou can override these attributes, or any [supported model viewer component attributes](https://modelviewer.dev/docs/index.html#stagingandcameras-attributes) by adding a parameter that matches the attribute name, and the desired value.\n", "syntax": "media | model_viewer_tag: attribute: string", "path": "/products/potion-bottle", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'model' %}\n    {{ media | model_viewer_tag: interaction-policy: 'allow-when-focused' }}\n  {% endif %}\n{% endfor %}", "parameter": true, "display_type": "text", "show_data_tab": false}, {"name": "image_size", "description": "Specify the dimensions of the model's poster image in pixels.", "syntax": "media | model_viewer_tag: image_size: string", "path": "/products/potion-bottle", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'model' %}\n    {{ media | model_viewer_tag: image_size: '400x' }}\n  {% endif %}\n{% endfor %}", "parameter": true, "display_type": "text", "show_data_tab": false}], "summary": "Generates a [Google model viewer component](https://modelviewer.dev/) for a given 3D model.", "syntax": "media | model_viewer_tag", "name": "model_viewer_tag"}, {"category": "media", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; When `mp4` videos are uploaded, Shopify generates an `m3u8` file as an additional [`video_source`](/docs/api/liquid/objects/video_source).\n&gt; An `m3u8` file enables video players to leverage [HTTP live streaming (HLS)](https://developer.apple.com/streaming/),\n&gt; resulting in an optimized video experience based on the user's internet connection. If loop is enabled, the HLS source is not used\n&gt; in order to allow progessive download to cache the video.\n&gt;\n&gt; If the `m3u8` source isn't supported, then the player falls back to the `mp4` source.", "parameters": [{"description": "The dimensions of the video's poster image.", "name": "image_size", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/potion-beats", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'video' %}\n    {{ media | video_tag }}\n  {% endif %}\n{% endfor %}", "parameter": false, "display_type": "html", "show_data_tab": false}, {"name": "image_size", "description": "Specify the dimensions of the video's poster image in pixels.", "syntax": "media | video_tag: image_size: string", "path": "/products/potion-beats", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'video' %}\n    {{ media | video_tag: image_size: '400x' }}\n  {% endif %}\n{% endfor %}", "parameter": true, "display_type": "html", "show_data_tab": false}, {"name": "Optional supported HTML5 attributes", "description": "`video_tag` supports all [HTML5 video attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/video#attributes).\nFor example:\n\n| Attribute | Value |\n| --- | --- |\n| `autoplay` | Whether to automatically play the video after it’s loaded. Accepted values:`true`,`false`|\n| `loop` | Whether to loop the video. Accepted values:`true`,`false`|\n| `muted` | Whether to mute the video’s audio. Accepted values:`true`,`false`|\n| `controls` | Whether a user can control the video playback. Accepted values:`true`,`false`|\n", "syntax": "media | video_tag: attribute: boolean", "path": "/products/potion-beats", "raw_liquid": "{% for media in product.media %}\n  {% if media.media_type == 'video' %}\n    {{ media | video_tag: autoplay: true, loop: true, muted: true, controls: true }}\n  {% endif %}\n{% endfor %}", "parameter": true, "display_type": "html", "show_data_tab": false}], "summary": "Generates an HTML `&lt;video&gt;` tag for a given video.", "syntax": "media | video_tag", "name": "video_tag"}, {"category": "metafield", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; The `metafield_tag` filter doesn't currently support list metafields other than `list.single_line_text_field` and `list.metaobject_reference`.", "parameters": [{"description": "Specifies which field should be used. Only applicable to `list.metaobject_reference` and `metaobject_reference` types.", "name": "field", "positional": false, "required": false, "types": ["string"]}, {"description": "The desired list format. Accepts `unordered` or `ordered`. Default: `unordered`.", "name": "list_format", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "Basic types", "description": "Most metafield types return a simple HTML element:\n\n| Type | Element | Attributes |\n| --- | --- | --- |\n| `boolean` | `&lt;span&gt;` | `class=\"metafield-boolean\"` |\n| `collection_reference` | `&lt;a&gt;` | `class=\"metafield-collection_reference\"` |\n| `color` | `&lt;span&gt;` | `class=\"metafield-color\"` |\n| `date` | `&lt;time&gt;` | `datetime=\"&lt;the metafield value&gt;\"`&lt;br&gt;&lt;br&gt;`class=\"metafield-date\"`&lt;br&gt;&lt;br&gt;Value is localized to the customer |\n| `date_time` | `&lt;time&gt;` | `datetime=\"&lt;the metafield value&gt;\"`&lt;br&gt;&lt;br&gt;`class=\"metafield-date\"`&lt;br&gt;&lt;br&gt;Value is localized to the customer |\n| `json` | `&lt;script&gt;` | `type=\"application/json\"`&lt;br&gt;&lt;br&gt;`class=\"metafield-json\"` |\n| `money` | `&lt;span&gt;` | `class=\"metafield-money\"`&lt;br&gt;&lt;br&gt;Value is formatted using the store's [HTML with currency setting](https://help.shopify.com/manual/payments/currency-formatting) |\n| `multi_line_text_field` | `&lt;span&gt;` | `class=\"metafield-multi_line_text_field\"` |\n| `number_decimal` | `&lt;span&gt;` | `class=\"metafield-number_decimal\"` |\n| `number_integer` | `&lt;span&gt;` | `class=\"metafield-number_integer\"` |\n| `page_reference` | `&lt;a&gt;` | `class=\"metafield-page_reference\"` |\n| `product_reference` | `&lt;a&gt;` | `class=\"metafield-page_reference\"` |\n| `rating` | `&lt;span&gt;` | `class=\"metafield-rating\"` | |\n| `single_line_text_field` | `&lt;span&gt;` | `class=\"metafield-single_line_text_field\"` |\n| `url` | `&lt;a&gt;` | `class=\"metafield-url\"` |\n| `variant_reference` | `&lt;a&gt;` | `class=\"metafield-variant_reference\"` |\n| `rich_text_field` | `&lt;div&gt;` | `class=\"metafield-rich_text_field\"` |\n", "syntax": "", "path": "/products/health-potion", "raw_liquid": "&lt;!-- boolean --&gt;\n{{ product.metafields.information.seasonal | metafield_tag }}\n\n&lt;!-- collection_reference --&gt;\n{{ product.metafields.information.related_collection | metafield_tag }}\n\n&lt;!-- color --&gt;\n{{ product.metafields.details.potion_color | metafield_tag }}\n\n&lt;!-- date --&gt;\n{{ product.metafields.information.expiry | metafield_tag }}\n\n&lt;!-- date_time --&gt;\n{{ product.metafields.information.brew_date | metafield_tag }}\n\n&lt;!-- json --&gt;\n{{ product.metafields.information.burn_temperature | metafield_tag }}\n\n&lt;!-- money --&gt;\n{{ product.metafields.details.price_per_ml | metafield_tag }}\n\n&lt;!-- multi_line_text_field --&gt;\n{{ product.metafields.information.shipping | metafield_tag }}\n\n&lt;!-- number_decimal --&gt;\n{{ product.metafields.information.salinity | metafield_tag }}\n\n&lt;!-- number_integer --&gt;\n{{ product.metafields.information.doses_per_day | metafield_tag }}\n\n&lt;!-- page_reference --&gt;\n{{ product.metafields.information.dosage | metafield_tag }}\n\n&lt;!-- product_reference --&gt;\n{{ product.metafields.information.related_product | metafield_tag }}\n\n&lt;!-- rating --&gt;\n{{ product.metafields.details.rating | metafield_tag }}\n\n&lt;!-- single_line_text_field --&gt;\n{{ product.metafields.information.directions | metafield_tag }}\n\n&lt;!-- url --&gt;\n{{ product.metafields.information.health | metafield_tag }}\n\n&lt;!-- variant_reference --&gt;\n{{ product.metafields.information.best_seller | metafield_tag }}\n\n&lt;!-- rich_text_field --&gt;\n{{ product.metafields.information.rich_description | metafield_tag }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "Complex types", "description": "\nThe following metafield types return nested elements, or different elements depending on the metafield contents:\n\n- [`dimension`](/docs/api/liquid/filters/metafield_tag#metafield_tag-dimension)\n- [`file_reference`](/docs/api/liquid/filters/metafield_tag#metafield_tag-file_reference)\n- [`list.metaobject_reference`](/docs/api/liquid/filters/metafield_tag#metafield_tag-list.metaobject_reference)\n- [`list.single_line_text_field`](/docs/api/liquid/filters/metafield_tag#metafield_tag-list.single_line_text_field)\n- [`metaobject_reference`](/docs/api/liquid/filters/metafield_tag#metafield_tag-metaobject_reference)\n- [`volume`](/docs/api/liquid/filters/metafield_tag#metafield_tag-volume)\n- [`weight`](/docs/api/liquid/filters/metafield_tag#metafield_tag-weight)\n", "syntax": "", "path": "/products/health-potion", "raw_liquid": "", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "dimension", "description": "Outputs a `&lt;span&gt;` element with the following attribute:\n\n| Attribute | Value |\n| --- | --- |\n| `class` | `metafield-dimension` |\n\nThe `&lt;span&gt;` element contains the following child elements:\n\n| Child element | HTML element | Attributes |\n| --- | --- | --- |\n| The dimension value.&lt;br&gt;&lt;br&gt;If it's a decimal with more than two places, then it'll be formatted to have a precision of two with trailing zeros removed. | `&lt;span&gt;` | `class=\"metafield-dimension_value\"` |\n| The dimension unit. | `&lt;span&gt;` | `class=\"metafield-dimension_unit\"` |\n", "syntax": "", "path": "/products/komodo-dragon-scale", "raw_liquid": "{{ product.metafields.details.scale_width | metafield_tag }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "file_reference", "description": "The output varies depending on the type of file. There are the following categories of file type:\n\n| File type | Description |\n| --- | --- |\n| Image | Images in the format of `jpg`, `png`, `gif`, `heic`, and `webp`. |\n| Video | Videos in the format of `mov`, and `mp4`. |\n| Other | Any other file type. |\n\n##### Image\n\nOutputs an `&lt;img&gt;` element with the following attributes:\n\n| Attribute | Value |\n| --- | --- |\n| `src` | The image's URL. |\n| `alt` | The image's alt text. |\n| `class` | `metafield-file_reference` |\n\n##### Video\n\nOutputs a `&lt;video&gt;` element with the following attributes:\n\n| Attribute | Value |\n| --- | --- |\n| `src` | The video's URL. |\n| `poster` | The video's preview image (poster) URL. |\n| `playsinline` | N/A - Indicates the video will be played \"inline\" within the element's playback area. |\n| `preload` | `metadata` - Only metadata is pre-fetched before the video is played. |\n\nThe `&lt;video&gt;` element contains the following child elements:\n\n| Child element | HTML element | Attributes |\n| --- | --- | --- |\n| The video's multimedia playlist source, for [HTTP live streaming (HLS)](https://developer.apple.com/streaming/) | `&lt;source&gt;` | `src=\"&lt;the video's m3u8 source URL&gt;\"`&lt;br&gt;&lt;br&gt;`type=\"application/x-mpegURL\"` |\n| The video's original source | `&lt;source&gt;` | `src=\"&lt;the video's source URL&gt;\"`&lt;br&gt;&lt;br&gt;`type=\"&lt;the video's original source MIME type&gt;\"` |\n| The video's preview (poster) image | `&lt;img&gt;` | `src=\"&lt;the video's preview image URL&gt;\"` |\n\n##### Other\n\nOutputs an `&lt;a&gt;` element with a link to the file and the following attribute:\n\n| Attribute | Value |\n| --- | --- |\n| `class` | `metafield-file_reference` |\n\nThe `&lt;a&gt;` element contains an `&lt;img&gt;` element for the file's [preview image](/docs/api/liquid/objects/generic_file#generic_file-preview_image) with the following attributes:\n\n| Attribute | Value |\n| --- | --- |\n| `src` | The file's preview image URL. |\n| `loading` | `lazy` - The image isn't loaded until it's almost in view. |\n", "syntax": "", "path": "/products/health-potion", "raw_liquid": "&lt;!-- Image --&gt;\n{{ product.metafields.information.promo_image | metafield_tag }}\n\n&lt;!-- Video --&gt;\n{{ product.metafields.information.promo_video | metafield_tag }}\n\n&lt;!-- Other --&gt;\n{{ product.metafields.information.disclaimers | metafield_tag }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "list.metaobject_reference", "description": "Outputs a `&lt;ul&gt;` element by default with the following attribute:\n\n| Attribute | Value |\n| --- | --- |\n| `class` | `metafield-single_line_text_field-array` |\n\nThe `&lt;ul&gt;` element contains an `&lt;li&gt;` element for each metaobject in the list with a `class` of `metafield-single_line_text_field`. The required `field` parameter specifies which field should be rendered for each metaobject. The `field` parameter can reference only metafields of type `single_line_text_field`.\n\nTo output an `&lt;ol&gt;` element, pass the `list_format` parameter with a value of `ordered`.\n", "syntax": "metafield | metafield_tag: field: string", "path": "/products/health-potion", "raw_liquid": "&lt;!-- &lt;ul&gt; element --&gt;\n{{ product.metafields.information.ingredients | metafield_tag: field: 'name' }}\n\n&lt;!-- &lt;ol&gt; element --&gt;\n{{ product.metafields.information.ingredients | metafield_tag: field: 'name', list_format: 'ordered' }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "list.single_line_text_field", "description": "Outputs a `&lt;ul&gt;` element by default with the following attribute:\n\n| Attribute | Value |\n| --- | --- |\n| `class` | `metafield-single_line_text_field-array` |\n\nThe `&lt;ul&gt;` element contains an `&lt;li&gt;` element for each item in the list with a `class` of `metafield-single_line_text_field`.\n\nTo output an `&lt;ol&gt;` element, pass the `list_format` parameter with a value of `ordered`.\n", "syntax": "", "path": "/products/health-potion", "raw_liquid": "&lt;!-- &lt;ul&gt; element --&gt;\n{{ product.metafields.information.pickup_locations | metafield_tag }}\n\n&lt;!-- &lt;ol&gt; element --&gt;\n{{ product.metafields.information.pickup_locations | metafield_tag: list_format: 'ordered' }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "metaobject_reference", "description": "Outputs an HTML element for the metaobject field specified by the required `field` parameter. The `field` parameter can reference only metafields of type `single_line_text_field`.\n", "syntax": "metafield | metafield_tag: field: string", "path": "/products/health-potion", "raw_liquid": "{{ product.metafields.information.primary_ingredient | metafield_tag: field: 'name' }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "volume", "description": "Outputs a `&lt;span&gt;` element with the following attribute:\n\n| Attribute | Value |\n| --- | --- |\n| `class` | `metafield-volume` |\n\nThe `&lt;span&gt;` element contains the following child elements:\n\n| Child element | HTML element | Attributes |\n| --- | --- | --- |\n| The volume value.&lt;br&gt;&lt;br&gt;If it's a decimal with more than two places, then it'll be formatted to have a precision of two with trailing zeros removed. | `&lt;span&gt;` | `class=\"metafield-volume_value\"` |\n| The volume unit. | `&lt;span&gt;` | `class=\"metafield-volume_unit\"` |\n", "syntax": "", "path": "/products/dandelion-milk", "raw_liquid": "{{ product.metafields.details.milk_container_volume | metafield_tag }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "weight", "description": "Outputs a `&lt;span&gt;` element with the following attribute:\n\n| Attribute | Value |\n| --- | --- |\n| `class` | `metafield-weight` |\n\nThe `&lt;span&gt;` element contains the following child elements:\n\n| Child element | HTML element | Attributes |\n| --- | --- | --- |\n| The weight value.&lt;br&gt;&lt;br&gt;If it's a decimal with more than two places, then it'll be formatted to have a precision of two with trailing zeros removed. | `&lt;span&gt;` | `class=\"metafield-weight_value\"` |\n| The weight unit. | `&lt;span&gt;` | `class=\"metafield-weight_unit\"` |\n", "syntax": "", "path": "/products/dried-chamomile", "raw_liquid": "{{ product.metafields.details.chamomile_base_weight | metafield_tag }}", "parameter": false, "display_type": "text", "show_data_tab": false}], "summary": "Generates an HTML element to host the data from a [`metafield` object](https://shopify.dev/docs/api/liquid/objects/metafield).\nThe type of element that's generated differs depending on the type of metafield.", "syntax": "metafield | metafield_tag", "name": "metafield_tag"}, {"category": "metafield", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; The `metafield_text` filter doesn't currently support list metafields other than `list.single_line_text_field` and `list.metaobject_reference`.", "parameters": [{"description": "Specifies which field should be used. Only applicable to types `list.metaobject_reference` and `metaobject_reference`.", "name": "field", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "Basic types", "description": "The following outlines the output for each metafield type:\n\n| Metafield type | Output |\n| --- | --- |\n| `single_line_text_field` | The metafield text. |\n| `multi_line_text_field` | The metafield text. |\n| `page_reference` | The page title. |\n| `product_reference` | The product title. |\n| `collection_reference` | The collection title. |\n| `variant_reference` | The variant title. |\n| `file_reference` | The file URL. |\n| `number_integer` | The number. |\n| `number_decimal` | The number. |\n| `date` | The date. |\n| `date-time` | The date and time. |\n| `url` | The URL. |\n| `json` | The JSON. |\n| `boolean` | The boolean value. |\n| `color` | The color value. |\n| `weight` | The weight value and unit.&lt;br&gt;&lt;br&gt;If the value is a decimal with more than two places, then it'll be formatted to have a precision of two with trailing zeros removed. |\n| `volume` | The volume value and unit.&lt;br&gt;&lt;br&gt;If the value is a decimal with more than two places, then it'll be formatted to have a precision of two with trailing zeros removed. |\n| `dimension` | The dimension value and unit.&lt;br&gt;&lt;br&gt;If the value is a decimal with more than two places, then it'll be formatted to have a precision of two with trailing zeros removed. |\n| `rating` | The rating value. |\n| `list.single_line_text_field` | The metafield values in sentence format.&lt;br&gt;&lt;br&gt;For example, if you had the values `Toronto`, `Ottawa`, and `Vancouver`, then the output would be:&lt;br&gt;&lt;br&gt;`Toronto, Ottawa, and Vancouver` |\n| `money` | The money value, formatted using the store's [**HTML with currency** setting](https://help.shopify.com/manual/payments/currency-formatting). |\n| `rich_text_field` | The rich text value as simple text. |\n", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.metafields.information.dosage | metafield_text }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "Complex types", "description": "\nThe following metafield types produce different output depending on the provided `field` parameter:\n\n- [`list.metaobject_reference`](/docs/api/liquid/filters/metafield_text#metafield_text-list.metaobject_reference)\n- [`metaobject_reference`](/docs/api/liquid/filters/metafield_text#metafield_text-metaobject_reference)\n", "syntax": "", "path": "/products/health-potion", "raw_liquid": "", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "list.metaobject_reference", "description": "Outputs the list of metaobjects in sentence format. The required `field` parameter specifies which field should be rendered for each metaobject. The `field` parameter can reference only metafields of type `single_line_text_field`.\n", "syntax": "metafield | metafield_text: field: string", "path": "/products/health-potion", "raw_liquid": "{{ product.metafields.information.ingredients | metafield_text: field: 'name' }}", "parameter": false, "display_type": "text", "show_data_tab": false}, {"name": "metaobject_reference", "description": "Outputs the metafield text for the metaobject field specified by the required `field` parameter. The `field` parameter can reference only metafields of type `single_line_text_field`.\n", "syntax": "metafield | metafield_text: field: string", "path": "/products/health-potion", "raw_liquid": "{{ product.metafields.information.primary_ingredient | metafield_tag: field: 'name' }}", "parameter": false, "display_type": "text", "show_data_tab": false}], "summary": "Generates a text version of the data from a [`metafield` object](https://shopify.dev/docs/api/liquid/objects/metafield).", "syntax": "metafield | metafield_text", "name": "metafield_text"}, {"category": "money", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.price | money }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Formats a given price based on the store's [**HTML without currency** setting](https://help.shopify.com/manual/payments/currency-formatting).", "syntax": "number | money", "name": "money"}, {"category": "money", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.price | money_with_currency }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Formats a given price based on the store's [**HTML with currency** setting](https://help.shopify.com/manual/payments/currency-formatting).", "syntax": "number | money_with_currency", "name": "money_with_currency"}, {"category": "money", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.price | money_without_currency }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Formats a given price based on the store's [**HTML without currency** setting](https://help.shopify.com/manual/payments/currency-formatting), without the currency symbol.", "syntax": "number | money_without_currency", "name": "money_without_currency"}, {"category": "money", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.price | money_without_trailing_zeros }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Formats a given price based on the store's [**HTML without currency** setting](https://help.shopify.com/manual/payments/currency-formatting), excluding the decimal separator\n(either `.` or `,`) and trailing zeros.\n\nIf the price has a non-zero decimal value, then the output is the same as the [`money` filter](/docs/api/liquid/filters#money).", "syntax": "number | money_without_trailing_zeros", "name": "money_without_trailing_zeros"}, {"category": "default", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The text for the previous page link.", "name": "previous", "positional": false, "required": false, "types": ["string"]}, {"description": "The text for the next page link.", "name": "next", "positional": false, "required": false, "types": ["string"]}, {"description": "The anchor to add to the pagination links.", "name": "anchor", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{% paginate collection.products by 2 %}\n  {% for product in collection.products %}\n    {{- product.title }}\n  {% endfor %}\n\n  {{- paginate | default_pagination -}}\n{% endpaginate %}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "previous", "description": "Specify the text for the previous page link.", "syntax": "paginate | default_pagination: previous: string", "path": "/collections/sale-potions", "raw_liquid": "{% paginate collection.products by 2 %}\n  {% for product in collection.products %}\n    {{- product.title }}\n  {% endfor %}\n\n  {{- paginate | default_pagination: previous: 'Previous' -}}\n{% endpaginate %}", "parameter": true, "display_type": "text", "show_data_tab": false}, {"name": "next", "description": "Specify the text for the next page link.", "syntax": "paginate | default_pagination: next: string", "path": "/collections/sale-potions", "raw_liquid": "{% paginate collection.products by 2 %}\n  {% for product in collection.products %}\n    {{- product.title }}\n  {% endfor %}\n\n  {{- paginate | default_pagination: next: 'Next' -}}\n{% endpaginate %}", "parameter": true, "display_type": "text", "show_data_tab": false}, {"name": "anchor", "description": "Specify the anchor to add to the pagination links.", "syntax": "paginate | default_pagination: anchor: string", "path": "/collections/sale-potions", "raw_liquid": "{% paginate collection.products by 2 %}\n  {% for product in collection.products %}\n    {{- product.title }}\n  {% endfor %}\n\n  &lt;div id=\"pagination\"&gt;\n    {{- paginate | default_pagination: anchor: 'pagination' -}}\n  &lt;/div&gt;\n{% endpaginate %}", "parameter": true, "display_type": "text", "show_data_tab": false}], "summary": "Generates HTML for a set of links for paginated results. Must be applied to the [`paginate` object](/docs/api/liquid/objects/paginate).", "syntax": "paginate | default_pagination", "name": "default_pagination"}, {"category": "customer", "deprecated": false, "deprecation_reason": "", "description": "&gt; Tip:\n&gt; Use with the [`customer.has_avatar?`](/docs/api/liquid/objects/customer#customer-has_avatar?) method to determine if the customer has an avatar.\n\n```liquid\n{{ customer | avatar }}\n```", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [], "summary": "Generates HTML to render a customer's avatar, if available.", "syntax": "customer | avatar", "name": "avatar"}, {"category": "customer", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; The presence of the [shop](/docs/api/liquid/objects/shop) object is required for validation purposes only.", "parameters": [{"description": "The action to be performed. Accepts the following values: default, follow.", "name": "action", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "", "raw_liquid": "", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "action", "description": "Controls the behavior of the button after authentication.\n\nAccepts the following values:\n\n- `default` - Authentication only\n- `follow` - Performs a side-effect after authentication which follows the current shop in the Shop app. Requires additional configuration. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)\n\n```liquid\n{{ shop | login_button: action: 'follow' }}\n```\n", "syntax": "shop | login_button: action: string", "path": "", "raw_liquid": "", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML Button that enables a customer to either sign in to the storefront using their Shop account or follow the shop in the Shop App.", "syntax": "shop | login_button", "name": "login_button"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'variable-name' | camelize }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string to CamelCase.", "syntax": "string | camelize", "name": "camelize"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "&gt; Note:\n&gt; The `handleize` filter has an alias of `handle`.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.title | handleize }}\n{{ product.title | handle }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts a string into a [handle](/docs/api/liquid/basics#handles).", "syntax": "string | handleize", "name": "handleize"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '&lt;p&gt;Health &amp; Love potions&lt;/p&gt;' | url_escape }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Escapes any URL-unsafe characters in a string.", "syntax": "string | url_escape", "name": "url_escape"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "The `url_param_escape` filter escapes the same characters as [`url_escape`](/docs/api/liquid/filters/url_escape), with the\naddition of `&amp;`.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ '&lt;p&gt;Health &amp; Love potions&lt;/p&gt;' | url_param_escape }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Escapes any characters in a string that are unsafe for URL parameters.", "syntax": "string | url_param_escape", "name": "url_param_escape"}, {"category": "format", "deprecated": false, "deprecation_reason": "", "description": "The `structured_data` filter can be used on the [`product`](/docs/api/liquid/objects/product) and [`article`](/docs/api/liquid/objects/article) objects.\n\nProduct objects are output as a [schema.org `Product`](https://schema.org/Product) if they have no variants, and a [`ProductGroup`](https://schema.org/ProductGroup) if they have one or more variants.\n\nArticle objects are output as a [schema.org `Article`.](https://schema.org/Article)", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/crocodile-tears", "raw_liquid": "&lt;script type=\"application/ld+json\"&gt;\n  {{ product | structured_data }}\n&lt;/script&gt;", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Converts an object into a schema.org structured data format.", "syntax": "variable | structured_data", "name": "structured_data"}, {"category": "collection", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions/extra-potent", "raw_liquid": "{% for tag in collection.all_tags %}\n  {{- tag | highlight_active_tag | link_to_tag: tag }}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Wraps a given tag within the [`collection` object](https://shopify.dev/docs/api/liquid/objects/collection) in an HTML `&lt;span&gt;` tag, with a `class` attribute of `active`, if the tag is currently active. Only\napplies to collection tags.", "syntax": "string | highlight_active_tag", "name": "highlight_active_tag"}, {"category": "tag", "deprecated": false, "deprecation_reason": "", "description": "&gt; Tip:\n&gt; To learn more about filtering by tag, refer to [Filter articles by tag](/themes/architecture/templates/blog#filter-articles-by-tag)\n&gt; or [Filter collections by tag](/themes/navigation-search/filtering/tag-filtering).", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{% for tag in collection.all_tags %}\n  {%- if current_tags contains tag -%}\n    {{ tag }}\n  {%- else -%}\n    {{ tag | link_to_add_tag: tag }}\n  {%- endif -%}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;a&gt;` tag with an `href` attribute linking to the current blog or collection, filtered to show\nonly articles or products that have a given tag, as well as any currently active tags.", "syntax": "string | link_to_add_tag", "name": "link_to_add_tag"}, {"category": "tag", "deprecated": false, "deprecation_reason": "", "description": "&gt; Tip:\n&gt; To learn more about filtering by tag, refer to [Filter articles by tag](/themes/architecture/templates/blog#filter-articles-by-tag)\n&gt; or [Filter collections by tag](/themes/navigation-search/filtering/tag-filtering).", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{% for tag in collection.all_tags %}\n  {%- if current_tags contains tag -%}\n    {{ tag | link_to_remove_tag: tag }}\n  {%- else -%}\n    {{ tag | link_to_add_tag: tag }}\n  {%- endif -%}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;a&gt;` tag with an `href` attribute linking to the current blog or collection, filtered to show\nonly articles or products that have any currently active tags, except the provided tag.", "syntax": "string | link_to_remove_tag", "name": "link_to_remove_tag"}, {"category": "tag", "deprecated": false, "deprecation_reason": "", "description": "&gt; Tip:\n&gt; To learn more about filtering by tag, refer to [Filter articles by tag](/themes/architecture/templates/blog#filter-articles-by-tag)\n&gt; or [Filter collections by tag](/themes/navigation-search/filtering/tag-filtering).", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{% for tag in collection.all_tags %}\n  {{- tag | link_to_tag: tag }}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;a&gt;` tag with an `href` attribute linking to the current blog or collection, filtered to show\nonly articles or products that have a given tag.", "syntax": "string | link_to_tag", "name": "link_to_tag"}, {"category": "localization", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ shop.address | format_address }}", "parameter": false, "display_type": "html", "show_data_tab": false}, {"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ customer.default_address | format_address }}", "parameter": false, "display_type": "html", "show_data_tab": false}], "summary": "Generates an HTML address display, with each address component ordered according to the address's locale.", "syntax": "address | format_address", "name": "format_address"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The string that you want to highlight.", "name": "highlighted_term", "positional": true, "required": true, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/search?q=love", "raw_liquid": "{% for item in search.results %}\n  {% if item.object_type == 'product' %}\n    {{ item.description | highlight: search.terms }}\n  {% else %}\n    {{ item.content | highlight: search.terms }}\n  {% endif %}\n{% endfor %}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Wraps all instances of a specific string, within a given string, with an HTML `&lt;strong&gt;` tag with a `class` attribute\nof `highlight`.", "syntax": "string | highlight: string", "name": "highlight"}, {"category": "string", "deprecated": false, "deprecation_reason": "", "description": "&gt; Caution:\n&gt; The `pluralize` filter applies English pluralization rules to determine which string to output. You shouldn't use this\n&gt; filter on non-English strings because it could lead to incorrect pluralizations.", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/cart", "raw_liquid": "Cart item count: {{ cart.item_count }} {{ cart.item_count | pluralize: 'item', 'items' }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Outputs the singular or plural version of a string based on a given number.", "syntax": "number | pluralize: string, string", "name": "pluralize"}, {"category": "format", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/blue-mountain-flower", "raw_liquid": "{%- assign variant = product.variants.first -%}\n\n{{ variant.unit_price | unit_price_with_measurement: variant.unit_price_measurement }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Formatted unit price", "description": "You can specify a formatted unit price using one of the [money filters](/docs/api/liquid/filters/payment_button#money-filters).", "syntax": "string | unit_price_with_measurement: unit_price_measurement", "path": "/products/blue-mountain-flower", "raw_liquid": "{%- assign variant = product.variants.first -%}\n\n{{ variant.unit_price | money_with_currency | unit_price_with_measurement: variant.unit_price_measurement }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Formats a given unit price and measurement based on the store's [**HTML without currency** setting](https://help.shopify.com/manual/payments/currency-formatting).", "syntax": "number | unit_price_with_measurement: unit_price_measurement", "name": "unit_price_with_measurement"}, {"category": "media", "deprecated": true, "deprecation_reason": "The `article_img_url` filter has been replaced by [`image_url`](/docs/api/liquid/filters/image_url).", "description": "", "parameters": [{"description": "The desired image size.", "name": "size", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.image | article_img_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "size", "description": "By default, the `article_img_url` filter returns the `small` version of the image (100 x 100 px). However, you can specify a [size](/docs/api/liquid/filters/img_url#img_url-size).\n", "syntax": "image | article_img_url: string", "path": "/blogs/potion-notions/how-to-tell-if-you-have-run-out-of-invisibility-potion", "raw_liquid": "{{ article.image | article_img_url: 'large' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for an [article's image](/docs/api/liquid/objects/article#article-image).", "syntax": "variable | article_img_url", "name": "article_img_url"}, {"category": "hosted_file", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The desired image size.", "name": "size", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'red-and-black-bramble-berries.jpg' | asset_img_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "size", "description": "By default, the `asset_img_url` filter returns the `small` version of the image (100 x 100 px). However, you can specify a [size](/docs/api/liquid/filters/img_url#img_url-size).\n", "syntax": "image | asset_img_url: string", "path": "/", "raw_liquid": "{{ 'red-and-black-bramble-berries.jpg' | asset_img_url: 'large' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for an image in the\n[`assets` directory](/themes/architecture#assets) of a theme.", "syntax": "string | asset_img_url", "name": "asset_img_url"}, {"category": "hosted_file", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'cart.js' | asset_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for a file in the\n[`assets` directory](/themes/architecture#assets) of a theme.", "syntax": "string | asset_url", "name": "asset_url"}, {"category": "media", "deprecated": true, "deprecation_reason": "The `collection_img_url` filter has been replaced by [`image_url`](/docs/api/liquid/filters/image_url).", "description": "", "parameters": [{"description": "The desired image size.", "name": "size", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/collections/sale-potions", "raw_liquid": "{{ collection.image | collection_img_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "The size parameter", "description": "By default, the `collection_img_url` filter returns the `small` version of the image (100 x 100 px). However, you can specify a [size](/docs/api/liquid/filters/img_url#img_url-size).\n", "syntax": "image | collection_img_url: string", "path": "/collections/sale-potions", "raw_liquid": "{{ collection.image | collection_img_url: 'large' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for a [collection's image](/docs/api/liquid/objects/collection#collection-image).", "syntax": "variable | collection_img_url", "name": "collection_img_url"}, {"category": "hosted_file", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The desired image size.", "name": "size", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'potions-header.png' | file_img_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "The size parameter", "description": "By default, the `file_img_url` filter returns the `small` version of the image (100 x 100 px). However, you can specify a [size](/docs/api/liquid/filters/img_url#img_url-size).\n", "syntax": "image | file_img_url: string", "path": "/", "raw_liquid": "{{ 'potions-header.png' | file_img_url: 'large' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for an image from the\n[Files](https://www.shopify.com/admin/settings/files) page of the Shopify admin.", "syntax": "string | file_img_url", "name": "file_img_url"}, {"category": "hosted_file", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The desired image size.", "name": "size", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'disclaimer.pdf' | file_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for a file from the\n[Files](https://www.shopify.com/admin/settings/files) page of the Shopify admin.", "syntax": "string | file_url", "name": "file_url"}, {"category": "hosted_file", "deprecated": false, "deprecation_reason": "", "description": "Global assets are kept in a directory on Shopify's server. Using global assets can be faster than loading the resource\ndirectly.\n\nDepending on the resource type, you might need to use an additional filter to load the resource. The following table\noutlines which filter to use for specific resource types.\n\n| Resource type | Additional filter |\n| --- | --- |\n| JavaScript (`.js`) | [`script_tag`](/docs/api/liquid/filters/script_tag) |\n| CSS (`.css`) | [`stylesheet_tag`](/docs/api/liquid/filters/stylesheet_tag)  |\n\nThe following table outlines the available global assets:\n\n| Category | Assets |\n| --- | --- |\n| Firebug | - `firebug/firebug.css`&lt;br&gt;- `firebug/firebug.html`&lt;br&gt;- `firebug/firebug.js`&lt;br&gt;- `firebug/firebugx.js`&lt;br&gt;- `firebug/errorIcon.png`&lt;br&gt;- `firebug/infoIcon.png`&lt;br&gt;- `firebug/warningIcon.png` |\n| JavaScript libraries | - `controls.js`&lt;br&gt;- `dragdrop.js`&lt;br&gt;- `effects.js`&lt;br&gt;- `ga.js`&lt;br&gt;- `mootools.js` |\n| Lightbox | - `lightbox.css`&lt;br&gt;- `lightbox.js`&lt;br&gt;&lt;br&gt;- `lightbox/v1/lightbox.css`&lt;br&gt;- `lightbox/v1/lightbox.js`&lt;br&gt;&lt;br&gt;- `lightbox/v2/lightbox.css`&lt;br&gt;- `lightbox/v2/lightbox.js`&lt;br&gt;- `lightbox/v2/close.gif`&lt;br&gt;- `lightbox/v2/loading.gif`&lt;br&gt;- `lightbox/v2/overlay.png`&lt;br&gt;- `lightbox/v2/zoom-lg.gif`&lt;br&gt;&lt;br&gt;- `lightbox/v204/lightbox.css`&lt;br&gt;- `lightbox/v204/lightbox.js`&lt;br&gt;- `lightbox/v204/bullet.gif`&lt;br&gt;- `lightbox/v204/close.gif`&lt;br&gt;- `lightbox/v204/closelabel.gif`&lt;br&gt;- `lightbox/v204/donatebutton.gif`&lt;br&gt;- `lightbox/v204/downloadicon.gif`&lt;br&gt;- `lightbox/v204/loading.gif`&lt;br&gt;- `lightbox/v204/nextlabel.png`&lt;br&gt;- `lightbox/v204/prevlabel.gif` |\n| Prototype | - `prototype.js`&lt;br&gt;- `prototype/1.5/prototype.js`&lt;br&gt;- `prototype/1.6/prototype.js` |\n| script.aculo.us | - `scriptaculous/1.8.2/scriptaculous.js`&lt;br&gt;- `scriptaculous/1.8.2/builder.js`&lt;br&gt;- `scriptaculous/1.8.2/controls.js`&lt;br&gt;- `scriptaculous/1.8.2/dragdrop.js`&lt;br&gt;- `scriptaculous/1.8.2/effects.js`&lt;br&gt;- `scriptaculous/1.8.2/slider.js`&lt;br&gt;- `scriptaculous/1.8.2/sound.js`&lt;br&gt;- `scriptaculous/1.8.2/unittest.js` |\n| Shopify | - `list-collection.css`&lt;br&gt;- `textile.css` |", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'lightbox.js' | global_asset_url | script_tag }}\n\n{{ 'lightbox.css' | global_asset_url | stylesheet_tag }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for a global asset.", "syntax": "string | global_asset_url", "name": "global_asset_url"}, {"category": "media", "deprecated": false, "deprecation_reason": "", "description": "You can use the `image_url` filter on the following objects, as well as their `src` property:\n\n- [`article`](/docs/api/liquid/objects/article)\n- [`collection`](/docs/api/liquid/objects/collection)\n- [`image`](/docs/api/liquid/objects/image)\n- [`line_item`](/docs/api/liquid/objects/line_item)\n- [`product`](/docs/api/liquid/objects/product)\n- [`variant`](/docs/api/liquid/objects/variant)\n- [`country`](/docs/api/liquid/objects/country)\n\n&gt; Caution:\n&gt; You need to specify either a [`width`](/docs/api/liquid/filters/image_url#image_url-width) or\n&gt; [`height`](/docs/api/liquid/filters/image_url#image_url-height) parameter. If neither are specified, then an error is returned.\n\n&gt; Note:\n&gt; Regardless of the specified dimensions, an image can never be resized to be larger than its original dimensions.", "parameters": [{"description": "How the image should be cropped to match the desired dimensions.", "name": "crop", "positional": false, "required": false, "types": ["string"]}, {"description": "The file format for the image.", "name": "format", "positional": false, "required": false, "types": ["string"]}, {"description": "The padding color, if the provided image is smaller than the requested dimensions.", "name": "pad_color", "positional": false, "required": false, "types": ["string"]}, {"description": "The desired image width, in pixels.", "name": "width", "positional": false, "required": true, "types": ["number"]}, {"description": "The desired image height, in pixels.", "name": "height", "positional": false, "required": true, "types": ["number"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 450 }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "width", "description": "Specify the width of the image up to a maximum of `5760px`. If only the width is specified, then the height is automatically calculated based on the image's dimensions.\n", "syntax": "variable | image_url: width: number", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 450 }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "height", "description": "Specify the height of the image up to a maximum of `5760px`. If only the height is specified, then the width is automatically calculated based on the image's dimensions.\n", "syntax": "variable | image_url: height: number", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: height: 450 }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "crop", "description": "Specify which part of the image to show if the specified dimensions result in an aspect ratio that differs from the original. You can use the following values:\n\n- `top`\n- `center`\n- `bottom`\n- `left`\n- `right`\n- `region`\n\nThe default value is `center`.\n\nWhen using the `region` crop mode, the starting point for the crop is defined by `crop_left` and `crop_top` and extends to the `crop_width` and `crop_height`.\nOptionally, to resize the region extracted by the crop, use the `width` and `height` parameters.\n\n&gt; Note:\n&gt; Country flags are SVG images and can only be cropped if a value for `format`\n&gt; is also provided.\n", "syntax": "variable | image_url: crop: string", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 400, height: 400, crop: 'bottom' }}\n\n{{ product | image_url: crop: 'region', crop_left: 32, crop_top: 32, crop_width: 512, crop_height: 512 }}\n\n{{ product | image_url: crop: 'region', width: 100, height: 100, crop_left: 32, crop_top: 32, crop_width: 512, crop_height: 512 }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "format", "description": "Specify which file format to use for the image. The valid formats are `pjpg` and `jpg`.\n\nIt's not practical to convert a lossy image format, like `jpg`, to a lossless image format, like `png`, so Shopify can do\nonly the following conversions:\n\n- `png` to `jpg`\n- `png` to `pjpg`\n- `jpg` to `pjpg`\n\n&gt; Note:\n&gt; Shopify automatically detects which image formats are supported by the client (e.g. `WebP`, `AVIF`, etc.) and\n&gt; selects a file format for optimal quality and file size. When a format is specified, Shopify takes into account\n&gt; the features (e.g. progressive, alpha channel) of the specified file format when making the final automatic format selection.\n&gt; To learn more, visit https://cdn.shopify.com/.\n", "syntax": "variable | image_url: format: string", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 450, format: 'pjpg' }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "pad_color", "description": "Specify a color to pad the image if the specified dimensions result in an aspect ratio that differs from the original. The color must be in hexadecimal format (`hex3` or `hex6`).\n", "syntax": "variable | image_url: pad_color: string", "path": "/products/health-potion", "raw_liquid": "{{ product | image_url: width: 400, height: 400, pad_color: '000' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for an image.", "syntax": "variable | image_url: width: number, height: number", "name": "image_url"}, {"category": "media", "deprecated": true, "deprecation_reason": "The `img_tag` filter has been replaced by [`image_tag`](/docs/api/liquid/filters/image_tag).", "description": "You can also use the `img_tag` filter on the following objects:\n\n- [`article`](/docs/api/liquid/objects/article)\n- [`collection`](/docs/api/liquid/objects/collection)\n- [`image`](/docs/api/liquid/objects/image)\n- [`line_item`](/docs/api/liquid/objects/line_item)\n- [`product`](/docs/api/liquid/objects/product)\n- [`variant`](/docs/api/liquid/objects/variant)", "parameters": [{"description": "The image's alt text.", "name": "alt", "positional": true, "required": false, "types": ["string"]}, {"description": "The desired `class` attribute.", "name": "class", "positional": true, "required": false, "types": ["string"]}, {"description": "The desired image size.", "name": "size", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product | img_tag }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Optional parameters", "description": "The `img_tag` filter accepts 3 unnamed parameters, separated by commas, to specify the `alt` and `class` attributes, and the\n[size](/docs/api/liquid/filters/img_url#img_url-size) of the image. Because the parameters are read in that order, you must include a value for each parameter before the last\nparameter you want to specify. If you don't want to include a parameter that precedes one that you do want to include, then\nyou can set the value to an empty string.\n\n&gt; Note:\n&gt; The `size` attribute of the `img_tag` filter can't be used in conjunction with the [`img_url` filter](/docs/api/liquid/filters/img_url).\n&gt; If both are used, then the `img_url` filter will override the `size` parameter of the `img_tag` filter.\n", "syntax": "variable | img_tag: string, string, string", "path": "/products/health-potion", "raw_liquid": "{{ product | img_tag: 'image alt text', '', '450x450' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;img&gt;` tag for a given image URL.", "syntax": "string | img_tag", "name": "img_tag"}, {"category": "media", "deprecated": true, "deprecation_reason": "The `img_url` filter has been replaced by [`image_url`](/docs/api/liquid/filters/image_url).", "description": "You can use the `img_url` filter on the following objects:\n\n- [`article`](/docs/api/liquid/objects/article)\n- [`collection`](/docs/api/liquid/objects/collection)\n- [`image`](/docs/api/liquid/objects/image)\n- [`line_item`](/docs/api/liquid/objects/line_item)\n- [`product`](/docs/api/liquid/objects/product)\n- [`variant`](/docs/api/liquid/objects/variant)", "parameters": [{"description": "The desired image size.", "name": "size", "positional": true, "required": false, "types": ["string"]}, {"description": "The part of the image to show if the specified image has an aspect ratio that differs from the original.", "name": "crop", "positional": false, "required": false, "types": ["string"]}, {"description": "The desired pixel density.", "name": "scale", "positional": false, "required": false, "types": ["number"]}, {"description": "The desired image format.", "name": "format", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product | img_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "size", "description": "The size parameter allows you to specify the dimensions of the image up to a maximum of 5760 x 5760 px. You can specify only the width, only the height, or both, and you can also use the following named sizes:\n\n| Name | Dimensions |\n| --- | --- |\n| `pico` | `16x16 px` |\n| `icon` | `32x32 px` |\n| `thumb` | `50x50 px` |\n| `small` | `100x100 px` |\n| `compact` | `160x160 px` |\n| `medium` | `240x240 px` |\n| `large` |`480x480 px` |\n| `grande` | `600x600 px` |\n| `original`&lt;br&gt;`master` | `1024x1024 px` |\n", "syntax": "variable | img_url: string", "path": "/products/health-potion", "raw_liquid": "{{ product | img_url: '480x' }}\n\n{{ product | img_url: 'x480' }}\n\n{{ product | img_url: '480x480' }}\n\n{{ product | img_url: 'large' }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "crop", "description": "The `crop` parameter allows you to specify which part of the image to show if the specified dimensions result in an aspect ratio that differs from the original. You can use the following values:\n\n- `top`\n- `center`\n- `bottom`\n- `left`\n- `right`\n\nThe default value is `center`.\n", "syntax": "variable | img_url: crop: string", "path": "/products/health-potion", "raw_liquid": "{{ product | img_url: crop: 'bottom' }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "format", "description": "Specify which file format to use for the image. The valid formats are `pjpg` and `jpg`.\n\nIt's not practical to convert a lossy image format, like `jpg`, to a lossless image format, like `png`, so this filter does\nonly the following conversions:\n\n- `png` to `jpg`\n- `png` to `pjpg`\n- `jpg` to `pjpg`\n\n&gt; Note:\n&gt; Shopify automatically detects which image formats are supported by the client (e.g. `WebP`, `AVIF`, etc.) and\n&gt; selects a file format for optimal quality and file size. When a format is specified, Shopify takes into account\n&gt; the features (e.g. progressive, alpha channel) of the specified file format when making the final automatic format selection.\n&gt; To learn more, visit https://cdn.shopify.com/.\n", "syntax": "variable | img_url: format: string", "path": "/products/health-potion", "raw_liquid": "{{ product | img_url: format: 'pjpg' }}", "parameter": true, "display_type": "text", "show_data_tab": true}, {"name": "scale", "description": "Specify the pixel density of the image. The valid densities are 2 and 3.", "syntax": "variable | img_url: scale: number", "path": "/products/health-potion", "raw_liquid": "{{ product | img_url: scale: 2 }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for an image.", "syntax": "variable | img_url", "name": "img_url"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The URL to link to.", "name": "url", "positional": true, "required": true, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'Shopify' | link_to: 'https://www.shopify.com' }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "HTML attributes", "description": "You can specify [HTML attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#attributes) by including a parameter that matches the attribute name, and the desired value.\n", "syntax": "string | link_to_type: attribute: string", "path": "/", "raw_liquid": "{{ 'Shopify' | link_to: 'https://www.shopify.com', class: 'link-class' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;a&gt;` tag.", "syntax": "string | link_to: string", "name": "link_to"}, {"category": "payment", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{% for type in shop.enabled_payment_types %}\n&lt;img src=\"{{ type | payment_type_img_url }}\" width=\"50\" height=\"50\" /&gt;\n{% endfor %}", "parameter": false, "display_type": "html", "show_data_tab": true}], "summary": "Returns the URL for an SVG image of a given [payment type](/docs/api/liquid/objects/shop#shop-enabled_payment_types).", "syntax": "string | payment_type_img_url", "name": "payment_type_img_url"}, {"category": "payment", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The desired `class` attribute.", "name": "class", "positional": false, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{% for type in shop.enabled_payment_types -%}\n  {{ type | payment_type_svg_tag }}\n{% endfor %}", "parameter": false, "display_type": "html", "show_data_tab": true}, {"name": "class", "description": "Specify the `class` attribute of the `&lt;svg&gt;` tag.", "syntax": "type | payment_type_svg_tag: class: string", "path": "/", "raw_liquid": "{% for type in shop.enabled_payment_types -%}\n  {{ type | payment_type_svg_tag: class: 'custom-class' }}\n{% endfor %}", "parameter": false, "display_type": "html", "show_data_tab": true}], "summary": "Generates an HTML `&lt;svg&gt;` tag for a given [payment type](/docs/api/liquid/objects/shop#shop-enabled_payment_types).", "syntax": "string | payment_type_svg_tag", "name": "payment_type_svg_tag"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "Accepts the following placeholder names:\n\n| Outline illustrations | Color illustrations |\n| --- | --- |\n| &lt;ul&gt;&lt;li&gt;`product-1`&lt;/li&gt;&lt;li&gt;`product-2`&lt;/li&gt;&lt;li&gt;`product-3`&lt;/li&gt;&lt;li&gt;`product-4`&lt;/li&gt; &lt;li&gt;`product-5`&lt;/li&gt;&lt;li&gt;`product-6`&lt;/li&gt;&lt;li&gt;`collection-1`&lt;/li&gt;&lt;li&gt;`collection-2`&lt;/li&gt;&lt;li&gt;`collection-3`&lt;/li&gt;&lt;li&gt;`collection-4`&lt;/li&gt;&lt;li&gt;`collection-5`&lt;/li&gt;&lt;li&gt;`collection-6`&lt;/li&gt;&lt;li&gt;`lifestyle-1`&lt;/li&gt;&lt;li&gt;`lifestyle-2`&lt;/li&gt;&lt;li&gt;`image`&lt;/li&gt;&lt;/ul&gt; | &lt;ul&gt;&lt;li&gt;`product-apparel-1`&lt;/li&gt;&lt;li&gt;`product-apparel-2`&lt;/li&gt;&lt;li&gt;`product-apparel-3`&lt;/li&gt;&lt;li&gt;`product-apparel-4`&lt;/li&gt;&lt;li&gt;`collection-apparel-1`&lt;/li&gt;&lt;li&gt;`collection-apparel-2`&lt;/li&gt;&lt;li&gt;`collection-apparel-3`&lt;/li&gt;&lt;li&gt;`collection-apparel-4`&lt;/li&gt;&lt;li&gt;`hero-apparel-1`&lt;/li&gt;&lt;li&gt;`hero-apparel-2`&lt;/li&gt;&lt;li&gt;`hero-apparel-3`&lt;/li&gt;&lt;li&gt;`blog-apparel-1`&lt;/li&gt;&lt;li&gt;`blog-apparel-2`&lt;/li&gt;&lt;li&gt;`blog-apparel-3`&lt;/li&gt;&lt;li&gt;`detailed-apparel-1`&lt;/li&gt;&lt;/ul&gt; |", "parameters": [{"description": "The desired `class` attribute.", "name": "class", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'collection-1' | placeholder_svg_tag }}", "parameter": false, "display_type": "html", "show_data_tab": true}, {"name": "class", "description": "Specify the `class` attribute for the `&lt;svg&gt;` tag.", "syntax": "string | placeholder_svg_tag: string", "path": "/", "raw_liquid": "{{ 'collection-1' | placeholder_svg_tag: 'custom-class' }}", "parameter": true, "display_type": "html", "show_data_tab": true}], "summary": "Generates an HTML `&lt;svg&gt;` tag for a given placeholder name.", "syntax": "string | placeholder_svg_tag", "name": "placeholder_svg_tag"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "You should use this filter sparingly. For example, consider preloading only resources necessary for rendering\nabove-the-fold content. To learn more about preloading resources, refer to\n[Performance best practices for Shopify themes](/themes/best-practices/performance#preload-key-resources-defer-or-avoid-loading-others).\n\n&gt; Tip:\n&gt; If you want to preload a stylesheet, then use [`stylesheet_tag`](/docs/api/liquid/filters/stylesheet_tag). If you want to\n&gt; preload an image, then use [`image_tag`](/docs/api/liquid/filters/image_tag).\n\nThe input to this filter must be a URL from one of the following filters:\n\n- [`asset_url`](/docs/api/liquid/filters/asset_url)\n- [`global_asset_url`](/docs/api/liquid/filters/global_asset_url)\n- [`shopify_asset_url`](/docs/api/liquid/filters/shopify_asset_url)\n\nThe `preload_tag` filter also requires an [`as` parameter](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/link#attr-as)\nbased on the kind of resource being preloaded.", "parameters": [{"description": "The type of element or resource to preload.", "name": "as", "positional": false, "required": true, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'cart.js' | asset_url | preload_tag: as: 'script' }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "HTML attributes", "description": "You can specify [HTML attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/link#attributes) by adding a parameter that matches the attribute name, and the desired value.\n", "syntax": "string | preload_tag: as: string, attribute: string", "path": "/", "raw_liquid": "{{ 'cart.js' | asset_url | preload_tag: as: 'script', type: 'text/javascript' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;link&gt;` tag with a `rel` attribute of `preload` to prioritize loading a given Shopify-hosted asset.\nThe asset URL is also added to the [Link header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Link)\nwith a `rel` attribute of `preload`.", "syntax": "string | preload_tag: as: string", "name": "preload_tag"}, {"category": "media", "deprecated": true, "deprecation_reason": "The `product_img_url` filter has been replaced by [`image_url`](/docs/api/liquid/filters/image_url).", "description": "This can be the product's `featured_image` or any image from the `images` array.", "parameters": [{"description": "The desired image size.", "name": "size", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/health-potion", "raw_liquid": "{{ product.featured_image | product_img_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "The size parameter", "description": "By default, the `product_img_url` filter returns the `small` version of the image (100 x 100 px). However, you can specify a [size](/docs/api/liquid/filters/img_url#img_url-size).\n", "syntax": "image | product_img_url: string", "path": "/products/health-potion", "raw_liquid": "{{ product.images[0] | product_img_url: 'large' }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for a [product image](/docs/api/liquid/objects/product).", "syntax": "variable | product_img_url", "name": "product_img_url"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'cart.js' | asset_url | script_tag }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;script&gt;` tag for a given resource URL. The tag has a `type` attribute of `text/javascript`.", "syntax": "string | script_tag", "name": "script_tag"}, {"category": "hosted_file", "deprecated": false, "deprecation_reason": "", "description": "The following are the globally accessible Shopify assets:\n\n- `option_selection.js`\n- `api.jquery.js`\n- `shopify_common.js`\n- `customer_area.js`\n- `currencies.js`\n- `customer.css`", "parameters": [], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'option_selection.js' | shopify_asset_url }}", "parameter": false, "display_type": "text", "show_data_tab": true}], "summary": "Returns the [CDN URL](/themes/best-practices/performance/platform#shopify-cdn) for a globally accessible Shopify asset.", "syntax": "string | shopify_asset_url", "name": "shopify_asset_url"}, {"category": "html", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The type of media that the resource applies to.", "name": "media", "positional": true, "required": false, "types": ["string"]}, {"description": "Whether the resource should be preloaded.", "name": "preload", "positional": false, "required": false, "types": ["boolean"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/", "raw_liquid": "{{ 'base.css' | asset_url | stylesheet_tag }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "preload", "description": "Specify whether the stylesheet should be preloaded.\n\nWhen `preload` is set to `true`, a resource hint is sent as a [Link header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Link) with a `rel` value of\n[`preload`](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types/preload).\n\n```liquid\nLink: &lt;STYLESHEET_URL&gt;; rel=preload; as=style\n```\n\nThis option doesn't affect the HTML link tag directly.\n\nYou should use the `preload` parameter sparingly. For example, consider preloading only render-blocking stylesheets that\nare needed for initial functionality of the page, such as above-the-fold content. To learn more about resource hints in\nShopify themes, refer to [Performance best practices for Shopify themes](/themes/best-practices/performance#preload-key-resources-defer-or-avoid-loading-others).\n", "syntax": "stylesheet_url | stylesheet_tag: preload: boolean", "path": "", "raw_liquid": "", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates an HTML `&lt;link&gt;` tag for a given resource URL. The tag has the following parameters:\n\n| Attribute | Value |\n| --- | --- |\n| `rel` | `stylesheet` |\n| `type` | `text/css` |\n| `media` | `all` |", "syntax": "string | stylesheet_tag", "name": "stylesheet_tag"}, {"category": "format", "deprecated": false, "deprecation_reason": "", "description": "", "parameters": [{"description": "The weight unit to use in place of the default weight unit.", "name": "weight_unit", "positional": true, "required": false, "types": ["string"]}], "return_type": [{"type": "string", "name": "", "description": "", "array_value": ""}], "examples": [{"name": "", "description": "", "syntax": "", "path": "/products/blue-mountain-flower", "raw_liquid": "{%- assign variant = product.variants.first -%}\n\n{{ variant.weight | weight_with_unit }}", "parameter": false, "display_type": "text", "show_data_tab": true}, {"name": "Override the default unit", "description": "You can specify a unit to override the default from the general settings.", "syntax": "number | weight_with_unit: variable", "path": "/products/blue-mountain-flower", "raw_liquid": "{%- assign variant = product.variants.first -%}\n\n{{ variant.weight | weight_with_unit: variant.weight_unit }}", "parameter": true, "display_type": "text", "show_data_tab": true}], "summary": "Generates a formatted weight for a [`variant` object](/docs/api/liquid/objects/variant#variant-weight). The weight unit is\nset in the [general settings](https://www.shopify.com/admin/settings/general) in the Shopify admin.", "syntax": "number | weight_with_unit", "name": "weight_with_unit"}]