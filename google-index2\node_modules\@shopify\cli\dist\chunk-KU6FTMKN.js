import {
  base_command_default
} from "./chunk-2IA24ROR.js";
import {
  ensureAuthenticatedUser,
  fetch,
  findSessionBy<PERSON>lias,
  getSessionAlias,
  identityFqdn
} from "./chunk-ZAVXS5HH.js";
import {
  setCurrentSessionId
} from "./chunk-G2VTHDI5.js";
import {
  outputCompleted,
  renderSelectPrompt
} from "./chunk-B36FYNEM.js";
import {
  require_lib
} from "./chunk-F7F4BQYW.js";
import {
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/commands/auth/login.ts
init_cjs_shims();

// ../cli-kit/dist/public/node/session-prompt.js
init_cjs_shims();
var NEW_LOGIN_VALUE = "NEW_LOGIN";
function buildSessionChoices(sessions, fqdn) {
  let choices = [], fqdnSessions = sessions[fqdn];
  if (fqdnSessions)
    for (let [userId, session] of Object.entries(fqdnSessions))
      choices.push({
        label: session.identity.alias ?? userId,
        value: userId
      });
  return choices;
}
async function handleNewLogin() {
  let result = await ensureAuthenticatedUser({}, { forceNewSession: !0 });
  return await getSessionAlias(result.userId) ?? result.userId;
}
async function getAllChoices() {
  let sessions = await fetch(), fqdn = await identityFqdn(), choices = [];
  return sessions && choices.push(...buildSessionChoices(sessions, fqdn)), choices.length > 0 && choices.push({
    label: "Log in with a different account",
    value: NEW_LOGIN_VALUE
  }), choices;
}
async function promptSessionSelect(alias) {
  if (alias) {
    let userId = await findSessionByAlias(alias);
    if (userId)
      return setCurrentSessionId(userId), alias;
  }
  let choices = await getAllChoices(), selectedValue = NEW_LOGIN_VALUE;
  return choices.length > 0 && (selectedValue = await renderSelectPrompt({ message: "Which account would you like to use?", choices })), selectedValue === NEW_LOGIN_VALUE ? handleNewLogin() : (setCurrentSessionId(selectedValue), choices.find((choice) => choice.value === selectedValue)?.label ?? selectedValue);
}

// src/cli/commands/auth/login.ts
var import_core = __toESM(require_lib(), 1);
var Login = class _Login extends base_command_default {
  static {
    this.description = "Logs you in to your Shopify account.";
  }
  static {
    this.flags = {
      alias: import_core.Flags.string({
        description: "Alias of the session you want to login to.",
        env: "SHOPIFY_FLAG_AUTH_ALIAS"
      })
    };
  }
  async run() {
    let { flags } = await this.parse(_Login), result = await promptSessionSelect(flags.alias);
    outputCompleted(`Current account: ${result}.`);
  }
};

export {
  promptSessionSelect,
  Login
};
//# sourceMappingURL=chunk-KU6FTMKN.js.map
