@font-face {
  font-family: 'Inter';
  src: url('../assets/inter-variable-font.woff2') format('woff2-variations');
}
@font-face {
  font-family: 'JetBrains Mono';
  src: url('../assets/jetbrainsmono-variable-font.woff2')
    format('woff2-variations');
}

* {
  box-sizing: border-box;
}

h1,
h2,
p {
  margin: 0;
  padding: 0;
}

h1 {
  font-size: 3rem;
  line-height: 1.25;
}

h2 {
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1.4;
}

p {
  font-size: 1rem;
  line-height: 1.4;
  font-weight: 500;
}

body {
  padding: 0;
  margin: 0;
  background: #fff;
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.hydrogen-virtual-route {
  display: grid;
  grid-template-rows: auto 1fr auto;
  min-height: 100vh;
}

header {
  display: flex;
  flex-direction: row;
  gap: 20px;
  padding: 14px 20px;
  width: 100%;
  height: 48px;
  background: #ffffff;
  align-items: center;
  color: #5c5f62;
}

header nav {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  margin-left: auto;
}

header h1 {
  font-size: 0.75rem;
}

header p {
  font-family: 'JetBrains Mono';
  font-weight: 700;
  font-size: 0.875rem;
  border: 1.5px solid #d2d5d8;
  border-radius: 4px;
  padding: 1px;
  white-space: nowrap;
}

main {
  padding: 2rem;
  max-width: 50rem;
  margin: auto;
}

footer {
  background: #f6f6f7;
}

footer div {
  display: block;
  max-width: 50rem;
  padding: 2rem;
  margin: auto;
}

footer a {
  color: #5c5f62;
  text-decoration: none;
}

main > h1 {
  color: #202223;
  font-weight: 900;
  margin-bottom: 24px;
}

main > p {
  margin-bottom: 16px;
}

main > section {
  margin-bottom: 40px;
}

main a {
  color: #475f91;
  font-size: 1rem;
  position: relative;
  font-weight: 500;
}

main a::after {
  content: url("data:image/svg+xml,%3Csvg width='18' height='24' viewBox='0 0 18 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.45312 19.3828H13.9297C15.5703 19.3828 16.3828 18.5703 16.3828 16.9609V7.42188C16.3828 5.8125 15.5703 5 13.9297 5H4.45312C2.82031 5 2 5.80469 2 7.42188V16.9609C2 18.5781 2.82031 19.3828 4.45312 19.3828ZM4.46875 18.125C3.6875 18.125 3.25781 17.7109 3.25781 16.8984V7.48438C3.25781 6.67188 3.6875 6.25781 4.46875 6.25781H13.9141C14.6875 6.25781 15.125 6.67188 15.125 7.48438V16.8984C15.125 17.7109 14.6875 18.125 13.9141 18.125H4.46875ZM11.6406 14.1172C11.9844 14.1172 12.2188 13.8516 12.2188 13.4922V9.80469C12.2188 9.34375 11.9609 9.16406 11.5625 9.16406H7.85938C7.49219 9.16406 7.25781 9.39062 7.25781 9.73438C7.25781 10.0781 7.5 10.3047 7.875 10.3047H9.29688L10.4531 10.1797L9.23438 11.3125L6.35156 14.1953C6.24219 14.3047 6.17188 14.4609 6.17188 14.6172C6.17188 14.9688 6.39844 15.1953 6.74219 15.1953C6.92969 15.1953 7.07812 15.125 7.1875 15.0156L10.0625 12.1406L11.1875 10.9375L11.0703 12.1562V13.5078C11.0703 13.875 11.2969 14.1172 11.6406 14.1172Z' fill='%23475F91'/%3E%3C/svg%3E%0A");
  vertical-align: middle;
}

main {
  display: flex;
  flex-direction: column;
}

.Links ul {
  margin: 0;
  padding: 0 0.5rem;
  list-style: none;
}

.Links li {
  margin-bottom: 1rem;
}

.Links li::before {
  content: '\25fc';
  color: #5c5f62;
  display: inline-block;
  vertical-align: text-bottom;
  width: 1.2rem;
  font-size: 0.8rem;
}

.Links h2 {
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  color: #44474a;
  font-weight: 700;
}

.Banner {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px;
  gap: 8px;
  box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  font-style: normal;
  font-size: 1rem;
  line-height: 24px;
  color: #5c5f62;
  border: 1px solid #d2d5d8;
  background: #fafbfb;
}

.Banner div {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

.Banner p {
  padding-left: 48px;
}

.Banner code {
  background: #f6f6f7;
  border: 1px solid #d2d5d8;
  font-family: monospace;
  border-radius: 4px;
  padding: 2px;
}

.Banner h2 {
  font-size: 1rem;
  color: #202223;
}

.Banner.ErrorBanner {
  color: #981c06;
  border: 1px solid #fda9a5;
  background: #fee9e8;
}

.Banner.ErrorBanner code {
  background: #fef4f4;
  border: 1px solid #fda9a5;
  border-radius: 4px;
  padding: 2px;
  color: #74180c;
}

.Banner.ErrorBanner a {
  color: #b92409;
}

.Banner.ErrorBanner a::after {
  content: url("data:image/svg+xml,%3Csvg width='18' height='24' viewBox='0 0 18 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.45312 19.3828H13.9297C15.5703 19.3828 16.3828 18.5703 16.3828 16.9609V7.42188C16.3828 5.8125 15.5703 5 13.9297 5H4.45312C2.82031 5 2 5.80469 2 7.42188V16.9609C2 18.5781 2.82031 19.3828 4.45312 19.3828ZM4.46875 18.125C3.6875 18.125 3.25781 17.7109 3.25781 16.8984V7.48438C3.25781 6.67188 3.6875 6.25781 4.46875 6.25781H13.9141C14.6875 6.25781 15.125 6.67188 15.125 7.48438V16.8984C15.125 17.7109 14.6875 18.125 13.9141 18.125H4.46875ZM11.6406 14.1172C11.9844 14.1172 12.2188 13.8516 12.2188 13.4922V9.80469C12.2188 9.34375 11.9609 9.16406 11.5625 9.16406H7.85938C7.49219 9.16406 7.25781 9.39062 7.25781 9.73438C7.25781 10.0781 7.5 10.3047 7.875 10.3047H9.29688L10.4531 10.1797L9.23438 11.3125L6.35156 14.1953C6.24219 14.3047 6.17188 14.4609 6.17188 14.6172C6.17188 14.9688 6.39844 15.1953 6.74219 15.1953C6.92969 15.1953 7.07812 15.125 7.1875 15.0156L10.0625 12.1406L11.1875 10.9375L11.0703 12.1562V13.5078C11.0703 13.875 11.2969 14.1172 11.6406 14.1172Z' fill='%23B92409'/%3E%3C/svg%3E%0A");
  vertical-align: middle;
  color: red;
}

.Banner.ErrorBanner > h2 {
  font-size: 1rem;
  color: #74180c;
}
