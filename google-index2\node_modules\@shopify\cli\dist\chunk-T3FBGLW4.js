import {
  base_command_default
} from "./chunk-2IA24ROR.js";
import {
  outputResult,
  renderTable
} from "./chunk-B36FYNEM.js";
import {
  require_lib
} from "./chunk-F7F4BQYW.js";
import {
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/commands/debug/command-flags.ts
init_cjs_shims();
var import_core = __toESM(require_lib(), 1), CommandFlags = class _CommandFlags extends base_command_default {
  static {
    this.description = "View all the available command flags";
  }
  static {
    this.hidden = !0;
  }
  static {
    this.flags = {
      // Similar options as the `commands` command from `plugin-commands`
      csv: import_core.Flags.boolean({
        description: "Output as CSV",
        env: "SHOPIFY_FLAG_OUTPUT_CSV",
        default: !1
      })
    };
  }
  async run() {
    let { flags } = await this.parse(_CommandFlags), data = [];
    for (let [_, plugin] of this.config.plugins)
      for (let command of plugin.commands) {
        let loaded = await command.load(), pluginName;
        plugin.name.startsWith("@shopify") ? pluginName = plugin.name.substring(9) : pluginName = plugin.name.startsWith("@oclif") ? plugin.name.substring(7) : plugin.name;
        let flags2 = loaded.flags;
        if (flags2)
          for (let [flagName, flagConfig] of Object.entries(flags2))
            data.push({
              pluginName,
              command: command.id,
              flagName: `--${flagName}`,
              flagChar: flagConfig.char ? `-${flagConfig.char}` : "",
              flagEnv: flagConfig.env
            });
      }
    if (flags.csv) {
      let columns = ["pluginName", "command", "flagName", "flagChar", "flagEnv"], header = `${columns.join(",")}
`, rows = data.map((obj) => columns.map((key) => obj[key]).join(",")).join(`
`), csvString = `${header}${rows}
`;
      outputResult(csvString);
    } else
      renderTable({
        rows: data,
        columns: {
          pluginName: {
            header: "plugin",
            color: "red"
          },
          command: {},
          flagName: {
            header: "long flag",
            color: "green"
          },
          flagChar: {
            header: "short flag",
            color: "green"
          },
          flagEnv: {
            header: "env variable",
            color: "blueBright"
          }
        }
      });
  }
};

export {
  CommandFlags
};
//# sourceMappingURL=chunk-T3FBGLW4.js.map
