import { Modal, VerticalStack, Text, Box, Image, HorizontalGrid, Button, Toast } from '@shopify/polaris';
import { useState } from 'react';

export default function GoogleModals({
  showCloudAccessModal,
  setShowCloudAccessModal,
  showWebsiteAccessModal,
  setShowWebsiteAccessModal,
  showOAuthModal,
  setShowOAuthModal,
  shopConfig,
  clientID,
  copyClientID,
  googleLogin,
  refreshShopConfig
}) {
  const myFetch = fetch;
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationToast, setVerificationToast] = useState({ show: false, content: '', error: false });
  
  const onVerifyPermission = async () => {
    setIsVerifying(true);
    try {
      const response = await myFetch('/api/checkWorkReady-Remote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
      });
      if (response.ok) {
        const result = await response.json();
        if (result.status) {
          setVerificationToast({
            show: true,
            content: 'Website access permission verified successfully!',
            error: false
          });
          refreshShopConfig();
        } else {
          setVerificationToast({
            show: true,
            content: 'Website access permission not granted yet. Please complete the setup in Google Search Console.',
            error: true
          });
        }
      }
    } catch (error) {
      console.error(`error:${error}`);
      setVerificationToast({
        show: true,
        content: 'Error verifying permission. Please try again.',
        error: true
      });
      
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <>
      {/* Verification Toast */}
      {verificationToast.show && (
        <Toast
          content={verificationToast.content}
          error={verificationToast.error}
          onDismiss={() => setVerificationToast({ show: false, content: '', error: false })}
          duration={4000}
        />
      )}
      
      {/* Cloud Access Modal */}
      <Modal
        open={showCloudAccessModal}
        onClose={() => setShowCloudAccessModal(false)}
        title="Accept Google Cloud Agreement"
        primaryAction={{
          content: 'Continue to Google Cloud',
          onAction: () => {
            window.open('https://console.cloud.google.com/', '_blank');
            setShowCloudAccessModal(false);
          },
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => setShowCloudAccessModal(false),
          },
        ]}
      >
        <Modal.Section>
          <VerticalStack gap="4">
            <Text variant="bodyMd" as="p">
              To enable Google indexing services, you need to accept the agreement for Google Cloud Console.
              This allows our application to access Google Search Console API via Google Cloud on your behalf. 
            </Text>

            <Box>
              <Image
                source="/Cloud.png"
                alt="Google Cloud Access Configuration Guide"
                width="100%"
              />
            </Box>

            <Text variant="bodyMd" as="p" color="subdued">
              Follow the steps shown in the image above. Click "Continue to Google Cloud" to open
              the Google Cloud Console page in a new tab.
            </Text>
          </VerticalStack>
        </Modal.Section>
      </Modal>

      {/* Website Access Modal */}
      <Modal
        open={showWebsiteAccessModal}
        onClose={() => setShowWebsiteAccessModal(false)}
        title="Grant Website Access"
        primaryAction={{
          content: 'Step1: Open Search Console',
          onAction: () => {
            let domain = shopConfig.shop || '';
            if (domain.startsWith('https://')) {
              domain = domain.replace('https://', '');
            } else if (domain.startsWith('http://')) {
              domain = domain.replace('http://', '');
            }
            domain = domain.replace(/\/$/, '');
            
            const searchConsoleUrl = `https://search.google.com/search-console/users?resource_id=sc-domain%3A${domain}`;
            window.open(searchConsoleUrl, '_blank');
            setShowWebsiteAccessModal(false);
          },
        }}
         secondaryActions={[
          {
            content: 'Step2: Verify Permission',
            onAction: onVerifyPermission,
          }
        ]}
      >
        <Modal.Section>
          <VerticalStack gap="4">
            <Text variant="bodyMd" as="p">
              To enable website indexing, you need to add your Shopify store to Google Search Console
              and verify ownership. This allows our application to submit your pages for indexing.
            </Text>

            {clientID && (
              <Box padding="4" background="bg-surface-secondary" borderRadius="2">
                <HorizontalGrid columns="1fr auto" gap="2" alignItems="center">
                  <VerticalStack gap="2">
                    <Text variant="bodyMd" as="p" fontWeight="semibold">
                      Service Account Email:
                    </Text>
                    <Text variant="bodyMd" as="p" color="subdued">
                      {clientID}
                    </Text>
                    <Text variant="bodySm" as="p" color="subdued">
                      You'll need to add this email as a user in Google Search Console with <b>"Owner"</b> permissions.
                    </Text>
                  </VerticalStack>
                  <Button
                    size="small"
                    onClick={copyClientID}
                  >
                    Copy Email
                  </Button>
                </HorizontalGrid>

              </Box>
              
            )}

            <Box>
              <Image
                source="/Email.png"
                alt="Google Search Console Website Setup Guide"
                width="100%"
              />
            </Box>


          </VerticalStack>
        </Modal.Section>
      </Modal>

      {/* OAuth Authentication Modal */}
      <Modal
        open={showOAuthModal}
        onClose={() => setShowOAuthModal(false)}
        title="Google OAuth Authentication"
        primaryAction={{
          content: 'Continue to Google OAuth',
          onAction: () => {
            googleLogin(shopConfig.privateShop);
            setShowOAuthModal(false);
          },
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => setShowOAuthModal(false),
          },
        ]}
      >
        <Modal.Section>
          <VerticalStack gap="4">
            <Text variant="bodyMd" as="p">
              To enable Google indexing services, you need to authenticate with Google.
              Please <strong style="color: red;"> DO CLICK SELECT ALL </strong> as below during the Google login.
            </Text>
            
            <Box>
              <Image
                source="/Oauth.png"
                alt="Google OAuth Authentication Guide"
                width="100%"
              />
            </Box>
            
            <Text variant="bodyMd" as="p" color="subdued">
              Click "Continue to Google OAuth" to start the authentication process.
              You'll be redirected to Google's login page in a new window.
            </Text>
          </VerticalStack>
        </Modal.Section>
      </Modal>
    </>
  );
}




