{"commands": {"app:build": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "This command executes the build script specified in the element's TOML file. You can specify a custom script in the file. To learn about configuration files in Shopify apps, refer to \"App configuration\" (https://shopify.dev/docs/apps/tools/cli/configuration).\n\n  If you're building a \"theme app extension\" (https://shopify.dev/docs/apps/online-store/theme-app-extensions), then running the `build` command runs \"Theme Check\" (https://shopify.dev/docs/themes/tools/theme-check) against your extension to ensure that it's valid.", "descriptionWithMarkdown": "This command executes the build script specified in the element's TOML file. You can specify a custom script in the file. To learn about configuration files in Shopify apps, refer to [App configuration](https://shopify.dev/docs/apps/tools/cli/configuration).\n\n  If you're building a [theme app extension](https://shopify.dev/docs/apps/online-store/theme-app-extensions), then running the `build` command runs [Theme Check](https://shopify.dev/docs/themes/tools/theme-check) against your extension to ensure that it's valid.", "flags": {"api-key": {"description": "Application's API key that will be exposed at build time.", "env": "SHOPIFY_FLAG_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "skip-dependencies-installation": {"allowNo": false, "description": "Skips the installation of dependencies. Deprecated, use workspaces instead.", "env": "SHOPIFY_FLAG_SKIP_DEPENDENCIES_INSTALLATION", "hidden": false, "name": "skip-dependencies-installation", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:build", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Build the app, including extensions."}, "app:config:link": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Pulls app configuration from the Partner Dashboard and creates or overwrites a configuration file. You can create a new app with this command to start with a default configuration file.\n\n  For more information on the format of the created TOML configuration file, refer to the \"App configuration\" (https://shopify.dev/docs/apps/tools/cli/configuration) page.\n  ", "descriptionWithMarkdown": "Pulls app configuration from the Partner Dashboard and creates or overwrites a configuration file. You can create a new app with this command to start with a default configuration file.\n\n  For more information on the format of the created TOML configuration file, refer to the [App configuration](https://shopify.dev/docs/apps/tools/cli/configuration) page.\n  ", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:config:link", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Fetch your app configuration from the Partner Dashboard."}, "app:config:use": {"aliases": [], "args": {"config": {"description": "The name of the app configuration. Can be 'shopify.app.staging.toml' or simply 'staging'.", "name": "config"}}, "customPluginName": "@shopify/app", "description": "Sets default configuration when you run app-related CLI commands. If you omit the `config-name` parameter, then you'll be prompted to choose from the configuration files in your project.", "descriptionWithMarkdown": "Sets default configuration when you run app-related CLI commands. If you omit the `config-name` parameter, then you'll be prompted to choose from the configuration files in your project.", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:config:use", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Activate an app configuration.", "usage": "app config use [config] [flags]"}, "app:deploy": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "\"Builds the app\" (https://shopify.dev/docs/api/shopify-cli/app/app-build), then deploys your app configuration and extensions.\n\n  This command creates an app version, which is a snapshot of your app configuration and all extensions, including the app extensions that you manage in the Partner Dashboard. This version is then released to users.\n\n  This command doesn't deploy your \"web app\" (https://shopify.dev/docs/apps/tools/cli/structure#web-components). You need to \"deploy your web app\" (https://shopify.dev/docs/apps/deployment/web) to your own hosting solution.\n  ", "descriptionWithMarkdown": "[Builds the app](https://shopify.dev/docs/api/shopify-cli/app/app-build), then deploys your app configuration and extensions.\n\n  This command creates an app version, which is a snapshot of your app configuration and all extensions, including the app extensions that you manage in the Partner Dashboard. This version is then released to users.\n\n  This command doesn't deploy your [web app](https://shopify.dev/docs/apps/tools/cli/structure#web-components). You need to [deploy your web app](https://shopify.dev/docs/apps/deployment/web) to your own hosting solution.\n  ", "flags": {"api-key": {"description": "The API key of your app.", "env": "SHOPIFY_FLAG_APP_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Deploy without asking for confirmation.", "env": "SHOPIFY_FLAG_FORCE", "hidden": false, "name": "force", "type": "boolean"}, "message": {"description": "Optional message that will be associated with this version. This is for internal use only and won't be available externally.", "env": "SHOPIFY_FLAG_MESSAGE", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "message", "type": "option"}, "no-build": {"allowNo": false, "description": "Use with caution: Skips building any elements of the app that require building. You should ensure your app has been prepared in advance, such as by running `shopify app build` or by caching build artifacts.", "env": "SHOPIFY_FLAG_NO_BUILD", "name": "no-build", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "no-release": {"allowNo": false, "description": "Creates a version but doesn't release it - it's not made available to merchants.", "env": "SHOPIFY_FLAG_NO_RELEASE", "hidden": false, "name": "no-release", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "source-control-url": {"description": "URL associated with the new app version.", "env": "SHOPIFY_FLAG_SOURCE_CONTROL_URL", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "source-control-url", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}, "version": {"description": "Optional version tag that will be associated with this app version. If not provided, an auto-generated identifier will be generated for this app version.", "env": "SHOPIFY_FLAG_VERSION", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "version", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:deploy", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Deploy your Shopify app."}, "app:dev": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Builds and previews your app on a development store, and watches for changes. \"Read more about testing apps locally\" (https://shopify.dev/docs/apps/build/cli-for-apps/test-apps-locally).", "descriptionWithMarkdown": "Builds and previews your app on a development store, and watches for changes. [Read more about testing apps locally](https://shopify.dev/docs/apps/build/cli-for-apps/test-apps-locally).", "flags": {"api-key": {"description": "The API key of your app.", "env": "SHOPIFY_FLAG_APP_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "checkout-cart-url": {"description": "Resource URL for checkout UI extension. Format: \"/cart/{productVariantID}:{productQuantity}\"", "env": "SHOPIFY_FLAG_CHECKOUT_CART_URL", "hasDynamicHelp": false, "multiple": false, "name": "checkout-cart-url", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "graphiql-key": {"description": "Key used to authenticate GraphiQL requests. Should be specified if exposing GraphiQL on a publicly accessible URL. By default, no key is required.", "env": "SHOPIFY_FLAG_GRAPHIQL_KEY", "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "graphiql-key", "type": "option"}, "graphiql-port": {"description": "Local port of the GraphiQL development server.", "env": "SHOPIFY_FLAG_GRAPHIQL_PORT", "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "graphiql-port", "type": "option"}, "localhost-port": {"description": "Port to use for localhost.", "env": "SHOPIFY_FLAG_LOCALHOST_PORT", "hasDynamicHelp": false, "multiple": false, "name": "localhost-port", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "no-update": {"allowNo": false, "description": "Skips the Partners Dashboard URL update step.", "env": "SHOPIFY_FLAG_NO_UPDATE", "name": "no-update", "type": "boolean"}, "notify": {"description": "The file path or URL. The file path is to a file that you want updated on idle. The URL path is where you want a webhook posted to report on file changes.", "env": "SHOPIFY_FLAG_NOTIFY", "hasDynamicHelp": false, "multiple": false, "name": "notify", "type": "option"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "skip-dependencies-installation": {"allowNo": false, "description": "Skips the installation of dependencies. Deprecated, use workspaces instead.", "env": "SHOPIFY_FLAG_SKIP_DEPENDENCIES_INSTALLATION", "name": "skip-dependencies-installation", "type": "boolean"}, "store": {"char": "s", "description": "Store URL. Must be an existing development or Shopify Plus sandbox store.", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "subscription-product-url": {"description": "Resource URL for subscription UI extension. Format: \"/products/{productId}\"", "env": "SHOPIFY_FLAG_SUBSCRIPTION_PRODUCT_URL", "hasDynamicHelp": false, "multiple": false, "name": "subscription-product-url", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the theme app extension host theme.", "env": "SHOPIFY_FLAG_THEME", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "theme-app-extension-port": {"description": "Local port of the theme app extension development server.", "env": "SHOPIFY_FLAG_THEME_APP_EXTENSION_PORT", "hasDynamicHelp": false, "multiple": false, "name": "theme-app-extension-port", "type": "option"}, "tunnel-url": {"description": "Use a custom tunnel, it must be running before executing dev. Format: \"https://my-tunnel-url:port\".", "env": "SHOPIFY_FLAG_TUNNEL_URL", "exclusive": ["tunnel"], "hasDynamicHelp": false, "multiple": false, "name": "tunnel-url", "type": "option"}, "use-localhost": {"allowNo": false, "description": "Service entry point will listen to localhost. A tunnel won't be used. Will work for testing many app features, but not those that directly invoke your app (E.g: Webhooks)", "env": "SHOPIFY_FLAG_USE_LOCALHOST", "exclusive": ["tunnel-url"], "name": "use-localhost", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:dev", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Run the app."}, "app:dev:clean": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Stop the app preview that was started with `shopify app dev`.\n\n  It restores the app's active version to the selected development store.\n  ", "descriptionWithMarkdown": "Stop the app preview that was started with `shopify app dev`.\n\n  It restores the app's active version to the selected development store.\n  ", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "store": {"char": "s", "description": "Store URL. Must be an existing development store.", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "store", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:dev:clean", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Cleans up the app preview from the selected store."}, "app:env:pull": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Creates or updates an `.env` files that contains app and app extension environment variables.\n\n  When an existing `.env` file is updated, changes to the variables are displayed in the terminal output. Existing variables and commented variables are preserved.", "descriptionWithMarkdown": "Creates or updates an `.env` files that contains app and app extension environment variables.\n\n  When an existing `.env` file is updated, changes to the variables are displayed in the terminal output. Existing variables and commented variables are preserved.", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "env-file": {"description": "Specify an environment file to update if the update flag is set", "env": "SHOPIFY_FLAG_ENV_FILE", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "env-file", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:env:pull", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Pull app and extensions environment variables."}, "app:env:show": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Displays environment variables that can be used to deploy apps and app extensions.", "descriptionWithMarkdown": "Displays environment variables that can be used to deploy apps and app extensions.", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:env:show", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Display app and extensions environment variables."}, "app:function:build": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Compiles the function in your current directory to WebAssembly (Wasm) for testing purposes.", "descriptionWithMarkdown": "Compiles the function in your current directory to WebAssembly (Wasm) for testing purposes.", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your function directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:function:build", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Compile a function to wasm."}, "app:function:replay": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Runs the function from your current directory for \"testing purposes\" (https://shopify.dev/docs/apps/functions/testing-and-debugging). To learn how you can monitor and debug functions when errors occur, refer to \"Shopify Functions error handling\" (https://shopify.dev/docs/api/functions/errors).", "descriptionWithMarkdown": "Runs the function from your current directory for [testing purposes](https://shopify.dev/docs/apps/functions/testing-and-debugging). To learn how you can monitor and debug functions when errors occur, refer to [Shopify Functions error handling](https://shopify.dev/docs/api/functions/errors).", "flags": {"api-key": {"description": "Application's API key", "env": "SHOPIFY_FLAG_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "log": {"char": "l", "description": "Specifies a log identifier to replay instead of selecting from a list. The identifier is provided in the output of `shopify app dev` and is the suffix of the log file name.", "env": "SHOPIFY_FLAG_LOG", "hasDynamicHelp": false, "multiple": false, "name": "log", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your function directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}, "watch": {"allowNo": true, "char": "w", "description": "Re-run the function when the source code changes.", "env": "SHOPIFY_FLAG_WATCH", "hidden": false, "name": "watch", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:function:replay", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Replays a function run from an app log."}, "app:function:run": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Runs the function from your current directory for \"testing purposes\" (https://shopify.dev/docs/apps/functions/testing-and-debugging). To learn how you can monitor and debug functions when errors occur, refer to \"Shopify Functions error handling\" (https://shopify.dev/docs/api/functions/errors).", "descriptionWithMarkdown": "Runs the function from your current directory for [testing purposes](https://shopify.dev/docs/apps/functions/testing-and-debugging). To learn how you can monitor and debug functions when errors occur, refer to [Shopify Functions error handling](https://shopify.dev/docs/api/functions/errors).", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "export": {"char": "e", "description": "Name of the WebAssembly export to invoke.", "env": "SHOPIFY_FLAG_EXPORT", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "export", "type": "option"}, "input": {"char": "i", "description": "The input JSON to pass to the function. If omitted, standard input is used.", "env": "SHOPIFY_FLAG_INPUT", "hasDynamicHelp": false, "multiple": false, "name": "input", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your function directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:function:run", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Run a function locally for testing."}, "app:function:schema": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Generates the latest \"GraphQL schema\" (https://shopify.dev/docs/apps/functions/input-output#graphql-schema) for a function in your app. Run this command from the function directory.\n\n  This command uses the API type and version of your function, as defined in your extension TOML file, to generate the latest GraphQL schema. The schema is written to the `schema.graphql` file.", "descriptionWithMarkdown": "Generates the latest [GraphQL schema](https://shopify.dev/docs/apps/functions/input-output#graphql-schema) for a function in your app. Run this command from the function directory.\n\n  This command uses the API type and version of your function, as defined in your extension TOML file, to generate the latest GraphQL schema. The schema is written to the `schema.graphql` file.", "flags": {"api-key": {"description": "The API key to fetch the schema with.", "env": "SHOPIFY_FLAG_APP_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your function directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "stdout": {"allowNo": false, "description": "Output the schema to stdout instead of writing to a file.", "env": "SHOPIFY_FLAG_STDOUT", "name": "stdout", "required": false, "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:function:schema", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Fetch the latest GraphQL schema for a function."}, "app:function:typegen": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Creates GraphQL types based on your \"input query\" (https://shopify.dev/docs/apps/functions/input-output#input) for a function written in JavaScript.", "descriptionWithMarkdown": "Creates GraphQL types based on your [input query](https://shopify.dev/docs/apps/functions/input-output#input) for a function written in JavaScript.", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your function directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:function:typegen", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Generate GraphQL types for a JavaScript function."}, "app:generate:extension": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Generates a new \"app extension\" (https://shopify.dev/docs/apps/app-extensions). For a list of app extensions that you can generate using this command, refer to \"Supported extensions\" (https://shopify.dev/docs/apps/structure/app-extensions/list).\n\n  Each new app extension is created in a folder under `extensions/`. To learn more about the extensions file structure, refer to \"App structure\" (https://shopify.dev/docs/apps/tools/cli/structure) and the documentation for your extension.\n  ", "descriptionWithMarkdown": "Generates a new [app extension](https://shopify.dev/docs/apps/app-extensions). For a list of app extensions that you can generate using this command, refer to [Supported extensions](https://shopify.dev/docs/apps/structure/app-extensions/list).\n\n  Each new app extension is created in a folder under `extensions/`. To learn more about the extensions file structure, refer to [App structure](https://shopify.dev/docs/apps/tools/cli/structure) and the documentation for your extension.\n  ", "flags": {"api-key": {"description": "The API key of your app.", "env": "SHOPIFY_FLAG_APP_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "clone-url": {"char": "u", "description": "The Git URL to clone the function extensions templates from. Defaults to: https://github.com/Shopify/function-examples", "env": "SHOPIFY_FLAG_CLONE_URL", "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "clone-url", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "flavor": {"description": "Choose a starting template for your extension, where applicable", "env": "SHOPIFY_FLAG_FLAVOR", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "flavor", "options": ["vanilla-js", "react", "typescript", "typescript-react", "wasm", "rust"], "type": "option"}, "name": {"char": "n", "description": "name of your Extension", "env": "SHOPIFY_FLAG_NAME", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "name", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "template": {"char": "t", "description": "Extension template", "env": "SHOPIFY_FLAG_EXTENSION_TEMPLATE", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "template", "type": "option"}, "type": {"char": "t", "description": "Deprecated. Please use --template", "env": "SHOPIFY_FLAG_EXTENSION_TYPE", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "type", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:generate:extension", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Generate a new app Extension."}, "app:generate:schema": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "\"DEPRECATED, use `app function schema`] Generates the latest [GraphQL schema\" (https://shopify.dev/docs/apps/functions/input-output#graphql-schema) for a function in your app. Run this command from the function directory.\n\n  This command uses the API type and version of your function, as defined in your extension TOML file, to generate the latest GraphQL schema. The schema is written to the `schema.graphql` file.", "descriptionWithMarkdown": "[DEPRECATED, use `app function schema`] Generates the latest [GraphQL schema](https://shopify.dev/docs/apps/functions/input-output#graphql-schema) for a function in your app. Run this command from the function directory.\n\n  This command uses the API type and version of your function, as defined in your extension TOML file, to generate the latest GraphQL schema. The schema is written to the `schema.graphql` file.", "flags": {"api-key": {"description": "The API key to fetch the schema with.", "env": "SHOPIFY_FLAG_APP_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your function directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "stdout": {"allowNo": false, "description": "Output the schema to stdout instead of writing to a file.", "env": "SHOPIFY_FLAG_STDOUT", "name": "stdout", "required": false, "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "app:generate:schema", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "summary": "Fetch the latest GraphQL schema for a function."}, "app:import-extensions": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Import dashboard-managed extensions into your app.", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:import-extensions", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "app:info": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "The information returned includes the following:\n\n  - The app and development store or Plus sandbox store that's used when you run the \"dev\" (https://shopify.dev/docs/api/shopify-cli/app/app-dev) command. You can reset these configurations using \"`dev --reset`\" (https://shopify.dev/docs/api/shopify-cli/app/app-dev#flags-propertydetail-reset).\n  - The \"structure\" (https://shopify.dev/docs/apps/tools/cli/structure) of your app project.\n  - The \"access scopes\" (https://shopify.dev/docs/api/usage) your app has requested.\n  - System information, including the package manager and version of Shopify CLI used in the project.", "descriptionWithMarkdown": "The information returned includes the following:\n\n  - The app and development store or Plus sandbox store that's used when you run the [dev](https://shopify.dev/docs/api/shopify-cli/app/app-dev) command. You can reset these configurations using [`dev --reset`](https://shopify.dev/docs/api/shopify-cli/app/app-dev#flags-propertydetail-reset).\n  - The [structure](https://shopify.dev/docs/apps/tools/cli/structure) of your app project.\n  - The [access scopes](https://shopify.dev/docs/api/usage) your app has requested.\n  - System information, including the package manager and version of Shopify CLI used in the project.", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}, "web-env": {"allowNo": false, "description": "Outputs environment variables necessary for running and deploying web/.", "env": "SHOPIFY_FLAG_OUTPUT_WEB_ENV", "hidden": false, "name": "web-env", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:info", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Print basic information about your app and extensions."}, "app:init": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "flags": {"client-id": {"description": "The Client ID of your app. Use this to automatically link your new project to an existing app. Using this flag avoids the app selection prompt.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "flavor": {"description": "Which flavor of the given template to use.", "env": "SHOPIFY_FLAG_TEMPLATE_FLAVOR", "hasDynamicHelp": false, "multiple": false, "name": "flavor", "type": "option"}, "local": {"allowNo": false, "char": "l", "env": "SHOPIFY_FLAG_LOCAL", "hidden": true, "name": "local", "type": "boolean"}, "name": {"char": "n", "env": "SHOPIFY_FLAG_NAME", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "name", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "package-manager": {"char": "d", "env": "SHOPIFY_FLAG_PACKAGE_MANAGER", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "package-manager", "options": ["npm", "yarn", "pnpm", "bun"], "type": "option"}, "path": {"char": "p", "default": ".", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "path", "type": "option"}, "template": {"description": "The app template. Accepts one of the following:\n       - <reactRouter|remix|none>\n       - Any GitHub repo with optional branch and subpath, e.g., https://github.com/Shopify/<repository>/[subpath]#[branch]", "env": "SHOPIFY_FLAG_TEMPLATE", "hasDynamicHelp": false, "multiple": false, "name": "template", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:init", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Create a new app project"}, "app:logs": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "\n  Opens a real-time stream of detailed app logs from the selected app and store.\n  Use the `--source` argument to limit output to a particular log source, such as a specific Shopify Function handle. Use the `shopify app logs sources` command to view a list of sources.\n  Use the `--status` argument to filter on status, either `success` or `failure`.\n  ```\n  shopify app logs --status=success --source=extension.discount-function\n  ```\n  ", "descriptionWithMarkdown": "\n  Opens a real-time stream of detailed app logs from the selected app and store.\n  Use the `--source` argument to limit output to a particular log source, such as a specific Shopify Function handle. Use the `shopify app logs sources` command to view a list of sources.\n  Use the `--status` argument to filter on status, either `success` or `failure`.\n  ```\n  shopify app logs --status=success --source=extension.discount-function\n  ```\n  ", "flags": {"api-key": {"description": "The API key of your app.", "env": "SHOPIFY_FLAG_APP_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "source": {"description": "Filters output to the specified log source.", "env": "SHOPIFY_FLAG_SOURCE", "hasDynamicHelp": false, "multiple": true, "name": "source", "type": "option"}, "status": {"description": "Filters output to the specified status (success or failure).", "env": "SHOPIFY_FLAG_STATUS", "hasDynamicHelp": false, "multiple": false, "name": "status", "options": ["success", "failure"], "type": "option"}, "store": {"char": "s", "description": "Store URL. Must be an existing development or Shopify Plus sandbox store.", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": true, "name": "store", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:logs", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Stream detailed logs for your Shopify app."}, "app:logs:sources": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "The output source names can be used with the `--source` argument of `shopify app logs` to filter log output. Currently only function extensions are supported as sources.", "descriptionWithMarkdown": "The output source names can be used with the `--source` argument of `shopify app logs` to filter log output. Currently only function extensions are supported as sources.", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:logs:sources", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Print out a list of sources that may be used with the logs command."}, "app:release": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Releases an existing app version. Pass the name of the version that you want to release using the `--version` flag.", "descriptionWithMarkdown": "Releases an existing app version. Pass the name of the version that you want to release using the `--version` flag.", "flags": {"api-key": {"description": "The API key of your app.", "env": "SHOPIFY_FLAG_APP_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Release without asking for confirmation.", "env": "SHOPIFY_FLAG_FORCE", "hidden": false, "name": "force", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}, "version": {"description": "The name of the app version to release.", "env": "SHOPIFY_FLAG_VERSION", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "version", "required": true, "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:release", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Release an app version.", "usage": "app release --version <version>"}, "app:versions:list": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "Lists the deployed app versions. An app version is a snapshot of your app extensions.", "descriptionWithMarkdown": "Lists the deployed app versions. An app version is a snapshot of your app extensions.", "flags": {"api-key": {"description": "Application's API key to fetch versions for.", "env": "SHOPIFY_FLAG_API_KEY", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "api-key", "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:versions:list", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "List deployed versions of your app."}, "app:webhook:trigger": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "\n  Triggers the delivery of a sample Admin API event topic payload to a designated address.\n\n  You should use this command to experiment with webhooks, to initially test your webhook configuration, or for unit testing. However, to test your webhook configuration from end to end, you should always trigger webhooks by performing the related action in Shopify.\n\n  Because most webhook deliveries use remote endpoints, you can trigger the command from any directory where you can use Shopify CLI, and send the webhook to any of the supported endpoint types. For example, you can run the command from your app's local directory, but send the webhook to a staging environment endpoint.\n\n  To learn more about using webhooks in a Shopify app, refer to \"Webhooks overview\" (https://shopify.dev/docs/apps/webhooks).\n\n  ### Limitations\n\n  - Webhooks triggered using this method always have the same payload, so they can't be used to test scenarios that differ based on the payload contents.\n  - Webhooks triggered using this method aren't retried when they fail.\n  - Trigger requests are rate-limited using the \"Partner API rate limit\" (https://shopify.dev/docs/api/partner#rate_limits).\n  - You can't use this method to validate your API webhook subscriptions.\n  ", "descriptionWithMarkdown": "\n  Triggers the delivery of a sample Admin API event topic payload to a designated address.\n\n  You should use this command to experiment with webhooks, to initially test your webhook configuration, or for unit testing. However, to test your webhook configuration from end to end, you should always trigger webhooks by performing the related action in Shopify.\n\n  Because most webhook deliveries use remote endpoints, you can trigger the command from any directory where you can use Shopify CLI, and send the webhook to any of the supported endpoint types. For example, you can run the command from your app's local directory, but send the webhook to a staging environment endpoint.\n\n  To learn more about using webhooks in a Shopify app, refer to [Webhooks overview](https://shopify.dev/docs/apps/webhooks).\n\n  ### Limitations\n\n  - Webhooks triggered using this method always have the same payload, so they can't be used to test scenarios that differ based on the payload contents.\n  - Webhooks triggered using this method aren't retried when they fail.\n  - Trigger requests are rate-limited using the [Partner API rate limit](https://shopify.dev/docs/api/partner#rate_limits).\n  - You can't use this method to validate your API webhook subscriptions.\n  ", "flags": {"address": {"description": "The URL where the webhook payload should be sent.\n                    You will need a different address type for each delivery-method:\n                          · For remote HTTP testing, use a URL that starts with https://\n      · For local HTTP testing, use http://localhost:{port}/{url-path}\n                          · For Google Pub/Sub, use pubsub://{project-id}:{topic-id}\n                          · For Amazon EventBridge, use an Amazon Resource Name (ARN) starting with arn:aws:events:", "env": "SHOPIFY_FLAG_ADDRESS", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "address", "required": false, "type": "option"}, "api-version": {"description": "The API Version of the webhook topic.", "env": "SHOPIFY_FLAG_API_VERSION", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "api-version", "required": false, "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "client-secret": {"description": "Your app's client secret. This secret allows us to return the X-Shopify-Hmac-SHA256 header that lets you validate the origin of the response that you receive.", "env": "SHOPIFY_FLAG_CLIENT_SECRET", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-secret", "required": false, "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "delivery-method": {"description": "Method chosen to deliver the topic payload. If not passed, it's inferred from the address.", "env": "SHOPIFY_FLAG_DELIVERY_METHOD", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "delivery-method", "options": ["http", "google-pub-sub", "event-bridge"], "required": false, "type": "option"}, "help": {"allowNo": false, "description": "This help. When you run the trigger command the CLI will prompt you for any information that isn't passed using flags.", "env": "SHOPIFY_FLAG_HELP", "hidden": false, "name": "help", "required": false, "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "shared-secret": {"description": "Deprecated. Please use client-secret.", "env": "SHOPIFY_FLAG_SHARED_SECRET", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "shared-secret", "required": false, "type": "option"}, "topic": {"description": "The requested webhook topic.", "env": "SHOPIFY_FLAG_TOPIC", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "topic", "required": false, "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "app:webhook:trigger", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Trigger delivery of a sample webhook topic payload to a designated address."}, "auth:login": {"aliases": [], "args": {}, "description": "Logs you in to your Shopify account.", "enableJsonFlag": false, "flags": {"alias": {"description": "<PERSON>as of the session you want to login to.", "env": "SHOPIFY_FLAG_AUTH_ALIAS", "hasDynamicHelp": false, "multiple": false, "name": "alias", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "auth:login", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "auth:logout": {"aliases": [], "args": {}, "description": "Logs you out of the Shopify account or Partner account and store.", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "auth:logout", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "cache:clear": {"aliases": [], "args": {}, "description": "Clear the CLI cache, used to store some API responses and handle notifications status", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "cache:clear", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "commands": {"aliases": [], "args": {}, "customPluginName": "@oclif/plugin-commands", "description": "List all <%= config.bin %> commands.", "enableJsonFlag": true, "flags": {"columns": {"char": "c", "delimiter": ",", "description": "Only show provided columns (comma-separated).", "exclusive": ["tree"], "hasDynamicHelp": false, "multiple": true, "name": "columns", "options": ["id", "plugin", "summary", "type"], "type": "option"}, "deprecated": {"allowNo": false, "description": "Show deprecated commands.", "name": "deprecated", "type": "boolean"}, "extended": {"allowNo": false, "char": "x", "description": "Show extra columns.", "exclusive": ["tree"], "name": "extended", "type": "boolean"}, "hidden": {"allowNo": false, "description": "Show hidden commands.", "name": "hidden", "type": "boolean"}, "json": {"allowNo": false, "description": "Format output as json.", "helpGroup": "GLOBAL", "name": "json", "type": "boolean"}, "no-truncate": {"allowNo": false, "description": "Do not truncate output.", "exclusive": ["tree"], "name": "no-truncate", "type": "boolean"}, "sort": {"default": "id", "description": "Property to sort by.", "exclusive": ["tree"], "hasDynamicHelp": false, "multiple": false, "name": "sort", "options": ["id", "plugin", "summary", "type"], "type": "option"}, "tree": {"allowNo": false, "description": "Show tree of commands.", "name": "tree", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "commands", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "config:autocorrect:off": {"aliases": [], "args": {}, "customPluginName": "@shopify/plugin-did-you-mean", "description": "Disable autocorrect. Off by default.\n\n  When autocorrection is enabled, Shopify CLI automatically runs a corrected version of your command if a correction is available.\n\n  When autocorrection is disabled, you need to confirm that you want to run corrections for mistyped commands.\n", "descriptionWithMarkdown": "Disable autocorrect. Off by default.\n\n  When autocorrection is enabled, Shopify CLI automatically runs a corrected version of your command if a correction is available.\n\n  When autocorrection is disabled, you need to confirm that you want to run corrections for mistyped commands.\n", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "config:autocorrect:off", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Disable autocorrect. Off by default."}, "config:autocorrect:on": {"aliases": [], "args": {}, "customPluginName": "@shopify/plugin-did-you-mean", "description": "Enable autocorrect. Off by default.\n\n  When autocorrection is enabled, Shopify CLI automatically runs a corrected version of your command if a correction is available.\n\n  When autocorrection is disabled, you need to confirm that you want to run corrections for mistyped commands.\n", "descriptionWithMarkdown": "Enable autocorrect. Off by default.\n\n  When autocorrection is enabled, Shopify CLI automatically runs a corrected version of your command if a correction is available.\n\n  When autocorrection is disabled, you need to confirm that you want to run corrections for mistyped commands.\n", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "config:autocorrect:on", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Enable autocorrect. Off by default."}, "config:autocorrect:status": {"aliases": [], "args": {}, "customPluginName": "@shopify/plugin-did-you-mean", "description": "Check whether autocorrect is enabled or disabled. On by default.\n\n  When autocorrection is enabled, Shopify CLI automatically runs a corrected version of your command if a correction is available.\n\n  When autocorrection is disabled, you need to confirm that you want to run corrections for mistyped commands.\n", "descriptionWithMarkdown": "Check whether autocorrect is enabled or disabled. On by default.\n\n  When autocorrection is enabled, Shopify CLI automatically runs a corrected version of your command if a correction is available.\n\n  When autocorrection is disabled, you need to confirm that you want to run corrections for mistyped commands.\n", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "config:autocorrect:status", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Check whether autocorrect is enabled or disabled. On by default."}, "debug:command-flags": {"aliases": [], "args": {}, "description": "View all the available command flags", "enableJsonFlag": false, "flags": {"csv": {"allowNo": false, "description": "Output as CSV", "env": "SHOPIFY_FLAG_OUTPUT_CSV", "name": "csv", "type": "boolean"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "debug:command-flags", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "demo:watcher": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "flags": {"client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "demo:watcher", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Watch and prints out changes to an app."}, "docs:generate": {"aliases": [], "args": {}, "description": "Generate CLI commands documentation", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "docs:generate", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "help": {"aliases": [], "args": {"command": {"description": "Command to show help for.", "name": "command", "required": false}}, "description": "Display help for Shopify CLI", "enableJsonFlag": false, "flags": {"nested-commands": {"allowNo": false, "char": "n", "description": "Include all nested commands in the output.", "env": "SHOPIFY_FLAG_CLI_NESTED_COMMANDS", "name": "nested-commands", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "help", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": false, "usage": "help [command] [flags]"}, "hydrogen:build": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Builds a Hydrogen storefront for production.", "descriptionWithMarkdown": "Builds a Hydrogen storefront for production. The client and app worker files are compiled to a `/dist` folder in your Hydrogen project directory.", "enableJsonFlag": false, "flags": {"bundle-stats": {"allowNo": true, "description": "Show a bundle size summary after building. Defaults to true, use `--no-bundle-stats` to disable.", "name": "bundle-stats", "type": "boolean"}, "codegen": {"allowNo": false, "description": "Automatically generates GraphQL types for your project’s Storefront API queries.", "name": "codegen", "required": false, "type": "boolean"}, "codegen-config-path": {"dependsOn": ["codegen"], "description": "Specifies a path to a codegen configuration file. Defaults to `<root>/codegen.ts` if this file exists.", "hasDynamicHelp": false, "multiple": false, "name": "codegen-config-path", "required": false, "type": "option"}, "diff": {"allowNo": false, "description": "Applies the current files on top of Hydrogen's starter template in a temporary directory.", "hidden": true, "name": "diff", "required": false, "type": "boolean"}, "disable-route-warning": {"allowNo": false, "description": "Disables any warnings about missing standard routes.", "env": "SHOPIFY_HYDROGEN_FLAG_DISABLE_ROUTE_WARNING", "name": "disable-route-warning", "type": "boolean"}, "entry": {"description": "Entry file for the worker. Defaults to `./server`.", "env": "SHOPIFY_HYDROGEN_FLAG_ENTRY", "hasDynamicHelp": false, "multiple": false, "name": "entry", "type": "option"}, "force-client-sourcemap": {"allowNo": false, "description": "Client sourcemapping is avoided by default because it makes backend code visible in the browser. Use this flag to force enabling it.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE_CLIENT_SOURCEMAP", "name": "force-client-sourcemap", "type": "boolean"}, "lockfile-check": {"allowNo": true, "description": "Checks that there is exactly one valid lockfile in the project. Defaults to `true`. Deactivate with `--no-lockfile-check`.", "env": "SHOPIFY_HYDROGEN_FLAG_LOCKFILE_CHECK", "name": "lockfile-check", "type": "boolean"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "sourcemap": {"allowNo": true, "description": "Controls whether server sourcemaps are generated. Default to `true`. Deactivate `--no-sourcemaps`.", "env": "SHOPIFY_HYDROGEN_FLAG_SOURCEMAP", "name": "sourcemap", "type": "boolean"}, "watch": {"allowNo": false, "description": "Watches for changes and rebuilds the project writing output to disk.", "env": "SHOPIFY_HYDROGEN_FLAG_WATCH", "name": "watch", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:build", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:check": {"aliases": [], "args": {"resource": {"description": "The resource to check. Currently only 'routes' is supported.", "name": "resource", "options": ["routes"], "required": true}}, "customPluginName": "@shopify/cli-hydrogen", "description": "Returns diagnostic information about a Hydrogen storefront.", "descriptionWithMarkdown": "Checks whether your Hydrogen app includes a set of standard Shopify routes.", "enableJsonFlag": false, "flags": {"path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:check", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:codegen": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Generate types for the Storefront API queries found in your project.", "descriptionWithMarkdown": "Automatically generates GraphQL types for your project’s Storefront API queries.", "enableJsonFlag": false, "flags": {"codegen-config-path": {"description": "Specify a path to a codegen configuration file. Defaults to `<root>/codegen.ts` if it exists.", "hasDynamicHelp": false, "multiple": false, "name": "codegen-config-path", "required": false, "type": "option"}, "diff": {"allowNo": false, "description": "Applies the current files on top of Hydrogen's starter template in a temporary directory.", "hidden": true, "name": "diff", "required": false, "type": "boolean"}, "force-sfapi-version": {"description": "Force generating Storefront API types for a specific version instead of using the one provided in Hydrogen. A token can also be provided with this format: `<version>:<token>`.", "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "force-sfapi-version", "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "watch": {"allowNo": false, "description": "Watch the project for changes to update types on file save.", "name": "watch", "required": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:codegen", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:customer-account-push": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Push project configuration to admin", "enableJsonFlag": false, "flags": {"dev-origin": {"description": "The development domain of your application.", "hasDynamicHelp": false, "multiple": false, "name": "dev-origin", "required": true, "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "relative-logout-uri": {"description": "The relative url of allowed url that will be redirected to post-logout for Customer Account API OAuth flow. Default to nothing.", "hasDynamicHelp": false, "multiple": false, "name": "relative-logout-uri", "type": "option"}, "relative-redirect-uri": {"description": "The relative url of allowed callback url for Customer Account API OAuth flow. Default is '/account/authorize'", "hasDynamicHelp": false, "multiple": false, "name": "relative-redirect-uri", "type": "option"}, "storefront-id": {"description": "The id of the storefront the configuration should be pushed to. Must start with 'gid://shopify/HydrogenStorefront/'", "hasDynamicHelp": false, "multiple": false, "name": "storefront-id", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:customer-account-push", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:debug:cpu": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Builds and profiles the server startup time the app.", "descriptionWithMarkdown": "Builds the app and runs the resulting code to profile the server startup time, watching for changes. This command can be used to [debug slow app startup times](https://shopify.dev/docs/custom-storefronts/hydrogen/debugging/cpu-startup) that cause failed deployments in Oxygen.\n\n  The profiling results are written to a `.cpuprofile` file that can be viewed with certain tools such as [Flame Chart Visualizer for Visual Studio Code](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-js-profile-flame).", "enableJsonFlag": false, "flags": {"diff": {"allowNo": false, "description": "Applies the current files on top of Hydrogen's starter template in a temporary directory.", "hidden": true, "name": "diff", "required": false, "type": "boolean"}, "entry": {"description": "Entry file for the worker. Defaults to `./server`.", "env": "SHOPIFY_HYDROGEN_FLAG_ENTRY", "hasDynamicHelp": false, "multiple": false, "name": "entry", "type": "option"}, "output": {"default": "startup.cpuprofile", "description": "Specify a path to generate the profile file. Defaults to \"startup.cpuprofile\".", "hasDynamicHelp": false, "multiple": false, "name": "output", "required": false, "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:debug:cpu", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:deploy": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Builds and deploys a Hydrogen storefront to Oxygen.", "descriptionWithMarkdown": "Builds and deploys your Hydrogen storefront to Oxygen. Requires an Oxygen deployment token to be set with the `--token` flag or an environment variable (`SHOPIFY_HYDROGEN_DEPLOYMENT_TOKEN`). If the storefront is [linked](https://shopify.dev/docs/api/shopify-cli/hydrogen/hydrogen-link) then the Oxygen deployment token for the linked storefront will be used automatically.", "enableJsonFlag": false, "flags": {"auth-bypass-token": {"allowNo": false, "description": "Generate an authentication bypass token, which can be used to perform end-to-end tests against the deployment.", "env": "AUTH_BYPASS_TOKEN", "name": "auth-bypass-token", "required": false, "type": "boolean"}, "auth-bypass-token-duration": {"dependsOn": ["auth-bypass-token"], "description": "Specify the duration (in hours) up to 12 hours for the authentication bypass token. Defaults to `2`", "env": "AUTH_BYPASS_TOKEN_DURATION", "hasDynamicHelp": false, "multiple": false, "name": "auth-bypass-token-duration", "required": false, "type": "option"}, "build-command": {"description": "Specify a build command to run before deploying. If not specified, `shopify hydrogen build` will be used.", "hasDynamicHelp": false, "multiple": false, "name": "build-command", "required": false, "type": "option"}, "diff": {"allowNo": false, "description": "Applies the current files on top of Hydrogen's starter template in a temporary directory.", "hidden": true, "name": "diff", "required": false, "type": "boolean"}, "entry": {"description": "Entry file for the worker. Defaults to `./server`.", "env": "SHOPIFY_HYDROGEN_FLAG_ENTRY", "hasDynamicHelp": false, "multiple": false, "name": "entry", "type": "option"}, "env": {"description": "Specifies the environment to perform the operation using its handle. Fetch the handle using the `env list` command.", "exclusive": ["env-branch"], "hasDynamicHelp": false, "multiple": false, "name": "env", "type": "option"}, "env-branch": {"deprecated": {"message": "--env-branch is deprecated. Use --env instead.", "to": "env"}, "description": "Specifies the environment to perform the operation using its Git branch name.", "env": "SHOPIFY_HYDROGEN_ENVIRONMENT_BRANCH", "hasDynamicHelp": false, "multiple": false, "name": "env-branch", "type": "option"}, "env-file": {"description": "Path to an environment file to override existing environment variables for the deployment.", "hasDynamicHelp": false, "multiple": false, "name": "env-file", "required": false, "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Forces a deployment to proceed if there are uncommited changes in its Git repository.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "required": false, "type": "boolean"}, "json-output": {"allowNo": true, "description": "Create a JSON file containing the deployment details in CI environments. Defaults to true, use `--no-json-output` to disable.", "name": "json-output", "required": false, "type": "boolean"}, "lockfile-check": {"allowNo": true, "description": "Checks that there is exactly one valid lockfile in the project. Defaults to `true`. Deactivate with `--no-lockfile-check`.", "env": "SHOPIFY_HYDROGEN_FLAG_LOCKFILE_CHECK", "name": "lockfile-check", "type": "boolean"}, "metadata-description": {"description": "Description of the changes in the deployment. Defaults to the commit message of the latest commit if there are no uncommited changes.", "env": "SHOPIFY_HYDROGEN_FLAG_METADATA_DESCRIPTION", "hasDynamicHelp": false, "multiple": false, "name": "metadata-description", "required": false, "type": "option"}, "metadata-url": {"env": "SHOPIFY_HYDROGEN_FLAG_METADATA_URL", "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "metadata-url", "required": false, "type": "option"}, "metadata-user": {"description": "User that initiated the deployment. Will be saved and displayed in the Shopify admin", "env": "SHOPIFY_HYDROGEN_FLAG_METADATA_USER", "hasDynamicHelp": false, "multiple": false, "name": "metadata-user", "required": false, "type": "option"}, "metadata-version": {"env": "SHOPIFY_HYDROGEN_FLAG_METADATA_VERSION", "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "metadata-version", "required": false, "type": "option"}, "no-verify": {"allowNo": false, "description": "Skip the routability verification step after deployment.", "name": "no-verify", "required": false, "type": "boolean"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "preview": {"allowNo": false, "description": "Deploys to the Preview environment.", "name": "preview", "required": false, "type": "boolean"}, "shop": {"char": "s", "description": "Shop URL. It can be the shop prefix (janes-apparel) or the full myshopify.com URL (janes-apparel.myshopify.com, https://janes-apparel.myshopify.com).", "env": "SHOPIFY_SHOP", "hasDynamicHelp": false, "multiple": false, "name": "shop", "type": "option"}, "token": {"char": "t", "description": "Oxygen deployment token. Defaults to the linked storefront's token if available.", "env": "SHOPIFY_HYDROGEN_DEPLOYMENT_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "token", "required": false, "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:deploy", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:dev": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Runs Hydrogen storefront in an Oxygen worker for development.", "descriptionWithMarkdown": "Runs a Hydrogen storefront in a local runtime that emulates an Oxygen worker for development.\n\n  If your project is [linked](https://shopify.dev/docs/api/shopify-cli/hydrogen/hydrogen-link) to a Hydrogen storefront, then its environment variables will be loaded with the runtime.", "enableJsonFlag": false, "flags": {"codegen": {"allowNo": false, "description": "Automatically generates GraphQL types for your project’s Storefront API queries.", "name": "codegen", "required": false, "type": "boolean"}, "codegen-config-path": {"dependsOn": ["codegen"], "description": "Specifies a path to a codegen configuration file. Defaults to `<root>/codegen.ts` if this file exists.", "hasDynamicHelp": false, "multiple": false, "name": "codegen-config-path", "required": false, "type": "option"}, "customer-account-push__unstable": {"allowNo": false, "description": "Use tunneling for local development and push the tunneling domain to admin. Required to use Customer Account API's Oauth flow", "env": "SHOPIFY_HYDROGEN_FLAG_CUSTOMER_ACCOUNT_PUSH", "hidden": true, "name": "customer-account-push__unstable", "required": false, "type": "boolean"}, "debug": {"allowNo": false, "description": "Enables inspector connections to the server with a debugger such as Visual Studio Code or Chrome DevTools.", "env": "SHOPIFY_HYDROGEN_FLAG_DEBUG", "name": "debug", "type": "boolean"}, "diff": {"allowNo": false, "description": "Applies the current files on top of Hydrogen's starter template in a temporary directory.", "hidden": true, "name": "diff", "required": false, "type": "boolean"}, "disable-deps-optimizer": {"allowNo": false, "description": "Disable adding dependencies to V<PERSON>'s `ssr.optimizeDeps.include` automatically", "env": "SHOPIFY_HYDROGEN_FLAG_DISABLE_DEPS_OPTIMIZER", "name": "disable-deps-optimizer", "type": "boolean"}, "disable-version-check": {"allowNo": false, "description": "Skip the version check when running `hydrogen dev`", "name": "disable-version-check", "required": false, "type": "boolean"}, "disable-virtual-routes": {"allowNo": false, "description": "Disable rendering fallback routes when a route file doesn't exist.", "env": "SHOPIFY_HYDROGEN_FLAG_DISABLE_VIRTUAL_ROUTES", "name": "disable-virtual-routes", "type": "boolean"}, "entry": {"description": "Entry file for the worker. Defaults to `./server`.", "env": "SHOPIFY_HYDROGEN_FLAG_ENTRY", "hasDynamicHelp": false, "multiple": false, "name": "entry", "type": "option"}, "env": {"description": "Specifies the environment to perform the operation using its handle. Fetch the handle using the `env list` command.", "exclusive": ["env-branch"], "hasDynamicHelp": false, "multiple": false, "name": "env", "type": "option"}, "env-branch": {"deprecated": {"message": "--env-branch is deprecated. Use --env instead.", "to": "env"}, "description": "Specifies the environment to perform the operation using its Git branch name.", "env": "SHOPIFY_HYDROGEN_ENVIRONMENT_BRANCH", "hasDynamicHelp": false, "multiple": false, "name": "env-branch", "type": "option"}, "env-file": {"default": ".env", "description": "Path to an environment file to override existing environment variables. Defaults to the '.env' located in your project path `--path`.", "hasDynamicHelp": false, "multiple": false, "name": "env-file", "required": false, "type": "option"}, "host": {"allowNo": false, "description": "Expose the server to the local network", "name": "host", "required": false, "type": "boolean"}, "inspector-port": {"description": "The port where the inspector is available. Defaults to 9229.", "env": "SHOPIFY_HYDROGEN_FLAG_INSPECTOR_PORT", "hasDynamicHelp": false, "multiple": false, "name": "inspector-port", "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "port": {"description": "The port to run the server on. Defaults to 3000.", "env": "SHOPIFY_HYDROGEN_FLAG_PORT", "hasDynamicHelp": false, "multiple": false, "name": "port", "required": false, "type": "option"}, "verbose": {"allowNo": false, "description": "Outputs more information about the command's execution.", "env": "SHOPIFY_HYDROGEN_FLAG_VERBOSE", "name": "verbose", "required": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:dev", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:env:list": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "List the environments on your linked Hydrogen storefront.", "descriptionWithMarkdown": "Lists all environments available on the linked Hydrogen storefront.", "enableJsonFlag": false, "flags": {"path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:env:list", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:env:pull": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Populate your .env with variables from your Hydrogen storefront.", "descriptionWithMarkdown": "Pulls environment variables from the linked Hydrogen storefront and writes them to an `.env` file.", "enableJsonFlag": false, "flags": {"env": {"description": "Specifies the environment to perform the operation using its handle. Fetch the handle using the `env list` command.", "exclusive": ["env-branch"], "hasDynamicHelp": false, "multiple": false, "name": "env", "type": "option"}, "env-branch": {"deprecated": {"message": "--env-branch is deprecated. Use --env instead.", "to": "env"}, "description": "Specifies the environment to perform the operation using its Git branch name.", "env": "SHOPIFY_HYDROGEN_ENVIRONMENT_BRANCH", "hasDynamicHelp": false, "multiple": false, "name": "env-branch", "type": "option"}, "env-file": {"default": ".env", "description": "Path to an environment file to override existing environment variables. Defaults to the '.env' located in your project path `--path`.", "hasDynamicHelp": false, "multiple": false, "name": "env-file", "required": false, "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Overwrites the destination directory and files if they already exist.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "type": "boolean"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:env:pull", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:env:push": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Push environment variables from the local .env file to your linked Hydrogen storefront.", "enableJsonFlag": false, "flags": {"env": {"description": "Specifies the environment to perform the operation using its handle. Fetch the handle using the `env list` command.", "exclusive": ["env-branch"], "hasDynamicHelp": false, "multiple": false, "name": "env", "type": "option"}, "env-file": {"default": ".env", "description": "Path to an environment file to override existing environment variables. Defaults to the '.env' located in your project path `--path`.", "hasDynamicHelp": false, "multiple": false, "name": "env-file", "required": false, "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:env:push", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:g": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Shortcut for `hydrogen generate`. See `hydrogen generate --help` for more information.", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "hydrogen:g", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": false}, "hydrogen:generate:route": {"aliases": [], "args": {"routeName": {"description": "The route to generate. One of home,page,cart,products,collections,policies,blogs,account,search,robots,sitemap,tokenlessApi,all.", "name": "routeName", "options": ["home", "page", "cart", "products", "collections", "policies", "blogs", "account", "search", "robots", "sitemap", "tokenlessApi", "all"], "required": true}}, "customPluginName": "@shopify/cli-hydrogen", "description": "Generates a standard Shopify route.", "descriptionWithMarkdown": "Generates a set of default routes from the starter template.", "enableJsonFlag": false, "flags": {"adapter": {"description": "Remix adapter used in the route. The default is `@shopify/remix-oxygen`.", "env": "SHOPIFY_HYDROGEN_FLAG_ADAPTER", "hasDynamicHelp": false, "multiple": false, "name": "adapter", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Overwrites the destination directory and files if they already exist.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "type": "boolean"}, "locale-param": {"description": "The param name in Remix routes for the i18n locale, if any. Example: `locale` becomes ($locale).", "env": "SHOPIFY_HYDROGEN_FLAG_ADAPTER", "hasDynamicHelp": false, "multiple": false, "name": "locale-param", "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "typescript": {"allowNo": false, "description": "Generate TypeScript files", "env": "SHOPIFY_HYDROGEN_FLAG_TYPESCRIPT", "name": "typescript", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:generate:route", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:generate:routes": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Generates all supported standard shopify routes.", "enableJsonFlag": false, "flags": {"adapter": {"description": "Remix adapter used in the route. The default is `@shopify/remix-oxygen`.", "env": "SHOPIFY_HYDROGEN_FLAG_ADAPTER", "hasDynamicHelp": false, "multiple": false, "name": "adapter", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Overwrites the destination directory and files if they already exist.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "type": "boolean"}, "locale-param": {"description": "The param name in Remix routes for the i18n locale, if any. Example: `locale` becomes ($locale).", "env": "SHOPIFY_HYDROGEN_FLAG_ADAPTER", "hasDynamicHelp": false, "multiple": false, "name": "locale-param", "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "typescript": {"allowNo": false, "description": "Generate TypeScript files", "env": "SHOPIFY_HYDROGEN_FLAG_TYPESCRIPT", "name": "typescript", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:generate:routes", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:init": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Creates a new Hydrogen storefront.", "descriptionWithMarkdown": "Creates a new Hydrogen storefront.", "enableJsonFlag": false, "flags": {"force": {"allowNo": false, "char": "f", "description": "Overwrites the destination directory and files if they already exist.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "type": "boolean"}, "git": {"allowNo": true, "description": "Init Git and create initial commits.", "env": "SHOPIFY_HYDROGEN_FLAG_GIT", "name": "git", "type": "boolean"}, "install-deps": {"allowNo": true, "description": "Auto installs dependencies using the active package manager.", "env": "SHOPIFY_HYDROGEN_FLAG_INSTALL_DEPS", "name": "install-deps", "type": "boolean"}, "language": {"description": "Sets the template language to use. One of `js` or `ts`.", "env": "SHOPIFY_HYDROGEN_FLAG_LANGUAGE", "hasDynamicHelp": false, "multiple": false, "name": "language", "type": "option"}, "markets": {"description": "Sets the URL structure to support multiple markets. Must be one of: `subfolders`, `domains`, `subdomains`, `none`. Example: `--markets subfolders`.", "env": "SHOPIFY_HYDROGEN_FLAG_I18N", "hasDynamicHelp": false, "multiple": false, "name": "markets", "type": "option"}, "mock-shop": {"allowNo": false, "description": "Use mock.shop as the data source for the storefront.", "env": "SHOPIFY_HYDROGEN_FLAG_MOCK_DATA", "name": "mock-shop", "type": "boolean"}, "package-manager": {"env": "SHOPIFY_HYDROGEN_FLAG_PACKAGE_MANAGER", "hasDynamicHelp": false, "hidden": true, "multiple": false, "name": "package-manager", "options": ["npm", "yarn", "pnpm", "unknown"], "type": "option"}, "path": {"description": "The path to the directory of the new Hydrogen storefront.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "quickstart": {"allowNo": false, "description": "Scaffolds a new Hydrogen project with a set of sensible defaults. Equivalent to `shopify hydrogen init --path hydrogen-quickstart --mock-shop --language js --shortcut --routes --markets none`", "env": "SHOPIFY_HYDROGEN_FLAG_QUICKSTART", "name": "quickstart", "type": "boolean"}, "routes": {"allowNo": true, "description": "Generate routes for all pages.", "env": "SHOPIFY_HYDROGEN_FLAG_ROUTES", "name": "routes", "type": "boolean"}, "shortcut": {"allowNo": true, "description": "Creates a global h2 shortcut for Shopify CLI using shell aliases. Deactivate with `--no-shortcut`.", "env": "SHOPIFY_HYDROGEN_FLAG_SHORTCUT", "name": "shortcut", "type": "boolean"}, "styling": {"description": "Sets the styling strategy to use. One of `tailwind`, `vanilla-extract`, `css-modules`, `postcss`, `none`.", "env": "SHOPIFY_HYDROGEN_FLAG_STYLING", "hasDynamicHelp": false, "multiple": false, "name": "styling", "type": "option"}, "template": {"description": "Scaffolds project based on an existing template or example from the Hydrogen repository.", "env": "SHOPIFY_HYDROGEN_FLAG_TEMPLATE", "hasDynamicHelp": false, "multiple": false, "name": "template", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:init", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:link": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Link a local project to one of your shop's Hydrogen storefronts.", "descriptionWithMarkdown": "Links your local development environment to a remote Hydrogen storefront. You can link an unlimited number of development environments to a single Hydrogen storefront.\n\n  Linking to a Hydrogen storefront enables you to run [dev](https://shopify.dev/docs/api/shopify-cli/hydrogen/hydrogen-dev) and automatically inject your linked Hydrogen storefront's environment variables directly into the server runtime.\n\n  After you run the `link` command, you can access the [env list](https://shopify.dev/docs/api/shopify-cli/hydrogen/hydrogen-env-list), [env pull](https://shopify.dev/docs/api/shopify-cli/hydrogen/hydrogen-env-pull), and [unlink](https://shopify.dev/docs/api/shopify-cli/hydrogen/hydrogen-unlink) commands.", "enableJsonFlag": false, "flags": {"force": {"allowNo": false, "char": "f", "description": "Overwrites the destination directory and files if they already exist.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "type": "boolean"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "storefront": {"description": "The name of a Hydrogen Storefront (e.g. \"Jane's Apparel\")", "env": "SHOPIFY_HYDROGEN_STOREFRONT", "hasDynamicHelp": false, "multiple": false, "name": "storefront", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:link", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:list": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Returns a list of Hydrogen storefronts available on a given shop.", "descriptionWithMarkdown": "Lists all remote Hydrogen storefronts available to link to your local development environment.", "enableJsonFlag": false, "flags": {"path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:list", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:login": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Login to your Shopify account.", "descriptionWithMarkdown": "Logs in to the specified shop and saves the shop domain to the project.", "enableJsonFlag": false, "flags": {"path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "shop": {"char": "s", "description": "Shop URL. It can be the shop prefix (janes-apparel) or the full myshopify.com URL (janes-apparel.myshopify.com, https://janes-apparel.myshopify.com).", "env": "SHOPIFY_SHOP", "hasDynamicHelp": false, "multiple": false, "name": "shop", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:login", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:logout": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Logout of your local session.", "descriptionWithMarkdown": "Log out from the current shop.", "enableJsonFlag": false, "flags": {"path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:logout", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:preview": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Runs a Hydrogen storefront in an Oxygen worker for production.", "descriptionWithMarkdown": "Runs a server in your local development environment that serves your Hydrogen app's production build. Requires running the [build](https://shopify.dev/docs/api/shopify-cli/hydrogen/hydrogen-build) command first.", "enableJsonFlag": false, "flags": {"build": {"allowNo": false, "description": "Builds the app before starting the preview server.", "name": "build", "type": "boolean"}, "codegen": {"allowNo": false, "dependsOn": ["build"], "description": "Automatically generates GraphQL types for your project’s Storefront API queries.", "name": "codegen", "required": false, "type": "boolean"}, "codegen-config-path": {"dependsOn": ["codegen"], "description": "Specifies a path to a codegen configuration file. Defaults to `<root>/codegen.ts` if this file exists.", "hasDynamicHelp": false, "multiple": false, "name": "codegen-config-path", "required": false, "type": "option"}, "debug": {"allowNo": false, "description": "Enables inspector connections to the server with a debugger such as Visual Studio Code or Chrome DevTools.", "env": "SHOPIFY_HYDROGEN_FLAG_DEBUG", "name": "debug", "type": "boolean"}, "diff": {"allowNo": false, "dependsOn": ["build"], "description": "Applies the current files on top of Hydrogen's starter template in a temporary directory.", "hidden": true, "name": "diff", "required": false, "type": "boolean"}, "entry": {"dependsOn": ["build"], "description": "Entry file for the worker. Defaults to `./server`.", "env": "SHOPIFY_HYDROGEN_FLAG_ENTRY", "hasDynamicHelp": false, "multiple": false, "name": "entry", "type": "option"}, "env": {"description": "Specifies the environment to perform the operation using its handle. Fetch the handle using the `env list` command.", "exclusive": ["env-branch"], "hasDynamicHelp": false, "multiple": false, "name": "env", "type": "option"}, "env-branch": {"deprecated": {"message": "--env-branch is deprecated. Use --env instead.", "to": "env"}, "description": "Specifies the environment to perform the operation using its Git branch name.", "env": "SHOPIFY_HYDROGEN_ENVIRONMENT_BRANCH", "hasDynamicHelp": false, "multiple": false, "name": "env-branch", "type": "option"}, "env-file": {"default": ".env", "description": "Path to an environment file to override existing environment variables. Defaults to the '.env' located in your project path `--path`.", "hasDynamicHelp": false, "multiple": false, "name": "env-file", "required": false, "type": "option"}, "inspector-port": {"description": "The port where the inspector is available. Defaults to 9229.", "env": "SHOPIFY_HYDROGEN_FLAG_INSPECTOR_PORT", "hasDynamicHelp": false, "multiple": false, "name": "inspector-port", "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "port": {"description": "The port to run the server on. Defaults to 3000.", "env": "SHOPIFY_HYDROGEN_FLAG_PORT", "hasDynamicHelp": false, "multiple": false, "name": "port", "type": "option"}, "verbose": {"allowNo": false, "description": "Outputs more information about the command's execution.", "env": "SHOPIFY_HYDROGEN_FLAG_VERBOSE", "name": "verbose", "required": false, "type": "boolean"}, "watch": {"allowNo": false, "dependsOn": ["build"], "description": "Watches for changes and rebuilds the project.", "name": "watch", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:preview", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:setup": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Scaffold routes and core functionality.", "enableJsonFlag": false, "flags": {"force": {"allowNo": false, "char": "f", "description": "Overwrites the destination directory and files if they already exist.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "type": "boolean"}, "install-deps": {"allowNo": true, "description": "Auto installs dependencies using the active package manager.", "env": "SHOPIFY_HYDROGEN_FLAG_INSTALL_DEPS", "name": "install-deps", "type": "boolean"}, "markets": {"description": "Sets the URL structure to support multiple markets. Must be one of: `subfolders`, `domains`, `subdomains`, `none`. Example: `--markets subfolders`.", "env": "SHOPIFY_HYDROGEN_FLAG_I18N", "hasDynamicHelp": false, "multiple": false, "name": "markets", "type": "option"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "shortcut": {"allowNo": true, "description": "Creates a global h2 shortcut for Shopify CLI using shell aliases. Deactivate with `--no-shortcut`.", "env": "SHOPIFY_HYDROGEN_FLAG_SHORTCUT", "name": "shortcut", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:setup", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:setup:css": {"aliases": [], "args": {"strategy": {"description": "The CSS strategy to setup. One of tailwind,vanilla-extract,css-modules,postcss", "name": "strategy", "options": ["tailwind", "vanilla-extract", "css-modules", "postcss"]}}, "customPluginName": "@shopify/cli-hydrogen", "description": "Setup CSS strategies for your project.", "descriptionWithMarkdown": "Adds support for certain CSS strategies to your project.", "enableJsonFlag": false, "flags": {"force": {"allowNo": false, "char": "f", "description": "Overwrites the destination directory and files if they already exist.", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "type": "boolean"}, "install-deps": {"allowNo": true, "description": "Auto installs dependencies using the active package manager.", "env": "SHOPIFY_HYDROGEN_FLAG_INSTALL_DEPS", "name": "install-deps", "type": "boolean"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:setup:css", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:setup:markets": {"aliases": [], "args": {"strategy": {"description": "The URL structure strategy to setup multiple markets. One of subfolders,domains,subdomains", "name": "strategy", "options": ["subfolders", "domains", "subdomains"]}}, "customPluginName": "@shopify/cli-hydrogen", "description": "Setup support for multiple markets in your project.", "descriptionWithMarkdown": "Adds support for multiple [markets](https://shopify.dev/docs/custom-storefronts/hydrogen/markets) to your project by using the URL structure.", "enableJsonFlag": false, "flags": {"path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:setup:markets", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:setup:vite": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "EXPERIMENTAL: Upgrades the project to use Vite.", "enableJsonFlag": false, "flags": {"path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:setup:vite", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:shortcut": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Creates a global `h2` shortcut for the Hydrogen CLI", "descriptionWithMarkdown": "Creates a global h2 shortcut for Shopify CLI using shell aliases.\n\n  The following shells are supported:\n\n  - Bash (using `~/.bashrc`)\n  - ZSH (using `~/.zshrc`)\n  - Fish (using `~/.config/fish/functions`)\n  - PowerShell (added to `$PROFILE`)\n\n  After the alias is created, you can call Shopify CLI from anywhere in your project using `h2 <command>`.", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:shortcut", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:unlink": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Unlink a local project from a Hydrogen storefront.", "descriptionWithMarkdown": "Unlinks your local development environment from a remote Hydrogen storefront.", "enableJsonFlag": false, "flags": {"path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:unlink", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "hydrogen:upgrade": {"aliases": [], "args": {}, "customPluginName": "@shopify/cli-hydrogen", "description": "Upgrade Remix and Hydrogen npm dependencies.", "descriptionWithMarkdown": "Upgrade Hydrogen project dependencies, preview features, fixes and breaking changes. The command also generates an instruction file for each upgrade.", "enableJsonFlag": false, "flags": {"force": {"allowNo": false, "char": "f", "description": "Ignore warnings and force the upgrade to the target version", "env": "SHOPIFY_HYDROGEN_FLAG_FORCE", "name": "force", "type": "boolean"}, "path": {"description": "The path to the directory of the Hydrogen storefront. Defaults to the current directory where the command is run.", "env": "SHOPIFY_HYDROGEN_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "type": "option"}, "version": {"char": "v", "description": "A target hydrogen version to update to", "hasDynamicHelp": false, "multiple": false, "name": "version", "required": false, "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "hydrogen:upgrade", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "kitchen-sink": {"aliases": [], "args": {}, "description": "View all the available UI kit components", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": ["kitchen-sink all"], "id": "kitchen-sink", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "kitchen-sink:async": {"aliases": [], "args": {}, "description": "View the UI kit components that process async tasks", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "kitchen-sink:async", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "kitchen-sink:prompts": {"aliases": [], "args": {}, "description": "View the UI kit components prompts", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "kitchen-sink:prompts", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "kitchen-sink:static": {"aliases": [], "args": {}, "description": "View the UI kit components that display static output", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "kitchen-sink:static", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "notifications:generate": {"aliases": [], "args": {}, "description": "Generate a notifications.json file for the the CLI, appending a new notification to the current file.", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "notifications:generate", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "notifications:list": {"aliases": [], "args": {}, "description": "List current notifications configured for the CLI.", "enableJsonFlag": false, "flags": {"ignore-errors": {"allowNo": false, "description": "Don't fail if an error occurs.", "env": "SHOPIFY_FLAG_IGNORE_ERRORS", "hidden": false, "name": "ignore-errors", "type": "boolean"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "notifications:list", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "plugins": {"aliases": [], "args": {}, "customPluginName": "@oclif/plugin-plugins", "description": "List installed plugins.", "enableJsonFlag": true, "examples": ["<%= config.bin %> <%= command.id %>"], "flags": {"core": {"allowNo": false, "description": "Show core plugins.", "name": "core", "type": "boolean"}, "json": {"allowNo": false, "description": "Format output as json.", "helpGroup": "GLOBAL", "name": "json", "type": "boolean"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "plugins", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "plugins:inspect": {"aliases": [], "args": {"plugin": {"default": ".", "description": "Plugin to inspect.", "name": "plugin", "required": true}}, "customPluginName": "@oclif/plugin-plugins", "description": "Displays installation properties of a plugin.", "enableJsonFlag": true, "examples": ["<%= config.bin %> <%= command.id %> <%- config.pjson.oclif.examplePlugin || \"myplugin\" %> "], "flags": {"help": {"allowNo": false, "char": "h", "description": "Show CLI help.", "name": "help", "type": "boolean"}, "json": {"allowNo": false, "description": "Format output as json.", "helpGroup": "GLOBAL", "name": "json", "type": "boolean"}, "verbose": {"allowNo": false, "char": "v", "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "plugins:inspect", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": false, "usage": "plugins:inspect PLUGIN..."}, "plugins:install": {"aliases": ["plugins:add"], "args": {"plugin": {"description": "Plugin to install.", "name": "plugin", "required": true}}, "customPluginName": "@oclif/plugin-plugins", "description": "", "enableJsonFlag": true, "examples": [{"command": "<%= config.bin %> <%= command.id %> <%- config.pjson.oclif.examplePlugin || \"myplugin\" %> ", "description": "Install a plugin from npm registry."}, {"command": "<%= config.bin %> <%= command.id %> https://github.com/someuser/someplugin", "description": "Install a plugin from a github url."}, {"command": "<%= config.bin %> <%= command.id %> someuser/someplugin", "description": "Install a plugin from a github slug."}], "flags": {"force": {"allowNo": false, "char": "f", "description": "Force npm to fetch remote resources even if a local copy exists on disk.", "name": "force", "type": "boolean"}, "help": {"allowNo": false, "char": "h", "description": "Show CLI help.", "name": "help", "type": "boolean"}, "jit": {"allowNo": false, "hidden": true, "name": "jit", "type": "boolean"}, "json": {"allowNo": false, "description": "Format output as json.", "helpGroup": "GLOBAL", "name": "json", "type": "boolean"}, "silent": {"allowNo": false, "char": "s", "description": "Silences npm output.", "exclusive": ["verbose"], "name": "silent", "type": "boolean"}, "verbose": {"allowNo": false, "char": "v", "description": "Show verbose npm output.", "exclusive": ["silent"], "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "plugins:install", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": false, "summary": "Installs a plugin into <%= config.bin %>."}, "plugins:link": {"aliases": [], "args": {"path": {"default": ".", "description": "path to plugin", "name": "path", "required": true}}, "customPluginName": "@oclif/plugin-plugins", "description": "Installation of a linked plugin will override a user-installed or core plugin.\n\ne.g. If you have a user-installed or core plugin that has a 'hello' command, installing a linked plugin with a 'hello' command will override the user-installed or core plugin implementation. This is useful for development work.\n", "enableJsonFlag": false, "examples": ["<%= config.bin %> <%= command.id %> <%- config.pjson.oclif.examplePlugin || \"myplugin\" %> "], "flags": {"help": {"allowNo": false, "char": "h", "description": "Show CLI help.", "name": "help", "type": "boolean"}, "install": {"allowNo": true, "description": "Install dependencies after linking the plugin.", "name": "install", "type": "boolean"}, "verbose": {"allowNo": false, "char": "v", "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "plugins:link", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Links a plugin into the CLI for development."}, "plugins:reset": {"aliases": [], "args": {}, "customPluginName": "@oclif/plugin-plugins", "enableJsonFlag": false, "flags": {"hard": {"allowNo": false, "name": "hard", "summary": "Delete node_modules and package manager related files in addition to uninstalling plugins.", "type": "boolean"}, "reinstall": {"allowNo": false, "name": "reinstall", "summary": "Reinstall all plugins after uninstalling.", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "plugins:reset", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Remove all user-installed and linked plugins."}, "plugins:uninstall": {"aliases": ["plugins:unlink", "plugins:remove"], "args": {"plugin": {"description": "plugin to uninstall", "name": "plugin"}}, "customPluginName": "@oclif/plugin-plugins", "description": "Removes a plugin from the CLI.", "enableJsonFlag": false, "examples": ["<%= config.bin %> <%= command.id %> <%- config.pjson.oclif.examplePlugin || \"myplugin\" %>"], "flags": {"help": {"allowNo": false, "char": "h", "description": "Show CLI help.", "name": "help", "type": "boolean"}, "verbose": {"allowNo": false, "char": "v", "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "plugins:uninstall", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": false}, "plugins:update": {"aliases": [], "args": {}, "customPluginName": "@oclif/plugin-plugins", "description": "Update installed plugins.", "enableJsonFlag": false, "flags": {"help": {"allowNo": false, "char": "h", "description": "Show CLI help.", "name": "help", "type": "boolean"}, "verbose": {"allowNo": false, "char": "v", "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "plugins:update", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "search": {"aliases": [], "args": {"query": {"name": "query"}}, "description": "Starts a search on shopify.dev.", "enableJsonFlag": false, "examples": ["# open the search modal on Shopify.dev\n    shopify search\n\n    # search for a term on Shopify.dev\n    shopify search <query>\n\n    # search for a phrase on Shopify.dev\n    shopify search \"<a search query separated by spaces>\"\n    "], "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "search", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "usage": "search [query]"}, "store:copy": {"aliases": [], "args": {}, "customPluginName": "@shopify/store", "description": "Examples:\n\n    COPY data from one store to another in your organization\n    shopify store copy --from-store source.myshopify.com --to-store target.myshopify.com\n\n    EXPORT store data to SQLite\n    shopify store copy --from-store source.myshopify.com --to-file path/to/file.sqlite\n\n    IMPORT data from SQLite to a store\n    shopify store copy --from-file path/to/file.sqlite --to-store target.myshopify.com\n", "flags": {"from-file": {"description": "The SQLite file to import data from. Can't be used with --to-file.", "env": "SHOPIFY_FLAG_FROM_FILE", "hasDynamicHelp": false, "multiple": false, "name": "from-file", "required": false, "type": "option"}, "from-store": {"description": "The source store domain to copy/export data from (e.g., source.myshopify.com).", "env": "SHOPIFY_FLAG_FROM_STORE", "hasDynamicHelp": false, "multiple": false, "name": "from-store", "required": false, "type": "option"}, "key": {"default": ["products:handle"], "description": "The identity key to use to match resources. ID metafields can be specified as <object_type>:metafield:<metafield_namespace>:<key>", "env": "SHOPIFY_FLAG_IDENTITY_KEY", "hasDynamicHelp": false, "multiple": true, "name": "key", "required": false, "type": "option"}, "mock": {"allowNo": false, "char": "m", "description": "Use mock data instead of real API calls (for development).", "env": "SHOPIFY_FLAG_MOCK", "hidden": true, "name": "mock", "required": false, "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "no-prompt": {"allowNo": false, "char": "y", "description": "Skip confirmation prompts.", "env": "SHOPIFY_FLAG_YES", "name": "no-prompt", "required": false, "type": "boolean"}, "to-file": {"description": "The SQLite file path to export data to. Omit to auto-generate filename. Can't be used with --from-file.", "env": "SHOPIFY_FLAG_TO_FILE", "hasDynamicHelp": false, "multiple": false, "name": "to-file", "required": false, "type": "option"}, "to-store": {"description": "The target store domain to copy/import data to (e.g., target.myshopify.com).", "env": "SHOPIFY_FLAG_TO_STORE", "hasDynamicHelp": false, "multiple": false, "name": "to-store", "required": false, "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "store:copy", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Copy, export, or import store data"}, "theme:check": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Calls and runs \"Theme Check\" (https://shopify.dev/docs/themes/tools/theme-check) to analyze your theme code for errors and to ensure that it follows theme and Liquid best practices. \"Learn more about the checks that Theme Check runs.\" (https://shopify.dev/docs/themes/tools/theme-check/checks)", "descriptionWithMarkdown": "Calls and runs [Theme Check](https://shopify.dev/docs/themes/tools/theme-check) to analyze your theme code for errors and to ensure that it follows theme and Liquid best practices. [Learn more about the checks that Theme Check runs.](https://shopify.dev/docs/themes/tools/theme-check/checks)", "flags": {"auto-correct": {"allowNo": false, "char": "a", "description": "Automatically fix offenses", "env": "SHOPIFY_FLAG_AUTO_CORRECT", "name": "auto-correct", "required": false, "type": "boolean"}, "config": {"char": "C", "description": "Use the config provided, overriding .theme-check.yml if present\n      Supports all theme-check: config values, e.g., theme-check:theme-app-extension,\n      theme-check:recommended, theme-check:all\n      For backwards compatibility, :theme_app_extension is also supported ", "env": "SHOPIFY_FLAG_CONFIG", "hasDynamicHelp": false, "multiple": false, "name": "config", "required": false, "type": "option"}, "environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "fail-level": {"default": "error", "description": "Minimum severity for exit with error code", "env": "SHOPIFY_FLAG_FAIL_LEVEL", "hasDynamicHelp": false, "multiple": false, "name": "fail-level", "options": ["crash", "error", "suggestion", "style", "warning", "info"], "required": false, "type": "option"}, "init": {"allowNo": false, "description": "Generate a .theme-check.yml file", "env": "SHOPIFY_FLAG_INIT", "name": "init", "required": false, "type": "boolean"}, "list": {"allowNo": false, "description": "List enabled checks", "env": "SHOPIFY_FLAG_LIST", "name": "list", "required": false, "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "output": {"char": "o", "default": "text", "description": "The output format to use", "env": "SHOPIFY_FLAG_OUTPUT", "hasDynamicHelp": false, "multiple": false, "name": "output", "options": ["text", "json"], "required": false, "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "print": {"allowNo": false, "description": "Output active config to STDOUT", "env": "SHOPIFY_FLAG_PRINT", "name": "print", "required": false, "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}, "version": {"allowNo": false, "char": "v", "description": "Print Theme Check version", "env": "SHOPIFY_FLAG_VERSION", "name": "version", "required": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:check", "multiEnvironmentsFlags": ["path"], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Validate the theme."}, "theme:console": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Starts the Shopify Liquid REPL (read-eval-print loop) tool. This tool provides an interactive terminal interface for evaluating Liquid code and exploring Liquid objects, filters, and tags using real store data.\n\n  You can also provide context to the console using a URL, as some Liquid objects are context-specific", "descriptionWithMarkdown": "Starts the Shopify Liquid REPL (read-eval-print loop) tool. This tool provides an interactive terminal interface for evaluating Liquid code and exploring Liquid objects, filters, and tags using real store data.\n\n  You can also provide context to the console using a URL, as some Liquid objects are context-specific", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "store-password": {"description": "The password for storefronts with password protection.", "env": "SHOPIFY_FLAG_STORE_PASSWORD", "hasDynamicHelp": false, "multiple": false, "name": "store-password", "type": "option"}, "url": {"default": "/", "description": "The url to be used as context", "env": "SHOPIFY_FLAG_URL", "hasDynamicHelp": false, "multiple": false, "name": "url", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:console", "multiEnvironmentsFlags": null, "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Shopify Liquid REPL (read-eval-print loop) tool", "usage": ["theme console", "theme console --url /products/classic-leather-jacket"]}, "theme:delete": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Deletes a theme from your store.\n\n  You can specify multiple themes by ID. If no theme is specified, then you're prompted to select the theme that you want to delete from the list of themes in your store.\n\n  You're asked to confirm that you want to delete the specified themes before they are deleted. You can skip this confirmation using the `--force` flag.", "descriptionWithMarkdown": "Deletes a theme from your store.\n\n  You can specify multiple themes by ID. If no theme is specified, then you're prompted to select the theme that you want to delete from the list of themes in your store.\n\n  You're asked to confirm that you want to delete the specified themes before they are deleted. You can skip this confirmation using the `--force` flag.", "flags": {"development": {"allowNo": false, "char": "d", "description": "Delete your development theme.", "env": "SHOPIFY_FLAG_DEVELOPMENT", "name": "development", "type": "boolean"}, "environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Skip confirmation.", "env": "SHOPIFY_FLAG_FORCE", "name": "force", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "show-all": {"allowNo": false, "char": "a", "description": "Include others development themes in theme list.", "env": "SHOPIFY_FLAG_SHOW_ALL", "name": "show-all", "type": "boolean"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": true, "name": "theme", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:delete", "multiEnvironmentsFlags": ["store", "password", ["development", "theme"]], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Delete remote themes from the connected store. This command can't be undone."}, "theme:dev": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "\n  Uploads the current theme as the specified theme, or a \"development theme\" (https://shopify.dev/docs/themes/tools/cli#development-themes), to a store so you can preview it.\n\nThis command returns the following information:\n\n- A link to your development theme at http://127.0.0.1:9292. This URL can hot reload local changes to CSS and sections, or refresh the entire page when a file changes, enabling you to preview changes in real time using the store's data.\n\n  You can specify a different network interface and port using `--host` and `--port`.\n\n- A link to the \"editor\" (https://shopify.dev/docs/themes/tools/online-editor) for the theme in the Shopify admin.\n\n- A \"preview link\" (https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with other developers.\n\nIf you already have a development theme for your current environment, then this command replaces the development theme with your local theme. You can override this using the `--theme-editor-sync` flag.\n\n> Note: You can't preview checkout customizations using http://127.0.0.1:9292.\n\nDevelopment themes are deleted when you run `shopify auth logout`. If you need a preview link that can be used after you log out, then you should \"share\" (https://shopify.dev/docs/api/shopify-cli/theme/theme-share) your theme or \"push\" (https://shopify.dev/docs/api/shopify-cli/theme/theme-push) to an unpublished theme on your store.\n\nYou can run this command only in a directory that matches the \"default Shopify theme folder structure\" (https://shopify.dev/docs/themes/tools/cli#directory-structure).", "descriptionWithMarkdown": "\n  Uploads the current theme as the specified theme, or a [development theme](https://shopify.dev/docs/themes/tools/cli#development-themes), to a store so you can preview it.\n\nThis command returns the following information:\n\n- A link to your development theme at http://127.0.0.1:9292. This URL can hot reload local changes to CSS and sections, or refresh the entire page when a file changes, enabling you to preview changes in real time using the store's data.\n\n  You can specify a different network interface and port using `--host` and `--port`.\n\n- A link to the [editor](https://shopify.dev/docs/themes/tools/online-editor) for the theme in the Shopify admin.\n\n- A [preview link](https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with other developers.\n\nIf you already have a development theme for your current environment, then this command replaces the development theme with your local theme. You can override this using the `--theme-editor-sync` flag.\n\n> Note: You can't preview checkout customizations using http://127.0.0.1:9292.\n\nDevelopment themes are deleted when you run `shopify auth logout`. If you need a preview link that can be used after you log out, then you should [share](https://shopify.dev/docs/api/shopify-cli/theme/theme-share) your theme or [push](https://shopify.dev/docs/api/shopify-cli/theme/theme-push) to an unpublished theme on your store.\n\nYou can run this command only in a directory that matches the [default Shopify theme folder structure](https://shopify.dev/docs/themes/tools/cli#directory-structure).", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "error-overlay": {"default": "default", "description": "Controls the visibility of the error overlay when an theme asset upload fails:\n- silent Prevents the error overlay from appearing.\n- default Displays the error overlay.\n      ", "env": "SHOPIFY_FLAG_ERROR_OVERLAY", "hasDynamicHelp": false, "multiple": false, "name": "error-overlay", "options": ["silent", "default"], "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Proceed without confirmation, if current directory does not seem to be theme directory.", "env": "SHOPIFY_FLAG_FORCE", "hidden": true, "name": "force", "type": "boolean"}, "host": {"description": "Set which network interface the web server listens on. The default value is 127.0.0.1.", "env": "SHOPIFY_FLAG_HOST", "hasDynamicHelp": false, "multiple": false, "name": "host", "type": "option"}, "ignore": {"char": "x", "description": "Skip hot reloading any files that match the specified pattern.", "env": "SHOPIFY_FLAG_IGNORE", "hasDynamicHelp": false, "multiple": true, "name": "ignore", "type": "option"}, "live-reload": {"default": "hot-reload", "description": "The live reload mode switches the server behavior when a file is modified:\n- hot-reload Hot reloads local changes to CSS and sections (default)\n- full-page  Always refreshes the entire page\n- off        Deactivate live reload", "env": "SHOPIFY_FLAG_LIVE_RELOAD", "hasDynamicHelp": false, "multiple": false, "name": "live-reload", "options": ["hot-reload", "full-page", "off"], "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "nodelete": {"allowNo": false, "char": "n", "description": "Prevents files from being deleted in the remote theme when a file has been deleted locally. This applies to files that are deleted while the command is running, and files that have been deleted locally before the command is run.", "env": "SHOPIFY_FLAG_NODELETE", "name": "nodelete", "type": "boolean"}, "notify": {"description": "The file path or URL. The file path is to a file that you want updated on idle. The URL path is where you want a webhook posted to report on file changes.", "env": "SHOPIFY_FLAG_NOTIFY", "hasDynamicHelp": false, "multiple": false, "name": "notify", "type": "option"}, "only": {"char": "o", "description": "Hot reload only files that match the specified pattern.", "env": "SHOPIFY_FLAG_ONLY", "hasDynamicHelp": false, "multiple": true, "name": "only", "type": "option"}, "open": {"allowNo": false, "description": "Automatically launch the theme preview in your default web browser.", "env": "SHOPIFY_FLAG_OPEN", "name": "open", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "poll": {"allowNo": false, "description": "Force polling to detect file changes.", "env": "SHOPIFY_FLAG_POLL", "hidden": true, "name": "poll", "type": "boolean"}, "port": {"description": "Local port to serve theme preview from.", "env": "SHOPIFY_FLAG_PORT", "hasDynamicHelp": false, "multiple": false, "name": "port", "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "store-password": {"description": "The password for storefronts with password protection.", "env": "SHOPIFY_FLAG_STORE_PASSWORD", "hasDynamicHelp": false, "multiple": false, "name": "store-password", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "theme-editor-sync": {"allowNo": false, "description": "Synchronize Theme Editor updates in the local theme files.", "env": "SHOPIFY_FLAG_THEME_EDITOR_SYNC", "name": "theme-editor-sync", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:dev", "multiEnvironmentsFlags": null, "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Uploads the current theme as a development theme to the connected store, then prints theme editor and preview URLs to your terminal. While running, changes will push to the store in real time."}, "theme:duplicate": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "If you want to duplicate your local theme, you need to run `shopify theme push` first.\n\nIf no theme ID is specified, you're prompted to select the theme that you want to duplicate from the list of themes in your store. You're asked to confirm that you want to duplicate the specified theme.\n\nPrompts and confirmations are not shown when duplicate is run in a CI environment or the `--force` flag is used, therefore you must specify a theme ID using the `--theme` flag.\n\nYou can optionally name the duplicated theme using the `--name` flag.\n\nIf you use the `--json` flag, then theme information is returned in JSON format, which can be used as a machine-readable input for scripts or continuous integration.\n\nSample JSON output:\n\n```json\n{\n  \"theme\": {\n    \"id\": 108267175958,\n    \"name\": \"A Duplicated Theme\",\n    \"role\": \"unpublished\",\n    \"shop\": \"mystore.myshopify.com\"\n  }\n}\n```\n\n```json\n{\n  \"message\": \"The theme 'Summer Edition' could not be duplicated due to errors\",\n  \"errors\": [\"Maximum number of themes reached\"],\n  \"requestId\": \"12345-abcde-67890\"\n}\n```", "descriptionWithMarkdown": "If you want to duplicate your local theme, you need to run `shopify theme push` first.\n\nIf no theme ID is specified, you're prompted to select the theme that you want to duplicate from the list of themes in your store. You're asked to confirm that you want to duplicate the specified theme.\n\nPrompts and confirmations are not shown when duplicate is run in a CI environment or the `--force` flag is used, therefore you must specify a theme ID using the `--theme` flag.\n\nYou can optionally name the duplicated theme using the `--name` flag.\n\nIf you use the `--json` flag, then theme information is returned in JSON format, which can be used as a machine-readable input for scripts or continuous integration.\n\nSample JSON output:\n\n```json\n{\n  \"theme\": {\n    \"id\": 108267175958,\n    \"name\": \"A Duplicated Theme\",\n    \"role\": \"unpublished\",\n    \"shop\": \"mystore.myshopify.com\"\n  }\n}\n```\n\n```json\n{\n  \"message\": \"The theme 'Summer Edition' could not be duplicated due to errors\",\n  \"errors\": [\"Maximum number of themes reached\"],\n  \"requestId\": \"12345-abcde-67890\"\n}\n```", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Force the duplicate operation to run without prompts or confirmations.", "env": "SHOPIFY_FLAG_FORCE", "name": "force", "type": "boolean"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "name": {"char": "n", "description": "Name of the newly duplicated theme.", "env": "SHOPIFY_FLAG_NAME", "hasDynamicHelp": false, "multiple": false, "name": "name", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:duplicate", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Duplicates a theme from your theme library.", "usage": ["theme duplicate", "theme duplicate --theme 10 --name 'New Theme'"]}, "theme:info": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Displays information about your theme environment, including your current store. Can also retrieve information about a specific theme.", "flags": {"development": {"allowNo": false, "char": "d", "description": "Retrieve info from your development theme.", "env": "SHOPIFY_FLAG_DEVELOPMENT", "name": "development", "type": "boolean"}, "environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:info", "multiEnvironmentsFlags": ["store", "password"], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "theme:init": {"aliases": [], "args": {"name": {"description": "Name of the new theme", "name": "name", "required": false}}, "customPluginName": "@shopify/theme", "description": "Clones a Git repository to your local machine to use as the starting point for building a theme.\n\n  If no Git repository is specified, then this command creates a copy of Shopify's \"Skeleton theme\" (https://github.com/Shopify/skeleton-theme.git), with the specified name in the current folder. If no name is provided, then you're prompted to enter one.\n\n  > Caution: If you're building a theme for the Shopify Theme Store, then you can use our example theme as a starting point. However, the theme that you submit needs to be \"substantively different from existing themes\" (https://shopify.dev/docs/themes/store/requirements#uniqueness) so that it provides added value for users.\n  ", "descriptionWithMarkdown": "Clones a Git repository to your local machine to use as the starting point for building a theme.\n\n  If no Git repository is specified, then this command creates a copy of Shopify's [Skeleton theme](https://github.com/Shopify/skeleton-theme.git), with the specified name in the current folder. If no name is provided, then you're prompted to enter one.\n\n  > Caution: If you're building a theme for the Shopify Theme Store, then you can use our example theme as a starting point. However, the theme that you submit needs to be [substantively different from existing themes](https://shopify.dev/docs/themes/store/requirements#uniqueness) so that it provides added value for users.\n  ", "flags": {"clone-url": {"char": "u", "default": "https://github.com/Shopify/skeleton-theme.git", "description": "The Git URL to clone from. Defaults to Shopify's Skeleton theme.", "env": "SHOPIFY_FLAG_CLONE_URL", "hasDynamicHelp": false, "multiple": false, "name": "clone-url", "type": "option"}, "latest": {"allowNo": false, "char": "l", "description": "Downloads the latest release of the `clone-url`", "env": "SHOPIFY_FLAG_LATEST", "name": "latest", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:init", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Clones a Git repository to use as a starting point for building a new theme.", "usage": "theme init [name] [flags]"}, "theme:language-server": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Starts the \"Language Server\" (https://shopify.dev/docs/themes/tools/cli/language-server).", "descriptionWithMarkdown": "Starts the [Language Server](https://shopify.dev/docs/themes/tools/cli/language-server).", "flags": {"no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:language-server", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Start a Language Server Protocol server."}, "theme:list": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Lists the themes in your store, along with their IDs and statuses.", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "id": {"description": "Only list theme with the given ID.", "env": "SHOPIFY_FLAG_ID", "hasDynamicHelp": false, "multiple": false, "name": "id", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "name": {"description": "Only list themes that contain the given name.", "env": "SHOPIFY_FLAG_NAME", "hasDynamicHelp": false, "multiple": false, "name": "name", "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "role": {"description": "Only list themes with the given role.", "env": "SHOPIFY_FLAG_ROLE", "hasDynamicHelp": false, "multiple": false, "name": "role", "options": ["live", "unpublished", "development"], "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:list", "multiEnvironmentsFlags": ["store", "password"], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "theme:metafields:pull": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Retrieves metafields from Shopify Admin.\n\nIf the metafields file already exists, it will be overwritten.", "descriptionWithMarkdown": "Retrieves metafields from Shopify Admin.\n\nIf the metafields file already exists, it will be overwritten.", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Proceed without confirmation, if current directory does not seem to be theme directory.", "env": "SHOPIFY_FLAG_FORCE", "hidden": true, "name": "force", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:metafields:pull", "multiEnvironmentsFlags": null, "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Download metafields definitions from your shop into a local file."}, "theme:open": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Returns links that let you preview the specified theme. The following links are returned:\n\n  - A link to the \"editor\" (https://shopify.dev/docs/themes/tools/online-editor) for the theme in the Shopify admin.\n  - A \"preview link\" (https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with other developers.\n\n  If you don't specify a theme, then you're prompted to select the theme to open from the list of the themes in your store.", "descriptionWithMarkdown": "Returns links that let you preview the specified theme. The following links are returned:\n\n  - A link to the [editor](https://shopify.dev/docs/themes/tools/online-editor) for the theme in the Shopify admin.\n  - A [preview link](https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with other developers.\n\n  If you don't specify a theme, then you're prompted to select the theme to open from the list of the themes in your store.", "flags": {"development": {"allowNo": false, "char": "d", "description": "Open your development theme.", "env": "SHOPIFY_FLAG_DEVELOPMENT", "name": "development", "type": "boolean"}, "editor": {"allowNo": false, "char": "E", "description": "Open the theme editor for the specified theme in the browser.", "env": "SHOPIFY_FLAG_EDITOR", "name": "editor", "type": "boolean"}, "environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "live": {"allowNo": false, "char": "l", "description": "Open your live (published) theme.", "env": "SHOPIFY_FLAG_LIVE", "name": "live", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:open", "multiEnvironmentsFlags": null, "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Opens the preview of your remote theme."}, "theme:package": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Packages your local theme files into a ZIP file that can be uploaded to Shopify.\n\n  Only folders that match the \"default Shopify theme folder structure\" (https://shopify.dev/docs/storefronts/themes/tools/cli#directory-structure) are included in the package.\n\n  The package includes the `listings` directory if present (required for multi-preset themes per \"Theme Store requirements\" (https://shopify.dev/docs/storefronts/themes/store/requirements#adding-presets-to-your-theme-zip-submission)).\n\n  The ZIP file uses the name `theme_name-theme_version.zip`, based on parameters in your \"settings_schema.json\" (https://shopify.dev/docs/storefronts/themes/architecture/config/settings-schema-json) file.", "descriptionWithMarkdown": "Packages your local theme files into a ZIP file that can be uploaded to Shopify.\n\n  Only folders that match the [default Shopify theme folder structure](https://shopify.dev/docs/storefronts/themes/tools/cli#directory-structure) are included in the package.\n\n  The package includes the `listings` directory if present (required for multi-preset themes per [Theme Store requirements](https://shopify.dev/docs/storefronts/themes/store/requirements#adding-presets-to-your-theme-zip-submission)).\n\n  The ZIP file uses the name `theme_name-theme_version.zip`, based on parameters in your [settings_schema.json](https://shopify.dev/docs/storefronts/themes/architecture/config/settings-schema-json) file.", "flags": {"no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:package", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Package your theme into a .zip file, ready to upload to the Online Store."}, "theme:profile": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Profile the Shopify Liquid on a given page.\n\n  This command will open a web page with the Speedscope profiler detailing the time spent executing Liquid on the given page.", "descriptionWithMarkdown": "Profile the Shopify Liquid on a given page.\n\n  This command will open a web page with the Speedscope profiler detailing the time spent executing Liquid on the given page.", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "store-password": {"description": "The password for storefronts with password protection.", "env": "SHOPIFY_FLAG_STORE_PASSWORD", "hasDynamicHelp": false, "multiple": false, "name": "store-password", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "url": {"default": "/", "description": "The url to be used as context", "env": "SHOPIFY_FLAG_URL", "hasDynamicHelp": false, "multiple": false, "name": "url", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:profile", "multiEnvironmentsFlags": null, "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Profile the Liquid rendering of a theme page.", "usage": ["theme profile", "theme profile --url /products/classic-leather-jacket"]}, "theme:publish": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Publishes an unpublished theme from your theme library.\n\nIf no theme ID is specified, then you're prompted to select the theme that you want to publish from the list of themes in your store.\n\nYou can run this command only in a directory that matches the \"default Shopify theme folder structure\" (https://shopify.dev/docs/themes/tools/cli#directory-structure).\n\nIf you want to publish your local theme, then you need to run `shopify theme push` first. You're asked to confirm that you want to publish the specified theme. You can skip this confirmation using the `--force` flag.", "descriptionWithMarkdown": "Publishes an unpublished theme from your theme library.\n\nIf no theme ID is specified, then you're prompted to select the theme that you want to publish from the list of themes in your store.\n\nYou can run this command only in a directory that matches the [default Shopify theme folder structure](https://shopify.dev/docs/themes/tools/cli#directory-structure).\n\nIf you want to publish your local theme, then you need to run `shopify theme push` first. You're asked to confirm that you want to publish the specified theme. You can skip this confirmation using the `--force` flag.", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Skip confirmation.", "env": "SHOPIFY_FLAG_FORCE", "name": "force", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:publish", "multiEnvironmentsFlags": ["store", "password", "theme"], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Set a remote theme as the live theme."}, "theme:pull": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Retrieves theme files from Shopify.\n\nIf no theme is specified, then you're prompted to select the theme to pull from the list of the themes in your store.", "descriptionWithMarkdown": "Retrieves theme files from Shopify.\n\nIf no theme is specified, then you're prompted to select the theme to pull from the list of the themes in your store.", "flags": {"development": {"allowNo": false, "char": "d", "description": "Pull theme files from your remote development theme.", "env": "SHOPIFY_FLAG_DEVELOPMENT", "name": "development", "type": "boolean"}, "environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Proceed without confirmation, if current directory does not seem to be theme directory.", "env": "SHOPIFY_FLAG_FORCE", "hidden": true, "name": "force", "type": "boolean"}, "ignore": {"char": "x", "description": "Skip downloading the specified files (Multiple flags allowed). Wrap the value in double quotes if you're using wildcards.", "env": "SHOPIFY_FLAG_IGNORE", "hasDynamicHelp": false, "multiple": true, "name": "ignore", "type": "option"}, "live": {"allowNo": false, "char": "l", "description": "Pull theme files from your remote live theme.", "env": "SHOPIFY_FLAG_LIVE", "name": "live", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "nodelete": {"allowNo": false, "char": "n", "description": "Prevent deleting local files that don't exist remotely.", "env": "SHOPIFY_FLAG_NODELETE", "name": "nodelete", "type": "boolean"}, "only": {"char": "o", "description": "Download only the specified files (Multiple flags allowed). Wrap the value in double quotes if you're using wildcards.", "env": "SHOPIFY_FLAG_ONLY", "hasDynamicHelp": false, "multiple": true, "name": "only", "type": "option"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:pull", "multiEnvironmentsFlags": ["store", "password", "path", ["live", "development", "theme"]], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Download your remote theme files locally."}, "theme:push": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Uploads your local theme files to Shopify, overwriting the remote version if specified.\n\n  If no theme is specified, then you're prompted to select the theme to overwrite from the list of the themes in your store.\n\n  You can run this command only in a directory that matches the \"default Shopify theme folder structure\" (https://shopify.dev/docs/themes/tools/cli#directory-structure).\n\n  This command returns the following information:\n\n  - A link to the \"editor\" (https://shopify.dev/docs/themes/tools/online-editor) for the theme in the Shopify admin.\n  - A \"preview link\" (https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with others.\n\n  If you use the `--json` flag, then theme information is returned in JSON format, which can be used as a machine-readable input for scripts or continuous integration.\n\n  Sample output:\n\n  ```json\n  {\n    \"theme\": {\n      \"id\": 108267175958,\n      \"name\": \"MyTheme\",\n      \"role\": \"unpublished\",\n      \"shop\": \"mystore.myshopify.com\",\n      \"editor_url\": \"https://mystore.myshopify.com/admin/themes/108267175958/editor\",\n      \"preview_url\": \"https://mystore.myshopify.com/?preview_theme_id=108267175958\"\n    }\n  }\n  ```\n    ", "descriptionWithMarkdown": "Uploads your local theme files to Shopify, overwriting the remote version if specified.\n\n  If no theme is specified, then you're prompted to select the theme to overwrite from the list of the themes in your store.\n\n  You can run this command only in a directory that matches the [default Shopify theme folder structure](https://shopify.dev/docs/themes/tools/cli#directory-structure).\n\n  This command returns the following information:\n\n  - A link to the [editor](https://shopify.dev/docs/themes/tools/online-editor) for the theme in the Shopify admin.\n  - A [preview link](https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with others.\n\n  If you use the `--json` flag, then theme information is returned in JSON format, which can be used as a machine-readable input for scripts or continuous integration.\n\n  Sample output:\n\n  ```json\n  {\n    \"theme\": {\n      \"id\": 108267175958,\n      \"name\": \"MyTheme\",\n      \"role\": \"unpublished\",\n      \"shop\": \"mystore.myshopify.com\",\n      \"editor_url\": \"https://mystore.myshopify.com/admin/themes/108267175958/editor\",\n      \"preview_url\": \"https://mystore.myshopify.com/?preview_theme_id=108267175958\"\n    }\n  }\n  ```\n    ", "flags": {"allow-live": {"allowNo": false, "char": "a", "description": "Allow push to a live theme.", "env": "SHOPIFY_FLAG_ALLOW_LIVE", "name": "allow-live", "type": "boolean"}, "development": {"allowNo": false, "char": "d", "description": "Push theme files from your remote development theme.", "env": "SHOPIFY_FLAG_DEVELOPMENT", "name": "development", "type": "boolean"}, "environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Proceed without confirmation, if current directory does not seem to be theme directory.", "env": "SHOPIFY_FLAG_FORCE", "hidden": true, "name": "force", "type": "boolean"}, "ignore": {"char": "x", "description": "Skip uploading the specified files (Multiple flags allowed). Wrap the value in double quotes if you're using wildcards.", "env": "SHOPIFY_FLAG_IGNORE", "hasDynamicHelp": false, "multiple": true, "name": "ignore", "type": "option"}, "json": {"allowNo": false, "char": "j", "description": "Output the result as JSON.", "env": "SHOPIFY_FLAG_JSON", "hidden": false, "name": "json", "type": "boolean"}, "live": {"allowNo": false, "char": "l", "description": "Push theme files from your remote live theme.", "env": "SHOPIFY_FLAG_LIVE", "name": "live", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "nodelete": {"allowNo": false, "char": "n", "description": "Prevent deleting remote files that don't exist locally.", "env": "SHOPIFY_FLAG_NODELETE", "name": "nodelete", "type": "boolean"}, "only": {"char": "o", "description": "Upload only the specified files (Multiple flags allowed). Wrap the value in double quotes if you're using wildcards.", "env": "SHOPIFY_FLAG_ONLY", "hasDynamicHelp": false, "multiple": true, "name": "only", "type": "option"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "publish": {"allowNo": false, "char": "p", "description": "Publish as the live theme after uploading.", "env": "SHOPIFY_FLAG_PUBLISH", "name": "publish", "type": "boolean"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "strict": {"allowNo": false, "description": "Require theme check to pass without errors before pushing. Warnings are allowed.", "env": "SHOPIFY_FLAG_STRICT_PUSH", "name": "strict", "type": "boolean"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "unpublished": {"allowNo": false, "char": "u", "description": "Create a new unpublished theme and push to it.", "env": "SHOPIFY_FLAG_UNPUBLISHED", "name": "unpublished", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:push", "multiEnvironmentsFlags": ["store", "password", "path", ["live", "development", "theme"]], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Uploads your local theme files to the connected store, overwriting the remote version if specified.", "usage": ["theme push", "theme push --unpublished --json"]}, "theme:rename": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Renames a theme in your store.\n\n  If no theme is specified, then you're prompted to select the theme that you want to rename from the list of themes in your store.\n  ", "descriptionWithMarkdown": "Renames a theme in your store.\n\n  If no theme is specified, then you're prompted to select the theme that you want to rename from the list of themes in your store.\n  ", "flags": {"development": {"allowNo": false, "char": "d", "description": "Rename your development theme.", "env": "SHOPIFY_FLAG_DEVELOPMENT", "name": "development", "type": "boolean"}, "environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "live": {"allowNo": false, "char": "l", "description": "Rename your remote live theme.", "env": "SHOPIFY_FLAG_LIVE", "name": "live", "type": "boolean"}, "name": {"char": "n", "description": "The new name for the theme.", "env": "SHOPIFY_FLAG_NEW_NAME", "hasDynamicHelp": false, "multiple": false, "name": "name", "required": false, "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:rename", "multiEnvironmentsFlags": ["store", "password", "name", ["live", "development", "theme"]], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Renames an existing theme."}, "theme:serve": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "\n  Uploads the current theme as the specified theme, or a \"development theme\" (https://shopify.dev/docs/themes/tools/cli#development-themes), to a store so you can preview it.\n\nThis command returns the following information:\n\n- A link to your development theme at http://127.0.0.1:9292. This URL can hot reload local changes to CSS and sections, or refresh the entire page when a file changes, enabling you to preview changes in real time using the store's data.\n\n  You can specify a different network interface and port using `--host` and `--port`.\n\n- A link to the \"editor\" (https://shopify.dev/docs/themes/tools/online-editor) for the theme in the Shopify admin.\n\n- A \"preview link\" (https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with other developers.\n\nIf you already have a development theme for your current environment, then this command replaces the development theme with your local theme. You can override this using the `--theme-editor-sync` flag.\n\n> Note: You can't preview checkout customizations using http://127.0.0.1:9292.\n\nDevelopment themes are deleted when you run `shopify auth logout`. If you need a preview link that can be used after you log out, then you should \"share\" (https://shopify.dev/docs/api/shopify-cli/theme/theme-share) your theme or \"push\" (https://shopify.dev/docs/api/shopify-cli/theme/theme-push) to an unpublished theme on your store.\n\nYou can run this command only in a directory that matches the \"default Shopify theme folder structure\" (https://shopify.dev/docs/themes/tools/cli#directory-structure).", "descriptionWithMarkdown": "\n  Uploads the current theme as the specified theme, or a [development theme](https://shopify.dev/docs/themes/tools/cli#development-themes), to a store so you can preview it.\n\nThis command returns the following information:\n\n- A link to your development theme at http://127.0.0.1:9292. This URL can hot reload local changes to CSS and sections, or refresh the entire page when a file changes, enabling you to preview changes in real time using the store's data.\n\n  You can specify a different network interface and port using `--host` and `--port`.\n\n- A link to the [editor](https://shopify.dev/docs/themes/tools/online-editor) for the theme in the Shopify admin.\n\n- A [preview link](https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with other developers.\n\nIf you already have a development theme for your current environment, then this command replaces the development theme with your local theme. You can override this using the `--theme-editor-sync` flag.\n\n> Note: You can't preview checkout customizations using http://127.0.0.1:9292.\n\nDevelopment themes are deleted when you run `shopify auth logout`. If you need a preview link that can be used after you log out, then you should [share](https://shopify.dev/docs/api/shopify-cli/theme/theme-share) your theme or [push](https://shopify.dev/docs/api/shopify-cli/theme/theme-push) to an unpublished theme on your store.\n\nYou can run this command only in a directory that matches the [default Shopify theme folder structure](https://shopify.dev/docs/themes/tools/cli#directory-structure).", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "error-overlay": {"default": "default", "description": "Controls the visibility of the error overlay when an theme asset upload fails:\n- silent Prevents the error overlay from appearing.\n- default Displays the error overlay.\n      ", "env": "SHOPIFY_FLAG_ERROR_OVERLAY", "hasDynamicHelp": false, "multiple": false, "name": "error-overlay", "options": ["silent", "default"], "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Proceed without confirmation, if current directory does not seem to be theme directory.", "env": "SHOPIFY_FLAG_FORCE", "hidden": true, "name": "force", "type": "boolean"}, "host": {"description": "Set which network interface the web server listens on. The default value is 127.0.0.1.", "env": "SHOPIFY_FLAG_HOST", "hasDynamicHelp": false, "multiple": false, "name": "host", "type": "option"}, "ignore": {"char": "x", "description": "Skip hot reloading any files that match the specified pattern.", "env": "SHOPIFY_FLAG_IGNORE", "hasDynamicHelp": false, "multiple": true, "name": "ignore", "type": "option"}, "live-reload": {"default": "hot-reload", "description": "The live reload mode switches the server behavior when a file is modified:\n- hot-reload Hot reloads local changes to CSS and sections (default)\n- full-page  Always refreshes the entire page\n- off        Deactivate live reload", "env": "SHOPIFY_FLAG_LIVE_RELOAD", "hasDynamicHelp": false, "multiple": false, "name": "live-reload", "options": ["hot-reload", "full-page", "off"], "type": "option"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "nodelete": {"allowNo": false, "char": "n", "description": "Prevents files from being deleted in the remote theme when a file has been deleted locally. This applies to files that are deleted while the command is running, and files that have been deleted locally before the command is run.", "env": "SHOPIFY_FLAG_NODELETE", "name": "nodelete", "type": "boolean"}, "notify": {"description": "The file path or URL. The file path is to a file that you want updated on idle. The URL path is where you want a webhook posted to report on file changes.", "env": "SHOPIFY_FLAG_NOTIFY", "hasDynamicHelp": false, "multiple": false, "name": "notify", "type": "option"}, "only": {"char": "o", "description": "Hot reload only files that match the specified pattern.", "env": "SHOPIFY_FLAG_ONLY", "hasDynamicHelp": false, "multiple": true, "name": "only", "type": "option"}, "open": {"allowNo": false, "description": "Automatically launch the theme preview in your default web browser.", "env": "SHOPIFY_FLAG_OPEN", "name": "open", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "poll": {"allowNo": false, "description": "Force polling to detect file changes.", "env": "SHOPIFY_FLAG_POLL", "hidden": true, "name": "poll", "type": "boolean"}, "port": {"description": "Local port to serve theme preview from.", "env": "SHOPIFY_FLAG_PORT", "hasDynamicHelp": false, "multiple": false, "name": "port", "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "store-password": {"description": "The password for storefronts with password protection.", "env": "SHOPIFY_FLAG_STORE_PASSWORD", "hasDynamicHelp": false, "multiple": false, "name": "store-password", "type": "option"}, "theme": {"char": "t", "description": "Theme ID or name of the remote theme.", "env": "SHOPIFY_FLAG_THEME_ID", "hasDynamicHelp": false, "multiple": false, "name": "theme", "type": "option"}, "theme-editor-sync": {"allowNo": false, "description": "Synchronize Theme Editor updates in the local theme files.", "env": "SHOPIFY_FLAG_THEME_EDITOR_SYNC", "name": "theme-editor-sync", "type": "boolean"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "theme:serve", "multiEnvironmentsFlags": null, "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "summary": "Uploads the current theme as a development theme to the connected store, then prints theme editor and preview URLs to your terminal. While running, changes will push to the store in real time."}, "theme:share": {"aliases": [], "args": {}, "customPluginName": "@shopify/theme", "description": "Uploads your theme as a new, unpublished theme in your theme library. The theme is given a randomized name.\n\n  This command returns a \"preview link\" (https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with others.", "descriptionWithMarkdown": "Uploads your theme as a new, unpublished theme in your theme library. The theme is given a randomized name.\n\n  This command returns a [preview link](https://help.shopify.com/manual/online-store/themes/adding-themes#share-a-theme-preview-with-others) that you can share with others.", "flags": {"environment": {"char": "e", "description": "The environment to apply to the current command.", "env": "SHOPIFY_FLAG_ENVIRONMENT", "hasDynamicHelp": false, "multiple": true, "name": "environment", "type": "option"}, "force": {"allowNo": false, "char": "f", "description": "Proceed without confirmation, if current directory does not seem to be theme directory.", "env": "SHOPIFY_FLAG_FORCE", "hidden": true, "name": "force", "type": "boolean"}, "no-color": {"allowNo": false, "description": "Disable color output.", "env": "SHOPIFY_FLAG_NO_COLOR", "hidden": false, "name": "no-color", "type": "boolean"}, "password": {"description": "Password generated from the Theme Access app.", "env": "SHOPIFY_CLI_THEME_TOKEN", "hasDynamicHelp": false, "multiple": false, "name": "password", "type": "option"}, "path": {"description": "The path where you want to run the command. Defaults to the current working directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "store": {"char": "s", "description": "Store URL. It can be the store prefix (example) or the full myshopify.com URL (example.myshopify.com, https://example.myshopify.com).", "env": "SHOPIFY_FLAG_STORE", "hasDynamicHelp": false, "multiple": false, "name": "store", "type": "option"}, "verbose": {"allowNo": false, "description": "Increase the verbosity of the output.", "env": "SHOPIFY_FLAG_VERBOSE", "hidden": false, "name": "verbose", "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "theme:share", "multiEnvironmentsFlags": ["store", "password", "path"], "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Creates a shareable, unpublished, and new theme on your theme library with a randomized name."}, "upgrade": {"aliases": [], "args": {}, "description": "Shows details on how to upgrade Shopify CLI.", "descriptionWithMarkdown": "Shows details on how to upgrade Shopify CLI.", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "upgrade", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true, "summary": "Shows details on how to upgrade Shopify CLI."}, "version": {"aliases": [], "args": {}, "description": "Shopify CLI version currently installed.", "enableJsonFlag": false, "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "version", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "strict": true}, "webhook:trigger": {"aliases": [], "args": {}, "customPluginName": "@shopify/app", "description": "\n  Triggers the delivery of a sample Admin API event topic payload to a designated address.\n\n  You should use this command to experiment with webhooks, to initially test your webhook configuration, or for unit testing. However, to test your webhook configuration from end to end, you should always trigger webhooks by performing the related action in Shopify.\n\n  Because most webhook deliveries use remote endpoints, you can trigger the command from any directory where you can use Shopify CLI, and send the webhook to any of the supported endpoint types. For example, you can run the command from your app's local directory, but send the webhook to a staging environment endpoint.\n\n  To learn more about using webhooks in a Shopify app, refer to \"Webhooks overview\" (https://shopify.dev/docs/apps/webhooks).\n\n  ### Limitations\n\n  - Webhooks triggered using this method always have the same payload, so they can't be used to test scenarios that differ based on the payload contents.\n  - Webhooks triggered using this method aren't retried when they fail.\n  - Trigger requests are rate-limited using the \"Partner API rate limit\" (https://shopify.dev/docs/api/partner#rate_limits).\n  - You can't use this method to validate your API webhook subscriptions.\n  ", "descriptionWithMarkdown": "\n  Triggers the delivery of a sample Admin API event topic payload to a designated address.\n\n  You should use this command to experiment with webhooks, to initially test your webhook configuration, or for unit testing. However, to test your webhook configuration from end to end, you should always trigger webhooks by performing the related action in Shopify.\n\n  Because most webhook deliveries use remote endpoints, you can trigger the command from any directory where you can use Shopify CLI, and send the webhook to any of the supported endpoint types. For example, you can run the command from your app's local directory, but send the webhook to a staging environment endpoint.\n\n  To learn more about using webhooks in a Shopify app, refer to [Webhooks overview](https://shopify.dev/docs/apps/webhooks).\n\n  ### Limitations\n\n  - Webhooks triggered using this method always have the same payload, so they can't be used to test scenarios that differ based on the payload contents.\n  - Webhooks triggered using this method aren't retried when they fail.\n  - Trigger requests are rate-limited using the [Partner API rate limit](https://shopify.dev/docs/api/partner#rate_limits).\n  - You can't use this method to validate your API webhook subscriptions.\n  ", "flags": {"address": {"description": "The URL where the webhook payload should be sent.\n                    You will need a different address type for each delivery-method:\n                          · For remote HTTP testing, use a URL that starts with https://\n      · For local HTTP testing, use http://localhost:{port}/{url-path}\n                          · For Google Pub/Sub, use pubsub://{project-id}:{topic-id}\n                          · For Amazon EventBridge, use an Amazon Resource Name (ARN) starting with arn:aws:events:", "env": "SHOPIFY_FLAG_ADDRESS", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "address", "required": false, "type": "option"}, "api-version": {"description": "The API Version of the webhook topic.", "env": "SHOPIFY_FLAG_API_VERSION", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "api-version", "required": false, "type": "option"}, "client-id": {"description": "The Client ID of your app.", "env": "SHOPIFY_FLAG_CLIENT_ID", "exclusive": ["config"], "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-id", "type": "option"}, "client-secret": {"description": "Your app's client secret. This secret allows us to return the X-Shopify-Hmac-SHA256 header that lets you validate the origin of the response that you receive.", "env": "SHOPIFY_FLAG_CLIENT_SECRET", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "client-secret", "required": false, "type": "option"}, "config": {"char": "c", "description": "The name of the app configuration.", "env": "SHOPIFY_FLAG_APP_CONFIG", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "config", "type": "option"}, "delivery-method": {"description": "Method chosen to deliver the topic payload. If not passed, it's inferred from the address.", "env": "SHOPIFY_FLAG_DELIVERY_METHOD", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "delivery-method", "options": ["http", "google-pub-sub", "event-bridge"], "required": false, "type": "option"}, "help": {"allowNo": false, "description": "This help. When you run the trigger command the CLI will prompt you for any information that isn't passed using flags.", "env": "SHOPIFY_FLAG_HELP", "hidden": false, "name": "help", "required": false, "type": "boolean"}, "path": {"description": "The path to your app directory.", "env": "SHOPIFY_FLAG_PATH", "hasDynamicHelp": false, "multiple": false, "name": "path", "noCacheDefault": true, "type": "option"}, "reset": {"allowNo": false, "description": "Reset all your settings.", "env": "SHOPIFY_FLAG_RESET", "exclusive": ["config"], "hidden": false, "name": "reset", "type": "boolean"}, "shared-secret": {"description": "Deprecated. Please use client-secret.", "env": "SHOPIFY_FLAG_SHARED_SECRET", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "shared-secret", "required": false, "type": "option"}, "topic": {"description": "The requested webhook topic.", "env": "SHOPIFY_FLAG_TOPIC", "hasDynamicHelp": false, "hidden": false, "multiple": false, "name": "topic", "required": false, "type": "option"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "webhook:trigger", "pluginAlias": "@shopify/cli", "pluginName": "@shopify/cli", "pluginType": "core", "summary": "Trigger delivery of a sample webhook topic payload to a designated address."}}, "version": "3.85.0"}