import {
  execaSync,
  fileExistsSync
} from "./chunk-B36FYNEM.js";
import {
  require_lib
} from "./chunk-F7F4BQYW.js";
import "./chunk-UMUTXITN.js";
import "./chunk-UATXMR5F.js";
import "./chunk-B5EXYCV3.js";
import "./chunk-G2ZZKGSV.js";
import "./chunk-75LV6AQS.js";
import "./chunk-UV5N2VL7.js";
import "./chunk-XE5EOEBL.js";
import {
  cwd,
  joinPath,
  sniffForPath
} from "./chunk-EG6MBBEN.js";
import {
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../cli-kit/dist/public/node/custom-oclif-loader.js
init_cjs_shims();
var import_core = __toESM(require_lib(), 1), ShopifyConfig = class extends import_core.Config {
  constructor(options) {
    let currentPath = cwd(), path = sniffForPath() ?? currentPath, currentPathMightBeHydrogenMonorepo = /(shopify|hydrogen)\/hydrogen/i.test(currentPath), ignoreHydrogenMonorepo = process.env.IGNORE_HYDROGEN_MONOREPO;
    currentPathMightBeHydrogenMonorepo && !ignoreHydrogenMonorepo && (path = execaSync("npm", ["prefix"]).stdout.trim()), fileExistsSync(joinPath(path, "package.json")) && (options.pluginAdditions = {
      core: ["@shopify/cli-hydrogen"],
      path
    }), super(options), this.determinePriority = this.customPriority;
  }
  customPriority(commands) {
    let oclifPlugins = this.pjson.oclif.plugins ?? [];
    return commands.sort((aCommand, bCommand) => {
      let pluginAliasA = aCommand.pluginAlias ?? "A-Cannot-Find-This", pluginAliasB = bCommand.pluginAlias ?? "B-Cannot-Find-This", aIndex = oclifPlugins.indexOf(pluginAliasA), bIndex = oclifPlugins.indexOf(pluginAliasB);
      return aCommand.pluginType === "core" && bCommand.pluginAlias === "@shopify/cli-hydrogen" ? 1 : aCommand.pluginAlias === "@shopify/cli-hydrogen" && bCommand.pluginType === "core" ? -1 : aCommand.pluginType === "core" && bCommand.pluginType === "core" ? aIndex - bIndex : bCommand.pluginType === "core" && aCommand.pluginType !== "core" ? 1 : aCommand.pluginType === "core" && bCommand.pluginType !== "core" ? -1 : aCommand.pluginType === "jit" && bCommand.pluginType !== "jit" ? 1 : bCommand.pluginType === "jit" && aCommand.pluginType !== "jit" ? -1 : 0;
    })[0];
  }
};
export {
  ShopifyConfig
};
//# sourceMappingURL=custom-oclif-loader-J2DN7UTY.js.map
