import {
  __commonJS,
  __require,
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/web-streams-polyfill@3.3.3/node_modules/web-streams-polyfill/dist/ponyfill.es2018.js
var require_ponyfill_es2018 = __commonJS({
  "../../node_modules/.pnpm/web-streams-polyfill@3.3.3/node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(exports, module) {
    init_cjs_shims();
    (function(global2, factory) {
      typeof exports == "object" && typeof module < "u" ? factory(exports) : typeof define == "function" && define.amd ? define(["exports"], factory) : (global2 = typeof globalThis < "u" ? globalThis : global2 || self, factory(global2.WebStreamsPolyfill = {}));
    })(exports, function(exports2) {
      "use strict";
      function noop() {
      }
      function typeIsObject(x2) {
        return typeof x2 == "object" && x2 !== null || typeof x2 == "function";
      }
      let rethrowAssertionErrorRejection = noop;
      function setFunctionName(fn, name) {
        try {
          Object.defineProperty(fn, "name", {
            value: name,
            configurable: !0
          });
        } catch {
        }
      }
      let originalPromise = Promise, originalPromiseThen = Promise.prototype.then, originalPromiseReject = Promise.reject.bind(originalPromise);
      function newPromise(executor) {
        return new originalPromise(executor);
      }
      function promiseResolvedWith(value) {
        return newPromise((resolve) => resolve(value));
      }
      function promiseRejectedWith(reason) {
        return originalPromiseReject(reason);
      }
      function PerformPromiseThen(promise, onFulfilled, onRejected) {
        return originalPromiseThen.call(promise, onFulfilled, onRejected);
      }
      function uponPromise(promise, onFulfilled, onRejected) {
        PerformPromiseThen(PerformPromiseThen(promise, onFulfilled, onRejected), void 0, rethrowAssertionErrorRejection);
      }
      function uponFulfillment(promise, onFulfilled) {
        uponPromise(promise, onFulfilled);
      }
      function uponRejection(promise, onRejected) {
        uponPromise(promise, void 0, onRejected);
      }
      function transformPromiseWith(promise, fulfillmentHandler, rejectionHandler) {
        return PerformPromiseThen(promise, fulfillmentHandler, rejectionHandler);
      }
      function setPromiseIsHandledToTrue(promise) {
        PerformPromiseThen(promise, void 0, rethrowAssertionErrorRejection);
      }
      let _queueMicrotask = (callback) => {
        if (typeof queueMicrotask == "function")
          _queueMicrotask = queueMicrotask;
        else {
          let resolvedPromise = promiseResolvedWith(void 0);
          _queueMicrotask = (cb) => PerformPromiseThen(resolvedPromise, cb);
        }
        return _queueMicrotask(callback);
      };
      function reflectCall(F, V, args) {
        if (typeof F != "function")
          throw new TypeError("Argument is not a function");
        return Function.prototype.apply.call(F, V, args);
      }
      function promiseCall(F, V, args) {
        try {
          return promiseResolvedWith(reflectCall(F, V, args));
        } catch (value) {
          return promiseRejectedWith(value);
        }
      }
      let QUEUE_MAX_ARRAY_SIZE = 16384;
      class SimpleQueue {
        constructor() {
          this._cursor = 0, this._size = 0, this._front = {
            _elements: [],
            _next: void 0
          }, this._back = this._front, this._cursor = 0, this._size = 0;
        }
        get length() {
          return this._size;
        }
        // For exception safety, this method is structured in order:
        // 1. Read state
        // 2. Calculate required state mutations
        // 3. Perform state mutations
        push(element) {
          let oldBack = this._back, newBack = oldBack;
          oldBack._elements.length === QUEUE_MAX_ARRAY_SIZE - 1 && (newBack = {
            _elements: [],
            _next: void 0
          }), oldBack._elements.push(element), newBack !== oldBack && (this._back = newBack, oldBack._next = newBack), ++this._size;
        }
        // Like push(), shift() follows the read -> calculate -> mutate pattern for
        // exception safety.
        shift() {
          let oldFront = this._front, newFront = oldFront, oldCursor = this._cursor, newCursor = oldCursor + 1, elements = oldFront._elements, element = elements[oldCursor];
          return newCursor === QUEUE_MAX_ARRAY_SIZE && (newFront = oldFront._next, newCursor = 0), --this._size, this._cursor = newCursor, oldFront !== newFront && (this._front = newFront), elements[oldCursor] = void 0, element;
        }
        // The tricky thing about forEach() is that it can be called
        // re-entrantly. The queue may be mutated inside the callback. It is easy to
        // see that push() within the callback has no negative effects since the end
        // of the queue is checked for on every iteration. If shift() is called
        // repeatedly within the callback then the next iteration may return an
        // element that has been removed. In this case the callback will be called
        // with undefined values until we either "catch up" with elements that still
        // exist or reach the back of the queue.
        forEach(callback) {
          let i2 = this._cursor, node = this._front, elements = node._elements;
          for (; (i2 !== elements.length || node._next !== void 0) && !(i2 === elements.length && (node = node._next, elements = node._elements, i2 = 0, elements.length === 0)); )
            callback(elements[i2]), ++i2;
        }
        // Return the element that would be returned if shift() was called now,
        // without modifying the queue.
        peek() {
          let front = this._front, cursor = this._cursor;
          return front._elements[cursor];
        }
      }
      let AbortSteps = Symbol("[[AbortSteps]]"), ErrorSteps = Symbol("[[ErrorSteps]]"), CancelSteps = Symbol("[[CancelSteps]]"), PullSteps = Symbol("[[PullSteps]]"), ReleaseSteps = Symbol("[[ReleaseSteps]]");
      function ReadableStreamReaderGenericInitialize(reader, stream) {
        reader._ownerReadableStream = stream, stream._reader = reader, stream._state === "readable" ? defaultReaderClosedPromiseInitialize(reader) : stream._state === "closed" ? defaultReaderClosedPromiseInitializeAsResolved(reader) : defaultReaderClosedPromiseInitializeAsRejected(reader, stream._storedError);
      }
      function ReadableStreamReaderGenericCancel(reader, reason) {
        let stream = reader._ownerReadableStream;
        return ReadableStreamCancel(stream, reason);
      }
      function ReadableStreamReaderGenericRelease(reader) {
        let stream = reader._ownerReadableStream;
        stream._state === "readable" ? defaultReaderClosedPromiseReject(reader, new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")) : defaultReaderClosedPromiseResetToRejected(reader, new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")), stream._readableStreamController[ReleaseSteps](), stream._reader = void 0, reader._ownerReadableStream = void 0;
      }
      function readerLockException(name) {
        return new TypeError("Cannot " + name + " a stream using a released reader");
      }
      function defaultReaderClosedPromiseInitialize(reader) {
        reader._closedPromise = newPromise((resolve, reject) => {
          reader._closedPromise_resolve = resolve, reader._closedPromise_reject = reject;
        });
      }
      function defaultReaderClosedPromiseInitializeAsRejected(reader, reason) {
        defaultReaderClosedPromiseInitialize(reader), defaultReaderClosedPromiseReject(reader, reason);
      }
      function defaultReaderClosedPromiseInitializeAsResolved(reader) {
        defaultReaderClosedPromiseInitialize(reader), defaultReaderClosedPromiseResolve(reader);
      }
      function defaultReaderClosedPromiseReject(reader, reason) {
        reader._closedPromise_reject !== void 0 && (setPromiseIsHandledToTrue(reader._closedPromise), reader._closedPromise_reject(reason), reader._closedPromise_resolve = void 0, reader._closedPromise_reject = void 0);
      }
      function defaultReaderClosedPromiseResetToRejected(reader, reason) {
        defaultReaderClosedPromiseInitializeAsRejected(reader, reason);
      }
      function defaultReaderClosedPromiseResolve(reader) {
        reader._closedPromise_resolve !== void 0 && (reader._closedPromise_resolve(void 0), reader._closedPromise_resolve = void 0, reader._closedPromise_reject = void 0);
      }
      let NumberIsFinite = Number.isFinite || function(x2) {
        return typeof x2 == "number" && isFinite(x2);
      }, MathTrunc = Math.trunc || function(v) {
        return v < 0 ? Math.ceil(v) : Math.floor(v);
      };
      function isDictionary(x2) {
        return typeof x2 == "object" || typeof x2 == "function";
      }
      function assertDictionary(obj, context) {
        if (obj !== void 0 && !isDictionary(obj))
          throw new TypeError(`${context} is not an object.`);
      }
      function assertFunction(x2, context) {
        if (typeof x2 != "function")
          throw new TypeError(`${context} is not a function.`);
      }
      function isObject(x2) {
        return typeof x2 == "object" && x2 !== null || typeof x2 == "function";
      }
      function assertObject(x2, context) {
        if (!isObject(x2))
          throw new TypeError(`${context} is not an object.`);
      }
      function assertRequiredArgument(x2, position, context) {
        if (x2 === void 0)
          throw new TypeError(`Parameter ${position} is required in '${context}'.`);
      }
      function assertRequiredField(x2, field, context) {
        if (x2 === void 0)
          throw new TypeError(`${field} is required in '${context}'.`);
      }
      function convertUnrestrictedDouble(value) {
        return Number(value);
      }
      function censorNegativeZero(x2) {
        return x2 === 0 ? 0 : x2;
      }
      function integerPart(x2) {
        return censorNegativeZero(MathTrunc(x2));
      }
      function convertUnsignedLongLongWithEnforceRange(value, context) {
        let upperBound = Number.MAX_SAFE_INTEGER, x2 = Number(value);
        if (x2 = censorNegativeZero(x2), !NumberIsFinite(x2))
          throw new TypeError(`${context} is not a finite number`);
        if (x2 = integerPart(x2), x2 < 0 || x2 > upperBound)
          throw new TypeError(`${context} is outside the accepted range of 0 to ${upperBound}, inclusive`);
        return !NumberIsFinite(x2) || x2 === 0 ? 0 : x2;
      }
      function assertReadableStream(x2, context) {
        if (!IsReadableStream(x2))
          throw new TypeError(`${context} is not a ReadableStream.`);
      }
      function AcquireReadableStreamDefaultReader(stream) {
        return new ReadableStreamDefaultReader(stream);
      }
      function ReadableStreamAddReadRequest(stream, readRequest) {
        stream._reader._readRequests.push(readRequest);
      }
      function ReadableStreamFulfillReadRequest(stream, chunk, done) {
        let readRequest = stream._reader._readRequests.shift();
        done ? readRequest._closeSteps() : readRequest._chunkSteps(chunk);
      }
      function ReadableStreamGetNumReadRequests(stream) {
        return stream._reader._readRequests.length;
      }
      function ReadableStreamHasDefaultReader(stream) {
        let reader = stream._reader;
        return !(reader === void 0 || !IsReadableStreamDefaultReader(reader));
      }
      class ReadableStreamDefaultReader {
        constructor(stream) {
          if (assertRequiredArgument(stream, 1, "ReadableStreamDefaultReader"), assertReadableStream(stream, "First parameter"), IsReadableStreamLocked(stream))
            throw new TypeError("This stream has already been locked for exclusive reading by another reader");
          ReadableStreamReaderGenericInitialize(this, stream), this._readRequests = new SimpleQueue();
        }
        /**
         * Returns a promise that will be fulfilled when the stream becomes closed,
         * or rejected if the stream ever errors or the reader's lock is released before the stream finishes closing.
         */
        get closed() {
          return IsReadableStreamDefaultReader(this) ? this._closedPromise : promiseRejectedWith(defaultReaderBrandCheckException("closed"));
        }
        /**
         * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.
         */
        cancel(reason = void 0) {
          return IsReadableStreamDefaultReader(this) ? this._ownerReadableStream === void 0 ? promiseRejectedWith(readerLockException("cancel")) : ReadableStreamReaderGenericCancel(this, reason) : promiseRejectedWith(defaultReaderBrandCheckException("cancel"));
        }
        /**
         * Returns a promise that allows access to the next chunk from the stream's internal queue, if available.
         *
         * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.
         */
        read() {
          if (!IsReadableStreamDefaultReader(this))
            return promiseRejectedWith(defaultReaderBrandCheckException("read"));
          if (this._ownerReadableStream === void 0)
            return promiseRejectedWith(readerLockException("read from"));
          let resolvePromise, rejectPromise, promise = newPromise((resolve, reject) => {
            resolvePromise = resolve, rejectPromise = reject;
          });
          return ReadableStreamDefaultReaderRead(this, {
            _chunkSteps: (chunk) => resolvePromise({ value: chunk, done: !1 }),
            _closeSteps: () => resolvePromise({ value: void 0, done: !0 }),
            _errorSteps: (e2) => rejectPromise(e2)
          }), promise;
        }
        /**
         * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.
         * If the associated stream is errored when the lock is released, the reader will appear errored in the same way
         * from now on; otherwise, the reader will appear closed.
         *
         * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by
         * the reader's {@link ReadableStreamDefaultReader.read | read()} method has not yet been settled. Attempting to
         * do so will throw a `TypeError` and leave the reader locked to the stream.
         */
        releaseLock() {
          if (!IsReadableStreamDefaultReader(this))
            throw defaultReaderBrandCheckException("releaseLock");
          this._ownerReadableStream !== void 0 && ReadableStreamDefaultReaderRelease(this);
        }
      }
      Object.defineProperties(ReadableStreamDefaultReader.prototype, {
        cancel: { enumerable: !0 },
        read: { enumerable: !0 },
        releaseLock: { enumerable: !0 },
        closed: { enumerable: !0 }
      }), setFunctionName(ReadableStreamDefaultReader.prototype.cancel, "cancel"), setFunctionName(ReadableStreamDefaultReader.prototype.read, "read"), setFunctionName(ReadableStreamDefaultReader.prototype.releaseLock, "releaseLock"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(ReadableStreamDefaultReader.prototype, Symbol.toStringTag, {
        value: "ReadableStreamDefaultReader",
        configurable: !0
      });
      function IsReadableStreamDefaultReader(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_readRequests") ? !1 : x2 instanceof ReadableStreamDefaultReader;
      }
      function ReadableStreamDefaultReaderRead(reader, readRequest) {
        let stream = reader._ownerReadableStream;
        stream._disturbed = !0, stream._state === "closed" ? readRequest._closeSteps() : stream._state === "errored" ? readRequest._errorSteps(stream._storedError) : stream._readableStreamController[PullSteps](readRequest);
      }
      function ReadableStreamDefaultReaderRelease(reader) {
        ReadableStreamReaderGenericRelease(reader);
        let e2 = new TypeError("Reader was released");
        ReadableStreamDefaultReaderErrorReadRequests(reader, e2);
      }
      function ReadableStreamDefaultReaderErrorReadRequests(reader, e2) {
        let readRequests = reader._readRequests;
        reader._readRequests = new SimpleQueue(), readRequests.forEach((readRequest) => {
          readRequest._errorSteps(e2);
        });
      }
      function defaultReaderBrandCheckException(name) {
        return new TypeError(`ReadableStreamDefaultReader.prototype.${name} can only be used on a ReadableStreamDefaultReader`);
      }
      let AsyncIteratorPrototype = Object.getPrototypeOf(Object.getPrototypeOf(async function* () {
      }).prototype);
      class ReadableStreamAsyncIteratorImpl {
        constructor(reader, preventCancel) {
          this._ongoingPromise = void 0, this._isFinished = !1, this._reader = reader, this._preventCancel = preventCancel;
        }
        next() {
          let nextSteps = () => this._nextSteps();
          return this._ongoingPromise = this._ongoingPromise ? transformPromiseWith(this._ongoingPromise, nextSteps, nextSteps) : nextSteps(), this._ongoingPromise;
        }
        return(value) {
          let returnSteps = () => this._returnSteps(value);
          return this._ongoingPromise ? transformPromiseWith(this._ongoingPromise, returnSteps, returnSteps) : returnSteps();
        }
        _nextSteps() {
          if (this._isFinished)
            return Promise.resolve({ value: void 0, done: !0 });
          let reader = this._reader, resolvePromise, rejectPromise, promise = newPromise((resolve, reject) => {
            resolvePromise = resolve, rejectPromise = reject;
          });
          return ReadableStreamDefaultReaderRead(reader, {
            _chunkSteps: (chunk) => {
              this._ongoingPromise = void 0, _queueMicrotask(() => resolvePromise({ value: chunk, done: !1 }));
            },
            _closeSteps: () => {
              this._ongoingPromise = void 0, this._isFinished = !0, ReadableStreamReaderGenericRelease(reader), resolvePromise({ value: void 0, done: !0 });
            },
            _errorSteps: (reason) => {
              this._ongoingPromise = void 0, this._isFinished = !0, ReadableStreamReaderGenericRelease(reader), rejectPromise(reason);
            }
          }), promise;
        }
        _returnSteps(value) {
          if (this._isFinished)
            return Promise.resolve({ value, done: !0 });
          this._isFinished = !0;
          let reader = this._reader;
          if (!this._preventCancel) {
            let result = ReadableStreamReaderGenericCancel(reader, value);
            return ReadableStreamReaderGenericRelease(reader), transformPromiseWith(result, () => ({ value, done: !0 }));
          }
          return ReadableStreamReaderGenericRelease(reader), promiseResolvedWith({ value, done: !0 });
        }
      }
      let ReadableStreamAsyncIteratorPrototype = {
        next() {
          return IsReadableStreamAsyncIterator(this) ? this._asyncIteratorImpl.next() : promiseRejectedWith(streamAsyncIteratorBrandCheckException("next"));
        },
        return(value) {
          return IsReadableStreamAsyncIterator(this) ? this._asyncIteratorImpl.return(value) : promiseRejectedWith(streamAsyncIteratorBrandCheckException("return"));
        }
      };
      Object.setPrototypeOf(ReadableStreamAsyncIteratorPrototype, AsyncIteratorPrototype);
      function AcquireReadableStreamAsyncIterator(stream, preventCancel) {
        let reader = AcquireReadableStreamDefaultReader(stream), impl = new ReadableStreamAsyncIteratorImpl(reader, preventCancel), iterator = Object.create(ReadableStreamAsyncIteratorPrototype);
        return iterator._asyncIteratorImpl = impl, iterator;
      }
      function IsReadableStreamAsyncIterator(x2) {
        if (!typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_asyncIteratorImpl"))
          return !1;
        try {
          return x2._asyncIteratorImpl instanceof ReadableStreamAsyncIteratorImpl;
        } catch {
          return !1;
        }
      }
      function streamAsyncIteratorBrandCheckException(name) {
        return new TypeError(`ReadableStreamAsyncIterator.${name} can only be used on a ReadableSteamAsyncIterator`);
      }
      let NumberIsNaN = Number.isNaN || function(x2) {
        return x2 !== x2;
      };
      var _a, _b, _c;
      function CreateArrayFromList(elements) {
        return elements.slice();
      }
      function CopyDataBlockBytes(dest, destOffset, src, srcOffset, n) {
        new Uint8Array(dest).set(new Uint8Array(src, srcOffset, n), destOffset);
      }
      let TransferArrayBuffer = (O) => (typeof O.transfer == "function" ? TransferArrayBuffer = (buffer) => buffer.transfer() : typeof structuredClone == "function" ? TransferArrayBuffer = (buffer) => structuredClone(buffer, { transfer: [buffer] }) : TransferArrayBuffer = (buffer) => buffer, TransferArrayBuffer(O)), IsDetachedBuffer = (O) => (typeof O.detached == "boolean" ? IsDetachedBuffer = (buffer) => buffer.detached : IsDetachedBuffer = (buffer) => buffer.byteLength === 0, IsDetachedBuffer(O));
      function ArrayBufferSlice(buffer, begin, end) {
        if (buffer.slice)
          return buffer.slice(begin, end);
        let length = end - begin, slice = new ArrayBuffer(length);
        return CopyDataBlockBytes(slice, 0, buffer, begin, length), slice;
      }
      function GetMethod(receiver, prop) {
        let func = receiver[prop];
        if (func != null) {
          if (typeof func != "function")
            throw new TypeError(`${String(prop)} is not a function`);
          return func;
        }
      }
      function CreateAsyncFromSyncIterator(syncIteratorRecord) {
        let syncIterable = {
          [Symbol.iterator]: () => syncIteratorRecord.iterator
        }, asyncIterator = async function* () {
          return yield* syncIterable;
        }(), nextMethod = asyncIterator.next;
        return { iterator: asyncIterator, nextMethod, done: !1 };
      }
      let SymbolAsyncIterator = (_c = (_a = Symbol.asyncIterator) !== null && _a !== void 0 ? _a : (_b = Symbol.for) === null || _b === void 0 ? void 0 : _b.call(Symbol, "Symbol.asyncIterator")) !== null && _c !== void 0 ? _c : "@@asyncIterator";
      function GetIterator(obj, hint = "sync", method) {
        if (method === void 0)
          if (hint === "async") {
            if (method = GetMethod(obj, SymbolAsyncIterator), method === void 0) {
              let syncMethod = GetMethod(obj, Symbol.iterator), syncIteratorRecord = GetIterator(obj, "sync", syncMethod);
              return CreateAsyncFromSyncIterator(syncIteratorRecord);
            }
          } else
            method = GetMethod(obj, Symbol.iterator);
        if (method === void 0)
          throw new TypeError("The object is not iterable");
        let iterator = reflectCall(method, obj, []);
        if (!typeIsObject(iterator))
          throw new TypeError("The iterator method must return an object");
        let nextMethod = iterator.next;
        return { iterator, nextMethod, done: !1 };
      }
      function IteratorNext(iteratorRecord) {
        let result = reflectCall(iteratorRecord.nextMethod, iteratorRecord.iterator, []);
        if (!typeIsObject(result))
          throw new TypeError("The iterator.next() method must return an object");
        return result;
      }
      function IteratorComplete(iterResult) {
        return !!iterResult.done;
      }
      function IteratorValue(iterResult) {
        return iterResult.value;
      }
      function IsNonNegativeNumber(v) {
        return !(typeof v != "number" || NumberIsNaN(v) || v < 0);
      }
      function CloneAsUint8Array(O) {
        let buffer = ArrayBufferSlice(O.buffer, O.byteOffset, O.byteOffset + O.byteLength);
        return new Uint8Array(buffer);
      }
      function DequeueValue(container) {
        let pair = container._queue.shift();
        return container._queueTotalSize -= pair.size, container._queueTotalSize < 0 && (container._queueTotalSize = 0), pair.value;
      }
      function EnqueueValueWithSize(container, value, size) {
        if (!IsNonNegativeNumber(size) || size === 1 / 0)
          throw new RangeError("Size must be a finite, non-NaN, non-negative number.");
        container._queue.push({ value, size }), container._queueTotalSize += size;
      }
      function PeekQueueValue(container) {
        return container._queue.peek().value;
      }
      function ResetQueue(container) {
        container._queue = new SimpleQueue(), container._queueTotalSize = 0;
      }
      function isDataViewConstructor(ctor) {
        return ctor === DataView;
      }
      function isDataView(view) {
        return isDataViewConstructor(view.constructor);
      }
      function arrayBufferViewElementSize(ctor) {
        return isDataViewConstructor(ctor) ? 1 : ctor.BYTES_PER_ELEMENT;
      }
      class ReadableStreamBYOBRequest {
        constructor() {
          throw new TypeError("Illegal constructor");
        }
        /**
         * Returns the view for writing in to, or `null` if the BYOB request has already been responded to.
         */
        get view() {
          if (!IsReadableStreamBYOBRequest(this))
            throw byobRequestBrandCheckException("view");
          return this._view;
        }
        respond(bytesWritten) {
          if (!IsReadableStreamBYOBRequest(this))
            throw byobRequestBrandCheckException("respond");
          if (assertRequiredArgument(bytesWritten, 1, "respond"), bytesWritten = convertUnsignedLongLongWithEnforceRange(bytesWritten, "First parameter"), this._associatedReadableByteStreamController === void 0)
            throw new TypeError("This BYOB request has been invalidated");
          if (IsDetachedBuffer(this._view.buffer))
            throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");
          ReadableByteStreamControllerRespond(this._associatedReadableByteStreamController, bytesWritten);
        }
        respondWithNewView(view) {
          if (!IsReadableStreamBYOBRequest(this))
            throw byobRequestBrandCheckException("respondWithNewView");
          if (assertRequiredArgument(view, 1, "respondWithNewView"), !ArrayBuffer.isView(view))
            throw new TypeError("You can only respond with array buffer views");
          if (this._associatedReadableByteStreamController === void 0)
            throw new TypeError("This BYOB request has been invalidated");
          if (IsDetachedBuffer(view.buffer))
            throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");
          ReadableByteStreamControllerRespondWithNewView(this._associatedReadableByteStreamController, view);
        }
      }
      Object.defineProperties(ReadableStreamBYOBRequest.prototype, {
        respond: { enumerable: !0 },
        respondWithNewView: { enumerable: !0 },
        view: { enumerable: !0 }
      }), setFunctionName(ReadableStreamBYOBRequest.prototype.respond, "respond"), setFunctionName(ReadableStreamBYOBRequest.prototype.respondWithNewView, "respondWithNewView"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(ReadableStreamBYOBRequest.prototype, Symbol.toStringTag, {
        value: "ReadableStreamBYOBRequest",
        configurable: !0
      });
      class ReadableByteStreamController {
        constructor() {
          throw new TypeError("Illegal constructor");
        }
        /**
         * Returns the current BYOB pull request, or `null` if there isn't one.
         */
        get byobRequest() {
          if (!IsReadableByteStreamController(this))
            throw byteStreamControllerBrandCheckException("byobRequest");
          return ReadableByteStreamControllerGetBYOBRequest(this);
        }
        /**
         * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is
         * over-full. An underlying byte source ought to use this information to determine when and how to apply backpressure.
         */
        get desiredSize() {
          if (!IsReadableByteStreamController(this))
            throw byteStreamControllerBrandCheckException("desiredSize");
          return ReadableByteStreamControllerGetDesiredSize(this);
        }
        /**
         * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from
         * the stream, but once those are read, the stream will become closed.
         */
        close() {
          if (!IsReadableByteStreamController(this))
            throw byteStreamControllerBrandCheckException("close");
          if (this._closeRequested)
            throw new TypeError("The stream has already been closed; do not close it again!");
          let state = this._controlledReadableByteStream._state;
          if (state !== "readable")
            throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be closed`);
          ReadableByteStreamControllerClose(this);
        }
        enqueue(chunk) {
          if (!IsReadableByteStreamController(this))
            throw byteStreamControllerBrandCheckException("enqueue");
          if (assertRequiredArgument(chunk, 1, "enqueue"), !ArrayBuffer.isView(chunk))
            throw new TypeError("chunk must be an array buffer view");
          if (chunk.byteLength === 0)
            throw new TypeError("chunk must have non-zero byteLength");
          if (chunk.buffer.byteLength === 0)
            throw new TypeError("chunk's buffer must have non-zero byteLength");
          if (this._closeRequested)
            throw new TypeError("stream is closed or draining");
          let state = this._controlledReadableByteStream._state;
          if (state !== "readable")
            throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be enqueued to`);
          ReadableByteStreamControllerEnqueue(this, chunk);
        }
        /**
         * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.
         */
        error(e2 = void 0) {
          if (!IsReadableByteStreamController(this))
            throw byteStreamControllerBrandCheckException("error");
          ReadableByteStreamControllerError(this, e2);
        }
        /** @internal */
        [CancelSteps](reason) {
          ReadableByteStreamControllerClearPendingPullIntos(this), ResetQueue(this);
          let result = this._cancelAlgorithm(reason);
          return ReadableByteStreamControllerClearAlgorithms(this), result;
        }
        /** @internal */
        [PullSteps](readRequest) {
          let stream = this._controlledReadableByteStream;
          if (this._queueTotalSize > 0) {
            ReadableByteStreamControllerFillReadRequestFromQueue(this, readRequest);
            return;
          }
          let autoAllocateChunkSize = this._autoAllocateChunkSize;
          if (autoAllocateChunkSize !== void 0) {
            let buffer;
            try {
              buffer = new ArrayBuffer(autoAllocateChunkSize);
            } catch (bufferE) {
              readRequest._errorSteps(bufferE);
              return;
            }
            let pullIntoDescriptor = {
              buffer,
              bufferByteLength: autoAllocateChunkSize,
              byteOffset: 0,
              byteLength: autoAllocateChunkSize,
              bytesFilled: 0,
              minimumFill: 1,
              elementSize: 1,
              viewConstructor: Uint8Array,
              readerType: "default"
            };
            this._pendingPullIntos.push(pullIntoDescriptor);
          }
          ReadableStreamAddReadRequest(stream, readRequest), ReadableByteStreamControllerCallPullIfNeeded(this);
        }
        /** @internal */
        [ReleaseSteps]() {
          if (this._pendingPullIntos.length > 0) {
            let firstPullInto = this._pendingPullIntos.peek();
            firstPullInto.readerType = "none", this._pendingPullIntos = new SimpleQueue(), this._pendingPullIntos.push(firstPullInto);
          }
        }
      }
      Object.defineProperties(ReadableByteStreamController.prototype, {
        close: { enumerable: !0 },
        enqueue: { enumerable: !0 },
        error: { enumerable: !0 },
        byobRequest: { enumerable: !0 },
        desiredSize: { enumerable: !0 }
      }), setFunctionName(ReadableByteStreamController.prototype.close, "close"), setFunctionName(ReadableByteStreamController.prototype.enqueue, "enqueue"), setFunctionName(ReadableByteStreamController.prototype.error, "error"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(ReadableByteStreamController.prototype, Symbol.toStringTag, {
        value: "ReadableByteStreamController",
        configurable: !0
      });
      function IsReadableByteStreamController(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_controlledReadableByteStream") ? !1 : x2 instanceof ReadableByteStreamController;
      }
      function IsReadableStreamBYOBRequest(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_associatedReadableByteStreamController") ? !1 : x2 instanceof ReadableStreamBYOBRequest;
      }
      function ReadableByteStreamControllerCallPullIfNeeded(controller) {
        if (!ReadableByteStreamControllerShouldCallPull(controller))
          return;
        if (controller._pulling) {
          controller._pullAgain = !0;
          return;
        }
        controller._pulling = !0;
        let pullPromise = controller._pullAlgorithm();
        uponPromise(pullPromise, () => (controller._pulling = !1, controller._pullAgain && (controller._pullAgain = !1, ReadableByteStreamControllerCallPullIfNeeded(controller)), null), (e2) => (ReadableByteStreamControllerError(controller, e2), null));
      }
      function ReadableByteStreamControllerClearPendingPullIntos(controller) {
        ReadableByteStreamControllerInvalidateBYOBRequest(controller), controller._pendingPullIntos = new SimpleQueue();
      }
      function ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor) {
        let done = !1;
        stream._state === "closed" && (done = !0);
        let filledView = ReadableByteStreamControllerConvertPullIntoDescriptor(pullIntoDescriptor);
        pullIntoDescriptor.readerType === "default" ? ReadableStreamFulfillReadRequest(stream, filledView, done) : ReadableStreamFulfillReadIntoRequest(stream, filledView, done);
      }
      function ReadableByteStreamControllerConvertPullIntoDescriptor(pullIntoDescriptor) {
        let bytesFilled = pullIntoDescriptor.bytesFilled, elementSize = pullIntoDescriptor.elementSize;
        return new pullIntoDescriptor.viewConstructor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, bytesFilled / elementSize);
      }
      function ReadableByteStreamControllerEnqueueChunkToQueue(controller, buffer, byteOffset, byteLength) {
        controller._queue.push({ buffer, byteOffset, byteLength }), controller._queueTotalSize += byteLength;
      }
      function ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller, buffer, byteOffset, byteLength) {
        let clonedChunk;
        try {
          clonedChunk = ArrayBufferSlice(buffer, byteOffset, byteOffset + byteLength);
        } catch (cloneE) {
          throw ReadableByteStreamControllerError(controller, cloneE), cloneE;
        }
        ReadableByteStreamControllerEnqueueChunkToQueue(controller, clonedChunk, 0, byteLength);
      }
      function ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, firstDescriptor) {
        firstDescriptor.bytesFilled > 0 && ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller, firstDescriptor.buffer, firstDescriptor.byteOffset, firstDescriptor.bytesFilled), ReadableByteStreamControllerShiftPendingPullInto(controller);
      }
      function ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor) {
        let maxBytesToCopy = Math.min(controller._queueTotalSize, pullIntoDescriptor.byteLength - pullIntoDescriptor.bytesFilled), maxBytesFilled = pullIntoDescriptor.bytesFilled + maxBytesToCopy, totalBytesToCopyRemaining = maxBytesToCopy, ready = !1, remainderBytes = maxBytesFilled % pullIntoDescriptor.elementSize, maxAlignedBytes = maxBytesFilled - remainderBytes;
        maxAlignedBytes >= pullIntoDescriptor.minimumFill && (totalBytesToCopyRemaining = maxAlignedBytes - pullIntoDescriptor.bytesFilled, ready = !0);
        let queue = controller._queue;
        for (; totalBytesToCopyRemaining > 0; ) {
          let headOfQueue = queue.peek(), bytesToCopy = Math.min(totalBytesToCopyRemaining, headOfQueue.byteLength), destStart = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;
          CopyDataBlockBytes(pullIntoDescriptor.buffer, destStart, headOfQueue.buffer, headOfQueue.byteOffset, bytesToCopy), headOfQueue.byteLength === bytesToCopy ? queue.shift() : (headOfQueue.byteOffset += bytesToCopy, headOfQueue.byteLength -= bytesToCopy), controller._queueTotalSize -= bytesToCopy, ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesToCopy, pullIntoDescriptor), totalBytesToCopyRemaining -= bytesToCopy;
        }
        return ready;
      }
      function ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, size, pullIntoDescriptor) {
        pullIntoDescriptor.bytesFilled += size;
      }
      function ReadableByteStreamControllerHandleQueueDrain(controller) {
        controller._queueTotalSize === 0 && controller._closeRequested ? (ReadableByteStreamControllerClearAlgorithms(controller), ReadableStreamClose(controller._controlledReadableByteStream)) : ReadableByteStreamControllerCallPullIfNeeded(controller);
      }
      function ReadableByteStreamControllerInvalidateBYOBRequest(controller) {
        controller._byobRequest !== null && (controller._byobRequest._associatedReadableByteStreamController = void 0, controller._byobRequest._view = null, controller._byobRequest = null);
      }
      function ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller) {
        for (; controller._pendingPullIntos.length > 0; ) {
          if (controller._queueTotalSize === 0)
            return;
          let pullIntoDescriptor = controller._pendingPullIntos.peek();
          ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor) && (ReadableByteStreamControllerShiftPendingPullInto(controller), ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor));
        }
      }
      function ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller) {
        let reader = controller._controlledReadableByteStream._reader;
        for (; reader._readRequests.length > 0; ) {
          if (controller._queueTotalSize === 0)
            return;
          let readRequest = reader._readRequests.shift();
          ReadableByteStreamControllerFillReadRequestFromQueue(controller, readRequest);
        }
      }
      function ReadableByteStreamControllerPullInto(controller, view, min, readIntoRequest) {
        let stream = controller._controlledReadableByteStream, ctor = view.constructor, elementSize = arrayBufferViewElementSize(ctor), { byteOffset, byteLength } = view, minimumFill = min * elementSize, buffer;
        try {
          buffer = TransferArrayBuffer(view.buffer);
        } catch (e2) {
          readIntoRequest._errorSteps(e2);
          return;
        }
        let pullIntoDescriptor = {
          buffer,
          bufferByteLength: buffer.byteLength,
          byteOffset,
          byteLength,
          bytesFilled: 0,
          minimumFill,
          elementSize,
          viewConstructor: ctor,
          readerType: "byob"
        };
        if (controller._pendingPullIntos.length > 0) {
          controller._pendingPullIntos.push(pullIntoDescriptor), ReadableStreamAddReadIntoRequest(stream, readIntoRequest);
          return;
        }
        if (stream._state === "closed") {
          let emptyView = new ctor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, 0);
          readIntoRequest._closeSteps(emptyView);
          return;
        }
        if (controller._queueTotalSize > 0) {
          if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {
            let filledView = ReadableByteStreamControllerConvertPullIntoDescriptor(pullIntoDescriptor);
            ReadableByteStreamControllerHandleQueueDrain(controller), readIntoRequest._chunkSteps(filledView);
            return;
          }
          if (controller._closeRequested) {
            let e2 = new TypeError("Insufficient bytes to fill elements in the given buffer");
            ReadableByteStreamControllerError(controller, e2), readIntoRequest._errorSteps(e2);
            return;
          }
        }
        controller._pendingPullIntos.push(pullIntoDescriptor), ReadableStreamAddReadIntoRequest(stream, readIntoRequest), ReadableByteStreamControllerCallPullIfNeeded(controller);
      }
      function ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor) {
        firstDescriptor.readerType === "none" && ReadableByteStreamControllerShiftPendingPullInto(controller);
        let stream = controller._controlledReadableByteStream;
        if (ReadableStreamHasBYOBReader(stream))
          for (; ReadableStreamGetNumReadIntoRequests(stream) > 0; ) {
            let pullIntoDescriptor = ReadableByteStreamControllerShiftPendingPullInto(controller);
            ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor);
          }
      }
      function ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, pullIntoDescriptor) {
        if (ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesWritten, pullIntoDescriptor), pullIntoDescriptor.readerType === "none") {
          ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, pullIntoDescriptor), ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);
          return;
        }
        if (pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill)
          return;
        ReadableByteStreamControllerShiftPendingPullInto(controller);
        let remainderSize = pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize;
        if (remainderSize > 0) {
          let end = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;
          ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller, pullIntoDescriptor.buffer, end - remainderSize, remainderSize);
        }
        pullIntoDescriptor.bytesFilled -= remainderSize, ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor), ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);
      }
      function ReadableByteStreamControllerRespondInternal(controller, bytesWritten) {
        let firstDescriptor = controller._pendingPullIntos.peek();
        ReadableByteStreamControllerInvalidateBYOBRequest(controller), controller._controlledReadableByteStream._state === "closed" ? ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor) : ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, firstDescriptor), ReadableByteStreamControllerCallPullIfNeeded(controller);
      }
      function ReadableByteStreamControllerShiftPendingPullInto(controller) {
        return controller._pendingPullIntos.shift();
      }
      function ReadableByteStreamControllerShouldCallPull(controller) {
        let stream = controller._controlledReadableByteStream;
        return stream._state !== "readable" || controller._closeRequested || !controller._started ? !1 : !!(ReadableStreamHasDefaultReader(stream) && ReadableStreamGetNumReadRequests(stream) > 0 || ReadableStreamHasBYOBReader(stream) && ReadableStreamGetNumReadIntoRequests(stream) > 0 || ReadableByteStreamControllerGetDesiredSize(controller) > 0);
      }
      function ReadableByteStreamControllerClearAlgorithms(controller) {
        controller._pullAlgorithm = void 0, controller._cancelAlgorithm = void 0;
      }
      function ReadableByteStreamControllerClose(controller) {
        let stream = controller._controlledReadableByteStream;
        if (!(controller._closeRequested || stream._state !== "readable")) {
          if (controller._queueTotalSize > 0) {
            controller._closeRequested = !0;
            return;
          }
          if (controller._pendingPullIntos.length > 0) {
            let firstPendingPullInto = controller._pendingPullIntos.peek();
            if (firstPendingPullInto.bytesFilled % firstPendingPullInto.elementSize !== 0) {
              let e2 = new TypeError("Insufficient bytes to fill elements in the given buffer");
              throw ReadableByteStreamControllerError(controller, e2), e2;
            }
          }
          ReadableByteStreamControllerClearAlgorithms(controller), ReadableStreamClose(stream);
        }
      }
      function ReadableByteStreamControllerEnqueue(controller, chunk) {
        let stream = controller._controlledReadableByteStream;
        if (controller._closeRequested || stream._state !== "readable")
          return;
        let { buffer, byteOffset, byteLength } = chunk;
        if (IsDetachedBuffer(buffer))
          throw new TypeError("chunk's buffer is detached and so cannot be enqueued");
        let transferredBuffer = TransferArrayBuffer(buffer);
        if (controller._pendingPullIntos.length > 0) {
          let firstPendingPullInto = controller._pendingPullIntos.peek();
          if (IsDetachedBuffer(firstPendingPullInto.buffer))
            throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");
          ReadableByteStreamControllerInvalidateBYOBRequest(controller), firstPendingPullInto.buffer = TransferArrayBuffer(firstPendingPullInto.buffer), firstPendingPullInto.readerType === "none" && ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, firstPendingPullInto);
        }
        if (ReadableStreamHasDefaultReader(stream))
          if (ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller), ReadableStreamGetNumReadRequests(stream) === 0)
            ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);
          else {
            controller._pendingPullIntos.length > 0 && ReadableByteStreamControllerShiftPendingPullInto(controller);
            let transferredView = new Uint8Array(transferredBuffer, byteOffset, byteLength);
            ReadableStreamFulfillReadRequest(stream, transferredView, !1);
          }
        else ReadableStreamHasBYOBReader(stream) ? (ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength), ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller)) : ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);
        ReadableByteStreamControllerCallPullIfNeeded(controller);
      }
      function ReadableByteStreamControllerError(controller, e2) {
        let stream = controller._controlledReadableByteStream;
        stream._state === "readable" && (ReadableByteStreamControllerClearPendingPullIntos(controller), ResetQueue(controller), ReadableByteStreamControllerClearAlgorithms(controller), ReadableStreamError(stream, e2));
      }
      function ReadableByteStreamControllerFillReadRequestFromQueue(controller, readRequest) {
        let entry = controller._queue.shift();
        controller._queueTotalSize -= entry.byteLength, ReadableByteStreamControllerHandleQueueDrain(controller);
        let view = new Uint8Array(entry.buffer, entry.byteOffset, entry.byteLength);
        readRequest._chunkSteps(view);
      }
      function ReadableByteStreamControllerGetBYOBRequest(controller) {
        if (controller._byobRequest === null && controller._pendingPullIntos.length > 0) {
          let firstDescriptor = controller._pendingPullIntos.peek(), view = new Uint8Array(firstDescriptor.buffer, firstDescriptor.byteOffset + firstDescriptor.bytesFilled, firstDescriptor.byteLength - firstDescriptor.bytesFilled), byobRequest = Object.create(ReadableStreamBYOBRequest.prototype);
          SetUpReadableStreamBYOBRequest(byobRequest, controller, view), controller._byobRequest = byobRequest;
        }
        return controller._byobRequest;
      }
      function ReadableByteStreamControllerGetDesiredSize(controller) {
        let state = controller._controlledReadableByteStream._state;
        return state === "errored" ? null : state === "closed" ? 0 : controller._strategyHWM - controller._queueTotalSize;
      }
      function ReadableByteStreamControllerRespond(controller, bytesWritten) {
        let firstDescriptor = controller._pendingPullIntos.peek();
        if (controller._controlledReadableByteStream._state === "closed") {
          if (bytesWritten !== 0)
            throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream");
        } else {
          if (bytesWritten === 0)
            throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");
          if (firstDescriptor.bytesFilled + bytesWritten > firstDescriptor.byteLength)
            throw new RangeError("bytesWritten out of range");
        }
        firstDescriptor.buffer = TransferArrayBuffer(firstDescriptor.buffer), ReadableByteStreamControllerRespondInternal(controller, bytesWritten);
      }
      function ReadableByteStreamControllerRespondWithNewView(controller, view) {
        let firstDescriptor = controller._pendingPullIntos.peek();
        if (controller._controlledReadableByteStream._state === "closed") {
          if (view.byteLength !== 0)
            throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream");
        } else if (view.byteLength === 0)
          throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");
        if (firstDescriptor.byteOffset + firstDescriptor.bytesFilled !== view.byteOffset)
          throw new RangeError("The region specified by view does not match byobRequest");
        if (firstDescriptor.bufferByteLength !== view.buffer.byteLength)
          throw new RangeError("The buffer of view has different capacity than byobRequest");
        if (firstDescriptor.bytesFilled + view.byteLength > firstDescriptor.byteLength)
          throw new RangeError("The region specified by view is larger than byobRequest");
        let viewByteLength = view.byteLength;
        firstDescriptor.buffer = TransferArrayBuffer(view.buffer), ReadableByteStreamControllerRespondInternal(controller, viewByteLength);
      }
      function SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize) {
        controller._controlledReadableByteStream = stream, controller._pullAgain = !1, controller._pulling = !1, controller._byobRequest = null, controller._queue = controller._queueTotalSize = void 0, ResetQueue(controller), controller._closeRequested = !1, controller._started = !1, controller._strategyHWM = highWaterMark, controller._pullAlgorithm = pullAlgorithm, controller._cancelAlgorithm = cancelAlgorithm, controller._autoAllocateChunkSize = autoAllocateChunkSize, controller._pendingPullIntos = new SimpleQueue(), stream._readableStreamController = controller;
        let startResult = startAlgorithm();
        uponPromise(promiseResolvedWith(startResult), () => (controller._started = !0, ReadableByteStreamControllerCallPullIfNeeded(controller), null), (r2) => (ReadableByteStreamControllerError(controller, r2), null));
      }
      function SetUpReadableByteStreamControllerFromUnderlyingSource(stream, underlyingByteSource, highWaterMark) {
        let controller = Object.create(ReadableByteStreamController.prototype), startAlgorithm, pullAlgorithm, cancelAlgorithm;
        underlyingByteSource.start !== void 0 ? startAlgorithm = () => underlyingByteSource.start(controller) : startAlgorithm = () => {
        }, underlyingByteSource.pull !== void 0 ? pullAlgorithm = () => underlyingByteSource.pull(controller) : pullAlgorithm = () => promiseResolvedWith(void 0), underlyingByteSource.cancel !== void 0 ? cancelAlgorithm = (reason) => underlyingByteSource.cancel(reason) : cancelAlgorithm = () => promiseResolvedWith(void 0);
        let autoAllocateChunkSize = underlyingByteSource.autoAllocateChunkSize;
        if (autoAllocateChunkSize === 0)
          throw new TypeError("autoAllocateChunkSize must be greater than 0");
        SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize);
      }
      function SetUpReadableStreamBYOBRequest(request, controller, view) {
        request._associatedReadableByteStreamController = controller, request._view = view;
      }
      function byobRequestBrandCheckException(name) {
        return new TypeError(`ReadableStreamBYOBRequest.prototype.${name} can only be used on a ReadableStreamBYOBRequest`);
      }
      function byteStreamControllerBrandCheckException(name) {
        return new TypeError(`ReadableByteStreamController.prototype.${name} can only be used on a ReadableByteStreamController`);
      }
      function convertReaderOptions(options, context) {
        assertDictionary(options, context);
        let mode = options?.mode;
        return {
          mode: mode === void 0 ? void 0 : convertReadableStreamReaderMode(mode, `${context} has member 'mode' that`)
        };
      }
      function convertReadableStreamReaderMode(mode, context) {
        if (mode = `${mode}`, mode !== "byob")
          throw new TypeError(`${context} '${mode}' is not a valid enumeration value for ReadableStreamReaderMode`);
        return mode;
      }
      function convertByobReadOptions(options, context) {
        var _a2;
        assertDictionary(options, context);
        let min = (_a2 = options?.min) !== null && _a2 !== void 0 ? _a2 : 1;
        return {
          min: convertUnsignedLongLongWithEnforceRange(min, `${context} has member 'min' that`)
        };
      }
      function AcquireReadableStreamBYOBReader(stream) {
        return new ReadableStreamBYOBReader(stream);
      }
      function ReadableStreamAddReadIntoRequest(stream, readIntoRequest) {
        stream._reader._readIntoRequests.push(readIntoRequest);
      }
      function ReadableStreamFulfillReadIntoRequest(stream, chunk, done) {
        let readIntoRequest = stream._reader._readIntoRequests.shift();
        done ? readIntoRequest._closeSteps(chunk) : readIntoRequest._chunkSteps(chunk);
      }
      function ReadableStreamGetNumReadIntoRequests(stream) {
        return stream._reader._readIntoRequests.length;
      }
      function ReadableStreamHasBYOBReader(stream) {
        let reader = stream._reader;
        return !(reader === void 0 || !IsReadableStreamBYOBReader(reader));
      }
      class ReadableStreamBYOBReader {
        constructor(stream) {
          if (assertRequiredArgument(stream, 1, "ReadableStreamBYOBReader"), assertReadableStream(stream, "First parameter"), IsReadableStreamLocked(stream))
            throw new TypeError("This stream has already been locked for exclusive reading by another reader");
          if (!IsReadableByteStreamController(stream._readableStreamController))
            throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");
          ReadableStreamReaderGenericInitialize(this, stream), this._readIntoRequests = new SimpleQueue();
        }
        /**
         * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or
         * the reader's lock is released before the stream finishes closing.
         */
        get closed() {
          return IsReadableStreamBYOBReader(this) ? this._closedPromise : promiseRejectedWith(byobReaderBrandCheckException("closed"));
        }
        /**
         * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.
         */
        cancel(reason = void 0) {
          return IsReadableStreamBYOBReader(this) ? this._ownerReadableStream === void 0 ? promiseRejectedWith(readerLockException("cancel")) : ReadableStreamReaderGenericCancel(this, reason) : promiseRejectedWith(byobReaderBrandCheckException("cancel"));
        }
        read(view, rawOptions = {}) {
          if (!IsReadableStreamBYOBReader(this))
            return promiseRejectedWith(byobReaderBrandCheckException("read"));
          if (!ArrayBuffer.isView(view))
            return promiseRejectedWith(new TypeError("view must be an array buffer view"));
          if (view.byteLength === 0)
            return promiseRejectedWith(new TypeError("view must have non-zero byteLength"));
          if (view.buffer.byteLength === 0)
            return promiseRejectedWith(new TypeError("view's buffer must have non-zero byteLength"));
          if (IsDetachedBuffer(view.buffer))
            return promiseRejectedWith(new TypeError("view's buffer has been detached"));
          let options;
          try {
            options = convertByobReadOptions(rawOptions, "options");
          } catch (e2) {
            return promiseRejectedWith(e2);
          }
          let min = options.min;
          if (min === 0)
            return promiseRejectedWith(new TypeError("options.min must be greater than 0"));
          if (isDataView(view)) {
            if (min > view.byteLength)
              return promiseRejectedWith(new RangeError("options.min must be less than or equal to view's byteLength"));
          } else if (min > view.length)
            return promiseRejectedWith(new RangeError("options.min must be less than or equal to view's length"));
          if (this._ownerReadableStream === void 0)
            return promiseRejectedWith(readerLockException("read from"));
          let resolvePromise, rejectPromise, promise = newPromise((resolve, reject) => {
            resolvePromise = resolve, rejectPromise = reject;
          });
          return ReadableStreamBYOBReaderRead(this, view, min, {
            _chunkSteps: (chunk) => resolvePromise({ value: chunk, done: !1 }),
            _closeSteps: (chunk) => resolvePromise({ value: chunk, done: !0 }),
            _errorSteps: (e2) => rejectPromise(e2)
          }), promise;
        }
        /**
         * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.
         * If the associated stream is errored when the lock is released, the reader will appear errored in the same way
         * from now on; otherwise, the reader will appear closed.
         *
         * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by
         * the reader's {@link ReadableStreamBYOBReader.read | read()} method has not yet been settled. Attempting to
         * do so will throw a `TypeError` and leave the reader locked to the stream.
         */
        releaseLock() {
          if (!IsReadableStreamBYOBReader(this))
            throw byobReaderBrandCheckException("releaseLock");
          this._ownerReadableStream !== void 0 && ReadableStreamBYOBReaderRelease(this);
        }
      }
      Object.defineProperties(ReadableStreamBYOBReader.prototype, {
        cancel: { enumerable: !0 },
        read: { enumerable: !0 },
        releaseLock: { enumerable: !0 },
        closed: { enumerable: !0 }
      }), setFunctionName(ReadableStreamBYOBReader.prototype.cancel, "cancel"), setFunctionName(ReadableStreamBYOBReader.prototype.read, "read"), setFunctionName(ReadableStreamBYOBReader.prototype.releaseLock, "releaseLock"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(ReadableStreamBYOBReader.prototype, Symbol.toStringTag, {
        value: "ReadableStreamBYOBReader",
        configurable: !0
      });
      function IsReadableStreamBYOBReader(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_readIntoRequests") ? !1 : x2 instanceof ReadableStreamBYOBReader;
      }
      function ReadableStreamBYOBReaderRead(reader, view, min, readIntoRequest) {
        let stream = reader._ownerReadableStream;
        stream._disturbed = !0, stream._state === "errored" ? readIntoRequest._errorSteps(stream._storedError) : ReadableByteStreamControllerPullInto(stream._readableStreamController, view, min, readIntoRequest);
      }
      function ReadableStreamBYOBReaderRelease(reader) {
        ReadableStreamReaderGenericRelease(reader);
        let e2 = new TypeError("Reader was released");
        ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e2);
      }
      function ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e2) {
        let readIntoRequests = reader._readIntoRequests;
        reader._readIntoRequests = new SimpleQueue(), readIntoRequests.forEach((readIntoRequest) => {
          readIntoRequest._errorSteps(e2);
        });
      }
      function byobReaderBrandCheckException(name) {
        return new TypeError(`ReadableStreamBYOBReader.prototype.${name} can only be used on a ReadableStreamBYOBReader`);
      }
      function ExtractHighWaterMark(strategy, defaultHWM) {
        let { highWaterMark } = strategy;
        if (highWaterMark === void 0)
          return defaultHWM;
        if (NumberIsNaN(highWaterMark) || highWaterMark < 0)
          throw new RangeError("Invalid highWaterMark");
        return highWaterMark;
      }
      function ExtractSizeAlgorithm(strategy) {
        let { size } = strategy;
        return size || (() => 1);
      }
      function convertQueuingStrategy(init, context) {
        assertDictionary(init, context);
        let highWaterMark = init?.highWaterMark, size = init?.size;
        return {
          highWaterMark: highWaterMark === void 0 ? void 0 : convertUnrestrictedDouble(highWaterMark),
          size: size === void 0 ? void 0 : convertQueuingStrategySize(size, `${context} has member 'size' that`)
        };
      }
      function convertQueuingStrategySize(fn, context) {
        return assertFunction(fn, context), (chunk) => convertUnrestrictedDouble(fn(chunk));
      }
      function convertUnderlyingSink(original, context) {
        assertDictionary(original, context);
        let abort = original?.abort, close = original?.close, start = original?.start, type = original?.type, write = original?.write;
        return {
          abort: abort === void 0 ? void 0 : convertUnderlyingSinkAbortCallback(abort, original, `${context} has member 'abort' that`),
          close: close === void 0 ? void 0 : convertUnderlyingSinkCloseCallback(close, original, `${context} has member 'close' that`),
          start: start === void 0 ? void 0 : convertUnderlyingSinkStartCallback(start, original, `${context} has member 'start' that`),
          write: write === void 0 ? void 0 : convertUnderlyingSinkWriteCallback(write, original, `${context} has member 'write' that`),
          type
        };
      }
      function convertUnderlyingSinkAbortCallback(fn, original, context) {
        return assertFunction(fn, context), (reason) => promiseCall(fn, original, [reason]);
      }
      function convertUnderlyingSinkCloseCallback(fn, original, context) {
        return assertFunction(fn, context), () => promiseCall(fn, original, []);
      }
      function convertUnderlyingSinkStartCallback(fn, original, context) {
        return assertFunction(fn, context), (controller) => reflectCall(fn, original, [controller]);
      }
      function convertUnderlyingSinkWriteCallback(fn, original, context) {
        return assertFunction(fn, context), (chunk, controller) => promiseCall(fn, original, [chunk, controller]);
      }
      function assertWritableStream(x2, context) {
        if (!IsWritableStream(x2))
          throw new TypeError(`${context} is not a WritableStream.`);
      }
      function isAbortSignal(value) {
        if (typeof value != "object" || value === null)
          return !1;
        try {
          return typeof value.aborted == "boolean";
        } catch {
          return !1;
        }
      }
      let supportsAbortController = typeof AbortController == "function";
      function createAbortController() {
        if (supportsAbortController)
          return new AbortController();
      }
      class WritableStream {
        constructor(rawUnderlyingSink = {}, rawStrategy = {}) {
          rawUnderlyingSink === void 0 ? rawUnderlyingSink = null : assertObject(rawUnderlyingSink, "First parameter");
          let strategy = convertQueuingStrategy(rawStrategy, "Second parameter"), underlyingSink = convertUnderlyingSink(rawUnderlyingSink, "First parameter");
          if (InitializeWritableStream(this), underlyingSink.type !== void 0)
            throw new RangeError("Invalid type is specified");
          let sizeAlgorithm = ExtractSizeAlgorithm(strategy), highWaterMark = ExtractHighWaterMark(strategy, 1);
          SetUpWritableStreamDefaultControllerFromUnderlyingSink(this, underlyingSink, highWaterMark, sizeAlgorithm);
        }
        /**
         * Returns whether or not the writable stream is locked to a writer.
         */
        get locked() {
          if (!IsWritableStream(this))
            throw streamBrandCheckException$2("locked");
          return IsWritableStreamLocked(this);
        }
        /**
         * Aborts the stream, signaling that the producer can no longer successfully write to the stream and it is to be
         * immediately moved to an errored state, with any queued-up writes discarded. This will also execute any abort
         * mechanism of the underlying sink.
         *
         * The returned promise will fulfill if the stream shuts down successfully, or reject if the underlying sink signaled
         * that there was an error doing so. Additionally, it will reject with a `TypeError` (without attempting to cancel
         * the stream) if the stream is currently locked.
         */
        abort(reason = void 0) {
          return IsWritableStream(this) ? IsWritableStreamLocked(this) ? promiseRejectedWith(new TypeError("Cannot abort a stream that already has a writer")) : WritableStreamAbort(this, reason) : promiseRejectedWith(streamBrandCheckException$2("abort"));
        }
        /**
         * Closes the stream. The underlying sink will finish processing any previously-written chunks, before invoking its
         * close behavior. During this time any further attempts to write will fail (without erroring the stream).
         *
         * The method returns a promise that will fulfill if all remaining chunks are successfully written and the stream
         * successfully closes, or rejects if an error is encountered during this process. Additionally, it will reject with
         * a `TypeError` (without attempting to cancel the stream) if the stream is currently locked.
         */
        close() {
          return IsWritableStream(this) ? IsWritableStreamLocked(this) ? promiseRejectedWith(new TypeError("Cannot close a stream that already has a writer")) : WritableStreamCloseQueuedOrInFlight(this) ? promiseRejectedWith(new TypeError("Cannot close an already-closing stream")) : WritableStreamClose(this) : promiseRejectedWith(streamBrandCheckException$2("close"));
        }
        /**
         * Creates a {@link WritableStreamDefaultWriter | writer} and locks the stream to the new writer. While the stream
         * is locked, no other writer can be acquired until this one is released.
         *
         * This functionality is especially useful for creating abstractions that desire the ability to write to a stream
         * without interruption or interleaving. By getting a writer for the stream, you can ensure nobody else can write at
         * the same time, which would cause the resulting written data to be unpredictable and probably useless.
         */
        getWriter() {
          if (!IsWritableStream(this))
            throw streamBrandCheckException$2("getWriter");
          return AcquireWritableStreamDefaultWriter(this);
        }
      }
      Object.defineProperties(WritableStream.prototype, {
        abort: { enumerable: !0 },
        close: { enumerable: !0 },
        getWriter: { enumerable: !0 },
        locked: { enumerable: !0 }
      }), setFunctionName(WritableStream.prototype.abort, "abort"), setFunctionName(WritableStream.prototype.close, "close"), setFunctionName(WritableStream.prototype.getWriter, "getWriter"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(WritableStream.prototype, Symbol.toStringTag, {
        value: "WritableStream",
        configurable: !0
      });
      function AcquireWritableStreamDefaultWriter(stream) {
        return new WritableStreamDefaultWriter(stream);
      }
      function CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark = 1, sizeAlgorithm = () => 1) {
        let stream = Object.create(WritableStream.prototype);
        InitializeWritableStream(stream);
        let controller = Object.create(WritableStreamDefaultController.prototype);
        return SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm), stream;
      }
      function InitializeWritableStream(stream) {
        stream._state = "writable", stream._storedError = void 0, stream._writer = void 0, stream._writableStreamController = void 0, stream._writeRequests = new SimpleQueue(), stream._inFlightWriteRequest = void 0, stream._closeRequest = void 0, stream._inFlightCloseRequest = void 0, stream._pendingAbortRequest = void 0, stream._backpressure = !1;
      }
      function IsWritableStream(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_writableStreamController") ? !1 : x2 instanceof WritableStream;
      }
      function IsWritableStreamLocked(stream) {
        return stream._writer !== void 0;
      }
      function WritableStreamAbort(stream, reason) {
        var _a2;
        if (stream._state === "closed" || stream._state === "errored")
          return promiseResolvedWith(void 0);
        stream._writableStreamController._abortReason = reason, (_a2 = stream._writableStreamController._abortController) === null || _a2 === void 0 || _a2.abort(reason);
        let state = stream._state;
        if (state === "closed" || state === "errored")
          return promiseResolvedWith(void 0);
        if (stream._pendingAbortRequest !== void 0)
          return stream._pendingAbortRequest._promise;
        let wasAlreadyErroring = !1;
        state === "erroring" && (wasAlreadyErroring = !0, reason = void 0);
        let promise = newPromise((resolve, reject) => {
          stream._pendingAbortRequest = {
            _promise: void 0,
            _resolve: resolve,
            _reject: reject,
            _reason: reason,
            _wasAlreadyErroring: wasAlreadyErroring
          };
        });
        return stream._pendingAbortRequest._promise = promise, wasAlreadyErroring || WritableStreamStartErroring(stream, reason), promise;
      }
      function WritableStreamClose(stream) {
        let state = stream._state;
        if (state === "closed" || state === "errored")
          return promiseRejectedWith(new TypeError(`The stream (in ${state} state) is not in the writable state and cannot be closed`));
        let promise = newPromise((resolve, reject) => {
          let closeRequest = {
            _resolve: resolve,
            _reject: reject
          };
          stream._closeRequest = closeRequest;
        }), writer = stream._writer;
        return writer !== void 0 && stream._backpressure && state === "writable" && defaultWriterReadyPromiseResolve(writer), WritableStreamDefaultControllerClose(stream._writableStreamController), promise;
      }
      function WritableStreamAddWriteRequest(stream) {
        return newPromise((resolve, reject) => {
          let writeRequest = {
            _resolve: resolve,
            _reject: reject
          };
          stream._writeRequests.push(writeRequest);
        });
      }
      function WritableStreamDealWithRejection(stream, error) {
        if (stream._state === "writable") {
          WritableStreamStartErroring(stream, error);
          return;
        }
        WritableStreamFinishErroring(stream);
      }
      function WritableStreamStartErroring(stream, reason) {
        let controller = stream._writableStreamController;
        stream._state = "erroring", stream._storedError = reason;
        let writer = stream._writer;
        writer !== void 0 && WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, reason), !WritableStreamHasOperationMarkedInFlight(stream) && controller._started && WritableStreamFinishErroring(stream);
      }
      function WritableStreamFinishErroring(stream) {
        stream._state = "errored", stream._writableStreamController[ErrorSteps]();
        let storedError = stream._storedError;
        if (stream._writeRequests.forEach((writeRequest) => {
          writeRequest._reject(storedError);
        }), stream._writeRequests = new SimpleQueue(), stream._pendingAbortRequest === void 0) {
          WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);
          return;
        }
        let abortRequest = stream._pendingAbortRequest;
        if (stream._pendingAbortRequest = void 0, abortRequest._wasAlreadyErroring) {
          abortRequest._reject(storedError), WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);
          return;
        }
        let promise = stream._writableStreamController[AbortSteps](abortRequest._reason);
        uponPromise(promise, () => (abortRequest._resolve(), WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream), null), (reason) => (abortRequest._reject(reason), WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream), null));
      }
      function WritableStreamFinishInFlightWrite(stream) {
        stream._inFlightWriteRequest._resolve(void 0), stream._inFlightWriteRequest = void 0;
      }
      function WritableStreamFinishInFlightWriteWithError(stream, error) {
        stream._inFlightWriteRequest._reject(error), stream._inFlightWriteRequest = void 0, WritableStreamDealWithRejection(stream, error);
      }
      function WritableStreamFinishInFlightClose(stream) {
        stream._inFlightCloseRequest._resolve(void 0), stream._inFlightCloseRequest = void 0, stream._state === "erroring" && (stream._storedError = void 0, stream._pendingAbortRequest !== void 0 && (stream._pendingAbortRequest._resolve(), stream._pendingAbortRequest = void 0)), stream._state = "closed";
        let writer = stream._writer;
        writer !== void 0 && defaultWriterClosedPromiseResolve(writer);
      }
      function WritableStreamFinishInFlightCloseWithError(stream, error) {
        stream._inFlightCloseRequest._reject(error), stream._inFlightCloseRequest = void 0, stream._pendingAbortRequest !== void 0 && (stream._pendingAbortRequest._reject(error), stream._pendingAbortRequest = void 0), WritableStreamDealWithRejection(stream, error);
      }
      function WritableStreamCloseQueuedOrInFlight(stream) {
        return !(stream._closeRequest === void 0 && stream._inFlightCloseRequest === void 0);
      }
      function WritableStreamHasOperationMarkedInFlight(stream) {
        return !(stream._inFlightWriteRequest === void 0 && stream._inFlightCloseRequest === void 0);
      }
      function WritableStreamMarkCloseRequestInFlight(stream) {
        stream._inFlightCloseRequest = stream._closeRequest, stream._closeRequest = void 0;
      }
      function WritableStreamMarkFirstWriteRequestInFlight(stream) {
        stream._inFlightWriteRequest = stream._writeRequests.shift();
      }
      function WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream) {
        stream._closeRequest !== void 0 && (stream._closeRequest._reject(stream._storedError), stream._closeRequest = void 0);
        let writer = stream._writer;
        writer !== void 0 && defaultWriterClosedPromiseReject(writer, stream._storedError);
      }
      function WritableStreamUpdateBackpressure(stream, backpressure) {
        let writer = stream._writer;
        writer !== void 0 && backpressure !== stream._backpressure && (backpressure ? defaultWriterReadyPromiseReset(writer) : defaultWriterReadyPromiseResolve(writer)), stream._backpressure = backpressure;
      }
      class WritableStreamDefaultWriter {
        constructor(stream) {
          if (assertRequiredArgument(stream, 1, "WritableStreamDefaultWriter"), assertWritableStream(stream, "First parameter"), IsWritableStreamLocked(stream))
            throw new TypeError("This stream has already been locked for exclusive writing by another writer");
          this._ownerWritableStream = stream, stream._writer = this;
          let state = stream._state;
          if (state === "writable")
            !WritableStreamCloseQueuedOrInFlight(stream) && stream._backpressure ? defaultWriterReadyPromiseInitialize(this) : defaultWriterReadyPromiseInitializeAsResolved(this), defaultWriterClosedPromiseInitialize(this);
          else if (state === "erroring")
            defaultWriterReadyPromiseInitializeAsRejected(this, stream._storedError), defaultWriterClosedPromiseInitialize(this);
          else if (state === "closed")
            defaultWriterReadyPromiseInitializeAsResolved(this), defaultWriterClosedPromiseInitializeAsResolved(this);
          else {
            let storedError = stream._storedError;
            defaultWriterReadyPromiseInitializeAsRejected(this, storedError), defaultWriterClosedPromiseInitializeAsRejected(this, storedError);
          }
        }
        /**
         * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or
         * the writer’s lock is released before the stream finishes closing.
         */
        get closed() {
          return IsWritableStreamDefaultWriter(this) ? this._closedPromise : promiseRejectedWith(defaultWriterBrandCheckException("closed"));
        }
        /**
         * Returns the desired size to fill the stream’s internal queue. It can be negative, if the queue is over-full.
         * A producer can use this information to determine the right amount of data to write.
         *
         * It will be `null` if the stream cannot be successfully written to (due to either being errored, or having an abort
         * queued up). It will return zero if the stream is closed. And the getter will throw an exception if invoked when
         * the writer’s lock is released.
         */
        get desiredSize() {
          if (!IsWritableStreamDefaultWriter(this))
            throw defaultWriterBrandCheckException("desiredSize");
          if (this._ownerWritableStream === void 0)
            throw defaultWriterLockException("desiredSize");
          return WritableStreamDefaultWriterGetDesiredSize(this);
        }
        /**
         * Returns a promise that will be fulfilled when the desired size to fill the stream’s internal queue transitions
         * from non-positive to positive, signaling that it is no longer applying backpressure. Once the desired size dips
         * back to zero or below, the getter will return a new promise that stays pending until the next transition.
         *
         * If the stream becomes errored or aborted, or the writer’s lock is released, the returned promise will become
         * rejected.
         */
        get ready() {
          return IsWritableStreamDefaultWriter(this) ? this._readyPromise : promiseRejectedWith(defaultWriterBrandCheckException("ready"));
        }
        /**
         * If the reader is active, behaves the same as {@link WritableStream.abort | stream.abort(reason)}.
         */
        abort(reason = void 0) {
          return IsWritableStreamDefaultWriter(this) ? this._ownerWritableStream === void 0 ? promiseRejectedWith(defaultWriterLockException("abort")) : WritableStreamDefaultWriterAbort(this, reason) : promiseRejectedWith(defaultWriterBrandCheckException("abort"));
        }
        /**
         * If the reader is active, behaves the same as {@link WritableStream.close | stream.close()}.
         */
        close() {
          if (!IsWritableStreamDefaultWriter(this))
            return promiseRejectedWith(defaultWriterBrandCheckException("close"));
          let stream = this._ownerWritableStream;
          return stream === void 0 ? promiseRejectedWith(defaultWriterLockException("close")) : WritableStreamCloseQueuedOrInFlight(stream) ? promiseRejectedWith(new TypeError("Cannot close an already-closing stream")) : WritableStreamDefaultWriterClose(this);
        }
        /**
         * Releases the writer’s lock on the corresponding stream. After the lock is released, the writer is no longer active.
         * If the associated stream is errored when the lock is released, the writer will appear errored in the same way from
         * now on; otherwise, the writer will appear closed.
         *
         * Note that the lock can still be released even if some ongoing writes have not yet finished (i.e. even if the
         * promises returned from previous calls to {@link WritableStreamDefaultWriter.write | write()} have not yet settled).
         * It’s not necessary to hold the lock on the writer for the duration of the write; the lock instead simply prevents
         * other producers from writing in an interleaved manner.
         */
        releaseLock() {
          if (!IsWritableStreamDefaultWriter(this))
            throw defaultWriterBrandCheckException("releaseLock");
          this._ownerWritableStream !== void 0 && WritableStreamDefaultWriterRelease(this);
        }
        write(chunk = void 0) {
          return IsWritableStreamDefaultWriter(this) ? this._ownerWritableStream === void 0 ? promiseRejectedWith(defaultWriterLockException("write to")) : WritableStreamDefaultWriterWrite(this, chunk) : promiseRejectedWith(defaultWriterBrandCheckException("write"));
        }
      }
      Object.defineProperties(WritableStreamDefaultWriter.prototype, {
        abort: { enumerable: !0 },
        close: { enumerable: !0 },
        releaseLock: { enumerable: !0 },
        write: { enumerable: !0 },
        closed: { enumerable: !0 },
        desiredSize: { enumerable: !0 },
        ready: { enumerable: !0 }
      }), setFunctionName(WritableStreamDefaultWriter.prototype.abort, "abort"), setFunctionName(WritableStreamDefaultWriter.prototype.close, "close"), setFunctionName(WritableStreamDefaultWriter.prototype.releaseLock, "releaseLock"), setFunctionName(WritableStreamDefaultWriter.prototype.write, "write"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(WritableStreamDefaultWriter.prototype, Symbol.toStringTag, {
        value: "WritableStreamDefaultWriter",
        configurable: !0
      });
      function IsWritableStreamDefaultWriter(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_ownerWritableStream") ? !1 : x2 instanceof WritableStreamDefaultWriter;
      }
      function WritableStreamDefaultWriterAbort(writer, reason) {
        let stream = writer._ownerWritableStream;
        return WritableStreamAbort(stream, reason);
      }
      function WritableStreamDefaultWriterClose(writer) {
        let stream = writer._ownerWritableStream;
        return WritableStreamClose(stream);
      }
      function WritableStreamDefaultWriterCloseWithErrorPropagation(writer) {
        let stream = writer._ownerWritableStream, state = stream._state;
        return WritableStreamCloseQueuedOrInFlight(stream) || state === "closed" ? promiseResolvedWith(void 0) : state === "errored" ? promiseRejectedWith(stream._storedError) : WritableStreamDefaultWriterClose(writer);
      }
      function WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, error) {
        writer._closedPromiseState === "pending" ? defaultWriterClosedPromiseReject(writer, error) : defaultWriterClosedPromiseResetToRejected(writer, error);
      }
      function WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, error) {
        writer._readyPromiseState === "pending" ? defaultWriterReadyPromiseReject(writer, error) : defaultWriterReadyPromiseResetToRejected(writer, error);
      }
      function WritableStreamDefaultWriterGetDesiredSize(writer) {
        let stream = writer._ownerWritableStream, state = stream._state;
        return state === "errored" || state === "erroring" ? null : state === "closed" ? 0 : WritableStreamDefaultControllerGetDesiredSize(stream._writableStreamController);
      }
      function WritableStreamDefaultWriterRelease(writer) {
        let stream = writer._ownerWritableStream, releasedError = new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");
        WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, releasedError), WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, releasedError), stream._writer = void 0, writer._ownerWritableStream = void 0;
      }
      function WritableStreamDefaultWriterWrite(writer, chunk) {
        let stream = writer._ownerWritableStream, controller = stream._writableStreamController, chunkSize = WritableStreamDefaultControllerGetChunkSize(controller, chunk);
        if (stream !== writer._ownerWritableStream)
          return promiseRejectedWith(defaultWriterLockException("write to"));
        let state = stream._state;
        if (state === "errored")
          return promiseRejectedWith(stream._storedError);
        if (WritableStreamCloseQueuedOrInFlight(stream) || state === "closed")
          return promiseRejectedWith(new TypeError("The stream is closing or closed and cannot be written to"));
        if (state === "erroring")
          return promiseRejectedWith(stream._storedError);
        let promise = WritableStreamAddWriteRequest(stream);
        return WritableStreamDefaultControllerWrite(controller, chunk, chunkSize), promise;
      }
      let closeSentinel = {};
      class WritableStreamDefaultController {
        constructor() {
          throw new TypeError("Illegal constructor");
        }
        /**
         * The reason which was passed to `WritableStream.abort(reason)` when the stream was aborted.
         *
         * @deprecated
         *  This property has been removed from the specification, see https://github.com/whatwg/streams/pull/1177.
         *  Use {@link WritableStreamDefaultController.signal}'s `reason` instead.
         */
        get abortReason() {
          if (!IsWritableStreamDefaultController(this))
            throw defaultControllerBrandCheckException$2("abortReason");
          return this._abortReason;
        }
        /**
         * An `AbortSignal` that can be used to abort the pending write or close operation when the stream is aborted.
         */
        get signal() {
          if (!IsWritableStreamDefaultController(this))
            throw defaultControllerBrandCheckException$2("signal");
          if (this._abortController === void 0)
            throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");
          return this._abortController.signal;
        }
        /**
         * Closes the controlled writable stream, making all future interactions with it fail with the given error `e`.
         *
         * This method is rarely used, since usually it suffices to return a rejected promise from one of the underlying
         * sink's methods. However, it can be useful for suddenly shutting down a stream in response to an event outside the
         * normal lifecycle of interactions with the underlying sink.
         */
        error(e2 = void 0) {
          if (!IsWritableStreamDefaultController(this))
            throw defaultControllerBrandCheckException$2("error");
          this._controlledWritableStream._state === "writable" && WritableStreamDefaultControllerError(this, e2);
        }
        /** @internal */
        [AbortSteps](reason) {
          let result = this._abortAlgorithm(reason);
          return WritableStreamDefaultControllerClearAlgorithms(this), result;
        }
        /** @internal */
        [ErrorSteps]() {
          ResetQueue(this);
        }
      }
      Object.defineProperties(WritableStreamDefaultController.prototype, {
        abortReason: { enumerable: !0 },
        signal: { enumerable: !0 },
        error: { enumerable: !0 }
      }), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(WritableStreamDefaultController.prototype, Symbol.toStringTag, {
        value: "WritableStreamDefaultController",
        configurable: !0
      });
      function IsWritableStreamDefaultController(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_controlledWritableStream") ? !1 : x2 instanceof WritableStreamDefaultController;
      }
      function SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm) {
        controller._controlledWritableStream = stream, stream._writableStreamController = controller, controller._queue = void 0, controller._queueTotalSize = void 0, ResetQueue(controller), controller._abortReason = void 0, controller._abortController = createAbortController(), controller._started = !1, controller._strategySizeAlgorithm = sizeAlgorithm, controller._strategyHWM = highWaterMark, controller._writeAlgorithm = writeAlgorithm, controller._closeAlgorithm = closeAlgorithm, controller._abortAlgorithm = abortAlgorithm;
        let backpressure = WritableStreamDefaultControllerGetBackpressure(controller);
        WritableStreamUpdateBackpressure(stream, backpressure);
        let startResult = startAlgorithm(), startPromise = promiseResolvedWith(startResult);
        uponPromise(startPromise, () => (controller._started = !0, WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller), null), (r2) => (controller._started = !0, WritableStreamDealWithRejection(stream, r2), null));
      }
      function SetUpWritableStreamDefaultControllerFromUnderlyingSink(stream, underlyingSink, highWaterMark, sizeAlgorithm) {
        let controller = Object.create(WritableStreamDefaultController.prototype), startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm;
        underlyingSink.start !== void 0 ? startAlgorithm = () => underlyingSink.start(controller) : startAlgorithm = () => {
        }, underlyingSink.write !== void 0 ? writeAlgorithm = (chunk) => underlyingSink.write(chunk, controller) : writeAlgorithm = () => promiseResolvedWith(void 0), underlyingSink.close !== void 0 ? closeAlgorithm = () => underlyingSink.close() : closeAlgorithm = () => promiseResolvedWith(void 0), underlyingSink.abort !== void 0 ? abortAlgorithm = (reason) => underlyingSink.abort(reason) : abortAlgorithm = () => promiseResolvedWith(void 0), SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm);
      }
      function WritableStreamDefaultControllerClearAlgorithms(controller) {
        controller._writeAlgorithm = void 0, controller._closeAlgorithm = void 0, controller._abortAlgorithm = void 0, controller._strategySizeAlgorithm = void 0;
      }
      function WritableStreamDefaultControllerClose(controller) {
        EnqueueValueWithSize(controller, closeSentinel, 0), WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);
      }
      function WritableStreamDefaultControllerGetChunkSize(controller, chunk) {
        try {
          return controller._strategySizeAlgorithm(chunk);
        } catch (chunkSizeE) {
          return WritableStreamDefaultControllerErrorIfNeeded(controller, chunkSizeE), 1;
        }
      }
      function WritableStreamDefaultControllerGetDesiredSize(controller) {
        return controller._strategyHWM - controller._queueTotalSize;
      }
      function WritableStreamDefaultControllerWrite(controller, chunk, chunkSize) {
        try {
          EnqueueValueWithSize(controller, chunk, chunkSize);
        } catch (enqueueE) {
          WritableStreamDefaultControllerErrorIfNeeded(controller, enqueueE);
          return;
        }
        let stream = controller._controlledWritableStream;
        if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._state === "writable") {
          let backpressure = WritableStreamDefaultControllerGetBackpressure(controller);
          WritableStreamUpdateBackpressure(stream, backpressure);
        }
        WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);
      }
      function WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller) {
        let stream = controller._controlledWritableStream;
        if (!controller._started || stream._inFlightWriteRequest !== void 0)
          return;
        if (stream._state === "erroring") {
          WritableStreamFinishErroring(stream);
          return;
        }
        if (controller._queue.length === 0)
          return;
        let value = PeekQueueValue(controller);
        value === closeSentinel ? WritableStreamDefaultControllerProcessClose(controller) : WritableStreamDefaultControllerProcessWrite(controller, value);
      }
      function WritableStreamDefaultControllerErrorIfNeeded(controller, error) {
        controller._controlledWritableStream._state === "writable" && WritableStreamDefaultControllerError(controller, error);
      }
      function WritableStreamDefaultControllerProcessClose(controller) {
        let stream = controller._controlledWritableStream;
        WritableStreamMarkCloseRequestInFlight(stream), DequeueValue(controller);
        let sinkClosePromise = controller._closeAlgorithm();
        WritableStreamDefaultControllerClearAlgorithms(controller), uponPromise(sinkClosePromise, () => (WritableStreamFinishInFlightClose(stream), null), (reason) => (WritableStreamFinishInFlightCloseWithError(stream, reason), null));
      }
      function WritableStreamDefaultControllerProcessWrite(controller, chunk) {
        let stream = controller._controlledWritableStream;
        WritableStreamMarkFirstWriteRequestInFlight(stream);
        let sinkWritePromise = controller._writeAlgorithm(chunk);
        uponPromise(sinkWritePromise, () => {
          WritableStreamFinishInFlightWrite(stream);
          let state = stream._state;
          if (DequeueValue(controller), !WritableStreamCloseQueuedOrInFlight(stream) && state === "writable") {
            let backpressure = WritableStreamDefaultControllerGetBackpressure(controller);
            WritableStreamUpdateBackpressure(stream, backpressure);
          }
          return WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller), null;
        }, (reason) => (stream._state === "writable" && WritableStreamDefaultControllerClearAlgorithms(controller), WritableStreamFinishInFlightWriteWithError(stream, reason), null));
      }
      function WritableStreamDefaultControllerGetBackpressure(controller) {
        return WritableStreamDefaultControllerGetDesiredSize(controller) <= 0;
      }
      function WritableStreamDefaultControllerError(controller, error) {
        let stream = controller._controlledWritableStream;
        WritableStreamDefaultControllerClearAlgorithms(controller), WritableStreamStartErroring(stream, error);
      }
      function streamBrandCheckException$2(name) {
        return new TypeError(`WritableStream.prototype.${name} can only be used on a WritableStream`);
      }
      function defaultControllerBrandCheckException$2(name) {
        return new TypeError(`WritableStreamDefaultController.prototype.${name} can only be used on a WritableStreamDefaultController`);
      }
      function defaultWriterBrandCheckException(name) {
        return new TypeError(`WritableStreamDefaultWriter.prototype.${name} can only be used on a WritableStreamDefaultWriter`);
      }
      function defaultWriterLockException(name) {
        return new TypeError("Cannot " + name + " a stream using a released writer");
      }
      function defaultWriterClosedPromiseInitialize(writer) {
        writer._closedPromise = newPromise((resolve, reject) => {
          writer._closedPromise_resolve = resolve, writer._closedPromise_reject = reject, writer._closedPromiseState = "pending";
        });
      }
      function defaultWriterClosedPromiseInitializeAsRejected(writer, reason) {
        defaultWriterClosedPromiseInitialize(writer), defaultWriterClosedPromiseReject(writer, reason);
      }
      function defaultWriterClosedPromiseInitializeAsResolved(writer) {
        defaultWriterClosedPromiseInitialize(writer), defaultWriterClosedPromiseResolve(writer);
      }
      function defaultWriterClosedPromiseReject(writer, reason) {
        writer._closedPromise_reject !== void 0 && (setPromiseIsHandledToTrue(writer._closedPromise), writer._closedPromise_reject(reason), writer._closedPromise_resolve = void 0, writer._closedPromise_reject = void 0, writer._closedPromiseState = "rejected");
      }
      function defaultWriterClosedPromiseResetToRejected(writer, reason) {
        defaultWriterClosedPromiseInitializeAsRejected(writer, reason);
      }
      function defaultWriterClosedPromiseResolve(writer) {
        writer._closedPromise_resolve !== void 0 && (writer._closedPromise_resolve(void 0), writer._closedPromise_resolve = void 0, writer._closedPromise_reject = void 0, writer._closedPromiseState = "resolved");
      }
      function defaultWriterReadyPromiseInitialize(writer) {
        writer._readyPromise = newPromise((resolve, reject) => {
          writer._readyPromise_resolve = resolve, writer._readyPromise_reject = reject;
        }), writer._readyPromiseState = "pending";
      }
      function defaultWriterReadyPromiseInitializeAsRejected(writer, reason) {
        defaultWriterReadyPromiseInitialize(writer), defaultWriterReadyPromiseReject(writer, reason);
      }
      function defaultWriterReadyPromiseInitializeAsResolved(writer) {
        defaultWriterReadyPromiseInitialize(writer), defaultWriterReadyPromiseResolve(writer);
      }
      function defaultWriterReadyPromiseReject(writer, reason) {
        writer._readyPromise_reject !== void 0 && (setPromiseIsHandledToTrue(writer._readyPromise), writer._readyPromise_reject(reason), writer._readyPromise_resolve = void 0, writer._readyPromise_reject = void 0, writer._readyPromiseState = "rejected");
      }
      function defaultWriterReadyPromiseReset(writer) {
        defaultWriterReadyPromiseInitialize(writer);
      }
      function defaultWriterReadyPromiseResetToRejected(writer, reason) {
        defaultWriterReadyPromiseInitializeAsRejected(writer, reason);
      }
      function defaultWriterReadyPromiseResolve(writer) {
        writer._readyPromise_resolve !== void 0 && (writer._readyPromise_resolve(void 0), writer._readyPromise_resolve = void 0, writer._readyPromise_reject = void 0, writer._readyPromiseState = "fulfilled");
      }
      function getGlobals() {
        if (typeof globalThis < "u")
          return globalThis;
        if (typeof self < "u")
          return self;
        if (typeof global < "u")
          return global;
      }
      let globals = getGlobals();
      function isDOMExceptionConstructor(ctor) {
        if (!(typeof ctor == "function" || typeof ctor == "object") || ctor.name !== "DOMException")
          return !1;
        try {
          return new ctor(), !0;
        } catch {
          return !1;
        }
      }
      function getFromGlobal() {
        let ctor = globals?.DOMException;
        return isDOMExceptionConstructor(ctor) ? ctor : void 0;
      }
      function createPolyfill() {
        let ctor = function(message, name) {
          this.message = message || "", this.name = name || "Error", Error.captureStackTrace && Error.captureStackTrace(this, this.constructor);
        };
        return setFunctionName(ctor, "DOMException"), ctor.prototype = Object.create(Error.prototype), Object.defineProperty(ctor.prototype, "constructor", { value: ctor, writable: !0, configurable: !0 }), ctor;
      }
      let DOMException2 = getFromGlobal() || createPolyfill();
      function ReadableStreamPipeTo(source, dest, preventClose, preventAbort, preventCancel, signal) {
        let reader = AcquireReadableStreamDefaultReader(source), writer = AcquireWritableStreamDefaultWriter(dest);
        source._disturbed = !0;
        let shuttingDown = !1, currentWrite = promiseResolvedWith(void 0);
        return newPromise((resolve, reject) => {
          let abortAlgorithm;
          if (signal !== void 0) {
            if (abortAlgorithm = () => {
              let error = signal.reason !== void 0 ? signal.reason : new DOMException2("Aborted", "AbortError"), actions = [];
              preventAbort || actions.push(() => dest._state === "writable" ? WritableStreamAbort(dest, error) : promiseResolvedWith(void 0)), preventCancel || actions.push(() => source._state === "readable" ? ReadableStreamCancel(source, error) : promiseResolvedWith(void 0)), shutdownWithAction(() => Promise.all(actions.map((action) => action())), !0, error);
            }, signal.aborted) {
              abortAlgorithm();
              return;
            }
            signal.addEventListener("abort", abortAlgorithm);
          }
          function pipeLoop() {
            return newPromise((resolveLoop, rejectLoop) => {
              function next(done) {
                done ? resolveLoop() : PerformPromiseThen(pipeStep(), next, rejectLoop);
              }
              next(!1);
            });
          }
          function pipeStep() {
            return shuttingDown ? promiseResolvedWith(!0) : PerformPromiseThen(writer._readyPromise, () => newPromise((resolveRead, rejectRead) => {
              ReadableStreamDefaultReaderRead(reader, {
                _chunkSteps: (chunk) => {
                  currentWrite = PerformPromiseThen(WritableStreamDefaultWriterWrite(writer, chunk), void 0, noop), resolveRead(!1);
                },
                _closeSteps: () => resolveRead(!0),
                _errorSteps: rejectRead
              });
            }));
          }
          if (isOrBecomesErrored(source, reader._closedPromise, (storedError) => (preventAbort ? shutdown(!0, storedError) : shutdownWithAction(() => WritableStreamAbort(dest, storedError), !0, storedError), null)), isOrBecomesErrored(dest, writer._closedPromise, (storedError) => (preventCancel ? shutdown(!0, storedError) : shutdownWithAction(() => ReadableStreamCancel(source, storedError), !0, storedError), null)), isOrBecomesClosed(source, reader._closedPromise, () => (preventClose ? shutdown() : shutdownWithAction(() => WritableStreamDefaultWriterCloseWithErrorPropagation(writer)), null)), WritableStreamCloseQueuedOrInFlight(dest) || dest._state === "closed") {
            let destClosed = new TypeError("the destination writable stream closed before all data could be piped to it");
            preventCancel ? shutdown(!0, destClosed) : shutdownWithAction(() => ReadableStreamCancel(source, destClosed), !0, destClosed);
          }
          setPromiseIsHandledToTrue(pipeLoop());
          function waitForWritesToFinish() {
            let oldCurrentWrite = currentWrite;
            return PerformPromiseThen(currentWrite, () => oldCurrentWrite !== currentWrite ? waitForWritesToFinish() : void 0);
          }
          function isOrBecomesErrored(stream, promise, action) {
            stream._state === "errored" ? action(stream._storedError) : uponRejection(promise, action);
          }
          function isOrBecomesClosed(stream, promise, action) {
            stream._state === "closed" ? action() : uponFulfillment(promise, action);
          }
          function shutdownWithAction(action, originalIsError, originalError) {
            if (shuttingDown)
              return;
            shuttingDown = !0, dest._state === "writable" && !WritableStreamCloseQueuedOrInFlight(dest) ? uponFulfillment(waitForWritesToFinish(), doTheRest) : doTheRest();
            function doTheRest() {
              return uponPromise(action(), () => finalize(originalIsError, originalError), (newError) => finalize(!0, newError)), null;
            }
          }
          function shutdown(isError, error) {
            shuttingDown || (shuttingDown = !0, dest._state === "writable" && !WritableStreamCloseQueuedOrInFlight(dest) ? uponFulfillment(waitForWritesToFinish(), () => finalize(isError, error)) : finalize(isError, error));
          }
          function finalize(isError, error) {
            return WritableStreamDefaultWriterRelease(writer), ReadableStreamReaderGenericRelease(reader), signal !== void 0 && signal.removeEventListener("abort", abortAlgorithm), isError ? reject(error) : resolve(void 0), null;
          }
        });
      }
      class ReadableStreamDefaultController {
        constructor() {
          throw new TypeError("Illegal constructor");
        }
        /**
         * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is
         * over-full. An underlying source ought to use this information to determine when and how to apply backpressure.
         */
        get desiredSize() {
          if (!IsReadableStreamDefaultController(this))
            throw defaultControllerBrandCheckException$1("desiredSize");
          return ReadableStreamDefaultControllerGetDesiredSize(this);
        }
        /**
         * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from
         * the stream, but once those are read, the stream will become closed.
         */
        close() {
          if (!IsReadableStreamDefaultController(this))
            throw defaultControllerBrandCheckException$1("close");
          if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this))
            throw new TypeError("The stream is not in a state that permits close");
          ReadableStreamDefaultControllerClose(this);
        }
        enqueue(chunk = void 0) {
          if (!IsReadableStreamDefaultController(this))
            throw defaultControllerBrandCheckException$1("enqueue");
          if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this))
            throw new TypeError("The stream is not in a state that permits enqueue");
          return ReadableStreamDefaultControllerEnqueue(this, chunk);
        }
        /**
         * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.
         */
        error(e2 = void 0) {
          if (!IsReadableStreamDefaultController(this))
            throw defaultControllerBrandCheckException$1("error");
          ReadableStreamDefaultControllerError(this, e2);
        }
        /** @internal */
        [CancelSteps](reason) {
          ResetQueue(this);
          let result = this._cancelAlgorithm(reason);
          return ReadableStreamDefaultControllerClearAlgorithms(this), result;
        }
        /** @internal */
        [PullSteps](readRequest) {
          let stream = this._controlledReadableStream;
          if (this._queue.length > 0) {
            let chunk = DequeueValue(this);
            this._closeRequested && this._queue.length === 0 ? (ReadableStreamDefaultControllerClearAlgorithms(this), ReadableStreamClose(stream)) : ReadableStreamDefaultControllerCallPullIfNeeded(this), readRequest._chunkSteps(chunk);
          } else
            ReadableStreamAddReadRequest(stream, readRequest), ReadableStreamDefaultControllerCallPullIfNeeded(this);
        }
        /** @internal */
        [ReleaseSteps]() {
        }
      }
      Object.defineProperties(ReadableStreamDefaultController.prototype, {
        close: { enumerable: !0 },
        enqueue: { enumerable: !0 },
        error: { enumerable: !0 },
        desiredSize: { enumerable: !0 }
      }), setFunctionName(ReadableStreamDefaultController.prototype.close, "close"), setFunctionName(ReadableStreamDefaultController.prototype.enqueue, "enqueue"), setFunctionName(ReadableStreamDefaultController.prototype.error, "error"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(ReadableStreamDefaultController.prototype, Symbol.toStringTag, {
        value: "ReadableStreamDefaultController",
        configurable: !0
      });
      function IsReadableStreamDefaultController(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_controlledReadableStream") ? !1 : x2 instanceof ReadableStreamDefaultController;
      }
      function ReadableStreamDefaultControllerCallPullIfNeeded(controller) {
        if (!ReadableStreamDefaultControllerShouldCallPull(controller))
          return;
        if (controller._pulling) {
          controller._pullAgain = !0;
          return;
        }
        controller._pulling = !0;
        let pullPromise = controller._pullAlgorithm();
        uponPromise(pullPromise, () => (controller._pulling = !1, controller._pullAgain && (controller._pullAgain = !1, ReadableStreamDefaultControllerCallPullIfNeeded(controller)), null), (e2) => (ReadableStreamDefaultControllerError(controller, e2), null));
      }
      function ReadableStreamDefaultControllerShouldCallPull(controller) {
        let stream = controller._controlledReadableStream;
        return !ReadableStreamDefaultControllerCanCloseOrEnqueue(controller) || !controller._started ? !1 : !!(IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0 || ReadableStreamDefaultControllerGetDesiredSize(controller) > 0);
      }
      function ReadableStreamDefaultControllerClearAlgorithms(controller) {
        controller._pullAlgorithm = void 0, controller._cancelAlgorithm = void 0, controller._strategySizeAlgorithm = void 0;
      }
      function ReadableStreamDefaultControllerClose(controller) {
        if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller))
          return;
        let stream = controller._controlledReadableStream;
        controller._closeRequested = !0, controller._queue.length === 0 && (ReadableStreamDefaultControllerClearAlgorithms(controller), ReadableStreamClose(stream));
      }
      function ReadableStreamDefaultControllerEnqueue(controller, chunk) {
        if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller))
          return;
        let stream = controller._controlledReadableStream;
        if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0)
          ReadableStreamFulfillReadRequest(stream, chunk, !1);
        else {
          let chunkSize;
          try {
            chunkSize = controller._strategySizeAlgorithm(chunk);
          } catch (chunkSizeE) {
            throw ReadableStreamDefaultControllerError(controller, chunkSizeE), chunkSizeE;
          }
          try {
            EnqueueValueWithSize(controller, chunk, chunkSize);
          } catch (enqueueE) {
            throw ReadableStreamDefaultControllerError(controller, enqueueE), enqueueE;
          }
        }
        ReadableStreamDefaultControllerCallPullIfNeeded(controller);
      }
      function ReadableStreamDefaultControllerError(controller, e2) {
        let stream = controller._controlledReadableStream;
        stream._state === "readable" && (ResetQueue(controller), ReadableStreamDefaultControllerClearAlgorithms(controller), ReadableStreamError(stream, e2));
      }
      function ReadableStreamDefaultControllerGetDesiredSize(controller) {
        let state = controller._controlledReadableStream._state;
        return state === "errored" ? null : state === "closed" ? 0 : controller._strategyHWM - controller._queueTotalSize;
      }
      function ReadableStreamDefaultControllerHasBackpressure(controller) {
        return !ReadableStreamDefaultControllerShouldCallPull(controller);
      }
      function ReadableStreamDefaultControllerCanCloseOrEnqueue(controller) {
        let state = controller._controlledReadableStream._state;
        return !controller._closeRequested && state === "readable";
      }
      function SetUpReadableStreamDefaultController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm) {
        controller._controlledReadableStream = stream, controller._queue = void 0, controller._queueTotalSize = void 0, ResetQueue(controller), controller._started = !1, controller._closeRequested = !1, controller._pullAgain = !1, controller._pulling = !1, controller._strategySizeAlgorithm = sizeAlgorithm, controller._strategyHWM = highWaterMark, controller._pullAlgorithm = pullAlgorithm, controller._cancelAlgorithm = cancelAlgorithm, stream._readableStreamController = controller;
        let startResult = startAlgorithm();
        uponPromise(promiseResolvedWith(startResult), () => (controller._started = !0, ReadableStreamDefaultControllerCallPullIfNeeded(controller), null), (r2) => (ReadableStreamDefaultControllerError(controller, r2), null));
      }
      function SetUpReadableStreamDefaultControllerFromUnderlyingSource(stream, underlyingSource, highWaterMark, sizeAlgorithm) {
        let controller = Object.create(ReadableStreamDefaultController.prototype), startAlgorithm, pullAlgorithm, cancelAlgorithm;
        underlyingSource.start !== void 0 ? startAlgorithm = () => underlyingSource.start(controller) : startAlgorithm = () => {
        }, underlyingSource.pull !== void 0 ? pullAlgorithm = () => underlyingSource.pull(controller) : pullAlgorithm = () => promiseResolvedWith(void 0), underlyingSource.cancel !== void 0 ? cancelAlgorithm = (reason) => underlyingSource.cancel(reason) : cancelAlgorithm = () => promiseResolvedWith(void 0), SetUpReadableStreamDefaultController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm);
      }
      function defaultControllerBrandCheckException$1(name) {
        return new TypeError(`ReadableStreamDefaultController.prototype.${name} can only be used on a ReadableStreamDefaultController`);
      }
      function ReadableStreamTee(stream, cloneForBranch2) {
        return IsReadableByteStreamController(stream._readableStreamController) ? ReadableByteStreamTee(stream) : ReadableStreamDefaultTee(stream);
      }
      function ReadableStreamDefaultTee(stream, cloneForBranch2) {
        let reader = AcquireReadableStreamDefaultReader(stream), reading = !1, readAgain = !1, canceled1 = !1, canceled2 = !1, reason1, reason2, branch1, branch2, resolveCancelPromise, cancelPromise = newPromise((resolve) => {
          resolveCancelPromise = resolve;
        });
        function pullAlgorithm() {
          return reading ? (readAgain = !0, promiseResolvedWith(void 0)) : (reading = !0, ReadableStreamDefaultReaderRead(reader, {
            _chunkSteps: (chunk) => {
              _queueMicrotask(() => {
                readAgain = !1;
                let chunk1 = chunk, chunk2 = chunk;
                canceled1 || ReadableStreamDefaultControllerEnqueue(branch1._readableStreamController, chunk1), canceled2 || ReadableStreamDefaultControllerEnqueue(branch2._readableStreamController, chunk2), reading = !1, readAgain && pullAlgorithm();
              });
            },
            _closeSteps: () => {
              reading = !1, canceled1 || ReadableStreamDefaultControllerClose(branch1._readableStreamController), canceled2 || ReadableStreamDefaultControllerClose(branch2._readableStreamController), (!canceled1 || !canceled2) && resolveCancelPromise(void 0);
            },
            _errorSteps: () => {
              reading = !1;
            }
          }), promiseResolvedWith(void 0));
        }
        function cancel1Algorithm(reason) {
          if (canceled1 = !0, reason1 = reason, canceled2) {
            let compositeReason = CreateArrayFromList([reason1, reason2]), cancelResult = ReadableStreamCancel(stream, compositeReason);
            resolveCancelPromise(cancelResult);
          }
          return cancelPromise;
        }
        function cancel2Algorithm(reason) {
          if (canceled2 = !0, reason2 = reason, canceled1) {
            let compositeReason = CreateArrayFromList([reason1, reason2]), cancelResult = ReadableStreamCancel(stream, compositeReason);
            resolveCancelPromise(cancelResult);
          }
          return cancelPromise;
        }
        function startAlgorithm() {
        }
        return branch1 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel1Algorithm), branch2 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel2Algorithm), uponRejection(reader._closedPromise, (r2) => (ReadableStreamDefaultControllerError(branch1._readableStreamController, r2), ReadableStreamDefaultControllerError(branch2._readableStreamController, r2), (!canceled1 || !canceled2) && resolveCancelPromise(void 0), null)), [branch1, branch2];
      }
      function ReadableByteStreamTee(stream) {
        let reader = AcquireReadableStreamDefaultReader(stream), reading = !1, readAgainForBranch1 = !1, readAgainForBranch2 = !1, canceled1 = !1, canceled2 = !1, reason1, reason2, branch1, branch2, resolveCancelPromise, cancelPromise = newPromise((resolve) => {
          resolveCancelPromise = resolve;
        });
        function forwardReaderError(thisReader) {
          uponRejection(thisReader._closedPromise, (r2) => (thisReader !== reader || (ReadableByteStreamControllerError(branch1._readableStreamController, r2), ReadableByteStreamControllerError(branch2._readableStreamController, r2), (!canceled1 || !canceled2) && resolveCancelPromise(void 0)), null));
        }
        function pullWithDefaultReader() {
          IsReadableStreamBYOBReader(reader) && (ReadableStreamReaderGenericRelease(reader), reader = AcquireReadableStreamDefaultReader(stream), forwardReaderError(reader)), ReadableStreamDefaultReaderRead(reader, {
            _chunkSteps: (chunk) => {
              _queueMicrotask(() => {
                readAgainForBranch1 = !1, readAgainForBranch2 = !1;
                let chunk1 = chunk, chunk2 = chunk;
                if (!canceled1 && !canceled2)
                  try {
                    chunk2 = CloneAsUint8Array(chunk);
                  } catch (cloneE) {
                    ReadableByteStreamControllerError(branch1._readableStreamController, cloneE), ReadableByteStreamControllerError(branch2._readableStreamController, cloneE), resolveCancelPromise(ReadableStreamCancel(stream, cloneE));
                    return;
                  }
                canceled1 || ReadableByteStreamControllerEnqueue(branch1._readableStreamController, chunk1), canceled2 || ReadableByteStreamControllerEnqueue(branch2._readableStreamController, chunk2), reading = !1, readAgainForBranch1 ? pull1Algorithm() : readAgainForBranch2 && pull2Algorithm();
              });
            },
            _closeSteps: () => {
              reading = !1, canceled1 || ReadableByteStreamControllerClose(branch1._readableStreamController), canceled2 || ReadableByteStreamControllerClose(branch2._readableStreamController), branch1._readableStreamController._pendingPullIntos.length > 0 && ReadableByteStreamControllerRespond(branch1._readableStreamController, 0), branch2._readableStreamController._pendingPullIntos.length > 0 && ReadableByteStreamControllerRespond(branch2._readableStreamController, 0), (!canceled1 || !canceled2) && resolveCancelPromise(void 0);
            },
            _errorSteps: () => {
              reading = !1;
            }
          });
        }
        function pullWithBYOBReader(view, forBranch2) {
          IsReadableStreamDefaultReader(reader) && (ReadableStreamReaderGenericRelease(reader), reader = AcquireReadableStreamBYOBReader(stream), forwardReaderError(reader));
          let byobBranch = forBranch2 ? branch2 : branch1, otherBranch = forBranch2 ? branch1 : branch2;
          ReadableStreamBYOBReaderRead(reader, view, 1, {
            _chunkSteps: (chunk) => {
              _queueMicrotask(() => {
                readAgainForBranch1 = !1, readAgainForBranch2 = !1;
                let byobCanceled = forBranch2 ? canceled2 : canceled1;
                if (forBranch2 ? canceled1 : canceled2)
                  byobCanceled || ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);
                else {
                  let clonedChunk;
                  try {
                    clonedChunk = CloneAsUint8Array(chunk);
                  } catch (cloneE) {
                    ReadableByteStreamControllerError(byobBranch._readableStreamController, cloneE), ReadableByteStreamControllerError(otherBranch._readableStreamController, cloneE), resolveCancelPromise(ReadableStreamCancel(stream, cloneE));
                    return;
                  }
                  byobCanceled || ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk), ReadableByteStreamControllerEnqueue(otherBranch._readableStreamController, clonedChunk);
                }
                reading = !1, readAgainForBranch1 ? pull1Algorithm() : readAgainForBranch2 && pull2Algorithm();
              });
            },
            _closeSteps: (chunk) => {
              reading = !1;
              let byobCanceled = forBranch2 ? canceled2 : canceled1, otherCanceled = forBranch2 ? canceled1 : canceled2;
              byobCanceled || ReadableByteStreamControllerClose(byobBranch._readableStreamController), otherCanceled || ReadableByteStreamControllerClose(otherBranch._readableStreamController), chunk !== void 0 && (byobCanceled || ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk), !otherCanceled && otherBranch._readableStreamController._pendingPullIntos.length > 0 && ReadableByteStreamControllerRespond(otherBranch._readableStreamController, 0)), (!byobCanceled || !otherCanceled) && resolveCancelPromise(void 0);
            },
            _errorSteps: () => {
              reading = !1;
            }
          });
        }
        function pull1Algorithm() {
          if (reading)
            return readAgainForBranch1 = !0, promiseResolvedWith(void 0);
          reading = !0;
          let byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch1._readableStreamController);
          return byobRequest === null ? pullWithDefaultReader() : pullWithBYOBReader(byobRequest._view, !1), promiseResolvedWith(void 0);
        }
        function pull2Algorithm() {
          if (reading)
            return readAgainForBranch2 = !0, promiseResolvedWith(void 0);
          reading = !0;
          let byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch2._readableStreamController);
          return byobRequest === null ? pullWithDefaultReader() : pullWithBYOBReader(byobRequest._view, !0), promiseResolvedWith(void 0);
        }
        function cancel1Algorithm(reason) {
          if (canceled1 = !0, reason1 = reason, canceled2) {
            let compositeReason = CreateArrayFromList([reason1, reason2]), cancelResult = ReadableStreamCancel(stream, compositeReason);
            resolveCancelPromise(cancelResult);
          }
          return cancelPromise;
        }
        function cancel2Algorithm(reason) {
          if (canceled2 = !0, reason2 = reason, canceled1) {
            let compositeReason = CreateArrayFromList([reason1, reason2]), cancelResult = ReadableStreamCancel(stream, compositeReason);
            resolveCancelPromise(cancelResult);
          }
          return cancelPromise;
        }
        function startAlgorithm() {
        }
        return branch1 = CreateReadableByteStream(startAlgorithm, pull1Algorithm, cancel1Algorithm), branch2 = CreateReadableByteStream(startAlgorithm, pull2Algorithm, cancel2Algorithm), forwardReaderError(reader), [branch1, branch2];
      }
      function isReadableStreamLike(stream) {
        return typeIsObject(stream) && typeof stream.getReader < "u";
      }
      function ReadableStreamFrom(source) {
        return isReadableStreamLike(source) ? ReadableStreamFromDefaultReader(source.getReader()) : ReadableStreamFromIterable(source);
      }
      function ReadableStreamFromIterable(asyncIterable) {
        let stream, iteratorRecord = GetIterator(asyncIterable, "async"), startAlgorithm = noop;
        function pullAlgorithm() {
          let nextResult;
          try {
            nextResult = IteratorNext(iteratorRecord);
          } catch (e2) {
            return promiseRejectedWith(e2);
          }
          let nextPromise = promiseResolvedWith(nextResult);
          return transformPromiseWith(nextPromise, (iterResult) => {
            if (!typeIsObject(iterResult))
              throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");
            if (IteratorComplete(iterResult))
              ReadableStreamDefaultControllerClose(stream._readableStreamController);
            else {
              let value = IteratorValue(iterResult);
              ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);
            }
          });
        }
        function cancelAlgorithm(reason) {
          let iterator = iteratorRecord.iterator, returnMethod;
          try {
            returnMethod = GetMethod(iterator, "return");
          } catch (e2) {
            return promiseRejectedWith(e2);
          }
          if (returnMethod === void 0)
            return promiseResolvedWith(void 0);
          let returnResult;
          try {
            returnResult = reflectCall(returnMethod, iterator, [reason]);
          } catch (e2) {
            return promiseRejectedWith(e2);
          }
          let returnPromise = promiseResolvedWith(returnResult);
          return transformPromiseWith(returnPromise, (iterResult) => {
            if (!typeIsObject(iterResult))
              throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object");
          });
        }
        return stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0), stream;
      }
      function ReadableStreamFromDefaultReader(reader) {
        let stream, startAlgorithm = noop;
        function pullAlgorithm() {
          let readPromise;
          try {
            readPromise = reader.read();
          } catch (e2) {
            return promiseRejectedWith(e2);
          }
          return transformPromiseWith(readPromise, (readResult) => {
            if (!typeIsObject(readResult))
              throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");
            if (readResult.done)
              ReadableStreamDefaultControllerClose(stream._readableStreamController);
            else {
              let value = readResult.value;
              ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);
            }
          });
        }
        function cancelAlgorithm(reason) {
          try {
            return promiseResolvedWith(reader.cancel(reason));
          } catch (e2) {
            return promiseRejectedWith(e2);
          }
        }
        return stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0), stream;
      }
      function convertUnderlyingDefaultOrByteSource(source, context) {
        assertDictionary(source, context);
        let original = source, autoAllocateChunkSize = original?.autoAllocateChunkSize, cancel = original?.cancel, pull = original?.pull, start = original?.start, type = original?.type;
        return {
          autoAllocateChunkSize: autoAllocateChunkSize === void 0 ? void 0 : convertUnsignedLongLongWithEnforceRange(autoAllocateChunkSize, `${context} has member 'autoAllocateChunkSize' that`),
          cancel: cancel === void 0 ? void 0 : convertUnderlyingSourceCancelCallback(cancel, original, `${context} has member 'cancel' that`),
          pull: pull === void 0 ? void 0 : convertUnderlyingSourcePullCallback(pull, original, `${context} has member 'pull' that`),
          start: start === void 0 ? void 0 : convertUnderlyingSourceStartCallback(start, original, `${context} has member 'start' that`),
          type: type === void 0 ? void 0 : convertReadableStreamType(type, `${context} has member 'type' that`)
        };
      }
      function convertUnderlyingSourceCancelCallback(fn, original, context) {
        return assertFunction(fn, context), (reason) => promiseCall(fn, original, [reason]);
      }
      function convertUnderlyingSourcePullCallback(fn, original, context) {
        return assertFunction(fn, context), (controller) => promiseCall(fn, original, [controller]);
      }
      function convertUnderlyingSourceStartCallback(fn, original, context) {
        return assertFunction(fn, context), (controller) => reflectCall(fn, original, [controller]);
      }
      function convertReadableStreamType(type, context) {
        if (type = `${type}`, type !== "bytes")
          throw new TypeError(`${context} '${type}' is not a valid enumeration value for ReadableStreamType`);
        return type;
      }
      function convertIteratorOptions(options, context) {
        return assertDictionary(options, context), { preventCancel: !!options?.preventCancel };
      }
      function convertPipeOptions(options, context) {
        assertDictionary(options, context);
        let preventAbort = options?.preventAbort, preventCancel = options?.preventCancel, preventClose = options?.preventClose, signal = options?.signal;
        return signal !== void 0 && assertAbortSignal(signal, `${context} has member 'signal' that`), {
          preventAbort: !!preventAbort,
          preventCancel: !!preventCancel,
          preventClose: !!preventClose,
          signal
        };
      }
      function assertAbortSignal(signal, context) {
        if (!isAbortSignal(signal))
          throw new TypeError(`${context} is not an AbortSignal.`);
      }
      function convertReadableWritablePair(pair, context) {
        assertDictionary(pair, context);
        let readable = pair?.readable;
        assertRequiredField(readable, "readable", "ReadableWritablePair"), assertReadableStream(readable, `${context} has member 'readable' that`);
        let writable = pair?.writable;
        return assertRequiredField(writable, "writable", "ReadableWritablePair"), assertWritableStream(writable, `${context} has member 'writable' that`), { readable, writable };
      }
      class ReadableStream2 {
        constructor(rawUnderlyingSource = {}, rawStrategy = {}) {
          rawUnderlyingSource === void 0 ? rawUnderlyingSource = null : assertObject(rawUnderlyingSource, "First parameter");
          let strategy = convertQueuingStrategy(rawStrategy, "Second parameter"), underlyingSource = convertUnderlyingDefaultOrByteSource(rawUnderlyingSource, "First parameter");
          if (InitializeReadableStream(this), underlyingSource.type === "bytes") {
            if (strategy.size !== void 0)
              throw new RangeError("The strategy for a byte stream cannot have a size function");
            let highWaterMark = ExtractHighWaterMark(strategy, 0);
            SetUpReadableByteStreamControllerFromUnderlyingSource(this, underlyingSource, highWaterMark);
          } else {
            let sizeAlgorithm = ExtractSizeAlgorithm(strategy), highWaterMark = ExtractHighWaterMark(strategy, 1);
            SetUpReadableStreamDefaultControllerFromUnderlyingSource(this, underlyingSource, highWaterMark, sizeAlgorithm);
          }
        }
        /**
         * Whether or not the readable stream is locked to a {@link ReadableStreamDefaultReader | reader}.
         */
        get locked() {
          if (!IsReadableStream(this))
            throw streamBrandCheckException$1("locked");
          return IsReadableStreamLocked(this);
        }
        /**
         * Cancels the stream, signaling a loss of interest in the stream by a consumer.
         *
         * The supplied `reason` argument will be given to the underlying source's {@link UnderlyingSource.cancel | cancel()}
         * method, which might or might not use it.
         */
        cancel(reason = void 0) {
          return IsReadableStream(this) ? IsReadableStreamLocked(this) ? promiseRejectedWith(new TypeError("Cannot cancel a stream that already has a reader")) : ReadableStreamCancel(this, reason) : promiseRejectedWith(streamBrandCheckException$1("cancel"));
        }
        getReader(rawOptions = void 0) {
          if (!IsReadableStream(this))
            throw streamBrandCheckException$1("getReader");
          return convertReaderOptions(rawOptions, "First parameter").mode === void 0 ? AcquireReadableStreamDefaultReader(this) : AcquireReadableStreamBYOBReader(this);
        }
        pipeThrough(rawTransform, rawOptions = {}) {
          if (!IsReadableStream(this))
            throw streamBrandCheckException$1("pipeThrough");
          assertRequiredArgument(rawTransform, 1, "pipeThrough");
          let transform = convertReadableWritablePair(rawTransform, "First parameter"), options = convertPipeOptions(rawOptions, "Second parameter");
          if (IsReadableStreamLocked(this))
            throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");
          if (IsWritableStreamLocked(transform.writable))
            throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");
          let promise = ReadableStreamPipeTo(this, transform.writable, options.preventClose, options.preventAbort, options.preventCancel, options.signal);
          return setPromiseIsHandledToTrue(promise), transform.readable;
        }
        pipeTo(destination, rawOptions = {}) {
          if (!IsReadableStream(this))
            return promiseRejectedWith(streamBrandCheckException$1("pipeTo"));
          if (destination === void 0)
            return promiseRejectedWith("Parameter 1 is required in 'pipeTo'.");
          if (!IsWritableStream(destination))
            return promiseRejectedWith(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));
          let options;
          try {
            options = convertPipeOptions(rawOptions, "Second parameter");
          } catch (e2) {
            return promiseRejectedWith(e2);
          }
          return IsReadableStreamLocked(this) ? promiseRejectedWith(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")) : IsWritableStreamLocked(destination) ? promiseRejectedWith(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")) : ReadableStreamPipeTo(this, destination, options.preventClose, options.preventAbort, options.preventCancel, options.signal);
        }
        /**
         * Tees this readable stream, returning a two-element array containing the two resulting branches as
         * new {@link ReadableStream} instances.
         *
         * Teeing a stream will lock it, preventing any other consumer from acquiring a reader.
         * To cancel the stream, cancel both of the resulting branches; a composite cancellation reason will then be
         * propagated to the stream's underlying source.
         *
         * Note that the chunks seen in each branch will be the same object. If the chunks are not immutable,
         * this could allow interference between the two branches.
         */
        tee() {
          if (!IsReadableStream(this))
            throw streamBrandCheckException$1("tee");
          let branches = ReadableStreamTee(this);
          return CreateArrayFromList(branches);
        }
        values(rawOptions = void 0) {
          if (!IsReadableStream(this))
            throw streamBrandCheckException$1("values");
          let options = convertIteratorOptions(rawOptions, "First parameter");
          return AcquireReadableStreamAsyncIterator(this, options.preventCancel);
        }
        [SymbolAsyncIterator](options) {
          return this.values(options);
        }
        /**
         * Creates a new ReadableStream wrapping the provided iterable or async iterable.
         *
         * This can be used to adapt various kinds of objects into a readable stream,
         * such as an array, an async generator, or a Node.js readable stream.
         */
        static from(asyncIterable) {
          return ReadableStreamFrom(asyncIterable);
        }
      }
      Object.defineProperties(ReadableStream2, {
        from: { enumerable: !0 }
      }), Object.defineProperties(ReadableStream2.prototype, {
        cancel: { enumerable: !0 },
        getReader: { enumerable: !0 },
        pipeThrough: { enumerable: !0 },
        pipeTo: { enumerable: !0 },
        tee: { enumerable: !0 },
        values: { enumerable: !0 },
        locked: { enumerable: !0 }
      }), setFunctionName(ReadableStream2.from, "from"), setFunctionName(ReadableStream2.prototype.cancel, "cancel"), setFunctionName(ReadableStream2.prototype.getReader, "getReader"), setFunctionName(ReadableStream2.prototype.pipeThrough, "pipeThrough"), setFunctionName(ReadableStream2.prototype.pipeTo, "pipeTo"), setFunctionName(ReadableStream2.prototype.tee, "tee"), setFunctionName(ReadableStream2.prototype.values, "values"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(ReadableStream2.prototype, Symbol.toStringTag, {
        value: "ReadableStream",
        configurable: !0
      }), Object.defineProperty(ReadableStream2.prototype, SymbolAsyncIterator, {
        value: ReadableStream2.prototype.values,
        writable: !0,
        configurable: !0
      });
      function CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark = 1, sizeAlgorithm = () => 1) {
        let stream = Object.create(ReadableStream2.prototype);
        InitializeReadableStream(stream);
        let controller = Object.create(ReadableStreamDefaultController.prototype);
        return SetUpReadableStreamDefaultController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm), stream;
      }
      function CreateReadableByteStream(startAlgorithm, pullAlgorithm, cancelAlgorithm) {
        let stream = Object.create(ReadableStream2.prototype);
        InitializeReadableStream(stream);
        let controller = Object.create(ReadableByteStreamController.prototype);
        return SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, 0, void 0), stream;
      }
      function InitializeReadableStream(stream) {
        stream._state = "readable", stream._reader = void 0, stream._storedError = void 0, stream._disturbed = !1;
      }
      function IsReadableStream(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_readableStreamController") ? !1 : x2 instanceof ReadableStream2;
      }
      function IsReadableStreamLocked(stream) {
        return stream._reader !== void 0;
      }
      function ReadableStreamCancel(stream, reason) {
        if (stream._disturbed = !0, stream._state === "closed")
          return promiseResolvedWith(void 0);
        if (stream._state === "errored")
          return promiseRejectedWith(stream._storedError);
        ReadableStreamClose(stream);
        let reader = stream._reader;
        if (reader !== void 0 && IsReadableStreamBYOBReader(reader)) {
          let readIntoRequests = reader._readIntoRequests;
          reader._readIntoRequests = new SimpleQueue(), readIntoRequests.forEach((readIntoRequest) => {
            readIntoRequest._closeSteps(void 0);
          });
        }
        let sourceCancelPromise = stream._readableStreamController[CancelSteps](reason);
        return transformPromiseWith(sourceCancelPromise, noop);
      }
      function ReadableStreamClose(stream) {
        stream._state = "closed";
        let reader = stream._reader;
        if (reader !== void 0 && (defaultReaderClosedPromiseResolve(reader), IsReadableStreamDefaultReader(reader))) {
          let readRequests = reader._readRequests;
          reader._readRequests = new SimpleQueue(), readRequests.forEach((readRequest) => {
            readRequest._closeSteps();
          });
        }
      }
      function ReadableStreamError(stream, e2) {
        stream._state = "errored", stream._storedError = e2;
        let reader = stream._reader;
        reader !== void 0 && (defaultReaderClosedPromiseReject(reader, e2), IsReadableStreamDefaultReader(reader) ? ReadableStreamDefaultReaderErrorReadRequests(reader, e2) : ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e2));
      }
      function streamBrandCheckException$1(name) {
        return new TypeError(`ReadableStream.prototype.${name} can only be used on a ReadableStream`);
      }
      function convertQueuingStrategyInit(init, context) {
        assertDictionary(init, context);
        let highWaterMark = init?.highWaterMark;
        return assertRequiredField(highWaterMark, "highWaterMark", "QueuingStrategyInit"), {
          highWaterMark: convertUnrestrictedDouble(highWaterMark)
        };
      }
      let byteLengthSizeFunction = (chunk) => chunk.byteLength;
      setFunctionName(byteLengthSizeFunction, "size");
      class ByteLengthQueuingStrategy {
        constructor(options) {
          assertRequiredArgument(options, 1, "ByteLengthQueuingStrategy"), options = convertQueuingStrategyInit(options, "First parameter"), this._byteLengthQueuingStrategyHighWaterMark = options.highWaterMark;
        }
        /**
         * Returns the high water mark provided to the constructor.
         */
        get highWaterMark() {
          if (!IsByteLengthQueuingStrategy(this))
            throw byteLengthBrandCheckException("highWaterMark");
          return this._byteLengthQueuingStrategyHighWaterMark;
        }
        /**
         * Measures the size of `chunk` by returning the value of its `byteLength` property.
         */
        get size() {
          if (!IsByteLengthQueuingStrategy(this))
            throw byteLengthBrandCheckException("size");
          return byteLengthSizeFunction;
        }
      }
      Object.defineProperties(ByteLengthQueuingStrategy.prototype, {
        highWaterMark: { enumerable: !0 },
        size: { enumerable: !0 }
      }), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(ByteLengthQueuingStrategy.prototype, Symbol.toStringTag, {
        value: "ByteLengthQueuingStrategy",
        configurable: !0
      });
      function byteLengthBrandCheckException(name) {
        return new TypeError(`ByteLengthQueuingStrategy.prototype.${name} can only be used on a ByteLengthQueuingStrategy`);
      }
      function IsByteLengthQueuingStrategy(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_byteLengthQueuingStrategyHighWaterMark") ? !1 : x2 instanceof ByteLengthQueuingStrategy;
      }
      let countSizeFunction = () => 1;
      setFunctionName(countSizeFunction, "size");
      class CountQueuingStrategy {
        constructor(options) {
          assertRequiredArgument(options, 1, "CountQueuingStrategy"), options = convertQueuingStrategyInit(options, "First parameter"), this._countQueuingStrategyHighWaterMark = options.highWaterMark;
        }
        /**
         * Returns the high water mark provided to the constructor.
         */
        get highWaterMark() {
          if (!IsCountQueuingStrategy(this))
            throw countBrandCheckException("highWaterMark");
          return this._countQueuingStrategyHighWaterMark;
        }
        /**
         * Measures the size of `chunk` by always returning 1.
         * This ensures that the total queue size is a count of the number of chunks in the queue.
         */
        get size() {
          if (!IsCountQueuingStrategy(this))
            throw countBrandCheckException("size");
          return countSizeFunction;
        }
      }
      Object.defineProperties(CountQueuingStrategy.prototype, {
        highWaterMark: { enumerable: !0 },
        size: { enumerable: !0 }
      }), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(CountQueuingStrategy.prototype, Symbol.toStringTag, {
        value: "CountQueuingStrategy",
        configurable: !0
      });
      function countBrandCheckException(name) {
        return new TypeError(`CountQueuingStrategy.prototype.${name} can only be used on a CountQueuingStrategy`);
      }
      function IsCountQueuingStrategy(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_countQueuingStrategyHighWaterMark") ? !1 : x2 instanceof CountQueuingStrategy;
      }
      function convertTransformer(original, context) {
        assertDictionary(original, context);
        let cancel = original?.cancel, flush = original?.flush, readableType = original?.readableType, start = original?.start, transform = original?.transform, writableType = original?.writableType;
        return {
          cancel: cancel === void 0 ? void 0 : convertTransformerCancelCallback(cancel, original, `${context} has member 'cancel' that`),
          flush: flush === void 0 ? void 0 : convertTransformerFlushCallback(flush, original, `${context} has member 'flush' that`),
          readableType,
          start: start === void 0 ? void 0 : convertTransformerStartCallback(start, original, `${context} has member 'start' that`),
          transform: transform === void 0 ? void 0 : convertTransformerTransformCallback(transform, original, `${context} has member 'transform' that`),
          writableType
        };
      }
      function convertTransformerFlushCallback(fn, original, context) {
        return assertFunction(fn, context), (controller) => promiseCall(fn, original, [controller]);
      }
      function convertTransformerStartCallback(fn, original, context) {
        return assertFunction(fn, context), (controller) => reflectCall(fn, original, [controller]);
      }
      function convertTransformerTransformCallback(fn, original, context) {
        return assertFunction(fn, context), (chunk, controller) => promiseCall(fn, original, [chunk, controller]);
      }
      function convertTransformerCancelCallback(fn, original, context) {
        return assertFunction(fn, context), (reason) => promiseCall(fn, original, [reason]);
      }
      class TransformStream {
        constructor(rawTransformer = {}, rawWritableStrategy = {}, rawReadableStrategy = {}) {
          rawTransformer === void 0 && (rawTransformer = null);
          let writableStrategy = convertQueuingStrategy(rawWritableStrategy, "Second parameter"), readableStrategy = convertQueuingStrategy(rawReadableStrategy, "Third parameter"), transformer = convertTransformer(rawTransformer, "First parameter");
          if (transformer.readableType !== void 0)
            throw new RangeError("Invalid readableType specified");
          if (transformer.writableType !== void 0)
            throw new RangeError("Invalid writableType specified");
          let readableHighWaterMark = ExtractHighWaterMark(readableStrategy, 0), readableSizeAlgorithm = ExtractSizeAlgorithm(readableStrategy), writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1), writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy), startPromise_resolve, startPromise = newPromise((resolve) => {
            startPromise_resolve = resolve;
          });
          InitializeTransformStream(this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm), SetUpTransformStreamDefaultControllerFromTransformer(this, transformer), transformer.start !== void 0 ? startPromise_resolve(transformer.start(this._transformStreamController)) : startPromise_resolve(void 0);
        }
        /**
         * The readable side of the transform stream.
         */
        get readable() {
          if (!IsTransformStream(this))
            throw streamBrandCheckException("readable");
          return this._readable;
        }
        /**
         * The writable side of the transform stream.
         */
        get writable() {
          if (!IsTransformStream(this))
            throw streamBrandCheckException("writable");
          return this._writable;
        }
      }
      Object.defineProperties(TransformStream.prototype, {
        readable: { enumerable: !0 },
        writable: { enumerable: !0 }
      }), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(TransformStream.prototype, Symbol.toStringTag, {
        value: "TransformStream",
        configurable: !0
      });
      function InitializeTransformStream(stream, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm) {
        function startAlgorithm() {
          return startPromise;
        }
        function writeAlgorithm(chunk) {
          return TransformStreamDefaultSinkWriteAlgorithm(stream, chunk);
        }
        function abortAlgorithm(reason) {
          return TransformStreamDefaultSinkAbortAlgorithm(stream, reason);
        }
        function closeAlgorithm() {
          return TransformStreamDefaultSinkCloseAlgorithm(stream);
        }
        stream._writable = CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, writableHighWaterMark, writableSizeAlgorithm);
        function pullAlgorithm() {
          return TransformStreamDefaultSourcePullAlgorithm(stream);
        }
        function cancelAlgorithm(reason) {
          return TransformStreamDefaultSourceCancelAlgorithm(stream, reason);
        }
        stream._readable = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, readableHighWaterMark, readableSizeAlgorithm), stream._backpressure = void 0, stream._backpressureChangePromise = void 0, stream._backpressureChangePromise_resolve = void 0, TransformStreamSetBackpressure(stream, !0), stream._transformStreamController = void 0;
      }
      function IsTransformStream(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_transformStreamController") ? !1 : x2 instanceof TransformStream;
      }
      function TransformStreamError(stream, e2) {
        ReadableStreamDefaultControllerError(stream._readable._readableStreamController, e2), TransformStreamErrorWritableAndUnblockWrite(stream, e2);
      }
      function TransformStreamErrorWritableAndUnblockWrite(stream, e2) {
        TransformStreamDefaultControllerClearAlgorithms(stream._transformStreamController), WritableStreamDefaultControllerErrorIfNeeded(stream._writable._writableStreamController, e2), TransformStreamUnblockWrite(stream);
      }
      function TransformStreamUnblockWrite(stream) {
        stream._backpressure && TransformStreamSetBackpressure(stream, !1);
      }
      function TransformStreamSetBackpressure(stream, backpressure) {
        stream._backpressureChangePromise !== void 0 && stream._backpressureChangePromise_resolve(), stream._backpressureChangePromise = newPromise((resolve) => {
          stream._backpressureChangePromise_resolve = resolve;
        }), stream._backpressure = backpressure;
      }
      class TransformStreamDefaultController {
        constructor() {
          throw new TypeError("Illegal constructor");
        }
        /**
         * Returns the desired size to fill the readable side’s internal queue. It can be negative, if the queue is over-full.
         */
        get desiredSize() {
          if (!IsTransformStreamDefaultController(this))
            throw defaultControllerBrandCheckException("desiredSize");
          let readableController = this._controlledTransformStream._readable._readableStreamController;
          return ReadableStreamDefaultControllerGetDesiredSize(readableController);
        }
        enqueue(chunk = void 0) {
          if (!IsTransformStreamDefaultController(this))
            throw defaultControllerBrandCheckException("enqueue");
          TransformStreamDefaultControllerEnqueue(this, chunk);
        }
        /**
         * Errors both the readable side and the writable side of the controlled transform stream, making all future
         * interactions with it fail with the given error `e`. Any chunks queued for transformation will be discarded.
         */
        error(reason = void 0) {
          if (!IsTransformStreamDefaultController(this))
            throw defaultControllerBrandCheckException("error");
          TransformStreamDefaultControllerError(this, reason);
        }
        /**
         * Closes the readable side and errors the writable side of the controlled transform stream. This is useful when the
         * transformer only needs to consume a portion of the chunks written to the writable side.
         */
        terminate() {
          if (!IsTransformStreamDefaultController(this))
            throw defaultControllerBrandCheckException("terminate");
          TransformStreamDefaultControllerTerminate(this);
        }
      }
      Object.defineProperties(TransformStreamDefaultController.prototype, {
        enqueue: { enumerable: !0 },
        error: { enumerable: !0 },
        terminate: { enumerable: !0 },
        desiredSize: { enumerable: !0 }
      }), setFunctionName(TransformStreamDefaultController.prototype.enqueue, "enqueue"), setFunctionName(TransformStreamDefaultController.prototype.error, "error"), setFunctionName(TransformStreamDefaultController.prototype.terminate, "terminate"), typeof Symbol.toStringTag == "symbol" && Object.defineProperty(TransformStreamDefaultController.prototype, Symbol.toStringTag, {
        value: "TransformStreamDefaultController",
        configurable: !0
      });
      function IsTransformStreamDefaultController(x2) {
        return !typeIsObject(x2) || !Object.prototype.hasOwnProperty.call(x2, "_controlledTransformStream") ? !1 : x2 instanceof TransformStreamDefaultController;
      }
      function SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm) {
        controller._controlledTransformStream = stream, stream._transformStreamController = controller, controller._transformAlgorithm = transformAlgorithm, controller._flushAlgorithm = flushAlgorithm, controller._cancelAlgorithm = cancelAlgorithm, controller._finishPromise = void 0, controller._finishPromise_resolve = void 0, controller._finishPromise_reject = void 0;
      }
      function SetUpTransformStreamDefaultControllerFromTransformer(stream, transformer) {
        let controller = Object.create(TransformStreamDefaultController.prototype), transformAlgorithm, flushAlgorithm, cancelAlgorithm;
        transformer.transform !== void 0 ? transformAlgorithm = (chunk) => transformer.transform(chunk, controller) : transformAlgorithm = (chunk) => {
          try {
            return TransformStreamDefaultControllerEnqueue(controller, chunk), promiseResolvedWith(void 0);
          } catch (transformResultE) {
            return promiseRejectedWith(transformResultE);
          }
        }, transformer.flush !== void 0 ? flushAlgorithm = () => transformer.flush(controller) : flushAlgorithm = () => promiseResolvedWith(void 0), transformer.cancel !== void 0 ? cancelAlgorithm = (reason) => transformer.cancel(reason) : cancelAlgorithm = () => promiseResolvedWith(void 0), SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm);
      }
      function TransformStreamDefaultControllerClearAlgorithms(controller) {
        controller._transformAlgorithm = void 0, controller._flushAlgorithm = void 0, controller._cancelAlgorithm = void 0;
      }
      function TransformStreamDefaultControllerEnqueue(controller, chunk) {
        let stream = controller._controlledTransformStream, readableController = stream._readable._readableStreamController;
        if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(readableController))
          throw new TypeError("Readable side is not in a state that permits enqueue");
        try {
          ReadableStreamDefaultControllerEnqueue(readableController, chunk);
        } catch (e2) {
          throw TransformStreamErrorWritableAndUnblockWrite(stream, e2), stream._readable._storedError;
        }
        ReadableStreamDefaultControllerHasBackpressure(readableController) !== stream._backpressure && TransformStreamSetBackpressure(stream, !0);
      }
      function TransformStreamDefaultControllerError(controller, e2) {
        TransformStreamError(controller._controlledTransformStream, e2);
      }
      function TransformStreamDefaultControllerPerformTransform(controller, chunk) {
        let transformPromise = controller._transformAlgorithm(chunk);
        return transformPromiseWith(transformPromise, void 0, (r2) => {
          throw TransformStreamError(controller._controlledTransformStream, r2), r2;
        });
      }
      function TransformStreamDefaultControllerTerminate(controller) {
        let stream = controller._controlledTransformStream, readableController = stream._readable._readableStreamController;
        ReadableStreamDefaultControllerClose(readableController);
        let error = new TypeError("TransformStream terminated");
        TransformStreamErrorWritableAndUnblockWrite(stream, error);
      }
      function TransformStreamDefaultSinkWriteAlgorithm(stream, chunk) {
        let controller = stream._transformStreamController;
        if (stream._backpressure) {
          let backpressureChangePromise = stream._backpressureChangePromise;
          return transformPromiseWith(backpressureChangePromise, () => {
            let writable = stream._writable;
            if (writable._state === "erroring")
              throw writable._storedError;
            return TransformStreamDefaultControllerPerformTransform(controller, chunk);
          });
        }
        return TransformStreamDefaultControllerPerformTransform(controller, chunk);
      }
      function TransformStreamDefaultSinkAbortAlgorithm(stream, reason) {
        let controller = stream._transformStreamController;
        if (controller._finishPromise !== void 0)
          return controller._finishPromise;
        let readable = stream._readable;
        controller._finishPromise = newPromise((resolve, reject) => {
          controller._finishPromise_resolve = resolve, controller._finishPromise_reject = reject;
        });
        let cancelPromise = controller._cancelAlgorithm(reason);
        return TransformStreamDefaultControllerClearAlgorithms(controller), uponPromise(cancelPromise, () => (readable._state === "errored" ? defaultControllerFinishPromiseReject(controller, readable._storedError) : (ReadableStreamDefaultControllerError(readable._readableStreamController, reason), defaultControllerFinishPromiseResolve(controller)), null), (r2) => (ReadableStreamDefaultControllerError(readable._readableStreamController, r2), defaultControllerFinishPromiseReject(controller, r2), null)), controller._finishPromise;
      }
      function TransformStreamDefaultSinkCloseAlgorithm(stream) {
        let controller = stream._transformStreamController;
        if (controller._finishPromise !== void 0)
          return controller._finishPromise;
        let readable = stream._readable;
        controller._finishPromise = newPromise((resolve, reject) => {
          controller._finishPromise_resolve = resolve, controller._finishPromise_reject = reject;
        });
        let flushPromise = controller._flushAlgorithm();
        return TransformStreamDefaultControllerClearAlgorithms(controller), uponPromise(flushPromise, () => (readable._state === "errored" ? defaultControllerFinishPromiseReject(controller, readable._storedError) : (ReadableStreamDefaultControllerClose(readable._readableStreamController), defaultControllerFinishPromiseResolve(controller)), null), (r2) => (ReadableStreamDefaultControllerError(readable._readableStreamController, r2), defaultControllerFinishPromiseReject(controller, r2), null)), controller._finishPromise;
      }
      function TransformStreamDefaultSourcePullAlgorithm(stream) {
        return TransformStreamSetBackpressure(stream, !1), stream._backpressureChangePromise;
      }
      function TransformStreamDefaultSourceCancelAlgorithm(stream, reason) {
        let controller = stream._transformStreamController;
        if (controller._finishPromise !== void 0)
          return controller._finishPromise;
        let writable = stream._writable;
        controller._finishPromise = newPromise((resolve, reject) => {
          controller._finishPromise_resolve = resolve, controller._finishPromise_reject = reject;
        });
        let cancelPromise = controller._cancelAlgorithm(reason);
        return TransformStreamDefaultControllerClearAlgorithms(controller), uponPromise(cancelPromise, () => (writable._state === "errored" ? defaultControllerFinishPromiseReject(controller, writable._storedError) : (WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, reason), TransformStreamUnblockWrite(stream), defaultControllerFinishPromiseResolve(controller)), null), (r2) => (WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, r2), TransformStreamUnblockWrite(stream), defaultControllerFinishPromiseReject(controller, r2), null)), controller._finishPromise;
      }
      function defaultControllerBrandCheckException(name) {
        return new TypeError(`TransformStreamDefaultController.prototype.${name} can only be used on a TransformStreamDefaultController`);
      }
      function defaultControllerFinishPromiseResolve(controller) {
        controller._finishPromise_resolve !== void 0 && (controller._finishPromise_resolve(), controller._finishPromise_resolve = void 0, controller._finishPromise_reject = void 0);
      }
      function defaultControllerFinishPromiseReject(controller, reason) {
        controller._finishPromise_reject !== void 0 && (setPromiseIsHandledToTrue(controller._finishPromise), controller._finishPromise_reject(reason), controller._finishPromise_resolve = void 0, controller._finishPromise_reject = void 0);
      }
      function streamBrandCheckException(name) {
        return new TypeError(`TransformStream.prototype.${name} can only be used on a TransformStream`);
      }
      exports2.ByteLengthQueuingStrategy = ByteLengthQueuingStrategy, exports2.CountQueuingStrategy = CountQueuingStrategy, exports2.ReadableByteStreamController = ReadableByteStreamController, exports2.ReadableStream = ReadableStream2, exports2.ReadableStreamBYOBReader = ReadableStreamBYOBReader, exports2.ReadableStreamBYOBRequest = ReadableStreamBYOBRequest, exports2.ReadableStreamDefaultController = ReadableStreamDefaultController, exports2.ReadableStreamDefaultReader = ReadableStreamDefaultReader, exports2.TransformStream = TransformStream, exports2.TransformStreamDefaultController = TransformStreamDefaultController, exports2.WritableStream = WritableStream, exports2.WritableStreamDefaultController = WritableStreamDefaultController, exports2.WritableStreamDefaultWriter = WritableStreamDefaultWriter;
    });
  }
});

// ../../node_modules/.pnpm/fetch-blob@3.2.0/node_modules/fetch-blob/streams.cjs
var require_streams = __commonJS({
  "../../node_modules/.pnpm/fetch-blob@3.2.0/node_modules/fetch-blob/streams.cjs"() {
    init_cjs_shims();
    if (!globalThis.ReadableStream)
      try {
        let process = __require("node:process"), { emitWarning } = process;
        try {
          process.emitWarning = () => {
          }, Object.assign(globalThis, __require("node:stream/web")), process.emitWarning = emitWarning;
        } catch (error) {
          throw process.emitWarning = emitWarning, error;
        }
      } catch {
        Object.assign(globalThis, require_ponyfill_es2018());
      }
    try {
      let { Blob: Blob3 } = __require("buffer");
      Blob3 && !Blob3.prototype.stream && (Blob3.prototype.stream = function(params) {
        let position = 0, blob = this;
        return new ReadableStream({
          type: "bytes",
          async pull(ctrl) {
            let buffer = await blob.slice(position, Math.min(blob.size, position + 65536)).arrayBuffer();
            position += buffer.byteLength, ctrl.enqueue(new Uint8Array(buffer)), position === blob.size && ctrl.close();
          }
        });
      });
    } catch {
    }
  }
});

// ../../node_modules/.pnpm/node-domexception@1.0.0/node_modules/node-domexception/index.js
var require_node_domexception = __commonJS({
  "../../node_modules/.pnpm/node-domexception@1.0.0/node_modules/node-domexception/index.js"(exports, module) {
    init_cjs_shims();
    if (!globalThis.DOMException)
      try {
        let { MessageChannel } = __require("worker_threads"), port = new MessageChannel().port1, ab = new ArrayBuffer();
        port.postMessage(ab, [ab, ab]);
      } catch (err) {
        err.constructor.name === "DOMException" && (globalThis.DOMException = err.constructor);
      }
    module.exports = globalThis.DOMException;
  }
});

// ../../node_modules/.pnpm/fetch-blob@3.2.0/node_modules/fetch-blob/file.js
init_cjs_shims();

// ../../node_modules/.pnpm/fetch-blob@3.2.0/node_modules/fetch-blob/index.js
init_cjs_shims();
var import_streams = __toESM(require_streams(), 1);
var POOL_SIZE = 65536;
async function* toIterator(parts, clone = !0) {
  for (let part of parts)
    if ("stream" in part)
      yield* (
        /** @type {AsyncIterableIterator<Uint8Array>} */
        part.stream()
      );
    else if (ArrayBuffer.isView(part))
      if (clone) {
        let position = part.byteOffset, end = part.byteOffset + part.byteLength;
        for (; position !== end; ) {
          let size = Math.min(end - position, POOL_SIZE), chunk = part.buffer.slice(position, position + size);
          position += chunk.byteLength, yield new Uint8Array(chunk);
        }
      } else
        yield part;
    else {
      let position = 0, b = (
        /** @type {Blob} */
        part
      );
      for (; position !== b.size; ) {
        let buffer = await b.slice(position, Math.min(b.size, position + POOL_SIZE)).arrayBuffer();
        position += buffer.byteLength, yield new Uint8Array(buffer);
      }
    }
}
var _Blob = class Blob {
  /** @type {Array.<(Blob|Uint8Array)>} */
  #parts = [];
  #type = "";
  #size = 0;
  #endings = "transparent";
  /**
   * The Blob() constructor returns a new Blob object. The content
   * of the blob consists of the concatenation of the values given
   * in the parameter array.
   *
   * @param {*} blobParts
   * @param {{ type?: string, endings?: string }} [options]
   */
  constructor(blobParts = [], options = {}) {
    if (typeof blobParts != "object" || blobParts === null)
      throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");
    if (typeof blobParts[Symbol.iterator] != "function")
      throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");
    if (typeof options != "object" && typeof options != "function")
      throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");
    options === null && (options = {});
    let encoder = new TextEncoder();
    for (let element of blobParts) {
      let part;
      ArrayBuffer.isView(element) ? part = new Uint8Array(element.buffer.slice(element.byteOffset, element.byteOffset + element.byteLength)) : element instanceof ArrayBuffer ? part = new Uint8Array(element.slice(0)) : element instanceof Blob ? part = element : part = encoder.encode(`${element}`), this.#size += ArrayBuffer.isView(part) ? part.byteLength : part.size, this.#parts.push(part);
    }
    this.#endings = `${options.endings === void 0 ? "transparent" : options.endings}`;
    let type = options.type === void 0 ? "" : String(options.type);
    this.#type = /^[\x20-\x7E]*$/.test(type) ? type : "";
  }
  /**
   * The Blob interface's size property returns the
   * size of the Blob in bytes.
   */
  get size() {
    return this.#size;
  }
  /**
   * The type property of a Blob object returns the MIME type of the file.
   */
  get type() {
    return this.#type;
  }
  /**
   * The text() method in the Blob interface returns a Promise
   * that resolves with a string containing the contents of
   * the blob, interpreted as UTF-8.
   *
   * @return {Promise<string>}
   */
  async text() {
    let decoder = new TextDecoder(), str = "";
    for await (let part of toIterator(this.#parts, !1))
      str += decoder.decode(part, { stream: !0 });
    return str += decoder.decode(), str;
  }
  /**
   * The arrayBuffer() method in the Blob interface returns a
   * Promise that resolves with the contents of the blob as
   * binary data contained in an ArrayBuffer.
   *
   * @return {Promise<ArrayBuffer>}
   */
  async arrayBuffer() {
    let data = new Uint8Array(this.size), offset = 0;
    for await (let chunk of toIterator(this.#parts, !1))
      data.set(chunk, offset), offset += chunk.length;
    return data.buffer;
  }
  stream() {
    let it = toIterator(this.#parts, !0);
    return new globalThis.ReadableStream({
      // @ts-ignore
      type: "bytes",
      async pull(ctrl) {
        let chunk = await it.next();
        chunk.done ? ctrl.close() : ctrl.enqueue(chunk.value);
      },
      async cancel() {
        await it.return();
      }
    });
  }
  /**
   * The Blob interface's slice() method creates and returns a
   * new Blob object which contains data from a subset of the
   * blob on which it's called.
   *
   * @param {number} [start]
   * @param {number} [end]
   * @param {string} [type]
   */
  slice(start = 0, end = this.size, type = "") {
    let { size } = this, relativeStart = start < 0 ? Math.max(size + start, 0) : Math.min(start, size), relativeEnd = end < 0 ? Math.max(size + end, 0) : Math.min(end, size), span = Math.max(relativeEnd - relativeStart, 0), parts = this.#parts, blobParts = [], added = 0;
    for (let part of parts) {
      if (added >= span)
        break;
      let size2 = ArrayBuffer.isView(part) ? part.byteLength : part.size;
      if (relativeStart && size2 <= relativeStart)
        relativeStart -= size2, relativeEnd -= size2;
      else {
        let chunk;
        ArrayBuffer.isView(part) ? (chunk = part.subarray(relativeStart, Math.min(size2, relativeEnd)), added += chunk.byteLength) : (chunk = part.slice(relativeStart, Math.min(size2, relativeEnd)), added += chunk.size), relativeEnd -= size2, blobParts.push(chunk), relativeStart = 0;
      }
    }
    let blob = new Blob([], { type: String(type).toLowerCase() });
    return blob.#size = span, blob.#parts = blobParts, blob;
  }
  get [Symbol.toStringTag]() {
    return "Blob";
  }
  static [Symbol.hasInstance](object) {
    return object && typeof object == "object" && typeof object.constructor == "function" && (typeof object.stream == "function" || typeof object.arrayBuffer == "function") && /^(Blob|File)$/.test(object[Symbol.toStringTag]);
  }
};
Object.defineProperties(_Blob.prototype, {
  size: { enumerable: !0 },
  type: { enumerable: !0 },
  slice: { enumerable: !0 }
});
var Blob2 = _Blob, fetch_blob_default = Blob2;

// ../../node_modules/.pnpm/fetch-blob@3.2.0/node_modules/fetch-blob/file.js
var _File = class extends fetch_blob_default {
  #lastModified = 0;
  #name = "";
  /**
   * @param {*[]} fileBits
   * @param {string} fileName
   * @param {{lastModified?: number, type?: string}} options
   */
  // @ts-ignore
  constructor(fileBits, fileName, options = {}) {
    if (arguments.length < 2)
      throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);
    super(fileBits, options), options === null && (options = {});
    let lastModified = options.lastModified === void 0 ? Date.now() : Number(options.lastModified);
    Number.isNaN(lastModified) || (this.#lastModified = lastModified), this.#name = String(fileName);
  }
  get name() {
    return this.#name;
  }
  get lastModified() {
    return this.#lastModified;
  }
  get [Symbol.toStringTag]() {
    return "File";
  }
  static [Symbol.hasInstance](object) {
    return !!object && object instanceof fetch_blob_default && /^(File)$/.test(object[Symbol.toStringTag]);
  }
}, File2 = _File, file_default = File2;

// ../../node_modules/.pnpm/formdata-polyfill@4.0.10/node_modules/formdata-polyfill/esm.min.js
init_cjs_shims();
var { toStringTag: t, iterator: i, hasInstance: h } = Symbol, r = Math.random, m = "append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","), f = (a, b, c) => (a += "", /^(Blob|File)$/.test(b && b[t]) ? [(c = c !== void 0 ? c + "" : b[t] == "File" ? b.name : "blob", a), b.name !== c || b[t] == "blob" ? new file_default([b], c, b) : b] : [a, b + ""]), e = (c, f2) => (f2 ? c : c.replace(/\r?\n|\r/g, `\r
`)).replace(/\n/g, "%0A").replace(/\r/g, "%0D").replace(/"/g, "%22"), x = (n, a, e2) => {
  if (a.length < e2)
    throw new TypeError(`Failed to execute '${n}' on 'FormData': ${e2} arguments required, but only ${a.length} present.`);
};
var FormData = class {
  #d = [];
  constructor(...a) {
    if (a.length) throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.");
  }
  get [t]() {
    return "FormData";
  }
  [i]() {
    return this.entries();
  }
  static [h](o) {
    return o && typeof o == "object" && o[t] === "FormData" && !m.some((m2) => typeof o[m2] != "function");
  }
  append(...a) {
    x("append", arguments, 2), this.#d.push(f(...a));
  }
  delete(a) {
    x("delete", arguments, 1), a += "", this.#d = this.#d.filter(([b]) => b !== a);
  }
  get(a) {
    x("get", arguments, 1), a += "";
    for (var b = this.#d, l = b.length, c = 0; c < l; c++) if (b[c][0] === a) return b[c][1];
    return null;
  }
  getAll(a, b) {
    return x("getAll", arguments, 1), b = [], a += "", this.#d.forEach((c) => c[0] === a && b.push(c[1])), b;
  }
  has(a) {
    return x("has", arguments, 1), a += "", this.#d.some((b) => b[0] === a);
  }
  forEach(a, b) {
    x("forEach", arguments, 1);
    for (var [c, d] of this) a.call(b, d, c, this);
  }
  set(...a) {
    x("set", arguments, 2);
    var b = [], c = !0;
    a = f(...a), this.#d.forEach((d) => {
      d[0] === a[0] ? c && (c = !b.push(a)) : b.push(d);
    }), c && b.push(a), this.#d = b;
  }
  *entries() {
    yield* this.#d;
  }
  *keys() {
    for (var [a] of this) yield a;
  }
  *values() {
    for (var [, a] of this) yield a;
  }
};
function formDataToBlob(F, B = fetch_blob_default) {
  var b = `${r()}${r()}`.replace(/\./g, "").slice(-28).padStart(32, "-"), c = [], p = `--${b}\r
Content-Disposition: form-data; name="`;
  return F.forEach((v, n) => typeof v == "string" ? c.push(p + e(n) + `"\r
\r
${v.replace(/\r(?!\n)|(?<!\r)\n/g, `\r
`)}\r
`) : c.push(p + e(n) + `"; filename="${e(v.name, 1)}"\r
Content-Type: ${v.type || "application/octet-stream"}\r
\r
`, v, `\r
`)), c.push(`--${b}--`), new B(c, { type: "multipart/form-data; boundary=" + b });
}

// ../../node_modules/.pnpm/fetch-blob@3.2.0/node_modules/fetch-blob/from.js
init_cjs_shims();
import { statSync, createReadStream, promises as fs } from "node:fs";
var import_node_domexception = __toESM(require_node_domexception(), 1);
var { stat } = fs;

export {
  fetch_blob_default,
  file_default,
  FormData,
  formDataToBlob
};
/*! Bundled license information:

web-streams-polyfill/dist/ponyfill.es2018.js:
  (**
   * @license
   * web-streams-polyfill v3.3.3
   * Copyright 2024 Mattias Buelens, Diwank Singh Tomer and other contributors.
   * This code is released under the MIT license.
   * SPDX-License-Identifier: MIT
   *)

node-domexception/index.js:
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)

fetch-blob/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)

formdata-polyfill/esm.min.js:
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
//# sourceMappingURL=chunk-25IMI7TH.js.map
