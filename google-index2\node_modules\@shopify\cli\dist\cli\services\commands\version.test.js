import {
  versionService
} from "../../../chunk-2IEQZDJI.js";
import {
  mockAndCaptureOutput
} from "../../../chunk-DJVJNSKZ.js";
import {
  afterEach,
  describe,
  globalExpect,
  test,
  vi
} from "../../../chunk-BQ3PZIHZ.js";
import "../../../chunk-WRIQTRQE.js";
import "../../../chunk-B36FYNEM.js";
import "../../../chunk-F7F4BQYW.js";
import "../../../chunk-UMUTXITN.js";
import "../../../chunk-UATXMR5F.js";
import "../../../chunk-B5EXYCV3.js";
import "../../../chunk-G2ZZKGSV.js";
import "../../../chunk-75LV6AQS.js";
import "../../../chunk-UV5N2VL7.js";
import "../../../chunk-XE5EOEBL.js";
import "../../../chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "../../../chunk-PKR7KJ6P.js";

// src/cli/services/commands/version.test.ts
init_cjs_shims();
vi.mock("@shopify/cli-kit/node/node-package-manager");
vi.mock("@shopify/cli-kit/common/version", () => ({ CLI_KIT_VERSION: "2.2.2" }));
afterEach(() => {
  mockAndCaptureOutput().clear();
});
describe("check CLI version", () => {
  test("displays latest version", async () => {
    let outputMock = mockAndCaptureOutput();
    await versionService(), globalExpect(outputMock.info()).toMatchInlineSnapshot(`
        "2.2.2"
      `);
  });
});
//# sourceMappingURL=version.test.js.map
