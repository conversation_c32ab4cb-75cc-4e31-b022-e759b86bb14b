import {
  list
} from "./chunk-ZXKNRG53.js";
import {
  base_command_default
} from "./chunk-2IA24ROR.js";
import {
  sendErrorToBugsnag
} from "./chunk-VLSFD7SJ.js";
import {
  require_lib
} from "./chunk-F7F4BQYW.js";
import {
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/commands/notifications/list.ts
init_cjs_shims();
var import_core = __toESM(require_lib(), 1);
var List = class _List extends base_command_default {
  static {
    this.description = "List current notifications configured for the CLI.";
  }
  static {
    this.hidden = !0;
  }
  static {
    this.flags = {
      "ignore-errors": import_core.Flags.boolean({
        hidden: !1,
        description: "Don't fail if an error occurs.",
        env: "SHOPIFY_FLAG_IGNORE_ERRORS"
      })
    };
  }
  async run() {
    let { flags } = await this.parse(_List);
    try {
      await list();
    } catch (error) {
      let message = "Error fetching notifications";
      if (error instanceof Error && (message = message.concat(`: ${error.message}`)), await sendErrorToBugsnag(message, "expected_error"), !flags["ignore-errors"])
        throw error;
    }
  }
};

export {
  List
};
//# sourceMappingURL=chunk-Y7JIZJYE.js.map
