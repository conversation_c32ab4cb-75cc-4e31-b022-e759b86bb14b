import {
  require_normalize_path
} from "./chunk-X7YTIMNN.js";
import {
  require_picomatch
} from "./chunk-7IK72W75.js";
import {
  __commonJS,
  __require,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/readdirp@3.6.0/node_modules/readdirp/index.js
var require_readdirp = __commonJS({
  "../../node_modules/.pnpm/readdirp@3.6.0/node_modules/readdirp/index.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var fs = __require("fs"), { Readable } = __require("stream"), sysPath = __require("path"), { promisify } = __require("util"), picomatch = require_picomatch(), readdir = promisify(fs.readdir), stat = promisify(fs.stat), lstat = promisify(fs.lstat), realpath = promisify(fs.realpath), BANG = "!", RECURSIVE_ERROR_CODE = "READDIRP_RECURSIVE_ERROR", NORMAL_FLOW_ERRORS = /* @__PURE__ */ new Set(["ENOENT", "EPERM", "EACCES", "ELOOP", RECURSIVE_ERROR_CODE]), FILE_TYPE = "files", DIR_TYPE = "directories", FILE_DIR_TYPE = "files_directories", EVERYTHING_TYPE = "all", ALL_TYPES = [FILE_TYPE, DIR_TYPE, FILE_DIR_TYPE, EVERYTHING_TYPE], isNormalFlowError = (error) => NORMAL_FLOW_ERRORS.has(error.code), [maj, min] = process.versions.node.split(".").slice(0, 2).map((n) => Number.parseInt(n, 10)), wantBigintFsStats = process.platform === "win32" && (maj > 10 || maj === 10 && min >= 5), normalizeFilter = (filter) => {
      if (filter !== void 0) {
        if (typeof filter == "function") return filter;
        if (typeof filter == "string") {
          let glob = picomatch(filter.trim());
          return (entry) => glob(entry.basename);
        }
        if (Array.isArray(filter)) {
          let positive = [], negative = [];
          for (let item of filter) {
            let trimmed = item.trim();
            trimmed.charAt(0) === BANG ? negative.push(picomatch(trimmed.slice(1))) : positive.push(picomatch(trimmed));
          }
          return negative.length > 0 ? positive.length > 0 ? (entry) => positive.some((f) => f(entry.basename)) && !negative.some((f) => f(entry.basename)) : (entry) => !negative.some((f) => f(entry.basename)) : (entry) => positive.some((f) => f(entry.basename));
        }
      }
    }, ReaddirpStream = class _ReaddirpStream extends Readable {
      static get defaultOptions() {
        return {
          root: ".",
          /* eslint-disable no-unused-vars */
          fileFilter: (path) => !0,
          directoryFilter: (path) => !0,
          /* eslint-enable no-unused-vars */
          type: FILE_TYPE,
          lstat: !1,
          depth: 2147483648,
          alwaysStat: !1
        };
      }
      constructor(options = {}) {
        super({
          objectMode: !0,
          autoDestroy: !0,
          highWaterMark: options.highWaterMark || 4096
        });
        let opts = { ..._ReaddirpStream.defaultOptions, ...options }, { root, type } = opts;
        this._fileFilter = normalizeFilter(opts.fileFilter), this._directoryFilter = normalizeFilter(opts.directoryFilter);
        let statMethod = opts.lstat ? lstat : stat;
        wantBigintFsStats ? this._stat = (path) => statMethod(path, { bigint: !0 }) : this._stat = statMethod, this._maxDepth = opts.depth, this._wantsDir = [DIR_TYPE, FILE_DIR_TYPE, EVERYTHING_TYPE].includes(type), this._wantsFile = [FILE_TYPE, FILE_DIR_TYPE, EVERYTHING_TYPE].includes(type), this._wantsEverything = type === EVERYTHING_TYPE, this._root = sysPath.resolve(root), this._isDirent = "Dirent" in fs && !opts.alwaysStat, this._statsProp = this._isDirent ? "dirent" : "stats", this._rdOptions = { encoding: "utf8", withFileTypes: this._isDirent }, this.parents = [this._exploreDir(root, 1)], this.reading = !1, this.parent = void 0;
      }
      async _read(batch) {
        if (!this.reading) {
          this.reading = !0;
          try {
            for (; !this.destroyed && batch > 0; ) {
              let { path, depth, files = [] } = this.parent || {};
              if (files.length > 0) {
                let slice = files.splice(0, batch).map((dirent) => this._formatEntry(dirent, path));
                for (let entry of await Promise.all(slice)) {
                  if (this.destroyed) return;
                  let entryType = await this._getEntryType(entry);
                  entryType === "directory" && this._directoryFilter(entry) ? (depth <= this._maxDepth && this.parents.push(this._exploreDir(entry.fullPath, depth + 1)), this._wantsDir && (this.push(entry), batch--)) : (entryType === "file" || this._includeAsFile(entry)) && this._fileFilter(entry) && this._wantsFile && (this.push(entry), batch--);
                }
              } else {
                let parent = this.parents.pop();
                if (!parent) {
                  this.push(null);
                  break;
                }
                if (this.parent = await parent, this.destroyed) return;
              }
            }
          } catch (error) {
            this.destroy(error);
          } finally {
            this.reading = !1;
          }
        }
      }
      async _exploreDir(path, depth) {
        let files;
        try {
          files = await readdir(path, this._rdOptions);
        } catch (error) {
          this._onError(error);
        }
        return { files, depth, path };
      }
      async _formatEntry(dirent, path) {
        let entry;
        try {
          let basename = this._isDirent ? dirent.name : dirent, fullPath = sysPath.resolve(sysPath.join(path, basename));
          entry = { path: sysPath.relative(this._root, fullPath), fullPath, basename }, entry[this._statsProp] = this._isDirent ? dirent : await this._stat(fullPath);
        } catch (err) {
          this._onError(err);
        }
        return entry;
      }
      _onError(err) {
        isNormalFlowError(err) && !this.destroyed ? this.emit("warn", err) : this.destroy(err);
      }
      async _getEntryType(entry) {
        let stats = entry && entry[this._statsProp];
        if (stats) {
          if (stats.isFile())
            return "file";
          if (stats.isDirectory())
            return "directory";
          if (stats && stats.isSymbolicLink()) {
            let full = entry.fullPath;
            try {
              let entryRealPath = await realpath(full), entryRealPathStats = await lstat(entryRealPath);
              if (entryRealPathStats.isFile())
                return "file";
              if (entryRealPathStats.isDirectory()) {
                let len = entryRealPath.length;
                if (full.startsWith(entryRealPath) && full.substr(len, 1) === sysPath.sep) {
                  let recursiveError = new Error(
                    `Circular symlink detected: "${full}" points to "${entryRealPath}"`
                  );
                  return recursiveError.code = RECURSIVE_ERROR_CODE, this._onError(recursiveError);
                }
                return "directory";
              }
            } catch (error) {
              this._onError(error);
            }
          }
        }
      }
      _includeAsFile(entry) {
        let stats = entry && entry[this._statsProp];
        return stats && this._wantsEverything && !stats.isDirectory();
      }
    }, readdirp = (root, options = {}) => {
      let type = options.entryType || options.type;
      if (type === "both" && (type = FILE_DIR_TYPE), type && (options.type = type), root) {
        if (typeof root != "string")
          throw new TypeError("readdirp: root argument must be a string. Usage: readdirp(root, options)");
        if (type && !ALL_TYPES.includes(type))
          throw new Error(`readdirp: Invalid type passed. Use one of ${ALL_TYPES.join(", ")}`);
      } else throw new Error("readdirp: root argument is required. Usage: readdirp(root, options)");
      return options.root = root, new ReaddirpStream(options);
    }, readdirpPromise = (root, options = {}) => new Promise((resolve, reject) => {
      let files = [];
      readdirp(root, options).on("data", (entry) => files.push(entry)).on("end", () => resolve(files)).on("error", (error) => reject(error));
    });
    readdirp.promise = readdirpPromise;
    readdirp.ReaddirpStream = ReaddirpStream;
    readdirp.default = readdirp;
    module.exports = readdirp;
  }
});

// ../../node_modules/.pnpm/anymatch@3.1.3/node_modules/anymatch/index.js
var require_anymatch = __commonJS({
  "../../node_modules/.pnpm/anymatch@3.1.3/node_modules/anymatch/index.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    Object.defineProperty(exports, "__esModule", { value: !0 });
    var picomatch = require_picomatch(), normalizePath = require_normalize_path(), BANG = "!", DEFAULT_OPTIONS = { returnIndex: !1 }, arrify = (item) => Array.isArray(item) ? item : [item], createPattern = (matcher, options) => {
      if (typeof matcher == "function")
        return matcher;
      if (typeof matcher == "string") {
        let glob = picomatch(matcher, options);
        return (string) => matcher === string || glob(string);
      }
      return matcher instanceof RegExp ? (string) => matcher.test(string) : (string) => !1;
    }, matchPatterns = (patterns, negPatterns, args, returnIndex) => {
      let isList = Array.isArray(args), _path = isList ? args[0] : args;
      if (!isList && typeof _path != "string")
        throw new TypeError("anymatch: second argument must be a string: got " + Object.prototype.toString.call(_path));
      let path = normalizePath(_path, !1);
      for (let index = 0; index < negPatterns.length; index++) {
        let nglob = negPatterns[index];
        if (nglob(path))
          return returnIndex ? -1 : !1;
      }
      let applied = isList && [path].concat(args.slice(1));
      for (let index = 0; index < patterns.length; index++) {
        let pattern = patterns[index];
        if (isList ? pattern(...applied) : pattern(path))
          return returnIndex ? index : !0;
      }
      return returnIndex ? -1 : !1;
    }, anymatch = (matchers, testString, options = DEFAULT_OPTIONS) => {
      if (matchers == null)
        throw new TypeError("anymatch: specify first argument");
      let opts = typeof options == "boolean" ? { returnIndex: options } : options, returnIndex = opts.returnIndex || !1, mtchers = arrify(matchers), negatedGlobs = mtchers.filter((item) => typeof item == "string" && item.charAt(0) === BANG).map((item) => item.slice(1)).map((item) => picomatch(item, opts)), patterns = mtchers.filter((item) => typeof item != "string" || typeof item == "string" && item.charAt(0) !== BANG).map((matcher) => createPattern(matcher, opts));
      return testString == null ? (testString2, ri = !1) => matchPatterns(patterns, negatedGlobs, testString2, typeof ri == "boolean" ? ri : !1) : matchPatterns(patterns, negatedGlobs, testString, returnIndex);
    };
    anymatch.default = anymatch;
    module.exports = anymatch;
  }
});

// ../../node_modules/.pnpm/binary-extensions@2.3.0/node_modules/binary-extensions/binary-extensions.json
var require_binary_extensions = __commonJS({
  "../../node_modules/.pnpm/binary-extensions@2.3.0/node_modules/binary-extensions/binary-extensions.json"(exports, module) {
    module.exports = [
      "3dm",
      "3ds",
      "3g2",
      "3gp",
      "7z",
      "a",
      "aac",
      "adp",
      "afdesign",
      "afphoto",
      "afpub",
      "ai",
      "aif",
      "aiff",
      "alz",
      "ape",
      "apk",
      "appimage",
      "ar",
      "arj",
      "asf",
      "au",
      "avi",
      "bak",
      "baml",
      "bh",
      "bin",
      "bk",
      "bmp",
      "btif",
      "bz2",
      "bzip2",
      "cab",
      "caf",
      "cgm",
      "class",
      "cmx",
      "cpio",
      "cr2",
      "cur",
      "dat",
      "dcm",
      "deb",
      "dex",
      "djvu",
      "dll",
      "dmg",
      "dng",
      "doc",
      "docm",
      "docx",
      "dot",
      "dotm",
      "dra",
      "DS_Store",
      "dsk",
      "dts",
      "dtshd",
      "dvb",
      "dwg",
      "dxf",
      "ecelp4800",
      "ecelp7470",
      "ecelp9600",
      "egg",
      "eol",
      "eot",
      "epub",
      "exe",
      "f4v",
      "fbs",
      "fh",
      "fla",
      "flac",
      "flatpak",
      "fli",
      "flv",
      "fpx",
      "fst",
      "fvt",
      "g3",
      "gh",
      "gif",
      "graffle",
      "gz",
      "gzip",
      "h261",
      "h263",
      "h264",
      "icns",
      "ico",
      "ief",
      "img",
      "ipa",
      "iso",
      "jar",
      "jpeg",
      "jpg",
      "jpgv",
      "jpm",
      "jxr",
      "key",
      "ktx",
      "lha",
      "lib",
      "lvp",
      "lz",
      "lzh",
      "lzma",
      "lzo",
      "m3u",
      "m4a",
      "m4v",
      "mar",
      "mdi",
      "mht",
      "mid",
      "midi",
      "mj2",
      "mka",
      "mkv",
      "mmr",
      "mng",
      "mobi",
      "mov",
      "movie",
      "mp3",
      "mp4",
      "mp4a",
      "mpeg",
      "mpg",
      "mpga",
      "mxu",
      "nef",
      "npx",
      "numbers",
      "nupkg",
      "o",
      "odp",
      "ods",
      "odt",
      "oga",
      "ogg",
      "ogv",
      "otf",
      "ott",
      "pages",
      "pbm",
      "pcx",
      "pdb",
      "pdf",
      "pea",
      "pgm",
      "pic",
      "png",
      "pnm",
      "pot",
      "potm",
      "potx",
      "ppa",
      "ppam",
      "ppm",
      "pps",
      "ppsm",
      "ppsx",
      "ppt",
      "pptm",
      "pptx",
      "psd",
      "pya",
      "pyc",
      "pyo",
      "pyv",
      "qt",
      "rar",
      "ras",
      "raw",
      "resources",
      "rgb",
      "rip",
      "rlc",
      "rmf",
      "rmvb",
      "rpm",
      "rtf",
      "rz",
      "s3m",
      "s7z",
      "scpt",
      "sgi",
      "shar",
      "snap",
      "sil",
      "sketch",
      "slk",
      "smv",
      "snk",
      "so",
      "stl",
      "suo",
      "sub",
      "swf",
      "tar",
      "tbz",
      "tbz2",
      "tga",
      "tgz",
      "thmx",
      "tif",
      "tiff",
      "tlz",
      "ttc",
      "ttf",
      "txz",
      "udf",
      "uvh",
      "uvi",
      "uvm",
      "uvp",
      "uvs",
      "uvu",
      "viv",
      "vob",
      "war",
      "wav",
      "wax",
      "wbmp",
      "wdp",
      "weba",
      "webm",
      "webp",
      "whl",
      "wim",
      "wm",
      "wma",
      "wmv",
      "wmx",
      "woff",
      "woff2",
      "wrm",
      "wvx",
      "xbm",
      "xif",
      "xla",
      "xlam",
      "xls",
      "xlsb",
      "xlsm",
      "xlsx",
      "xlt",
      "xltm",
      "xltx",
      "xm",
      "xmind",
      "xpi",
      "xpm",
      "xwd",
      "xz",
      "z",
      "zip",
      "zipx"
    ];
  }
});

// ../../node_modules/.pnpm/binary-extensions@2.3.0/node_modules/binary-extensions/index.js
var require_binary_extensions2 = __commonJS({
  "../../node_modules/.pnpm/binary-extensions@2.3.0/node_modules/binary-extensions/index.js"(exports, module) {
    init_cjs_shims();
    module.exports = require_binary_extensions();
  }
});

// ../../node_modules/.pnpm/is-binary-path@2.1.0/node_modules/is-binary-path/index.js
var require_is_binary_path = __commonJS({
  "../../node_modules/.pnpm/is-binary-path@2.1.0/node_modules/is-binary-path/index.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var path = __require("path"), binaryExtensions = require_binary_extensions2(), extensions = new Set(binaryExtensions);
    module.exports = (filePath) => extensions.has(path.extname(filePath).slice(1).toLowerCase());
  }
});

export {
  require_readdirp,
  require_anymatch,
  require_is_binary_path
};
//# sourceMappingURL=chunk-F7J5CUMZ.js.map
