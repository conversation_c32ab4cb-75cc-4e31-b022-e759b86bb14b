import {
  Banner, Text, HorizontalGrid, Button, AlphaCard,
  VerticalStack, Icon, Tooltip
} from '@shopify/polaris';
import {InfoMinor, ClockMajor} from '@shopify/polaris-icons';
import {useNavigate} from "react-router-dom";
import React from 'react';

export default function DashboardHeader({ 
  shopConfig, 
  consoleMetrics, 
  currentTab,
  onBillApp,
  onShowOAuthModal,
  onShowCloudAccessModal,
  onShowWebsiteAccessModal
}) {
  const navigate = useNavigate();
  
  // 导航函数
  const navigateToTab = (tabName) => {
    if (tabName === currentTab) return; // 如果已经在当前页面，不做任何操作
    
    switch(tabName) {
      case 'all':
        navigate('/');
        break;
      case 'indexed':
        navigate('/IndexedPage');
        break;
      case 'nonindexed':
        navigate('/NonIndexPage');
        break;
      default:
        break;
    }
  };

  // 渲染付款banner
  const renderBillingBanner = () => {
    const billPlan = shopConfig.billPlan || 'Free';
    
    if (billPlan === 'Free') {
      return (
        <Banner status="info">
          <HorizontalGrid columns={"10fr 3fr"} gap="4">
            <p>
You are using the demo version. Upgrade to unlock all features and remove limitations.<strong>First 100 users: $1.99/month.</strong> 5-day free trial for first-timers.
            </p>
            
            <Button primary onClick={onBillApp}>Upgrade Now! </Button>
          </HorizontalGrid>
        </Banner>
      );
    }
    return null;
  };

  // 根据remainIndexToday值渲染状态banner
  const renderStatusBanner = () => {
    const remainIndexToday = shopConfig.remainIndexToday;
    
    if (remainIndexToday === -666) {
      // Banner1: 没有OauthToken
      return (
        <Banner status="critical">
          <HorizontalGrid columns={"10fr 2fr"} gap="2">
            <p>
              <strong>Config Step1:</strong> Google OAuth authentication required. Please authenticate with Google to enable indexing services.
            </p>
            <Button onClick={onShowOAuthModal || (() => navigate('/'))}>Authenticate with Google</Button>
          </HorizontalGrid>
        </Banner>
      );
    } else if (remainIndexToday === -777) {
      // Banner2: 没有修改云的权限
      return (
        <Banner status="critical">
          <HorizontalGrid columns={"10fr 3fr"} gap="4">
            <p>
              <strong>Config Step2:</strong> Google Cloud agreement enforcement required. Please agree to the Google Cloud service agreement to continue.
            </p>
            <Button primary onClick={onShowCloudAccessModal || (() => navigate('/'))}>Accept Cloud Agreement</Button>
          </HorizontalGrid>
        </Banner>
      );
    } else if (remainIndexToday === -888) {
      // Banner3: 系统错误
      return (
        <Banner status="critical">
          <HorizontalGrid columns={"10fr 3fr"} gap="4">
            <p>
              System error occurred. Please contact support or try refreshing the page.
            </p>
            <Button primary onClick={() => window.location.reload()}>Refresh Page</Button>
          </HorizontalGrid>
        </Banner>
      );
    } else if (remainIndexToday === -999) {
      // Banner4: 没有网站权限
      return (
        <Banner status="critical">
          <HorizontalGrid columns={"10fr 3fr"} gap="4">
            <p>
              <strong>Config Step3:</strong> Website access permission required. Please grant access to your website in Google Search Console.
            </p>
            <Button primary onClick={onShowWebsiteAccessModal || (() => navigate('/'))}>Grant Website Access</Button>
          </HorizontalGrid>
        </Banner>
      );
    }

    return null;
  };

  return (
    <VerticalStack gap={{ xs: "8", sm: "4" }}>
      {/* 渲染付款banner */}
      {renderBillingBanner()}
      
      {/* 渲染状态banner */}
      {renderStatusBanner()}
      
      {/* 卡片区域 */}
      <HorizontalGrid columns={"1fr 1fr 1fr 1fr"} gap="5">
        <AlphaCard background={currentTab === 'all' ? "bg-surface-secondary" : undefined}>
          <div onClick={() => navigateToTab('all')} style={{ width: '100%', height: '100%', cursor: 'pointer' }}>
            <VerticalStack gap={{ xs: "8", sm: "4" }}>
              <Tooltip content="Pages that have not been requested for indexing">
                <div className="Polaris-Tooltip__TooltipContainer" style={{ borderBottom: '3px dotted var(--p-color-border)', display: 'inline-block' }}>
                  <Text variant="bodyMd" as="p" fontWeight="medium" alignment="center" color="subdued">
                    Un-Requested
                  </Text>
                </div>
              </Tooltip>
              <Text variant="headingXl" as="h4" alignment="left">{consoleMetrics.unIndexed}</Text>
            </VerticalStack>
          </div>
        </AlphaCard>
        <AlphaCard background={currentTab === 'indexed' ? "bg-surface-success-subdued" : undefined}>
          <div onClick={() => navigateToTab('indexed')} style={{ width: '100%', height: '100%', cursor: 'pointer' }}>
            <VerticalStack gap={{ xs: "8", sm: "4" }}>
              <Tooltip content="Pages that have been successfully requested for indexing">
                <div className="Polaris-Tooltip__TooltipContainer" style={{ borderBottom: '3px dotted var(--p-color-border)', display: 'inline-block' }}>
                  <Text variant="bodyMd" as="p" fontWeight="medium" alignment="center" color="subdued">
                    Requested
                  </Text>
                </div>
              </Tooltip>
              <Text variant="headingXl" as="h4" color="success" alignment="left">{consoleMetrics.indexedC}</Text>
            </VerticalStack>
          </div>
        </AlphaCard>
        <AlphaCard background={currentTab === 'nonindexed' ? "bg-surface-critical-subdued" : undefined}>
          <div onClick={() => navigateToTab('nonindexed')} style={{ width: '100%', height: '100%', cursor: 'pointer' }}>
            <VerticalStack gap={{ xs: "8", sm: "4" }}>
              <Tooltip content="Pages that blocked to be auto indexed">
                <div className="Polaris-Tooltip__TooltipContainer" style={{ borderBottom: '3px dotted var(--p-color-border)', display: 'inline-block' }}>
                  <Text variant="bodyMd" as="p" fontWeight="medium" alignment="center" color="subdued">
                    Non-Indexed
                  </Text>
                </div>
              </Tooltip>
              <Text variant="headingXl" as="h4" alignment="left" color="critical">{consoleMetrics.noindexedC}</Text>
            </VerticalStack>
          </div>
        </AlphaCard>
        <AlphaCard>
          <VerticalStack gap={{ xs: "8", sm: "4" }}>
            <Tooltip content="This value represents the number of indexing requests (submit or delete) you can make today. The quota resets daily.">
              <div className="Polaris-Tooltip__TooltipContainer" style={{ borderBottom: '3px dotted var(--p-color-border)', display: 'inline-block' }}>
                <Text variant="bodyMd" as="p" fontWeight="medium" alignment="left" color="subdued">
                  Remaining Request for Today
                </Text>
              </div>
            </Tooltip>
            <Text variant="headingXl" as="h4" alignment="left" color="highlight">
              {shopConfig.remainIndexToday < 0 ? 0 : shopConfig.remainIndexToday}
            </Text>
          </VerticalStack>
        </AlphaCard>
      </HorizontalGrid>
    </VerticalStack>
  );
}







