import {
  base_command_default
} from "./chunk-2IA24ROR.js";
import {
  mkdir,
  outputInfo,
  rmdir,
  writeFile
} from "./chunk-B36FYNEM.js";
import {
  cwd,
  joinPath
} from "./chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/commands/docs/generate.ts
init_cjs_shims();
var docsPath = joinPath(cwd(), "/docs-shopify.dev/commands"), DocsGenerate = class extends base_command_default {
  static {
    this.description = "Generate CLI commands documentation";
  }
  static {
    this.hidden = !0;
  }
  async run() {
    let commands = this.config.commands;
    await rmdir(docsPath), await mkdir(docsPath);
    let promises = commands.sort((ca, cb) => ca.id.length - cb.id.length).filter((command) => !isHidden(command)).flatMap((command) => {
      let commandData = extractCommandData(command);
      return [
        writeCommandFlagInterface(command, commandData),
        writeCommandUsageExampleFile(command, commandData),
        writeCommandDocumentation(command, commandData)
      ];
    });
    await Promise.all(promises);
  }
}, hiddenTopics = ["commands", "help", "plugins"], generalTopics = ["config", "auth"];
function isHidden(command) {
  return command.hidden ? (hiddenTopics.push(command.id), !0) : command.pluginType === "user" ? !0 : hiddenTopics.some((topic) => command.id.startsWith(`${topic}:`));
}
function extractCommandData(command) {
  let commandName = command.id.replace(/[:]/g, " "), fileName = command.id.replace(/[:]/g, "-"), interfaceName = command.id.replace(/[:-]/g, ""), hasTopic = command.id.includes(":"), topic = command.id.split(":")[0], hasFlags = command.flags && Object.keys(command.flags).length > 0;
  return { commandName, fileName, interfaceName, hasTopic, topic, hasFlags };
}
async function writeCommandDocumentation(command, { commandName, fileName, interfaceName, hasTopic, topic, hasFlags }) {
  let flagDoc = `
  {
    title: 'Flags',
    description: 'The following flags are available for the \`${commandName}\` command:',
    type: '${interfaceName}',
  },`, description = command.descriptionWithMarkdown ?? command.description ?? command.summary ?? "", cleanDescription = description?.replace(/`/g, "\\`").replace(/https:\/\/shopify\.dev/g, ""), cleanPreview = (command.summary ?? description ?? "").replace(/`/g, "\\`").replace(/https:\/\/shopify\.dev/g, ""), category = hasTopic && !generalTopics.includes(topic) ? topic : "general commands", updatedDocString = `// This is an autogenerated file. Don't edit this file manually.
import {ReferenceEntityTemplateSchema} from '@shopify/generate-docs'

const data: ReferenceEntityTemplateSchema = {
  name: '${commandName}',
  description: \`${cleanDescription}\`,
  overviewPreviewDescription: \`${cleanPreview}\`,
  type: 'command',
  isVisualComponent: false,
  defaultExample: {
    codeblock: {
      tabs: [
        {
          title: '${commandName}',
          code: './examples/${fileName}.example.sh',
          language: 'bash',
        },
      ],
      title: '${commandName}',
    },
  },
  definitions: [${hasFlags ? flagDoc : ""}
  ],
  category: '${category}',
  related: [
  ],
}

export default data`.replaceAll("<%= config.bin %>", "shopify");
  await writeFile(`${docsPath}/${fileName}.doc.ts`, updatedDocString), outputInfo(`Generated docs for ${commandName}`);
}
async function writeCommandFlagInterface(command, { fileName, interfaceName }) {
  let flagsDetails = Object.keys(command.flags).map((flagName) => {
    let flag = command.flags[flagName];
    if (!flag || flag.hidden) return;
    let flagDescription = flag.description || "", char = flag.char ? `-${flag.char}, ` : "", type = flag.type === "option" ? "string" : "''", value = flag.type === "option" ? " <value>" : "", optional = flag.required ? "" : "?", envValue = flag.env !== void 0 ? ` @environment ${flag.env}` : "";
    return `  /**
   * ${flagDescription}
   *${envValue}
   */
  '${char}--${flagName}${value}'${optional}: ${type}`;
  }).filter((str) => str && str?.length > 0).join(`

`), commandContent = `// This is an autogenerated file. Don't edit this file manually.
export interface ${interfaceName} {
${flagsDetails}
}
`;
  await mkdir(`${docsPath}/interfaces`), await writeFile(`${docsPath}/interfaces/${fileName}.interface.ts`, commandContent);
}
async function writeCommandUsageExampleFile(command, { fileName, commandName }) {
  let usage = "", hasFlags = command.flags && Object.keys(command.flags).length > 0;
  typeof command.usage == "string" ? usage = prependShopify(command.usage) : Array.isArray(command.usage) ? usage = command.usage.map((usage2) => prependShopify(usage2)).join(`

`) : usage = `${prependShopify(commandName)}${hasFlags ? " [flags]" : ""}`, await mkdir(`${docsPath}/examples`), await writeFile(`${docsPath}/examples/${fileName}.example.sh`, usage);
}
function prependShopify(command) {
  return command.startsWith("shopify ") ? command : `shopify ${command}`;
}

export {
  DocsGenerate,
  extractCommandData,
  writeCommandDocumentation,
  writeCommandFlagInterface,
  writeCommandUsageExampleFile
};
//# sourceMappingURL=chunk-4JN3JAPL.js.map
