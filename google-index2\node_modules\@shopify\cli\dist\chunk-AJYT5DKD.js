import {
  renderConcurrent,
  renderTasks
} from "./chunk-B36FYNEM.js";
import {
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/services/kitchen-sink/async.ts
init_cjs_shims();
async function asyncTasks() {
  let backendPromiseResolve, backendPromise = new Promise(function(resolve, _reject) {
    backendPromiseResolve = resolve;
  });
  await renderConcurrent({
    processes: [{
      prefix: "backend",
      action: async (stdout, _stderr, _signal) => {
        stdout.write("first backend message"), await new Promise((resolve) => setTimeout(resolve, 1e3)), stdout.write("second backend message"), await new Promise((resolve) => setTimeout(resolve, 1e3)), stdout.write("third backend message"), await new Promise((resolve) => setTimeout(resolve, 1e3)), backendPromiseResolve();
      }
    }, {
      prefix: "frontend",
      action: async (stdout, _stderr, _signal) => {
        await backendPromise, stdout.write("first frontend message"), await new Promise((resolve) => setTimeout(resolve, 1e3)), stdout.write("second frontend message"), await new Promise((resolve) => setTimeout(resolve, 1e3)), stdout.write("third frontend message");
      }
    }]
  }), await renderTasks([
    {
      title: "Installing dependencies",
      task: async () => {
        await new Promise((resolve) => setTimeout(resolve, 2e3));
      }
    },
    {
      title: "Downloading assets",
      task: async () => {
        await new Promise((resolve) => setTimeout(resolve, 2e3));
      }
    }
  ]);
}

export {
  asyncTasks
};
//# sourceMappingURL=chunk-AJYT5DKD.js.map
