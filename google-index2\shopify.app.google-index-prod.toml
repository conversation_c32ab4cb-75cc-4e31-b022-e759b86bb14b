# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "4d9ff006fed6eb64c69496ab15b01a22"
name = "JIndex"
handle = "JIndex"
application_url = "https://app.jindex.org"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "jeffs-test-url.myshopify.com"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_content,read_products,unauthenticated_read_content,unauthenticated_read_product_listings"

[auth]
redirect_urls = ["https://app.jindex.org/auth/callback", "https://app.jindex.org/auth/shopify/callback", "https://app.jindex.org/api/auth/callback"]

[webhooks]
api_version = "2025-07"

[pos]
embedded = false
