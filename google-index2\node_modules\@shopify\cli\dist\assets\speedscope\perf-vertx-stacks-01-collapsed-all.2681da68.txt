java;read;check_events_[k];hypercall_page_[k] 1
java;start_thread;java_start;GCTaskThread::run;ScavengeRootsTask::do_it;ClassLoaderDataGraph::oops_do;ClassLoaderData::oops_do;PSScavengeKlassClosure::do_klass 1
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;PSPromotionManager::drain_stacks_depth;oopDesc* PSPromotionManager::copy_to_survivor_space<false>;InstanceKlass::oop_push_contents 1
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;ParallelTaskTerminator::offer_termination 5
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 7
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/buffer/AbstractByteBufAllocator:.directBuffer_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/AbstractReferenceCountedByteBuf:.release_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];io/netty/buffer/PooledByteBuf:.internalNioBuffer_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/NativeThread:.current_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];  3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];Java_sun_nio_ch_FileDispatcherImpl_write0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;sys_write_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];fget_light_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];__srcu_read_lock_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];__tcp_push_pending_frames_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];ktime_get_real_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];skb_clone_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_set_skb_tso_segs_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_hard_start_xmit_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_pick_tx_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];dev_hard_start_xmit_[k];dev_queue_xmit_nit_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];netif_rx_[k];netif_rx.part.82_[k];xen_restore_fl_direct_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];netif_rx_[k];netif_rx.part.82_[k];xen_restore_fl_direct_end_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];dma_issue_pending_all_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];__inet_lookup_established_[k] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_event_data_recv_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];sock_def_readable_[k];__wake_up_sync_key_[k];check_events_[k];hypercall_page_[k] 19
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];bictcp_acked_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];ktime_get_real_[k];getnstimeofday_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];ktime_get_real_[k];getnstimeofday_[k];xen_clocksource_get_cycles_[k];xen_clocksource_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];tcp_rtt_estimator_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];tcp_valid_rtt_meas_[k];tcp_rtt_estimator_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_finish_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];rcu_bh_qs_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];netif_skb_features_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_output_[k] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ktime_get_real_[k];getnstimeofday_[k];xen_clocksource_get_cycles_[k];pvclock_clocksource_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ktime_get_real_[k];getnstimeofday_[k];xen_clocksource_get_cycles_[k];xen_clocksource_read_[k];pvclock_clocksource_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ktime_get_real_[k];xen_clocksource_get_cycles_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];skb_dst_set_noref_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];lock_sock_nested_[k];_raw_spin_lock_bh_[k];local_bh_disable_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];__kmalloc_node_track_caller_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];__kmalloc_node_track_caller_[k];arch_local_irq_save_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];__phys_addr_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];get_slab_[k] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];kmem_cache_alloc_node_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];ksize_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_send_mss_[k];tcp_current_mss_[k];ipv4_mtu_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_send_mss_[k];tcp_current_mss_[k];tcp_established_options_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_send_mss_[k];tcp_xmit_size_goal_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_xmit_size_goal_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];fsnotify_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];fsnotify_[k];__srcu_read_lock_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];rw_verify_area_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];rw_verify_area_[k];apparmor_file_permission_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];rw_verify_area_[k];security_file_permission_[k];apparmor_file_permission_[k];common_file_perm_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];sock_aio_write_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/SocketChannelImpl:.writerCleanup_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.writerCleanup_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/util/Recycler:.recycle_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/buffer/AbstractReferenceCountedByteBuf:.release_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];java/util/concurrent/ConcurrentHashMap:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/mozilla/javascript/Context:.getWrapFactory_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/ScriptableObject:.getParentScope_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/WrapFactory:.wrapAsJavaObject_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/WrapFactory:.wrapAsJavaObject_[j];java/util/HashMap:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/WrapFactory:.wrap_[j];java/util/HashMap:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j];org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j];vtable chunks_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];org/mozilla/javascript/ScriptableObject$Slot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.name_[j];org/mozilla/javascript/IdScriptableObject:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.has_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.setAttributes_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/MemberBox:.invoke_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/WrapFactory:.wrap_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.findFunction_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaObject:.get_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.setAttributes_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j];org/mozilla/javascript/ScriptableObject$Slot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis_[j];org/mozilla/javascript/NativeJavaObject:.get_[j];java/util/HashMap:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];jint_disjoint_arraycopy_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.get_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.setAttributes_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];vtable chunks_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/NativeFunction:.initScriptFunction_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 6
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptableObject:.getParentScope_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 4
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 5
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 6
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptableObject:.getPrototype_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptableObject:.getParentScope_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/TopLevel:.getBuiltinPrototype_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/ScriptRuntime:.name_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];vtable chunks_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.setAttributes_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/TopLevel:.getBuiltinPrototype_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/ScriptRuntime:.indexFromString_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/ScriptRuntime:.setObjectElem_[j];org/mozilla/javascript/ScriptRuntime:.indexFromString_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];io/netty/handler/codec/http/DefaultHttpHeaders:.set_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/buffer/AbstractByteBuf:.writeBytes_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/handler/codec/http/HttpObjectEncoder:.encode_[j];io/netty/buffer/AbstractByteBuf:.writeBytes_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/handler/codec/http/HttpObjectEncoder:.encode_[j];io/netty/buffer/AbstractByteBufAllocator:.directBuffer_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/handler/codec/http/HttpObjectEncoder:.encode_[j];io/netty/buffer/AbstractByteBufAllocator:.directBuffer_[j];io/netty/util/concurrent/FastThreadLocal:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/handler/codec/http/HttpObjectEncoder:.encode_[j];java/util/ArrayList:.add_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/util/internal/RecyclableArrayList:.newInstance_[j];io/netty/util/concurrent/FastThreadLocal:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];java/util/ArrayList:.ensureExplicitCapacity_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];vtable chunks_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/handler/codec/http/DefaultHttpHeaders:.add0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/handler/codec/http/DefaultHttpHeaders:.set_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];sun/nio/cs/UTF_8$Encoder:.<init>_[j];jbyte_disjoint_arraycopy_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.name_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/buffer/AbstractByteBuf:.forEachByteAsc0_[j];io/netty/util/internal/AppendableCharSequence:.append_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpHeaders:.isTransferEncodingChunked_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j];io/netty/buffer/AbstractByteBuf:.forEachByteAsc0_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j];io/netty/handler/codec/http/HttpHeaders:.hash_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j];io/netty/handler/codec/http/HttpObjectDecoder:.splitHeader_[j] 5
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j];java/util/Arrays:.fill_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];java/nio/channels/spi/AbstractInterruptibleChannel:.end_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;sys_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];do_sync_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];__kfree_skb_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_rcv_space_adjust_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];__kfree_skb_[k];skb_release_data_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];__kfree_skb_[k];skb_release_head_state_[k];dst_release_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];_raw_spin_lock_bh_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];skb_copy_datagram_iovec_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];skb_copy_datagram_iovec_[k];copy_user_enhanced_fast_string_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_cleanup_rbuf_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_cleanup_rbuf_[k];__tcp_select_window_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];rw_verify_area_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j] 1
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;PSIsAliveClosure::do_object_b 1
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;StringTable::unlink_or_oops_do 2
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;pthread_cond_signal@@GLIBC_2.3.2;system_call_fastpath_[k];sys_futex_[k];do_futex_[k];futex_wake_op_[k] 1
java;write;check_events_[k];hypercall_page_[k] 3
