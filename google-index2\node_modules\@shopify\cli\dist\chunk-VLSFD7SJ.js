import {
  require_once
} from "./chunk-SHWOPMLQ.js";
import {
  fanoutHooks,
  reportAnalyticsEvent
} from "./chunk-K2VBTQSL.js";
import {
  getEnvironmentData,
  isLocalEnvironment
} from "./chunk-ZAVXS5HH.js";
import {
  runWithRateLimit
} from "./chunk-G2VTHDI5.js";
import {
  CLI_KIT_VERSION
} from "./chunk-WRIQTRQE.js";
import {
  AbortSilentError,
  CancelExecution,
  bugsnagApiKey,
  cleanSingleStackTracePath,
  errorMapper,
  getAllPublicMetadata,
  getAllSensitiveMetadata,
  handler,
  outputDebug,
  outputInfo,
  reportingRateLimit,
  require_stacktracey,
  shouldReportErrorAsUnexpected
} from "./chunk-B36FYNEM.js";
import {
  require_lib
} from "./chunk-F7F4BQYW.js";
import {
  cwd,
  isAbsolutePath,
  joinPath,
  normalizePath,
  relativePath
} from "./chunk-EG6MBBEN.js";
import {
  __commonJS,
  __require,
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/stackframe@1.3.4/node_modules/stackframe/stackframe.js
var require_stackframe = __commonJS({
  "../../node_modules/.pnpm/stackframe@1.3.4/node_modules/stackframe/stackframe.js"(exports, module) {
    init_cjs_shims();
    (function(root, factory) {
      "use strict";
      typeof define == "function" && define.amd ? define("stackframe", [], factory) : typeof exports == "object" ? module.exports = factory() : root.StackFrame = factory();
    })(exports, function() {
      "use strict";
      function _isNumber(n) {
        return !isNaN(parseFloat(n)) && isFinite(n);
      }
      function _capitalize(str) {
        return str.charAt(0).toUpperCase() + str.substring(1);
      }
      function _getter(p) {
        return function() {
          return this[p];
        };
      }
      var booleanProps = ["isConstructor", "isEval", "isNative", "isToplevel"], numericProps = ["columnNumber", "lineNumber"], stringProps = ["fileName", "functionName", "source"], arrayProps = ["args"], objectProps = ["evalOrigin"], props = booleanProps.concat(numericProps, stringProps, arrayProps, objectProps);
      function StackFrame(obj) {
        if (obj)
          for (var i2 = 0; i2 < props.length; i2++)
            obj[props[i2]] !== void 0 && this["set" + _capitalize(props[i2])](obj[props[i2]]);
      }
      StackFrame.prototype = {
        getArgs: function() {
          return this.args;
        },
        setArgs: function(v) {
          if (Object.prototype.toString.call(v) !== "[object Array]")
            throw new TypeError("Args must be an Array");
          this.args = v;
        },
        getEvalOrigin: function() {
          return this.evalOrigin;
        },
        setEvalOrigin: function(v) {
          if (v instanceof StackFrame)
            this.evalOrigin = v;
          else if (v instanceof Object)
            this.evalOrigin = new StackFrame(v);
          else
            throw new TypeError("Eval Origin must be an Object or StackFrame");
        },
        toString: function() {
          var fileName = this.getFileName() || "", lineNumber = this.getLineNumber() || "", columnNumber = this.getColumnNumber() || "", functionName = this.getFunctionName() || "";
          return this.getIsEval() ? fileName ? "[eval] (" + fileName + ":" + lineNumber + ":" + columnNumber + ")" : "[eval]:" + lineNumber + ":" + columnNumber : functionName ? functionName + " (" + fileName + ":" + lineNumber + ":" + columnNumber + ")" : fileName + ":" + lineNumber + ":" + columnNumber;
        }
      }, StackFrame.fromString = function(str) {
        var argsStartIndex = str.indexOf("("), argsEndIndex = str.lastIndexOf(")"), functionName = str.substring(0, argsStartIndex), args = str.substring(argsStartIndex + 1, argsEndIndex).split(","), locationString = str.substring(argsEndIndex + 1);
        if (locationString.indexOf("@") === 0)
          var parts = /@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(locationString, ""), fileName = parts[1], lineNumber = parts[2], columnNumber = parts[3];
        return new StackFrame({
          functionName,
          args: args || void 0,
          fileName,
          lineNumber: lineNumber || void 0,
          columnNumber: columnNumber || void 0
        });
      };
      for (var i = 0; i < booleanProps.length; i++)
        StackFrame.prototype["get" + _capitalize(booleanProps[i])] = _getter(booleanProps[i]), StackFrame.prototype["set" + _capitalize(booleanProps[i])] = /* @__PURE__ */ function(p) {
          return function(v) {
            this[p] = !!v;
          };
        }(booleanProps[i]);
      for (var j = 0; j < numericProps.length; j++)
        StackFrame.prototype["get" + _capitalize(numericProps[j])] = _getter(numericProps[j]), StackFrame.prototype["set" + _capitalize(numericProps[j])] = /* @__PURE__ */ function(p) {
          return function(v) {
            if (!_isNumber(v))
              throw new TypeError(p + " must be a Number");
            this[p] = Number(v);
          };
        }(numericProps[j]);
      for (var k = 0; k < stringProps.length; k++)
        StackFrame.prototype["get" + _capitalize(stringProps[k])] = _getter(stringProps[k]), StackFrame.prototype["set" + _capitalize(stringProps[k])] = /* @__PURE__ */ function(p) {
          return function(v) {
            this[p] = String(v);
          };
        }(stringProps[k]);
      return StackFrame;
    });
  }
});

// ../../node_modules/.pnpm/error-stack-parser@2.1.4/node_modules/error-stack-parser/error-stack-parser.js
var require_error_stack_parser = __commonJS({
  "../../node_modules/.pnpm/error-stack-parser@2.1.4/node_modules/error-stack-parser/error-stack-parser.js"(exports, module) {
    init_cjs_shims();
    (function(root, factory) {
      "use strict";
      typeof define == "function" && define.amd ? define("error-stack-parser", ["stackframe"], factory) : typeof exports == "object" ? module.exports = factory(require_stackframe()) : root.ErrorStackParser = factory(root.StackFrame);
    })(exports, function(StackFrame) {
      "use strict";
      var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\S+:\d+/, CHROME_IE_STACK_REGEXP = /^\s*at .*(\S+:\d+|\(native\))/m, SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\[native code])?$/;
      return {
        /**
         * Given an Error object, extract the most information from it.
         *
         * @param {Error} error object
         * @return {Array} of StackFrames
         */
        parse: function(error) {
          if (typeof error.stacktrace < "u" || typeof error["opera#sourceloc"] < "u")
            return this.parseOpera(error);
          if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP))
            return this.parseV8OrIE(error);
          if (error.stack)
            return this.parseFFOrSafari(error);
          throw new Error("Cannot parse given Error object");
        },
        // Separate line and column numbers from a string of the form: (URI:Line:Column)
        extractLocation: function(urlLike) {
          if (urlLike.indexOf(":") === -1)
            return [urlLike];
          var regExp = /(.+?)(?::(\d+))?(?::(\d+))?$/, parts = regExp.exec(urlLike.replace(/[()]/g, ""));
          return [parts[1], parts[2] || void 0, parts[3] || void 0];
        },
        parseV8OrIE: function(error) {
          var filtered = error.stack.split(`
`).filter(function(line) {
            return !!line.match(CHROME_IE_STACK_REGEXP);
          }, this);
          return filtered.map(function(line) {
            line.indexOf("(eval ") > -1 && (line = line.replace(/eval code/g, "eval").replace(/(\(eval at [^()]*)|(,.*$)/g, ""));
            var sanitizedLine = line.replace(/^\s+/, "").replace(/\(eval code/g, "(").replace(/^.*?\s+/, ""), location = sanitizedLine.match(/ (\(.+\)$)/);
            sanitizedLine = location ? sanitizedLine.replace(location[0], "") : sanitizedLine;
            var locationParts = this.extractLocation(location ? location[1] : sanitizedLine), functionName = location && sanitizedLine || void 0, fileName = ["eval", "<anonymous>"].indexOf(locationParts[0]) > -1 ? void 0 : locationParts[0];
            return new StackFrame({
              functionName,
              fileName,
              lineNumber: locationParts[1],
              columnNumber: locationParts[2],
              source: line
            });
          }, this);
        },
        parseFFOrSafari: function(error) {
          var filtered = error.stack.split(`
`).filter(function(line) {
            return !line.match(SAFARI_NATIVE_CODE_REGEXP);
          }, this);
          return filtered.map(function(line) {
            if (line.indexOf(" > eval") > -1 && (line = line.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g, ":$1")), line.indexOf("@") === -1 && line.indexOf(":") === -1)
              return new StackFrame({
                functionName: line
              });
            var functionNameRegex = /((.*".+"[^@]*)?[^@]*)(?:@)/, matches = line.match(functionNameRegex), functionName = matches && matches[1] ? matches[1] : void 0, locationParts = this.extractLocation(line.replace(functionNameRegex, ""));
            return new StackFrame({
              functionName,
              fileName: locationParts[0],
              lineNumber: locationParts[1],
              columnNumber: locationParts[2],
              source: line
            });
          }, this);
        },
        parseOpera: function(e) {
          return !e.stacktrace || e.message.indexOf(`
`) > -1 && e.message.split(`
`).length > e.stacktrace.split(`
`).length ? this.parseOpera9(e) : e.stack ? this.parseOpera11(e) : this.parseOpera10(e);
        },
        parseOpera9: function(e) {
          for (var lineRE = /Line (\d+).*script (?:in )?(\S+)/i, lines = e.message.split(`
`), result = [], i = 2, len = lines.length; i < len; i += 2) {
            var match = lineRE.exec(lines[i]);
            match && result.push(new StackFrame({
              fileName: match[2],
              lineNumber: match[1],
              source: lines[i]
            }));
          }
          return result;
        },
        parseOpera10: function(e) {
          for (var lineRE = /Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i, lines = e.stacktrace.split(`
`), result = [], i = 0, len = lines.length; i < len; i += 2) {
            var match = lineRE.exec(lines[i]);
            match && result.push(
              new StackFrame({
                functionName: match[3] || void 0,
                fileName: match[2],
                lineNumber: match[1],
                source: lines[i]
              })
            );
          }
          return result;
        },
        // Opera 10.65+ Error.stack very similar to FF/Safari
        parseOpera11: function(error) {
          var filtered = error.stack.split(`
`).filter(function(line) {
            return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);
          }, this);
          return filtered.map(function(line) {
            var tokens = line.split("@"), locationParts = this.extractLocation(tokens.pop()), functionCall = tokens.shift() || "", functionName = functionCall.replace(/<anonymous function(: (\w+))?>/, "$2").replace(/\([^)]*\)/g, "") || void 0, argsRaw;
            functionCall.match(/\(([^)]*)\)/) && (argsRaw = functionCall.replace(/^[^(]+\(([^)]*)\)$/, "$1"));
            var args = argsRaw === void 0 || argsRaw === "[arguments not available]" ? void 0 : argsRaw.split(",");
            return new StackFrame({
              functionName,
              args,
              fileName: locationParts[0],
              lineNumber: locationParts[1],
              columnNumber: locationParts[2],
              source: line
            });
          }, this);
        }
      };
    });
  }
});

// ../../node_modules/.pnpm/iserror@0.0.2/node_modules/iserror/index.js
var require_iserror = __commonJS({
  "../../node_modules/.pnpm/iserror@0.0.2/node_modules/iserror/index.js"(exports, module) {
    init_cjs_shims();
    module.exports = isError;
    function isError(value) {
      switch (Object.prototype.toString.call(value)) {
        case "[object Error]":
          return !0;
        case "[object Exception]":
          return !0;
        case "[object DOMException]":
          return !0;
        default:
          return value instanceof Error;
      }
    }
  }
});

// ../../node_modules/.pnpm/stack-generator@2.0.10/node_modules/stack-generator/stack-generator.js
var require_stack_generator = __commonJS({
  "../../node_modules/.pnpm/stack-generator@2.0.10/node_modules/stack-generator/stack-generator.js"(exports, module) {
    init_cjs_shims();
    (function(root, factory) {
      "use strict";
      typeof define == "function" && define.amd ? define("stack-generator", ["stackframe"], factory) : typeof exports == "object" ? module.exports = factory(require_stackframe()) : root.StackGenerator = factory(root.StackFrame);
    })(exports, function(StackFrame) {
      return {
        backtrace: function(opts) {
          var stack = [], maxStackSize = 10;
          typeof opts == "object" && typeof opts.maxStackSize == "number" && (maxStackSize = opts.maxStackSize);
          for (var curr = arguments.callee; curr && stack.length < maxStackSize && curr.arguments; ) {
            for (var args = new Array(curr.arguments.length), i = 0; i < args.length; ++i)
              args[i] = curr.arguments[i];
            /function(?:\s+([\w$]+))+\s*\(/.test(curr.toString()) ? stack.push(new StackFrame({ functionName: RegExp.$1 || void 0, args })) : stack.push(new StackFrame({ args }));
            try {
              curr = curr.caller;
            } catch {
              break;
            }
          }
          return stack;
        }
      };
    });
  }
});

// ../../node_modules/.pnpm/end-of-stream@1.4.5/node_modules/end-of-stream/index.js
var require_end_of_stream = __commonJS({
  "../../node_modules/.pnpm/end-of-stream@1.4.5/node_modules/end-of-stream/index.js"(exports, module) {
    init_cjs_shims();
    var once = require_once(), noop = function() {
    }, qnt = global.Bare ? queueMicrotask : process.nextTick.bind(process), isRequest = function(stream) {
      return stream.setHeader && typeof stream.abort == "function";
    }, isChildProcess = function(stream) {
      return stream.stdio && Array.isArray(stream.stdio) && stream.stdio.length === 3;
    }, eos = function(stream, opts, callback) {
      if (typeof opts == "function") return eos(stream, null, opts);
      opts || (opts = {}), callback = once(callback || noop);
      var ws = stream._writableState, rs = stream._readableState, readable = opts.readable || opts.readable !== !1 && stream.readable, writable = opts.writable || opts.writable !== !1 && stream.writable, cancelled = !1, onlegacyfinish = function() {
        stream.writable || onfinish();
      }, onfinish = function() {
        writable = !1, readable || callback.call(stream);
      }, onend = function() {
        readable = !1, writable || callback.call(stream);
      }, onexit = function(exitCode) {
        callback.call(stream, exitCode ? new Error("exited with error code: " + exitCode) : null);
      }, onerror = function(err) {
        callback.call(stream, err);
      }, onclose = function() {
        qnt(onclosenexttick);
      }, onclosenexttick = function() {
        if (!cancelled) {
          if (readable && !(rs && rs.ended && !rs.destroyed)) return callback.call(stream, new Error("premature close"));
          if (writable && !(ws && ws.ended && !ws.destroyed)) return callback.call(stream, new Error("premature close"));
        }
      }, onrequest = function() {
        stream.req.on("finish", onfinish);
      };
      return isRequest(stream) ? (stream.on("complete", onfinish), stream.on("abort", onclose), stream.req ? onrequest() : stream.on("request", onrequest)) : writable && !ws && (stream.on("end", onlegacyfinish), stream.on("close", onlegacyfinish)), isChildProcess(stream) && stream.on("exit", onexit), stream.on("end", onend), stream.on("finish", onfinish), opts.error !== !1 && stream.on("error", onerror), stream.on("close", onclose), function() {
        cancelled = !0, stream.removeListener("complete", onfinish), stream.removeListener("abort", onclose), stream.removeListener("request", onrequest), stream.req && stream.req.removeListener("finish", onfinish), stream.removeListener("end", onlegacyfinish), stream.removeListener("close", onlegacyfinish), stream.removeListener("finish", onfinish), stream.removeListener("exit", onexit), stream.removeListener("end", onend), stream.removeListener("error", onerror), stream.removeListener("close", onclose);
      };
    };
    module.exports = eos;
  }
});

// ../../node_modules/.pnpm/pump@3.0.2/node_modules/pump/index.js
var require_pump = __commonJS({
  "../../node_modules/.pnpm/pump@3.0.2/node_modules/pump/index.js"(exports, module) {
    init_cjs_shims();
    var once = require_once(), eos = require_end_of_stream(), fs;
    try {
      fs = __require("fs");
    } catch {
    }
    var noop = function() {
    }, ancient = /^v?\.0/.test(process.version), isFn = function(fn) {
      return typeof fn == "function";
    }, isFS = function(stream) {
      return !ancient || !fs ? !1 : (stream instanceof (fs.ReadStream || noop) || stream instanceof (fs.WriteStream || noop)) && isFn(stream.close);
    }, isRequest = function(stream) {
      return stream.setHeader && isFn(stream.abort);
    }, destroyer = function(stream, reading, writing, callback) {
      callback = once(callback);
      var closed = !1;
      stream.on("close", function() {
        closed = !0;
      }), eos(stream, { readable: reading, writable: writing }, function(err) {
        if (err) return callback(err);
        closed = !0, callback();
      });
      var destroyed = !1;
      return function(err) {
        if (!closed && !destroyed) {
          if (destroyed = !0, isFS(stream)) return stream.close(noop);
          if (isRequest(stream)) return stream.abort();
          if (isFn(stream.destroy)) return stream.destroy();
          callback(err || new Error("stream was destroyed"));
        }
      };
    }, call = function(fn) {
      fn();
    }, pipe = function(from, to) {
      return from.pipe(to);
    }, pump = function() {
      var streams = Array.prototype.slice.call(arguments), callback = isFn(streams[streams.length - 1] || noop) && streams.pop() || noop;
      if (Array.isArray(streams[0]) && (streams = streams[0]), streams.length < 2) throw new Error("pump requires two streams per minimum");
      var error, destroys = streams.map(function(stream, i) {
        var reading = i < streams.length - 1, writing = i > 0;
        return destroyer(stream, reading, writing, function(err) {
          error || (error = err), err && destroys.forEach(call), !reading && (destroys.forEach(call), callback(error));
        });
      });
      return streams.reduce(pipe);
    };
    module.exports = pump;
  }
});

// ../../node_modules/.pnpm/byline@5.0.0/node_modules/byline/lib/byline.js
var require_byline = __commonJS({
  "../../node_modules/.pnpm/byline@5.0.0/node_modules/byline/lib/byline.js"(exports, module) {
    init_cjs_shims();
    var stream = __require("stream"), util = __require("util"), timers = __require("timers");
    module.exports = function(readStream, options) {
      return module.exports.createStream(readStream, options);
    };
    module.exports.createStream = function(readStream, options) {
      return readStream ? createLineStream(readStream, options) : new LineStream(options);
    };
    module.exports.createLineStream = function(readStream) {
      return console.log("WARNING: byline#createLineStream is deprecated and will be removed soon"), createLineStream(readStream);
    };
    function createLineStream(readStream, options) {
      if (!readStream)
        throw new Error("expected readStream");
      if (!readStream.readable)
        throw new Error("readStream must be readable");
      var ls = new LineStream(options);
      return readStream.pipe(ls), ls;
    }
    module.exports.LineStream = LineStream;
    function LineStream(options) {
      stream.Transform.call(this, options), options = options || {}, this._readableState.objectMode = !0, this._lineBuffer = [], this._keepEmptyLines = options.keepEmptyLines || !1, this._lastChunkEndedWithCR = !1;
      var self2 = this;
      this.on("pipe", function(src) {
        self2.encoding || src instanceof stream.Readable && (self2.encoding = src._readableState.encoding);
      });
    }
    util.inherits(LineStream, stream.Transform);
    LineStream.prototype._transform = function(chunk, encoding, done) {
      encoding = encoding || "utf8", Buffer.isBuffer(chunk) && (encoding == "buffer" ? (chunk = chunk.toString(), encoding = "utf8") : chunk = chunk.toString(encoding)), this._chunkEncoding = encoding;
      var lines = chunk.split(/\r\n|[\n\v\f\r\x85\u2028\u2029]/g);
      this._lastChunkEndedWithCR && chunk[0] == `
` && lines.shift(), this._lineBuffer.length > 0 && (this._lineBuffer[this._lineBuffer.length - 1] += lines[0], lines.shift()), this._lastChunkEndedWithCR = chunk[chunk.length - 1] == "\r", this._lineBuffer = this._lineBuffer.concat(lines), this._pushBuffer(encoding, 1, done);
    };
    LineStream.prototype._pushBuffer = function(encoding, keep, done) {
      for (; this._lineBuffer.length > keep; ) {
        var line = this._lineBuffer.shift();
        if ((this._keepEmptyLines || line.length > 0) && !this.push(this._reencode(line, encoding))) {
          var self2 = this;
          timers.setImmediate(function() {
            self2._pushBuffer(encoding, keep, done);
          });
          return;
        }
      }
      done();
    };
    LineStream.prototype._flush = function(done) {
      this._pushBuffer(this._chunkEncoding, 0, done);
    };
    LineStream.prototype._reencode = function(line, chunkEncoding) {
      return this.encoding && this.encoding != chunkEncoding ? new Buffer(line, chunkEncoding).toString(this.encoding) : this.encoding ? line : new Buffer(line, chunkEncoding);
    };
  }
});

// ../../node_modules/.pnpm/@bugsnag+node@7.25.0/node_modules/@bugsnag/node/dist/bugsnag.js
var require_bugsnag = __commonJS({
  "../../node_modules/.pnpm/@bugsnag+node@7.25.0/node_modules/@bugsnag/node/dist/bugsnag.js"(exports, module) {
    init_cjs_shims();
    (function(f) {
      if (typeof exports == "object" && typeof module < "u")
        module.exports = f();
      else if (typeof define == "function" && define.amd)
        define([], f);
      else {
        var g;
        typeof window < "u" ? g = window : typeof global < "u" ? g = global : typeof self < "u" ? g = self : g = this, g.bugsnag = f();
      }
    })(function() {
      var define2, module2, exports2, Breadcrumb = /* @__PURE__ */ function() {
        function Breadcrumb2(message, metadata, type, timestamp) {
          timestamp === void 0 && (timestamp = /* @__PURE__ */ new Date()), this.type = type, this.message = message, this.metadata = metadata, this.timestamp = timestamp;
        }
        var _proto = Breadcrumb2.prototype;
        return _proto.toJSON = function() {
          return {
            type: this.type,
            name: this.message,
            timestamp: this.timestamp,
            metaData: this.metadata
          };
        }, Breadcrumb2;
      }(), _$Breadcrumb_1 = Breadcrumb, _$breadcrumbTypes_6 = ["navigation", "request", "process", "log", "user", "state", "error", "manual"], _$reduce_16 = function(arr, fn, accum) {
        for (var val = accum, i = 0, len = arr.length; i < len; i++) val = fn(val, arr[i], i, arr);
        return val;
      }, _$filter_11 = function(arr, fn) {
        return _$reduce_16(arr, function(accum, item, i, arr2) {
          return fn(item, i, arr2) ? accum.concat(item) : accum;
        }, []);
      }, _$includes_12 = function(arr, x) {
        return _$reduce_16(arr, function(accum, item, i, arr2) {
          return accum === !0 || item === x;
        }, !1);
      }, _$isArray_13 = function(obj) {
        return Object.prototype.toString.call(obj) === "[object Array]";
      }, _hasDontEnumBug = !{
        toString: null
      }.propertyIsEnumerable("toString"), _dontEnums = ["toString", "toLocaleString", "valueOf", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "constructor"], _$keys_14 = function(obj) {
        var result = [], prop;
        for (prop in obj)
          Object.prototype.hasOwnProperty.call(obj, prop) && result.push(prop);
        if (!_hasDontEnumBug) return result;
        for (var i = 0, len = _dontEnums.length; i < len; i++)
          Object.prototype.hasOwnProperty.call(obj, _dontEnums[i]) && result.push(_dontEnums[i]);
        return result;
      }, _$intRange_25 = function(min, max) {
        return min === void 0 && (min = 1), max === void 0 && (max = 1 / 0), function(value) {
          return typeof value == "number" && parseInt("" + value, 10) === value && value >= min && value <= max;
        };
      }, _$listOfFunctions_26 = function(value) {
        return typeof value == "function" || _$isArray_13(value) && _$filter_11(value, function(f) {
          return typeof f == "function";
        }).length === value.length;
      }, _$stringWithLength_27 = function(value) {
        return typeof value == "string" && !!value.length;
      }, _$config_3 = {}, defaultErrorTypes = function() {
        return {
          unhandledExceptions: !0,
          unhandledRejections: !0
        };
      };
      _$config_3.schema = {
        apiKey: {
          defaultValue: function() {
            return null;
          },
          message: "is required",
          validate: _$stringWithLength_27
        },
        appVersion: {
          defaultValue: function() {
          },
          message: "should be a string",
          validate: function(value) {
            return value === void 0 || _$stringWithLength_27(value);
          }
        },
        appType: {
          defaultValue: function() {
          },
          message: "should be a string",
          validate: function(value) {
            return value === void 0 || _$stringWithLength_27(value);
          }
        },
        autoDetectErrors: {
          defaultValue: function() {
            return !0;
          },
          message: "should be true|false",
          validate: function(value) {
            return value === !0 || value === !1;
          }
        },
        enabledErrorTypes: {
          defaultValue: function() {
            return defaultErrorTypes();
          },
          message: "should be an object containing the flags { unhandledExceptions:true|false, unhandledRejections:true|false }",
          allowPartialObject: !0,
          validate: function(value) {
            if (typeof value != "object" || !value) return !1;
            var providedKeys = _$keys_14(value), defaultKeys = _$keys_14(defaultErrorTypes());
            return !(_$filter_11(providedKeys, function(k) {
              return _$includes_12(defaultKeys, k);
            }).length < providedKeys.length || _$filter_11(_$keys_14(value), function(k) {
              return typeof value[k] != "boolean";
            }).length > 0);
          }
        },
        onError: {
          defaultValue: function() {
            return [];
          },
          message: "should be a function or array of functions",
          validate: _$listOfFunctions_26
        },
        onSession: {
          defaultValue: function() {
            return [];
          },
          message: "should be a function or array of functions",
          validate: _$listOfFunctions_26
        },
        onBreadcrumb: {
          defaultValue: function() {
            return [];
          },
          message: "should be a function or array of functions",
          validate: _$listOfFunctions_26
        },
        endpoints: {
          defaultValue: function() {
            return {
              notify: "https://notify.bugsnag.com",
              sessions: "https://sessions.bugsnag.com"
            };
          },
          message: "should be an object containing endpoint URLs { notify, sessions }",
          validate: function(val) {
            return (
              // first, ensure it's an object
              val && typeof val == "object" && // notify and sessions must always be set
              _$stringWithLength_27(val.notify) && _$stringWithLength_27(val.sessions) && // ensure no keys other than notify/session are set on endpoints object
              _$filter_11(_$keys_14(val), function(k) {
                return !_$includes_12(["notify", "sessions"], k);
              }).length === 0
            );
          }
        },
        autoTrackSessions: {
          defaultValue: function(val) {
            return !0;
          },
          message: "should be true|false",
          validate: function(val) {
            return val === !0 || val === !1;
          }
        },
        enabledReleaseStages: {
          defaultValue: function() {
            return null;
          },
          message: "should be an array of strings",
          validate: function(value) {
            return value === null || _$isArray_13(value) && _$filter_11(value, function(f) {
              return typeof f == "string";
            }).length === value.length;
          }
        },
        releaseStage: {
          defaultValue: function() {
            return "production";
          },
          message: "should be a string",
          validate: function(value) {
            return typeof value == "string" && value.length;
          }
        },
        maxBreadcrumbs: {
          defaultValue: function() {
            return 25;
          },
          message: "should be a number \u2264100",
          validate: function(value) {
            return _$intRange_25(0, 100)(value);
          }
        },
        enabledBreadcrumbTypes: {
          defaultValue: function() {
            return _$breadcrumbTypes_6;
          },
          message: "should be null or a list of available breadcrumb types (" + _$breadcrumbTypes_6.join(",") + ")",
          validate: function(value) {
            return value === null || _$isArray_13(value) && _$reduce_16(value, function(accum, maybeType) {
              return accum === !1 ? accum : _$includes_12(_$breadcrumbTypes_6, maybeType);
            }, !0);
          }
        },
        context: {
          defaultValue: function() {
          },
          message: "should be a string",
          validate: function(value) {
            return value === void 0 || typeof value == "string";
          }
        },
        user: {
          defaultValue: function() {
            return {};
          },
          message: "should be an object with { id, email, name } properties",
          validate: function(value) {
            return value === null || value && _$reduce_16(_$keys_14(value), function(accum, key) {
              return accum && _$includes_12(["id", "email", "name"], key);
            }, !0);
          }
        },
        metadata: {
          defaultValue: function() {
            return {};
          },
          message: "should be an object",
          validate: function(value) {
            return typeof value == "object" && value !== null;
          }
        },
        logger: {
          defaultValue: function() {
          },
          message: "should be null or an object with methods { debug, info, warn, error }",
          validate: function(value) {
            return !value || value && _$reduce_16(["debug", "info", "warn", "error"], function(accum, method) {
              return accum && typeof value[method] == "function";
            }, !0);
          }
        },
        redactedKeys: {
          defaultValue: function() {
            return ["password"];
          },
          message: "should be an array of strings|regexes",
          validate: function(value) {
            return _$isArray_13(value) && value.length === _$filter_11(value, function(s) {
              return typeof s == "string" || s && typeof s.test == "function";
            }).length;
          }
        },
        plugins: {
          defaultValue: function() {
            return [];
          },
          message: "should be an array of plugin objects",
          validate: function(value) {
            return _$isArray_13(value) && value.length === _$filter_11(value, function(p) {
              return p && typeof p == "object" && typeof p.load == "function";
            }).length;
          }
        },
        featureFlags: {
          defaultValue: function() {
            return [];
          },
          message: 'should be an array of objects that have a "name" property',
          validate: function(value) {
            return _$isArray_13(value) && value.length === _$filter_11(value, function(feature) {
              return feature && typeof feature == "object" && typeof feature.name == "string";
            }).length;
          }
        }
      };
      var _$errorStackParser_9 = require_error_stack_parser(), _$assign_10 = function(target) {
        for (var i = 1; i < arguments.length; i++) {
          var source = arguments[i];
          for (var key in source)
            Object.prototype.hasOwnProperty.call(source, key) && (target[key] = source[key]);
        }
        return target;
      }, _$map_15 = function(arr, fn) {
        return _$reduce_16(arr, function(accum, item, i, arr2) {
          return accum.concat(fn(item, i, arr2));
        }, []);
      }, _$safeJsonStringify_31 = function(data, replacer, space, opts) {
        var redactedKeys = opts && opts.redactedKeys ? opts.redactedKeys : [], redactedPaths = opts && opts.redactedPaths ? opts.redactedPaths : [];
        return JSON.stringify(prepareObjForSerialization(data, redactedKeys, redactedPaths), replacer, space);
      }, MAX_DEPTH = 20, MAX_EDGES = 25e3, MIN_PRESERVED_DEPTH = 8, REPLACEMENT_NODE = "...";
      function isError(o) {
        return o instanceof Error || /^\[object (Error|(Dom)?Exception)\]$/.test(Object.prototype.toString.call(o));
      }
      function throwsMessage(err) {
        return "[Throws: " + (err ? err.message : "?") + "]";
      }
      function find(haystack, needle) {
        for (var i = 0, len = haystack.length; i < len; i++)
          if (haystack[i] === needle) return !0;
        return !1;
      }
      function isDescendent(paths, path2) {
        for (var i = 0, len = paths.length; i < len; i++)
          if (path2.indexOf(paths[i]) === 0) return !0;
        return !1;
      }
      function shouldRedact(patterns, key) {
        for (var i = 0, len = patterns.length; i < len; i++)
          if (typeof patterns[i] == "string" && patterns[i].toLowerCase() === key.toLowerCase() || patterns[i] && typeof patterns[i].test == "function" && patterns[i].test(key)) return !0;
        return !1;
      }
      function __isArray_31(obj) {
        return Object.prototype.toString.call(obj) === "[object Array]";
      }
      function safelyGetProp(obj, prop) {
        try {
          return obj[prop];
        } catch (err) {
          return throwsMessage(err);
        }
      }
      function prepareObjForSerialization(obj, redactedKeys, redactedPaths) {
        var seen = [], edges = 0;
        function visit(obj2, path2) {
          function edgesExceeded() {
            return path2.length > MIN_PRESERVED_DEPTH && edges > MAX_EDGES;
          }
          if (edges++, path2.length > MAX_DEPTH || edgesExceeded()) return REPLACEMENT_NODE;
          if (obj2 === null || typeof obj2 != "object") return obj2;
          if (find(seen, obj2)) return "[Circular]";
          if (seen.push(obj2), typeof obj2.toJSON == "function")
            try {
              edges--;
              var fResult = visit(obj2.toJSON(), path2);
              return seen.pop(), fResult;
            } catch (err) {
              return throwsMessage(err);
            }
          var er = isError(obj2);
          if (er) {
            edges--;
            var eResult = visit({
              name: obj2.name,
              message: obj2.message
            }, path2);
            return seen.pop(), eResult;
          }
          if (__isArray_31(obj2)) {
            for (var aResult = [], i = 0, len = obj2.length; i < len; i++) {
              if (edgesExceeded()) {
                aResult.push(REPLACEMENT_NODE);
                break;
              }
              aResult.push(visit(obj2[i], path2.concat("[]")));
            }
            return seen.pop(), aResult;
          }
          var result = {};
          try {
            for (var prop in obj2)
              if (Object.prototype.hasOwnProperty.call(obj2, prop)) {
                if (isDescendent(redactedPaths, path2.join(".")) && shouldRedact(redactedKeys, prop)) {
                  result[prop] = "[REDACTED]";
                  continue;
                }
                if (edgesExceeded()) {
                  result[prop] = REPLACEMENT_NODE;
                  break;
                }
                result[prop] = visit(safelyGetProp(obj2, prop), path2.concat(prop));
              }
          } catch {
          }
          return seen.pop(), result;
        }
        return visit(obj, []);
      }
      function add(existingFeatures, existingFeatureKeys, name2, variant) {
        if (typeof name2 == "string") {
          variant === void 0 ? variant = null : variant !== null && typeof variant != "string" && (variant = _$safeJsonStringify_31(variant));
          var existingIndex = existingFeatureKeys[name2];
          if (typeof existingIndex == "number") {
            existingFeatures[existingIndex] = {
              name: name2,
              variant
            };
            return;
          }
          existingFeatures.push({
            name: name2,
            variant
          }), existingFeatureKeys[name2] = existingFeatures.length - 1;
        }
      }
      function merge(existingFeatures, newFeatures, existingFeatureKeys) {
        if (_$isArray_13(newFeatures)) {
          for (var i = 0; i < newFeatures.length; ++i) {
            var feature = newFeatures[i];
            feature === null || typeof feature != "object" || add(existingFeatures, existingFeatureKeys, feature.name, feature.variant);
          }
          return existingFeatures;
        }
      }
      function toEventApi(featureFlags) {
        return _$map_15(_$filter_11(featureFlags, Boolean), function(_ref) {
          var name2 = _ref.name, variant = _ref.variant, flag = {
            featureFlag: name2
          };
          return typeof variant == "string" && (flag.variant = variant), flag;
        });
      }
      function clear(features, featuresIndex, name2) {
        var existingIndex = featuresIndex[name2];
        typeof existingIndex == "number" && (features[existingIndex] = null, delete featuresIndex[name2]);
      }
      var _$featureFlagDelegate_17 = {
        add,
        clear,
        merge,
        toEventApi
      }, _$hasStack_18 = function(err) {
        return !!err && (!!err.stack || !!err.stacktrace || !!err["opera#sourceloc"]) && typeof (err.stack || err.stacktrace || err["opera#sourceloc"]) == "string" && err.stack !== err.name + ": " + err.message;
      }, _$iserror_19 = require_iserror(), __add_21 = function(state, section, keyOrObj, maybeVal) {
        var _updates;
        if (section) {
          var updates;
          if (keyOrObj === null) return __clear_21(state, section);
          typeof keyOrObj == "object" && (updates = keyOrObj), typeof keyOrObj == "string" && (updates = (_updates = {}, _updates[keyOrObj] = maybeVal, _updates)), updates && (section === "__proto__" || section === "constructor" || section === "prototype" || (state[section] || (state[section] = {}), state[section] = _$assign_10({}, state[section], updates)));
        }
      }, get = function(state, section, key) {
        if (typeof section == "string") {
          if (!key)
            return state[section];
          if (state[section])
            return state[section][key];
        }
      }, __clear_21 = function(state, section, key) {
        if (typeof section == "string") {
          if (!key) {
            delete state[section];
            return;
          }
          section === "__proto__" || section === "constructor" || section === "prototype" || state[section] && delete state[section][key];
        }
      }, _$metadataDelegate_21 = {
        add: __add_21,
        get,
        clear: __clear_21
      };
      function _extends() {
        return _extends = Object.assign ? Object.assign.bind() : function(target) {
          for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];
            for (var key in source)
              Object.prototype.hasOwnProperty.call(source, key) && (target[key] = source[key]);
          }
          return target;
        }, _extends.apply(this, arguments);
      }
      var StackGenerator = require_stack_generator(), Event = /* @__PURE__ */ function() {
        function Event2(errorClass, errorMessage, stacktrace, handledState, originalError) {
          stacktrace === void 0 && (stacktrace = []), handledState === void 0 && (handledState = defaultHandledState()), this.apiKey = void 0, this.context = void 0, this.groupingHash = void 0, this.originalError = originalError, this._handledState = handledState, this.severity = this._handledState.severity, this.unhandled = this._handledState.unhandled, this.app = {}, this.device = {}, this.request = {}, this.breadcrumbs = [], this.threads = [], this._metadata = {}, this._features = [], this._featuresIndex = {}, this._user = {}, this._session = void 0, this._correlation = void 0, this.errors = [createBugsnagError(errorClass, errorMessage, Event2.__type, stacktrace)];
        }
        var _proto = Event2.prototype;
        return _proto.addMetadata = function(section, keyOrObj, maybeVal) {
          return _$metadataDelegate_21.add(this._metadata, section, keyOrObj, maybeVal);
        }, _proto.setTraceCorrelation = function(traceId, spanId) {
          typeof traceId == "string" && (this._correlation = _extends({
            traceId
          }, typeof spanId == "string" ? {
            spanId
          } : {}));
        }, _proto.getMetadata = function(section, key) {
          return _$metadataDelegate_21.get(this._metadata, section, key);
        }, _proto.clearMetadata = function(section, key) {
          return _$metadataDelegate_21.clear(this._metadata, section, key);
        }, _proto.addFeatureFlag = function(name2, variant) {
          variant === void 0 && (variant = null), _$featureFlagDelegate_17.add(this._features, this._featuresIndex, name2, variant);
        }, _proto.addFeatureFlags = function(featureFlags) {
          _$featureFlagDelegate_17.merge(this._features, featureFlags, this._featuresIndex);
        }, _proto.getFeatureFlags = function() {
          return _$featureFlagDelegate_17.toEventApi(this._features);
        }, _proto.clearFeatureFlag = function(name2) {
          _$featureFlagDelegate_17.clear(this._features, this._featuresIndex, name2);
        }, _proto.clearFeatureFlags = function() {
          this._features = [], this._featuresIndex = {};
        }, _proto.getUser = function() {
          return this._user;
        }, _proto.setUser = function(id, email, name2) {
          this._user = {
            id,
            email,
            name: name2
          };
        }, _proto.toJSON = function() {
          return {
            payloadVersion: "4",
            exceptions: _$map_15(this.errors, function(er) {
              return _$assign_10({}, er, {
                message: er.errorMessage
              });
            }),
            severity: this.severity,
            unhandled: this._handledState.unhandled,
            severityReason: this._handledState.severityReason,
            app: this.app,
            device: this.device,
            request: this.request,
            breadcrumbs: this.breadcrumbs,
            context: this.context,
            groupingHash: this.groupingHash,
            metaData: this._metadata,
            user: this._user,
            session: this._session,
            featureFlags: this.getFeatureFlags(),
            correlation: this._correlation
          };
        }, Event2;
      }(), formatStackframe = function(frame) {
        var f = {
          file: frame.fileName,
          method: normaliseFunctionName(frame.functionName),
          lineNumber: frame.lineNumber,
          columnNumber: frame.columnNumber,
          code: void 0,
          inProject: void 0
        };
        return f.lineNumber > -1 && !f.file && !f.method && (f.file = "global code"), f;
      }, normaliseFunctionName = function(name2) {
        return /^global code$/i.test(name2) ? "global code" : name2;
      }, defaultHandledState = function() {
        return {
          unhandled: !1,
          severity: "warning",
          severityReason: {
            type: "handledException"
          }
        };
      }, ensureString = function(str) {
        return typeof str == "string" ? str : "";
      };
      function createBugsnagError(errorClass, errorMessage, type, stacktrace) {
        return {
          errorClass: ensureString(errorClass),
          errorMessage: ensureString(errorMessage),
          type,
          stacktrace: _$reduce_16(stacktrace, function(accum, frame) {
            var f = formatStackframe(frame);
            try {
              return JSON.stringify(f) === "{}" ? accum : accum.concat(f);
            } catch {
              return accum;
            }
          }, [])
        };
      }
      function getCauseStack(error) {
        return error.cause ? [error].concat(getCauseStack(error.cause)) : [error];
      }
      Event.getStacktrace = function(error, errorFramesToSkip, backtraceFramesToSkip) {
        if (_$hasStack_18(error)) return _$errorStackParser_9.parse(error).slice(errorFramesToSkip);
        try {
          return _$filter_11(StackGenerator.backtrace(), function(frame) {
            return (frame.functionName || "").indexOf("StackGenerator$$") === -1;
          }).slice(1 + backtraceFramesToSkip);
        } catch {
          return [];
        }
      }, Event.create = function(maybeError, tolerateNonErrors, handledState, component, errorFramesToSkip, logger) {
        errorFramesToSkip === void 0 && (errorFramesToSkip = 0);
        var _normaliseError = normaliseError(maybeError, tolerateNonErrors, component, logger), error = _normaliseError[0], internalFrames = _normaliseError[1], event;
        try {
          var stacktrace = Event.getStacktrace(
            error,
            // if an error was created/throw in the normaliseError() function, we need to
            // tell the getStacktrace() function to skip the number of frames we know will
            // be from our own functions. This is added to the number of frames deep we
            // were told about
            internalFrames > 0 ? 1 + internalFrames + errorFramesToSkip : 0,
            // if there's no stacktrace, the callstack may be walked to generated one.
            // this is how many frames should be removed because they come from our library
            1 + errorFramesToSkip
          );
          event = new Event(error.name, error.message, stacktrace, handledState, maybeError);
        } catch {
          event = new Event(error.name, error.message, [], handledState, maybeError);
        }
        if (error.name === "InvalidError" && event.addMetadata("" + component, "non-error parameter", makeSerialisable(maybeError)), error.cause) {
          var _event$errors, causes = getCauseStack(error).slice(1), normalisedCauses = _$map_15(causes, function(cause) {
            var stacktrace2 = _$iserror_19(cause) && _$hasStack_18(cause) ? _$errorStackParser_9.parse(cause) : [], _normaliseError2 = normaliseError(cause, !0, "error cause"), error2 = _normaliseError2[0];
            return error2.name === "InvalidError" && event.addMetadata("error cause", makeSerialisable(cause)), createBugsnagError(error2.name, error2.message, Event.__type, stacktrace2);
          });
          (_event$errors = event.errors).push.apply(_event$errors, normalisedCauses);
        }
        return event;
      };
      var makeSerialisable = function(err) {
        return err === null ? "null" : err === void 0 ? "undefined" : err;
      }, normaliseError = function(maybeError, tolerateNonErrors, component, logger) {
        var error, internalFrames = 0, createAndLogInputError = function(reason) {
          var verb = component === "error cause" ? "was" : "received";
          logger && logger.warn(component + " " + verb + ' a non-error: "' + reason + '"');
          var err = new Error(component + " " + verb + ' a non-error. See "' + component + '" tab for more detail.');
          return err.name = "InvalidError", err;
        };
        if (!tolerateNonErrors)
          _$iserror_19(maybeError) ? error = maybeError : (error = createAndLogInputError(typeof maybeError), internalFrames += 2);
        else
          switch (typeof maybeError) {
            case "string":
            case "number":
            case "boolean":
              error = new Error(String(maybeError)), internalFrames += 1;
              break;
            case "function":
              error = createAndLogInputError("function"), internalFrames += 2;
              break;
            case "object":
              maybeError !== null && _$iserror_19(maybeError) ? error = maybeError : maybeError !== null && hasNecessaryFields(maybeError) ? (error = new Error(maybeError.message || maybeError.errorMessage), error.name = maybeError.name || maybeError.errorClass, internalFrames += 1) : (error = createAndLogInputError(maybeError === null ? "null" : "unsupported object"), internalFrames += 2);
              break;
            default:
              error = createAndLogInputError("nothing"), internalFrames += 2;
          }
        if (!_$hasStack_18(error))
          try {
            throw error;
          } catch (e) {
            _$hasStack_18(e) && (error = e, internalFrames = 1);
          }
        return [error, internalFrames];
      };
      Event.__type = "browserjs";
      var hasNecessaryFields = function(error) {
        return (typeof error.name == "string" || typeof error.errorClass == "string") && (typeof error.message == "string" || typeof error.errorMessage == "string");
      }, _$Event_4 = Event, _$asyncEvery_5 = function(arr, fn, cb) {
        var index = 0, next = function() {
          if (index >= arr.length) return cb(null, !0);
          fn(arr[index], function(err, result) {
            if (err) return cb(err);
            if (result === !1) return cb(null, !1);
            index++, next();
          });
        };
        next();
      }, _$callbackRunner_7 = function(callbacks, event, onCallbackError, cb) {
        var runMaybeAsyncCallback = function(fn, cb2) {
          if (typeof fn != "function") return cb2(null);
          try {
            if (fn.length !== 2) {
              var ret = fn(event);
              return ret && typeof ret.then == "function" ? ret.then(
                // resolve
                function(val) {
                  return setTimeout(function() {
                    return cb2(null, val);
                  });
                },
                // reject
                function(err) {
                  setTimeout(function() {
                    return onCallbackError(err), cb2(null, !0);
                  });
                }
              ) : cb2(null, ret);
            }
            fn(event, function(err, result) {
              if (err)
                return onCallbackError(err), cb2(null);
              cb2(null, result);
            });
          } catch (e) {
            onCallbackError(e), cb2(null);
          }
        };
        _$asyncEvery_5(callbacks, runMaybeAsyncCallback, cb);
      }, _$syncCallbackRunner_24 = function(callbacks, callbackArg, callbackType, logger) {
        for (var ignore = !1, cbs = callbacks.slice(); !ignore && cbs.length; )
          try {
            ignore = cbs.pop()(callbackArg) === !1;
          } catch (e) {
            logger.error("Error occurred in " + callbackType + " callback, continuing anyway\u2026"), logger.error(e);
          }
        return ignore;
      }, _$pad_30 = function(num, size) {
        var s = "000000000" + num;
        return s.substr(s.length - size);
      }, os = __require("os"), padding = 2, pid = _$pad_30(process.pid.toString(36), padding), hostname = os.hostname(), length = hostname.length, hostId = _$pad_30(hostname.split("").reduce(function(prev, char) {
        return +prev + char.charCodeAt(0);
      }, +length + 36).toString(36), padding), _$fingerprint_29 = function() {
        return pid + hostId;
      }, c = 0, blockSize = 4, base = 36, discreteValues = Math.pow(base, blockSize);
      function randomBlock() {
        return _$pad_30((Math.random() * discreteValues << 0).toString(base), blockSize);
      }
      function safeCounter() {
        return c = c < discreteValues ? c : 0, c++, c - 1;
      }
      function cuid() {
        var letter = "c", timestamp = (/* @__PURE__ */ new Date()).getTime().toString(base), counter = _$pad_30(safeCounter().toString(base), blockSize), print = _$fingerprint_29(), random = randomBlock() + randomBlock();
        return letter + timestamp + counter + print + random;
      }
      cuid.fingerprint = _$fingerprint_29;
      var _$cuid_28 = cuid, Session = /* @__PURE__ */ function() {
        function Session2() {
          this.id = _$cuid_28(), this.startedAt = /* @__PURE__ */ new Date(), this._handled = 0, this._unhandled = 0, this._user = {}, this.app = {}, this.device = {};
        }
        var _proto = Session2.prototype;
        return _proto.getUser = function() {
          return this._user;
        }, _proto.setUser = function(id, email, name2) {
          this._user = {
            id,
            email,
            name: name2
          };
        }, _proto.toJSON = function() {
          return {
            id: this.id,
            startedAt: this.startedAt,
            events: {
              handled: this._handled,
              unhandled: this._unhandled
            }
          };
        }, _proto._track = function(event) {
          this[event._handledState.unhandled ? "_unhandled" : "_handled"] += 1;
        }, Session2;
      }(), _$Session_32 = Session, __add_2 = _$featureFlagDelegate_17.add, __clear_2 = _$featureFlagDelegate_17.clear, __merge_2 = _$featureFlagDelegate_17.merge, noop = function() {
      }, Client = /* @__PURE__ */ function() {
        function Client2(configuration, schema2, internalPlugins2, notifier) {
          var _this = this;
          schema2 === void 0 && (schema2 = _$config_3.schema), internalPlugins2 === void 0 && (internalPlugins2 = []), this._notifier = notifier, this._config = {}, this._schema = schema2, this._delivery = {
            sendSession: noop,
            sendEvent: noop
          }, this._logger = {
            debug: noop,
            info: noop,
            warn: noop,
            error: noop
          }, this._plugins = {}, this._breadcrumbs = [], this._session = null, this._metadata = {}, this._featuresIndex = {}, this._features = [], this._context = void 0, this._user = {}, this._cbs = {
            e: [],
            s: [],
            sp: [],
            b: []
          }, this.Client = Client2, this.Event = _$Event_4, this.Breadcrumb = _$Breadcrumb_1, this.Session = _$Session_32, this._config = this._configure(configuration, internalPlugins2), _$map_15(internalPlugins2.concat(this._config.plugins), function(pl) {
            pl && _this._loadPlugin(pl);
          }), this._depth = 1;
          var self2 = this, notify = this.notify;
          this.notify = function() {
            return notify.apply(self2, arguments);
          };
        }
        var _proto = Client2.prototype;
        return _proto.addMetadata = function(section, keyOrObj, maybeVal) {
          return _$metadataDelegate_21.add(this._metadata, section, keyOrObj, maybeVal);
        }, _proto.getMetadata = function(section, key) {
          return _$metadataDelegate_21.get(this._metadata, section, key);
        }, _proto.clearMetadata = function(section, key) {
          return _$metadataDelegate_21.clear(this._metadata, section, key);
        }, _proto.addFeatureFlag = function(name2, variant) {
          variant === void 0 && (variant = null), __add_2(this._features, this._featuresIndex, name2, variant);
        }, _proto.addFeatureFlags = function(featureFlags) {
          __merge_2(this._features, featureFlags, this._featuresIndex);
        }, _proto.clearFeatureFlag = function(name2) {
          __clear_2(this._features, this._featuresIndex, name2);
        }, _proto.clearFeatureFlags = function() {
          this._features = [], this._featuresIndex = {};
        }, _proto.getContext = function() {
          return this._context;
        }, _proto.setContext = function(c2) {
          this._context = c2;
        }, _proto._configure = function(opts, internalPlugins2) {
          var schema2 = _$reduce_16(internalPlugins2, function(schema3, plugin) {
            return plugin && plugin.configSchema ? _$assign_10({}, schema3, plugin.configSchema) : schema3;
          }, this._schema), _reduce = _$reduce_16(_$keys_14(schema2), function(accum, key) {
            var defaultValue = schema2[key].defaultValue(opts[key]);
            if (opts[key] !== void 0) {
              var valid = schema2[key].validate(opts[key]);
              valid ? schema2[key].allowPartialObject ? accum.config[key] = _$assign_10(defaultValue, opts[key]) : accum.config[key] = opts[key] : (accum.errors[key] = schema2[key].message, accum.config[key] = defaultValue);
            } else
              accum.config[key] = defaultValue;
            return accum;
          }, {
            errors: {},
            config: {}
          }), errors = _reduce.errors, config = _reduce.config;
          if (schema2.apiKey) {
            if (!config.apiKey) throw new Error("No Bugsnag API Key set");
            /^[0-9a-f]{32}$/i.test(config.apiKey) || (errors.apiKey = "should be a string of 32 hexadecimal characters");
          }
          return this._metadata = _$assign_10({}, config.metadata), __merge_2(this._features, config.featureFlags, this._featuresIndex), this._user = _$assign_10({}, config.user), this._context = config.context, config.logger && (this._logger = config.logger), config.onError && (this._cbs.e = this._cbs.e.concat(config.onError)), config.onBreadcrumb && (this._cbs.b = this._cbs.b.concat(config.onBreadcrumb)), config.onSession && (this._cbs.s = this._cbs.s.concat(config.onSession)), _$keys_14(errors).length && this._logger.warn(generateConfigErrorMessage(errors, opts)), config;
        }, _proto.getUser = function() {
          return this._user;
        }, _proto.setUser = function(id, email, name2) {
          this._user = {
            id,
            email,
            name: name2
          };
        }, _proto._loadPlugin = function(plugin) {
          var result = plugin.load(this);
          return plugin.name && (this._plugins["~" + plugin.name + "~"] = result), this;
        }, _proto.getPlugin = function(name2) {
          return this._plugins["~" + name2 + "~"];
        }, _proto._setDelivery = function(d) {
          this._delivery = d(this);
        }, _proto.startSession = function() {
          var session = new _$Session_32();
          session.app.releaseStage = this._config.releaseStage, session.app.version = this._config.appVersion, session.app.type = this._config.appType, session._user = _$assign_10({}, this._user);
          var ignore = _$syncCallbackRunner_24(this._cbs.s, session, "onSession", this._logger);
          return ignore ? (this._logger.debug("Session not started due to onSession callback"), this) : this._sessionDelegate.startSession(this, session);
        }, _proto.addOnError = function(fn, front) {
          front === void 0 && (front = !1), this._cbs.e[front ? "unshift" : "push"](fn);
        }, _proto.removeOnError = function(fn) {
          this._cbs.e = _$filter_11(this._cbs.e, function(f) {
            return f !== fn;
          });
        }, _proto._addOnSessionPayload = function(fn) {
          this._cbs.sp.push(fn);
        }, _proto.addOnSession = function(fn) {
          this._cbs.s.push(fn);
        }, _proto.removeOnSession = function(fn) {
          this._cbs.s = _$filter_11(this._cbs.s, function(f) {
            return f !== fn;
          });
        }, _proto.addOnBreadcrumb = function(fn, front) {
          front === void 0 && (front = !1), this._cbs.b[front ? "unshift" : "push"](fn);
        }, _proto.removeOnBreadcrumb = function(fn) {
          this._cbs.b = _$filter_11(this._cbs.b, function(f) {
            return f !== fn;
          });
        }, _proto.pauseSession = function() {
          return this._sessionDelegate.pauseSession(this);
        }, _proto.resumeSession = function() {
          return this._sessionDelegate.resumeSession(this);
        }, _proto.leaveBreadcrumb = function(message, metadata, type) {
          if (message = typeof message == "string" ? message : "", type = typeof type == "string" && _$includes_12(_$breadcrumbTypes_6, type) ? type : "manual", metadata = typeof metadata == "object" && metadata !== null ? metadata : {}, !!message) {
            var crumb = new _$Breadcrumb_1(message, metadata, type), ignore = _$syncCallbackRunner_24(this._cbs.b, crumb, "onBreadcrumb", this._logger);
            if (ignore) {
              this._logger.debug("Breadcrumb not attached due to onBreadcrumb callback");
              return;
            }
            this._breadcrumbs.push(crumb), this._breadcrumbs.length > this._config.maxBreadcrumbs && (this._breadcrumbs = this._breadcrumbs.slice(this._breadcrumbs.length - this._config.maxBreadcrumbs));
          }
        }, _proto._isBreadcrumbTypeEnabled = function(type) {
          var types = this._config.enabledBreadcrumbTypes;
          return types === null || _$includes_12(types, type);
        }, _proto.notify = function(maybeError, onError, postReportCallback) {
          postReportCallback === void 0 && (postReportCallback = noop);
          var event = _$Event_4.create(maybeError, !0, void 0, "notify()", this._depth + 1, this._logger);
          this._notify(event, onError, postReportCallback);
        }, _proto._notify = function(event, onError, postReportCallback) {
          var _this2 = this;
          if (postReportCallback === void 0 && (postReportCallback = noop), event.app = _$assign_10({}, event.app, {
            releaseStage: this._config.releaseStage,
            version: this._config.appVersion,
            type: this._config.appType
          }), event.context = event.context || this._context, event._metadata = _$assign_10({}, event._metadata, this._metadata), event._user = _$assign_10({}, event._user, this._user), event.breadcrumbs = this._breadcrumbs.slice(), __merge_2(event._features, this._features, event._featuresIndex), this._config.enabledReleaseStages !== null && !_$includes_12(this._config.enabledReleaseStages, this._config.releaseStage))
            return this._logger.warn("Event not sent due to releaseStage/enabledReleaseStages configuration"), postReportCallback(null, event);
          var originalSeverity = event.severity, onCallbackError = function(err) {
            _this2._logger.error("Error occurred in onError callback, continuing anyway\u2026"), _this2._logger.error(err);
          }, callbacks = [].concat(this._cbs.e).concat(onError);
          _$callbackRunner_7(callbacks, event, onCallbackError, function(err, shouldSend) {
            if (err && onCallbackError(err), !shouldSend)
              return _this2._logger.debug("Event not sent due to onError callback"), postReportCallback(null, event);
            _this2._isBreadcrumbTypeEnabled("error") && Client2.prototype.leaveBreadcrumb.call(_this2, event.errors[0].errorClass, {
              errorClass: event.errors[0].errorClass,
              errorMessage: event.errors[0].errorMessage,
              severity: event.severity
            }, "error"), originalSeverity !== event.severity && (event._handledState.severityReason = {
              type: "userCallbackSetSeverity"
            }), event.unhandled !== event._handledState.unhandled && (event._handledState.severityReason.unhandledOverridden = !0, event._handledState.unhandled = event.unhandled), _this2._session && (_this2._session._track(event), event._session = _this2._session), _this2._delivery.sendEvent({
              apiKey: event.apiKey || _this2._config.apiKey,
              notifier: _this2._notifier,
              events: [event]
            }, function(err2) {
              return postReportCallback(err2, event);
            });
          });
        }, Client2;
      }(), generateConfigErrorMessage = function(errors, rawInput) {
        var er = new Error(`Invalid configuration
` + _$map_15(_$keys_14(errors), function(key) {
          return "  - " + key + " " + errors[key] + ", got " + stringify(rawInput[key]);
        }).join(`

`));
        return er;
      }, stringify = function(val) {
        switch (typeof val) {
          case "string":
          case "number":
          case "object":
            return JSON.stringify(val);
          default:
            return String(val);
        }
      }, _$Client_2 = Client, _$jsonPayload_20 = {}, EVENT_REDACTION_PATHS = ["events.[].metaData", "events.[].breadcrumbs.[].metaData", "events.[].request"];
      _$jsonPayload_20.event = function(event, redactedKeys) {
        var payload = _$safeJsonStringify_31(event, null, null, {
          redactedPaths: EVENT_REDACTION_PATHS,
          redactedKeys
        });
        return payload.length > 1e6 && (event.events[0]._metadata = {
          notifier: `WARNING!
Serialized payload was ` + payload.length / 1e6 + `MB (limit = 1MB)
metadata was removed`
        }, payload = _$safeJsonStringify_31(event, null, null, {
          redactedPaths: EVENT_REDACTION_PATHS,
          redactedKeys
        })), payload;
      }, _$jsonPayload_20.session = function(session, redactedKeys) {
        var payload = _$safeJsonStringify_31(session, null, null);
        return payload;
      };
      var http = __require("http"), https = __require("https"), ___require_34 = __require("url"), parse = ___require_34.parse, _$request_34 = function(_ref, cb) {
        var url2 = _ref.url, headers = _ref.headers, body = _ref.body, agent = _ref.agent, didError = !1, onError = function(err) {
          didError || (didError = !0, cb(err));
        }, parsedUrl = parse(url2), secure = parsedUrl.protocol === "https:", transport = secure ? https : http, req = transport.request({
          method: "POST",
          hostname: parsedUrl.hostname,
          port: parsedUrl.port,
          path: parsedUrl.path,
          headers,
          agent
        });
        req.on("error", onError), req.on("response", function(res) {
          bufferResponse(res, function(err, body2) {
            if (err) return onError(err);
            if (res.statusCode < 200 || res.statusCode >= 300)
              return onError(new Error("Bad statusCode from API: " + res.statusCode + `
` + body2));
            cb(null, body2);
          });
        }), req.write(body), req.end();
      }, bufferResponse = function(stream, cb) {
        var data = "";
        stream.on("error", cb), stream.setEncoding("utf8"), stream.on("data", function(d) {
          data += d;
        }), stream.on("end", function() {
          return cb(null, data);
        });
      }, _$delivery_33 = function(client) {
        return {
          sendEvent: function(event, cb) {
            cb === void 0 && (cb = function() {
            });
            var body = _$jsonPayload_20.event(event, client._config.redactedKeys), _cb = function(err) {
              err && client._logger.error(`Event failed to send\u2026
` + (err && err.stack ? err.stack : err), err), body.length > 1e6 && client._logger.warn("Event oversized (" + (body.length / 1e6).toFixed(2) + " MB)"), cb(err);
            };
            try {
              _$request_34({
                url: client._config.endpoints.notify,
                headers: {
                  "Content-Type": "application/json",
                  "Bugsnag-Api-Key": event.apiKey || client._config.apiKey,
                  "Bugsnag-Payload-Version": "4",
                  "Bugsnag-Sent-At": (/* @__PURE__ */ new Date()).toISOString()
                },
                body,
                agent: client._config.agent
              }, function(err, body2) {
                return _cb(err);
              });
            } catch (e) {
              _cb(e);
            }
          },
          sendSession: function(session, cb) {
            cb === void 0 && (cb = function() {
            });
            var _cb = function(err) {
              err && client._logger.error(`Session failed to send\u2026
` + (err && err.stack ? err.stack : err), err), cb(err);
            };
            try {
              _$request_34({
                url: client._config.endpoints.sessions,
                headers: {
                  "Content-Type": "application/json",
                  "Bugsnag-Api-Key": client._config.apiKey,
                  "Bugsnag-Payload-Version": "1",
                  "Bugsnag-Sent-At": (/* @__PURE__ */ new Date()).toISOString()
                },
                body: _$jsonPayload_20.session(session, client._config.redactedKeys),
                agent: client._config.agent
              }, function(err) {
                return _cb(err);
              });
            } catch (e) {
              _cb(e);
            }
          }
        };
      };
      function ___extends_35() {
        return ___extends_35 = Object.assign ? Object.assign.bind() : function(target) {
          for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];
            for (var key in source)
              Object.prototype.hasOwnProperty.call(source, key) && (target[key] = source[key]);
          }
          return target;
        }, ___extends_35.apply(this, arguments);
      }
      var schema = _$config_3.schema, __os_35 = __require("os"), _require2 = __require("util"), inspect = _require2.inspect, _$config_35 = {
        appType: ___extends_35({}, schema.appType, {
          defaultValue: function() {
            return "node";
          }
        }),
        projectRoot: {
          defaultValue: function() {
            return process.cwd();
          },
          validate: function(value) {
            return value === null || _$stringWithLength_27(value);
          },
          message: "should be string"
        },
        hostname: {
          defaultValue: function() {
            return __os_35.hostname();
          },
          message: "should be a string",
          validate: function(value) {
            return value === null || _$stringWithLength_27(value);
          }
        },
        logger: ___extends_35({}, schema.logger, {
          defaultValue: function() {
            return getPrefixedConsole();
          }
        }),
        releaseStage: ___extends_35({}, schema.releaseStage, {
          defaultValue: function() {
            return process.env.NODE_ENV || "production";
          }
        }),
        agent: {
          defaultValue: function() {
          },
          message: "should be an HTTP(s) agent",
          validate: function(value) {
            return value === void 0 || isAgent(value);
          }
        },
        onUncaughtException: {
          defaultValue: function() {
            return function(err, event, logger) {
              logger.error("Uncaught exception" + getContext(event) + `, the process will now terminate\u2026
` + printError(err)), process.exit(1);
            };
          },
          message: "should be a function",
          validate: function(value) {
            return typeof value == "function";
          }
        },
        onUnhandledRejection: {
          defaultValue: function() {
            return function(err, event, logger) {
              logger.error("Unhandled rejection" + getContext(event) + `\u2026
` + printError(err));
            };
          },
          message: "should be a function",
          validate: function(value) {
            return typeof value == "function";
          }
        }
      }, printError = function(err) {
        return err && err.stack ? err.stack : inspect(err);
      }, getPrefixedConsole = function() {
        return ["debug", "info", "warn", "error"].reduce(function(accum, method) {
          var consoleMethod = console[method] || console.log;
          return accum[method] = consoleMethod.bind(console, "[bugsnag]"), accum;
        }, {});
      }, getContext = function(event) {
        return event.request && Object.keys(event.request).length ? " at " + event.request.httpMethod + " " + (event.request.path || event.request.url) : "";
      }, isAgent = function(value) {
        return typeof value == "object" && value !== null || typeof value == "boolean";
      }, appStart = /* @__PURE__ */ new Date(), reset = function() {
        appStart = /* @__PURE__ */ new Date();
      }, _$app_37 = {
        name: "appDuration",
        load: function(client) {
          return client.addOnError(function(event) {
            var now = /* @__PURE__ */ new Date();
            event.app.duration = now - appStart;
          }, !0), {
            reset
          };
        }
      }, _$nodeFallbackStack_22 = {};
      _$nodeFallbackStack_22.getStack = function() {
        return new Error().stack.split(`
`).slice(3).join(`
`);
      }, _$nodeFallbackStack_22.maybeUseFallbackStack = function(err, fallbackStack) {
        var lines = err.stack.split(`
`);
        return (lines.length === 1 || lines.length === 2 && /at Error \(native\)/.test(lines[1])) && (err.stack = lines[0] + `
` + fallbackStack), err;
      };
      var _$contextualize_38 = {}, domain = __require("domain"), getStack = _$nodeFallbackStack_22.getStack, maybeUseFallbackStack = _$nodeFallbackStack_22.maybeUseFallbackStack;
      _$contextualize_38 = {
        name: "contextualize",
        load: function(client) {
          var contextualize = function(fn, onError) {
            var fallbackStack = getStack(), dom = domain.create();
            dom.on("error", function(err) {
              err.stack && maybeUseFallbackStack(err, fallbackStack);
              var event = client.Event.create(err, !0, {
                severity: "error",
                unhandled: !0,
                severityReason: {
                  type: "unhandledException"
                }
              }, "contextualize()", 1);
              client._notify(event, onError, function(e, event2) {
                e && client._logger.error("Failed to send event to Bugsnag"), client._config.onUncaughtException(err, event2, client._logger);
              });
            }), process.nextTick(function() {
              return dom.run(fn);
            });
          };
          return contextualize;
        }
      }, _$contextualize_38.default = _$contextualize_38;
      var _$intercept_39 = {}, __getStack_39 = _$nodeFallbackStack_22.getStack, __maybeUseFallbackStack_39 = _$nodeFallbackStack_22.maybeUseFallbackStack;
      _$intercept_39 = {
        name: "intercept",
        load: function(client) {
          var intercept = function(onError, cb) {
            onError === void 0 && (onError = function() {
            }), typeof cb != "function" && (cb = onError, onError = function() {
            });
            var fallbackStack = __getStack_39();
            return function(err) {
              if (err) {
                err.stack && __maybeUseFallbackStack_39(err, fallbackStack);
                var event = client.Event.create(err, !0, {
                  severity: "warning",
                  unhandled: !1,
                  severityReason: {
                    type: "callbackErrorIntercept"
                  }
                }, "intercept()", 1);
                client._notify(event, onError);
                return;
              }
              for (var _len = arguments.length, data = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++)
                data[_key - 1] = arguments[_key];
              cb.apply(void 0, data);
            };
          };
          return intercept;
        }
      }, _$intercept_39.default = _$intercept_39;
      function ___extends_40() {
        return ___extends_40 = Object.assign ? Object.assign.bind() : function(target) {
          for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];
            for (var key in source)
              Object.prototype.hasOwnProperty.call(source, key) && (target[key] = source[key]);
          }
          return target;
        }, ___extends_40.apply(this, arguments);
      }
      var __os_40 = __require("os"), _$device_40 = {
        load: function(client) {
          var device = {
            osName: __os_40.platform() + " (" + __os_40.arch() + ")",
            osVersion: __os_40.release(),
            totalMemory: __os_40.totalmem(),
            hostname: client._config.hostname,
            runtimeVersions: {
              node: process.versions.node
            }
          };
          client._addOnSessionPayload(function(sp) {
            sp.device = ___extends_40({}, sp.device, device);
          }), client.addOnError(function(event) {
            event.device = ___extends_40({}, event.device, device, {
              freeMemory: __os_40.freemem(),
              time: /* @__PURE__ */ new Date()
            });
          }, !0);
        }
      }, ___require_23 = __require("path"), join = ___require_23.join, resolve = ___require_23.resolve, _$pathNormalizer_23 = function(p) {
        return join(resolve(p), "/");
      }, _$inProject_41 = {
        load: function(client) {
          return client.addOnError(function(event) {
            if (client._config.projectRoot) {
              var projectRoot = _$pathNormalizer_23(client._config.projectRoot), allFrames = event.errors.reduce(function(accum, er) {
                return accum.concat(er.stacktrace);
              }, []);
              allFrames.map(function(stackframe) {
                stackframe.inProject = typeof stackframe.file == "string" && stackframe.file.indexOf(projectRoot) === 0 && !/\/node_modules\//.test(stackframe.file);
              });
            }
          });
        }
      };
      function ___extends_42() {
        return ___extends_42 = Object.assign ? Object.assign.bind() : function(target) {
          for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];
            for (var key in source)
              Object.prototype.hasOwnProperty.call(source, key) && (target[key] = source[key]);
          }
          return target;
        }, ___extends_42.apply(this, arguments);
      }
      function _inheritsLoose(subClass, superClass) {
        subClass.prototype = Object.create(superClass.prototype), subClass.prototype.constructor = subClass, _setPrototypeOf(subClass, superClass);
      }
      function _setPrototypeOf(o, p) {
        return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(o2, p2) {
          return o2.__proto__ = p2, o2;
        }, _setPrototypeOf(o, p);
      }
      var SURROUNDING_LINES = 3, MAX_LINE_LENGTH = 200, ___require_42 = __require("fs"), createReadStream = ___require_42.createReadStream, ___require2_42 = __require("stream"), Writable = ___require2_42.Writable, pump = require_pump(), byline = require_byline(), path = __require("path"), _$surroundingCode_42 = {
        load: function(client) {
          if (client._config.sendCode) {
            var loadSurroundingCode = function(stackframe, cache) {
              return new Promise(function(resolve2, reject) {
                try {
                  if (!stackframe.lineNumber || !stackframe.file) return resolve2(stackframe);
                  var file = path.resolve(client._config.projectRoot, stackframe.file), cacheKey = file + "@" + stackframe.lineNumber;
                  if (cacheKey in cache)
                    return stackframe.code = cache[cacheKey], resolve2(stackframe);
                  getSurroundingCode(file, stackframe.lineNumber, function(err, code) {
                    return err || (stackframe.code = cache[cacheKey] = code), resolve2(stackframe);
                  });
                } catch {
                  return resolve2(stackframe);
                }
              });
            };
            client.addOnError(function(event) {
              return new Promise(function(resolve2, reject) {
                var cache = /* @__PURE__ */ Object.create(null), allFrames = event.errors.reduce(function(accum, er) {
                  return accum.concat(er.stacktrace);
                }, []);
                pMapSeries(allFrames.map(function(stackframe) {
                  return function() {
                    return loadSurroundingCode(stackframe, cache);
                  };
                })).then(resolve2).catch(reject);
              });
            });
          }
        },
        configSchema: {
          sendCode: {
            defaultValue: function() {
              return !0;
            },
            validate: function(value) {
              return value === !0 || value === !1;
            },
            message: "should be true or false"
          }
        }
      }, getSurroundingCode = function(file, lineNumber, cb) {
        var start = lineNumber - SURROUNDING_LINES, end = lineNumber + SURROUNDING_LINES, reader = createReadStream(file, {
          encoding: "utf8"
        }), splitter = new byline.LineStream({
          keepEmptyLines: !0
        }), slicer = new CodeRange({
          start,
          end
        });
        slicer.on("done", function() {
          return reader.destroy();
        }), pump(reader, splitter, slicer, function(err) {
          if (err && err.message !== "premature close") return cb(err);
          cb(null, slicer.getCode());
        });
      }, CodeRange = /* @__PURE__ */ function(_Writable) {
        _inheritsLoose(CodeRange2, _Writable);
        function CodeRange2(opts) {
          var _this;
          return _this = _Writable.call(this, ___extends_42({}, opts, {
            decodeStrings: !1
          })) || this, _this._start = opts.start, _this._end = opts.end, _this._n = 0, _this._code = {}, _this;
        }
        var _proto = CodeRange2.prototype;
        return _proto._write = function(chunk, enc, cb) {
          return this._n++, this._n < this._start ? cb(null) : this._n <= this._end ? (this._code[String(this._n)] = chunk.length <= MAX_LINE_LENGTH ? chunk : chunk.substr(0, MAX_LINE_LENGTH), cb(null)) : (this.emit("done"), cb(null));
        }, _proto.getCode = function() {
          return this._code;
        }, CodeRange2;
      }(Writable), pMapSeries = function(ps) {
        return new Promise(function(resolve2, reject) {
          var res = [];
          ps.reduce(function(accum, p) {
            return accum.then(function(r) {
              return res.push(r), p();
            });
          }, Promise.resolve()).then(function(r) {
            res.push(r);
          }).then(function() {
            resolve2(res.slice(1));
          });
        });
      }, _handler, _$uncaughtException_43 = {
        load: function(client) {
          client._config.autoDetectErrors && client._config.enabledErrorTypes.unhandledExceptions && (_handler = function(err) {
            var event = client.Event.create(err, !1, {
              severity: "error",
              unhandled: !0,
              severityReason: {
                type: "unhandledException"
              }
            }, "uncaughtException handler", 1);
            client._notify(event, function() {
            }, function(e, event2) {
              e && client._logger.error("Failed to send event to Bugsnag"), client._config.onUncaughtException(err, event2, client._logger);
            });
          }, process.on("uncaughtException", _handler));
        },
        destroy: function() {
          process.removeListener("uncaughtException", _handler);
        }
      }, ___handler_44, _$unhandledRejection_44 = {
        load: function(client) {
          !client._config.autoDetectErrors || !client._config.enabledErrorTypes.unhandledRejections || (___handler_44 = function(err) {
            var event = client.Event.create(err, !1, {
              severity: "error",
              unhandled: !0,
              severityReason: {
                type: "unhandledPromiseRejection"
              }
            }, "unhandledRejection handler", 1);
            return new Promise(function(resolve2) {
              client._notify(event, function() {
              }, function(e, event2) {
                e && client._logger.error("Failed to send event to Bugsnag"), client._config.onUnhandledRejection(err, event2, client._logger), resolve2();
              });
            });
          }, process.prependListener ? process.prependListener("unhandledRejection", ___handler_44) : process.on("unhandledRejection", ___handler_44));
        },
        destroy: function() {
          process.removeListener("unhandledRejection", ___handler_44);
        }
      }, _$cloneClient_8 = {}, onCloneCallbacks = [];
      _$cloneClient_8 = function(client) {
        var clone = new client.Client({}, {}, [], client._notifier);
        return clone._config = client._config, clone._breadcrumbs = client._breadcrumbs.slice(), clone._metadata = _$assign_10({}, client._metadata), clone._features = [].concat(client._features), clone._featuresIndex = _$assign_10({}, client._featuresIndex), clone._user = _$assign_10({}, client._user), clone._context = client._context, clone._cbs = {
          e: client._cbs.e.slice(),
          s: client._cbs.s.slice(),
          sp: client._cbs.sp.slice(),
          b: client._cbs.b.slice()
        }, clone._logger = client._logger, clone._delivery = client._delivery, clone._sessionDelegate = client._sessionDelegate, onCloneCallbacks.forEach(function(callback) {
          callback(clone);
        }), clone;
      }, _$cloneClient_8.registerCallback = function(callback) {
        onCloneCallbacks.push(callback);
      };
      var _$Backoff_45 = Backoff;
      function Backoff(opts) {
        opts = opts || {}, this.ms = opts.min || 100, this.max = opts.max || 1e4, this.factor = opts.factor || 2, this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0, this.attempts = 0;
      }
      Backoff.prototype.duration = function() {
        var ms = this.ms * Math.pow(this.factor, this.attempts++);
        if (this.jitter) {
          var rand = Math.random(), deviation = Math.floor(rand * this.jitter * ms);
          ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;
        }
        return Math.min(ms, this.max) | 0;
      }, Backoff.prototype.reset = function() {
        this.attempts = 0;
      };
      function _assertThisInitialized(self2) {
        if (self2 === void 0)
          throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
        return self2;
      }
      function ___inheritsLoose_47(subClass, superClass) {
        subClass.prototype = Object.create(superClass.prototype), subClass.prototype.constructor = subClass, ___setPrototypeOf_47(subClass, superClass);
      }
      function ___setPrototypeOf_47(o, p) {
        return ___setPrototypeOf_47 = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(o2, p2) {
          return o2.__proto__ = p2, o2;
        }, ___setPrototypeOf_47(o, p);
      }
      var DEFAULT_SUMMARY_INTERVAL = 10 * 1e3, Emitter = __require("events").EventEmitter, _$tracker_47 = /* @__PURE__ */ function(_Emitter) {
        ___inheritsLoose_47(SessionTracker, _Emitter);
        function SessionTracker(intervalLength) {
          var _this;
          return _this = _Emitter.call(this) || this, _this._sessions = /* @__PURE__ */ new Map(), _this._interval = null, _this._intervalLength = intervalLength || DEFAULT_SUMMARY_INTERVAL, _this._summarize = _this._summarize.bind(_assertThisInitialized(_this)), _this;
        }
        var _proto = SessionTracker.prototype;
        return _proto.start = function() {
          this._interval || (this._interval = setInterval(this._summarize, this._intervalLength).unref());
        }, _proto.stop = function() {
          clearInterval(this._interval), this._interval = null;
        }, _proto.track = function(session) {
          var key = dateToMsKey(session.startedAt), cur = this._sessions.get(key);
          return this._sessions.set(key, typeof cur > "u" ? 1 : cur + 1), session;
        }, _proto._summarize = function() {
          var _this2 = this, summary = [];
          this._sessions.forEach(function(val, key) {
            summary.push({
              startedAt: key,
              sessionsStarted: val
            }), _this2._sessions.delete(key);
          }), summary.length && this.emit("summary", summary);
        }, SessionTracker;
      }(Emitter), dateToMsKey = function(d) {
        var dk = new Date(d);
        return dk.setSeconds(0), dk.setMilliseconds(0), dk.toISOString();
      }, _$session_46 = {
        load: function(client) {
          var sessionTracker = new _$tracker_47(client._config.sessionSummaryInterval);
          sessionTracker.on("summary", sendSessionSummary(client)), sessionTracker.start(), client._sessionDelegate = {
            startSession: function(client2, session) {
              var sessionClient = _$cloneClient_8(client2);
              return sessionClient._session = session, sessionClient._pausedSession = null, sessionTracker.track(sessionClient._session), sessionClient;
            },
            pauseSession: function(client2) {
              client2._pausedSession = client2._session, client2._session = null;
            },
            resumeSession: function(client2) {
              return client2._session ? client2 : client2._pausedSession ? (client2._session = client2._pausedSession, client2._pausedSession = null, client2) : client2.startSession();
            }
          };
        },
        configSchema: {
          sessionSummaryInterval: {
            defaultValue: function() {
            },
            validate: function(value) {
              return value === void 0 || _$intRange_25()(value);
            },
            message: "should be a positive integer"
          }
        }
      }, sendSessionSummary = function(client) {
        return function(sessionCounts) {
          if (client._config.enabledReleaseStages !== null && !client._config.enabledReleaseStages.includes(client._config.releaseStage)) {
            client._logger.warn("Session not sent due to releaseStage/enabledReleaseStages configuration");
            return;
          }
          if (!sessionCounts.length) return;
          var backoff = new _$Backoff_45({
            min: 1e3,
            max: 1e4
          }), maxAttempts = 10;
          req(handleRes);
          function handleRes(err) {
            if (!err) {
              var sessionCount = sessionCounts.reduce(function(accum, s) {
                return accum + s.sessionsStarted;
              }, 0);
              return client._logger.debug(sessionCount + " session(s) reported");
            }
            if (backoff.attempts === 10) {
              client._logger.error("Session delivery failed, max retries exceeded", err);
              return;
            }
            client._logger.debug("Session delivery failed, retry #" + (backoff.attempts + 1) + "/" + maxAttempts, err), setTimeout(function() {
              return req(handleRes);
            }, backoff.duration());
          }
          function req(cb) {
            var payload = {
              notifier: client._notifier,
              device: {},
              app: {
                releaseStage: client._config.releaseStage,
                version: client._config.appVersion,
                type: client._config.appType
              },
              sessionCounts
            }, ignore = _$syncCallbackRunner_24(client._cbs.sp, payload, "onSessionPayload", client._logger);
            if (ignore)
              return client._logger.debug("Session not sent due to onSessionPayload callback"), cb(null);
            client._delivery.sendSession(payload, cb);
          }
        };
      }, _$pathNormaliser_48 = {
        load: function(client) {
          client.addOnError(function(event) {
            var allFrames = event.errors.reduce(function(accum, er) {
              return accum.concat(er.stacktrace);
            }, []);
            allFrames.forEach(function(stackframe) {
              typeof stackframe.file == "string" && (stackframe.file = stackframe.file.replace(/\\/g, "/"));
            });
          });
        }
      }, _$stripProjectRoot_49 = {
        load: function(client) {
          return client.addOnError(function(event) {
            if (client._config.projectRoot) {
              var projectRoot = _$pathNormalizer_23(client._config.projectRoot), allFrames = event.errors.reduce(function(accum, er) {
                return accum.concat(er.stacktrace);
              }, []);
              allFrames.map(function(stackframe) {
                typeof stackframe.file == "string" && stackframe.file.indexOf(projectRoot) === 0 && (stackframe.file = stackframe.file.replace(projectRoot, ""));
              });
            }
          });
        }
      }, _$notifier_36 = {};
      function ___extends_36() {
        return ___extends_36 = Object.assign ? Object.assign.bind() : function(target) {
          for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];
            for (var key in source)
              Object.prototype.hasOwnProperty.call(source, key) && (target[key] = source[key]);
          }
          return target;
        }, ___extends_36.apply(this, arguments);
      }
      var name = "Bugsnag Node", version = "7.25.0", url = "https://github.com/bugsnag/bugsnag-js";
      _$Event_4.__type = "nodejs";
      var __schema_36 = ___extends_36({}, _$config_3.schema, _$config_35);
      delete __schema_36.enabledBreadcrumbTypes;
      var internalPlugins = [_$app_37, _$surroundingCode_42, _$inProject_41, _$stripProjectRoot_49, _$session_46, _$device_40, _$uncaughtException_43, _$unhandledRejection_44, _$intercept_39, _$contextualize_38, _$pathNormaliser_48], Bugsnag2 = {
        _client: null,
        createClient: function(opts) {
          typeof opts == "string" && (opts = {
            apiKey: opts
          }), opts || (opts = {});
          var bugsnag = new _$Client_2(opts, __schema_36, internalPlugins, {
            name,
            version,
            url
          });
          return bugsnag._setDelivery(_$delivery_33), bugsnag._logger.debug("Loaded!"), bugsnag.leaveBreadcrumb = function() {
            bugsnag._logger.warn("Breadcrumbs are not supported in Node.js yet");
          }, bugsnag._config.enabledBreadcrumbTypes = [], bugsnag;
        },
        start: function(opts) {
          return Bugsnag2._client ? (Bugsnag2._client._logger.warn("Bugsnag.start() was called more than once. Ignoring."), Bugsnag2._client) : (Bugsnag2._client = Bugsnag2.createClient(opts), Bugsnag2._client);
        },
        isStarted: function() {
          return Bugsnag2._client != null;
        }
      };
      return Object.keys(_$Client_2.prototype).forEach(function(m) {
        /^_/.test(m) || (Bugsnag2[m] = function() {
          if (!Bugsnag2._client) return console.error("Bugsnag." + m + "() was called before Bugsnag.start()");
          Bugsnag2._client._depth += 1;
          var ret = Bugsnag2._client[m].apply(Bugsnag2._client, arguments);
          return Bugsnag2._client._depth -= 1, ret;
        });
      }), _$notifier_36 = Bugsnag2, _$notifier_36.Client = _$Client_2, _$notifier_36.Event = _$Event_4, _$notifier_36.Session = _$Session_32, _$notifier_36.Breadcrumb = _$Breadcrumb_1, _$notifier_36.default = Bugsnag2, _$notifier_36;
    });
  }
});

// ../../node_modules/.pnpm/@bugsnag+js@7.25.0/node_modules/@bugsnag/js/node/notifier.js
var require_notifier = __commonJS({
  "../../node_modules/.pnpm/@bugsnag+js@7.25.0/node_modules/@bugsnag/js/node/notifier.js"(exports, module) {
    init_cjs_shims();
    module.exports = require_bugsnag();
  }
});

// ../cli-kit/dist/public/node/error-handler.js
init_cjs_shims();
var import_core = __toESM(require_lib()), import_stacktracey = __toESM(require_stacktracey()), import_js = __toESM(require_notifier());
import { realpath } from "fs/promises";
async function errorHandler(error, config) {
  if (error instanceof CancelExecution)
    error.message && error.message !== "" && outputInfo(`\u2728  ${error.message}`);
  else if (!(error instanceof AbortSilentError))
    return errorMapper(error).then((error2) => handler(error2)).then((mappedError) => reportError(mappedError, config));
}
var reportError = async (error, config) => {
  let exitMode = "expected_error";
  shouldReportErrorAsUnexpected(error) && (exitMode = "unexpected_error"), config !== void 0 && await reportAnalyticsEvent({ config, errorMessage: error instanceof Error ? error.message : void 0, exitMode }), await sendErrorToBugsnag(error, exitMode);
};
async function sendErrorToBugsnag(error, exitMode) {
  try {
    if (isLocalEnvironment() || import_core.settings.debug)
      return outputDebug("Skipping Bugsnag report"), { reported: !1, error, unhandled: void 0 };
    let unhandled = exitMode === "unexpected_error", reportableError, stacktrace, report = !1;
    error instanceof Error ? (report = !0, reportableError = new Error(error.message), stacktrace = error.stack) : typeof error == "string" && error.trim().length !== 0 ? (report = !0, reportableError = new Error(error), stacktrace = reportableError.stack) : (report = !1, reportableError = new Error("Unknown error"));
    let formattedStacktrace = new import_stacktracey.default(stacktrace ?? "").clean().items.map((item) => {
      let filePath = cleanSingleStackTracePath(item.file);
      return `    at ${item.callee} (${filePath}:${item.line}:${item.column})`;
    }).join(`
`);
    reportableError.stack = `Error: ${reportableError.message}
${formattedStacktrace}`;
    let withinRateLimit = !1;
    return await runWithRateLimit({
      key: "send-error-to-bugsnag",
      ...reportingRateLimit,
      task: async () => {
        withinRateLimit = !0;
      }
    }), withinRateLimit || (outputDebug("Skipping Bugsnag report due to rate limiting"), report = !1), report && (initializeBugsnag(), await new Promise((resolve, reject) => {
      outputDebug(`Reporting ${unhandled ? "unhandled" : "handled"} error to Bugsnag: ${reportableError.message}`);
      let eventHandler = (event) => {
        event.severity = "error", event.unhandled = unhandled;
      }, errorHandler2 = (error2) => {
        error2 ? reject(error2) : resolve(reportableError);
      };
      import_js.default.notify(reportableError, eventHandler, errorHandler2);
    })), { error: reportableError, reported: report, unhandled };
  } catch (err) {
    return outputDebug(`Error reporting to Bugsnag: ${err}`), { error, reported: !1, unhandled: void 0 };
  }
}
function cleanStackFrameFilePath({ currentFilePath, projectRoot, pluginLocations }) {
  let fullLocation = isAbsolutePath(currentFilePath) ? currentFilePath : joinPath(projectRoot, currentFilePath), matchingPluginPath = pluginLocations.filter(({ pluginPath }) => fullLocation.startsWith(pluginPath))[0];
  return matchingPluginPath !== void 0 ? joinPath(matchingPluginPath.name, relativePath(matchingPluginPath.pluginPath, fullLocation)) : currentFilePath.replace(/.*node_modules\//, "");
}
async function registerCleanBugsnagErrorsFromWithinPlugins(config) {
  let bugsnagConfigProjectRoot = import_js.default?._client?._config?.projectRoot ?? cwd(), projectRoot = normalizePath(bugsnagConfigProjectRoot), pluginLocations = await Promise.all([...config.plugins].map(async ([_, plugin]) => {
    let followSymlinks = await realpath(plugin.root);
    return { name: plugin.name, pluginPath: normalizePath(followSymlinks) };
  }));
  initializeBugsnag(), import_js.default.addOnError(async (event) => {
    event.errors.forEach((error) => {
      error.stacktrace.forEach((stackFrame) => {
        stackFrame.file = cleanStackFrameFilePath({ currentFilePath: stackFrame.file, projectRoot, pluginLocations });
      });
    });
    try {
      await addBugsnagMetadata(event, config);
    } catch (metadataError) {
      outputDebug(`There was an error adding metadata to the Bugsnag report; Ignoring and carrying on ${metadataError}`);
    }
  });
}
async function addBugsnagMetadata(event, config) {
  let publicData = getAllPublicMetadata(), { commandStartOptions } = getAllSensitiveMetadata(), { startCommand } = commandStartOptions ?? {}, { "@shopify/app": appPublic, ...otherPluginsPublic } = await fanoutHooks(config, "public_command_metadata", {}), environment = await getEnvironmentData(config), allMetadata = {
    command: startCommand,
    ...appPublic,
    ...publicData,
    ...environment,
    pluginData: otherPluginsPublic
  }, appData = {}, commandData = {}, environmentData = {}, miscData = {}, appKeys = ["api_key", "business_platform_id", "partner_id", "project_type"], commandKeys = ["command"], environmentKeys = ["cli_version", "node_version", "uname"];
  Object.entries(allMetadata).forEach(([key, value]) => {
    key.startsWith("app_") || appKeys.includes(key) ? appData[key] = value : key.startsWith("cmd_") || commandKeys.includes(key) ? commandData[key] = value : key.startsWith("env_") || environmentKeys ? environmentData[key] = value : miscData[key] = value;
  }), Object.entries({
    "Shopify App": appData,
    Command: commandData,
    Environment: environmentData,
    Misc: miscData
  }).forEach(([section, values]) => {
    event.addMetadata(section, values);
  });
}
function initializeBugsnag() {
  import_js.default.isStarted() || import_js.default.start({
    appType: "node",
    apiKey: bugsnagApiKey,
    logger: null,
    appVersion: CLI_KIT_VERSION,
    autoTrackSessions: !1,
    autoDetectErrors: !1,
    enabledReleaseStages: ["production"],
    endpoints: {
      notify: "https://error-analytics-production.shopifysvc.com",
      sessions: "https://error-analytics-sessions-production.shopifysvc.com"
    }
  });
}

export {
  require_error_stack_parser,
  require_iserror,
  require_stack_generator,
  require_end_of_stream,
  require_pump,
  require_byline,
  errorHandler,
  sendErrorToBugsnag,
  cleanStackFrameFilePath,
  registerCleanBugsnagErrorsFromWithinPlugins,
  addBugsnagMetadata
};
//# sourceMappingURL=chunk-VLSFD7SJ.js.map
