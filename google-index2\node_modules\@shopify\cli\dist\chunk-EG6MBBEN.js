import {
  __commonJS,
  __require,
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/commondir@1.0.1/node_modules/commondir/index.js
var require_commondir = __commonJS({
  "../../node_modules/.pnpm/commondir@1.0.1/node_modules/commondir/index.js"(exports, module) {
    init_cjs_shims();
    var path2 = __require("path");
    module.exports = function(basedir, relfiles) {
      if (relfiles)
        var files = relfiles.map(function(r) {
          return path2.resolve(basedir, r);
        });
      else
        var files = basedir;
      var res = files.slice(1).reduce(function(ps, file) {
        if (!file.match(/^([A-Za-z]:)?\/|\\/))
          throw new Error("relative path without a basedir");
        for (var xs = file.split(/\/+|\\+/), i = 0; ps[i] === xs[i] && i < Math.min(ps.length, xs.length); i++) ;
        return ps.slice(0, i);
      }, files[0].split(/\/+|\\+/));
      return res.length > 1 ? res.join("/") : "/";
    };
  }
});

// ../cli-kit/dist/public/node/path.js
init_cjs_shims();
var import_commondir = __toESM(require_commondir());

// ../../node_modules/.pnpm/pathe@1.1.2/node_modules/pathe/dist/index.mjs
init_cjs_shims();

// ../../node_modules/.pnpm/pathe@1.1.2/node_modules/pathe/dist/shared/pathe.ff20891b.mjs
init_cjs_shims();
var _DRIVE_LETTER_START_RE = /^[A-Za-z]:\//;
function normalizeWindowsPath(input = "") {
  return input && input.replace(/\\/g, "/").replace(_DRIVE_LETTER_START_RE, (r) => r.toUpperCase());
}
var _UNC_REGEX = /^[/\\]{2}/, _IS_ABSOLUTE_RE = /^[/\\](?![/\\])|^[/\\]{2}(?!\.)|^[A-Za-z]:[/\\]/, _DRIVE_LETTER_RE = /^[A-Za-z]:$/, _ROOT_FOLDER_RE = /^\/([A-Za-z]:)?$/;
var delimiter = ":", normalize = function(path2) {
  if (path2.length === 0)
    return ".";
  path2 = normalizeWindowsPath(path2);
  let isUNCPath = path2.match(_UNC_REGEX), isPathAbsolute = isAbsolute(path2), trailingSeparator = path2[path2.length - 1] === "/";
  return path2 = normalizeString(path2, !isPathAbsolute), path2.length === 0 ? isPathAbsolute ? "/" : trailingSeparator ? "./" : "." : (trailingSeparator && (path2 += "/"), _DRIVE_LETTER_RE.test(path2) && (path2 += "/"), isUNCPath ? isPathAbsolute ? `//${path2}` : `//./${path2}` : isPathAbsolute && !isAbsolute(path2) ? `/${path2}` : path2);
}, join = function(...arguments_) {
  if (arguments_.length === 0)
    return ".";
  let joined;
  for (let argument of arguments_)
    argument && argument.length > 0 && (joined === void 0 ? joined = argument : joined += `/${argument}`);
  return joined === void 0 ? "." : normalize(joined.replace(/\/\/+/g, "/"));
};
function cwd() {
  return typeof process < "u" && typeof process.cwd == "function" ? process.cwd().replace(/\\/g, "/") : "/";
}
var resolve = function(...arguments_) {
  arguments_ = arguments_.map((argument) => normalizeWindowsPath(argument));
  let resolvedPath = "", resolvedAbsolute = !1;
  for (let index = arguments_.length - 1; index >= -1 && !resolvedAbsolute; index--) {
    let path2 = index >= 0 ? arguments_[index] : cwd();
    !path2 || path2.length === 0 || (resolvedPath = `${path2}/${resolvedPath}`, resolvedAbsolute = isAbsolute(path2));
  }
  return resolvedPath = normalizeString(resolvedPath, !resolvedAbsolute), resolvedAbsolute && !isAbsolute(resolvedPath) ? `/${resolvedPath}` : resolvedPath.length > 0 ? resolvedPath : ".";
};
function normalizeString(path2, allowAboveRoot) {
  let res = "", lastSegmentLength = 0, lastSlash = -1, dots = 0, char = null;
  for (let index = 0; index <= path2.length; ++index) {
    if (index < path2.length)
      char = path2[index];
    else {
      if (char === "/")
        break;
      char = "/";
    }
    if (char === "/") {
      if (!(lastSlash === index - 1 || dots === 1)) if (dots === 2) {
        if (res.length < 2 || lastSegmentLength !== 2 || res[res.length - 1] !== "." || res[res.length - 2] !== ".") {
          if (res.length > 2) {
            let lastSlashIndex = res.lastIndexOf("/");
            lastSlashIndex === -1 ? (res = "", lastSegmentLength = 0) : (res = res.slice(0, lastSlashIndex), lastSegmentLength = res.length - 1 - res.lastIndexOf("/")), lastSlash = index, dots = 0;
            continue;
          } else if (res.length > 0) {
            res = "", lastSegmentLength = 0, lastSlash = index, dots = 0;
            continue;
          }
        }
        allowAboveRoot && (res += res.length > 0 ? "/.." : "..", lastSegmentLength = 2);
      } else
        res.length > 0 ? res += `/${path2.slice(lastSlash + 1, index)}` : res = path2.slice(lastSlash + 1, index), lastSegmentLength = index - lastSlash - 1;
      lastSlash = index, dots = 0;
    } else char === "." && dots !== -1 ? ++dots : dots = -1;
  }
  return res;
}
var isAbsolute = function(p) {
  return _IS_ABSOLUTE_RE.test(p);
};
var _EXTNAME_RE = /.(\.[^./]+)$/, extname = function(p) {
  let match = _EXTNAME_RE.exec(normalizeWindowsPath(p));
  return match && match[1] || "";
}, relative = function(from, to) {
  let _from = resolve(from).replace(_ROOT_FOLDER_RE, "$1").split("/"), _to = resolve(to).replace(_ROOT_FOLDER_RE, "$1").split("/");
  if (_to[0][1] === ":" && _from[0][1] === ":" && _from[0] !== _to[0])
    return _to.join("/");
  let _fromCopy = [..._from];
  for (let segment of _fromCopy) {
    if (_to[0] !== segment)
      break;
    _from.shift(), _to.shift();
  }
  return [..._from.map(() => ".."), ..._to].join("/");
}, dirname = function(p) {
  let segments = normalizeWindowsPath(p).replace(/\/$/, "").split("/").slice(0, -1);
  return segments.length === 1 && _DRIVE_LETTER_RE.test(segments[0]) && (segments[0] += "/"), segments.join("/") || (isAbsolute(p) ? "/" : ".");
};
var basename = function(p, extension) {
  let lastSegment = normalizeWindowsPath(p).split("/").pop();
  return extension && lastSegment.endsWith(extension) ? lastSegment.slice(0, -extension.length) : lastSegment;
};

// ../cli-kit/dist/public/node/path.js
import { fileURLToPath } from "url";
function joinPath(...paths) {
  return join(...paths);
}
function normalizePath(path2) {
  return normalize(path2);
}
function resolvePath(...paths) {
  return resolve(...paths);
}
function relativePath(from, to) {
  return relative(from, to);
}
function isAbsolutePath(path2) {
  return isAbsolute(path2);
}
function dirname2(path2) {
  return dirname(path2);
}
function basename2(path2, ext) {
  return basename(path2, ext);
}
function extname2(path2) {
  return extname(path2);
}
function relativizePath(path2, dir = cwd2()) {
  let result = (0, import_commondir.default)([path2, dir]), relativePath2 = relative(dir, path2), relativeComponents = relativePath2.split("/").filter((component) => component === "..").length;
  return result === "/" || relativePath2 === "" || relativeComponents > 2 ? path2 : relativePath2;
}
function isSubpath(mainPath, subpath) {
  let relativePath2 = relative(mainPath, subpath);
  return !relativePath2.startsWith("..") && !isAbsolutePath(relativePath2);
}
function moduleDirectory(moduleURL) {
  return dirname2(fileURLToPath(moduleURL));
}
function cwd2() {
  return normalize(process.env.INIT_CWD ? process.env.INIT_CWD : process.cwd());
}
function sniffForPath(argv = process.argv) {
  let pathFlagIndex = argv.indexOf("--path");
  if (pathFlagIndex === -1)
    return argv.find((arg) => arg.startsWith("--path="))?.split("=")[1];
  let pathFlag = argv[pathFlagIndex + 1];
  if (!(!pathFlag || pathFlag.startsWith("-")))
    return pathFlag;
}
function sniffForJson(argv = process.argv) {
  return argv.includes("--json") || argv.includes("-j");
}

export {
  delimiter,
  joinPath,
  normalizePath,
  resolvePath,
  relativePath,
  isAbsolutePath,
  dirname2 as dirname,
  basename2 as basename,
  extname2 as extname,
  relativizePath,
  isSubpath,
  moduleDirectory,
  cwd2 as cwd,
  sniffForPath,
  sniffForJson
};
//# sourceMappingURL=chunk-EG6MBBEN.js.map
