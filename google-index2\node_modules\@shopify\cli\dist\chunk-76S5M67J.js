import {
  asyncTasks
} from "./chunk-AJYT5DKD.js";
import {
  prompts
} from "./chunk-Z7TXQNKI.js";
import {
  staticService
} from "./chunk-DKG2TMNN.js";
import {
  base_command_default
} from "./chunk-2IA24ROR.js";
import {
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/commands/kitchen-sink/index.ts
init_cjs_shims();
var KitchenSinkAll = class extends base_command_default {
  static {
    this.description = "View all the available UI kit components";
  }
  static {
    this.hiddenAliases = ["kitchen-sink all"];
  }
  static {
    this.hidden = !0;
  }
  async run() {
    await staticService(), await prompts(), await asyncTasks();
  }
};

export {
  KitchenSinkAll
};
//# sourceMappingURL=chunk-76S5M67J.js.map
