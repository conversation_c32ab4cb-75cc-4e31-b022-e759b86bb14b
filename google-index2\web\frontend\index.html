<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <!-- Ensures that the UI is properly scaled in the Shopify Mobile app -->
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <meta name="shopify-api-key" content="0dbbb2925aabd5e4c118b6c0a826289b" />
    <script src="https://cdn.shopify.com/shopifycloud/app-bridge.js"></script>
    
    <script type="module">
      if (!import.meta.env || !import.meta.env.PROD) {
        // Because the base HTML is rendered in the server side, we need to manually embed the code to enable HMR in our
        // code, so that the vite server is properly enabled to run HMR
        const script = document.createElement('script');
        script.setAttribute('type', "module");
        script.setAttribute('src', "./dev_embed.js");
        document.getElementsByTagName('head')[0].append(script);
      }
    </script>
  </head>
  <body>
    <div id="app"><!--index.jsx injects App.jsx here--></div>
    <script type="module" src="/index.jsx"></script>
  </body>
</html>
