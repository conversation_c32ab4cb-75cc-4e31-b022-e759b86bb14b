import {
  base_command_default
} from "./chunk-2IA24ROR.js";
import {
  require_lib
} from "./chunk-F7F4BQYW.js";
import {
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/commands/help.ts
init_cjs_shims();
var import_core = __toESM(require_lib(), 1);
var HelpCommand = class _HelpCommand extends base_command_default {
  static {
    this.args = {
      command: import_core.Args.string({ description: "Command to show help for.", required: !1 })
    };
  }
  static {
    this.description = "Display help for Shopify CLI";
  }
  static {
    this.usage = "help [command] [flags]";
  }
  static {
    this.flags = {
      "nested-commands": import_core.Flags.boolean({
        char: "n",
        description: "Include all nested commands in the output.",
        env: "SHOPIFY_FLAG_CLI_NESTED_COMMANDS",
        default: !1
      })
    };
  }
  static {
    this.strict = !1;
  }
  async run() {
    let { argv, flags } = await this.parse(_HelpCommand), Help = await (0, import_core.loadHelpClass)(this.config);
    await new Help(this.config, { all: flags["nested-commands"] }).showHelp(argv);
  }
};

export {
  HelpCommand
};
//# sourceMappingURL=chunk-Y4DWHLHN.js.map
