{"$schema": "http://json-schema.org/draft-07/schema#", "$comment": "A @theme block entry", "type": "object", "additionalProperties": false, "required": ["type"], "properties": {"type": {"const": "@theme", "description": "The \"@theme\" type denotes that this container accepts theme blocks that live in the blocks/ folder of the theme.", "markdownDescription": "The `@theme` type denotes that this container accepts theme blocks that live in the `blocks/` folder of the theme.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#blocks)"}}}