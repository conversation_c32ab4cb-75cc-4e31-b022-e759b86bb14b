import {
  require_anymatch,
  require_is_binary_path,
  require_readdirp
} from "./chunk-F7J5CUMZ.js";
import {
  require_normalize_path
} from "./chunk-X7YTIMNN.js";
import {
  require_braces,
  require_glob_parent,
  require_is_glob
} from "./chunk-7IK72W75.js";
import {
  __commonJS,
  __require,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/chokidar@3.5.3/node_modules/chokidar/lib/constants.js
var require_constants = __commonJS({
  "../../node_modules/.pnpm/chokidar@3.5.3/node_modules/chokidar/lib/constants.js"(exports) {
    "use strict";
    init_cjs_shims();
    var { sep } = __require("path"), { platform } = process, os = __require("os");
    exports.EV_ALL = "all";
    exports.EV_READY = "ready";
    exports.EV_ADD = "add";
    exports.EV_CHANGE = "change";
    exports.EV_ADD_DIR = "addDir";
    exports.EV_UNLINK = "unlink";
    exports.EV_UNLINK_DIR = "unlinkDir";
    exports.EV_RAW = "raw";
    exports.EV_ERROR = "error";
    exports.STR_DATA = "data";
    exports.STR_END = "end";
    exports.STR_CLOSE = "close";
    exports.FSEVENT_CREATED = "created";
    exports.FSEVENT_MODIFIED = "modified";
    exports.FSEVENT_DELETED = "deleted";
    exports.FSEVENT_MOVED = "moved";
    exports.FSEVENT_CLONED = "cloned";
    exports.FSEVENT_UNKNOWN = "unknown";
    exports.FSEVENT_TYPE_FILE = "file";
    exports.FSEVENT_TYPE_DIRECTORY = "directory";
    exports.FSEVENT_TYPE_SYMLINK = "symlink";
    exports.KEY_LISTENERS = "listeners";
    exports.KEY_ERR = "errHandlers";
    exports.KEY_RAW = "rawEmitters";
    exports.HANDLER_KEYS = [exports.KEY_LISTENERS, exports.KEY_ERR, exports.KEY_RAW];
    exports.DOT_SLASH = `.${sep}`;
    exports.BACK_SLASH_RE = /\\/g;
    exports.DOUBLE_SLASH_RE = /\/\//;
    exports.SLASH_OR_BACK_SLASH_RE = /[/\\]/;
    exports.DOT_RE = /\..*\.(sw[px])$|~$|\.subl.*\.tmp/;
    exports.REPLACER_RE = /^\.[/\\]/;
    exports.SLASH = "/";
    exports.SLASH_SLASH = "//";
    exports.BRACE_START = "{";
    exports.BANG = "!";
    exports.ONE_DOT = ".";
    exports.TWO_DOTS = "..";
    exports.STAR = "*";
    exports.GLOBSTAR = "**";
    exports.ROOT_GLOBSTAR = "/**/*";
    exports.SLASH_GLOBSTAR = "/**";
    exports.DIR_SUFFIX = "Dir";
    exports.ANYMATCH_OPTS = { dot: !0 };
    exports.STRING_TYPE = "string";
    exports.FUNCTION_TYPE = "function";
    exports.EMPTY_STR = "";
    exports.EMPTY_FN = () => {
    };
    exports.IDENTITY_FN = (val) => val;
    exports.isWindows = platform === "win32";
    exports.isMacos = platform === "darwin";
    exports.isLinux = platform === "linux";
    exports.isIBMi = os.type() === "OS400";
  }
});

// ../../node_modules/.pnpm/chokidar@3.5.3/node_modules/chokidar/lib/nodefs-handler.js
var require_nodefs_handler = __commonJS({
  "../../node_modules/.pnpm/chokidar@3.5.3/node_modules/chokidar/lib/nodefs-handler.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var fs = __require("fs"), sysPath = __require("path"), { promisify } = __require("util"), isBinaryPath = require_is_binary_path(), {
      isWindows,
      isLinux,
      EMPTY_FN,
      EMPTY_STR,
      KEY_LISTENERS,
      KEY_ERR,
      KEY_RAW,
      HANDLER_KEYS,
      EV_CHANGE,
      EV_ADD,
      EV_ADD_DIR,
      EV_ERROR,
      STR_DATA,
      STR_END,
      BRACE_START,
      STAR
    } = require_constants(), THROTTLE_MODE_WATCH = "watch", open = promisify(fs.open), stat = promisify(fs.stat), lstat = promisify(fs.lstat), close = promisify(fs.close), fsrealpath = promisify(fs.realpath), statMethods = { lstat, stat }, foreach = (val, fn) => {
      val instanceof Set ? val.forEach(fn) : fn(val);
    }, addAndConvert = (main, prop, item) => {
      let container = main[prop];
      container instanceof Set || (main[prop] = container = /* @__PURE__ */ new Set([container])), container.add(item);
    }, clearItem = (cont) => (key) => {
      let set = cont[key];
      set instanceof Set ? set.clear() : delete cont[key];
    }, delFromSet = (main, prop, item) => {
      let container = main[prop];
      container instanceof Set ? container.delete(item) : container === item && delete main[prop];
    }, isEmptySet = (val) => val instanceof Set ? val.size === 0 : !val, FsWatchInstances = /* @__PURE__ */ new Map();
    function createFsWatchInstance(path, options, listener, errHandler, emitRaw) {
      let handleEvent = (rawEvent, evPath) => {
        listener(path), emitRaw(rawEvent, evPath, { watchedPath: path }), evPath && path !== evPath && fsWatchBroadcast(
          sysPath.resolve(path, evPath),
          KEY_LISTENERS,
          sysPath.join(path, evPath)
        );
      };
      try {
        return fs.watch(path, options, handleEvent);
      } catch (error) {
        errHandler(error);
      }
    }
    var fsWatchBroadcast = (fullPath, type, val1, val2, val3) => {
      let cont = FsWatchInstances.get(fullPath);
      cont && foreach(cont[type], (listener) => {
        listener(val1, val2, val3);
      });
    }, setFsWatchListener = (path, fullPath, options, handlers) => {
      let { listener, errHandler, rawEmitter } = handlers, cont = FsWatchInstances.get(fullPath), watcher;
      if (!options.persistent)
        return watcher = createFsWatchInstance(
          path,
          options,
          listener,
          errHandler,
          rawEmitter
        ), watcher.close.bind(watcher);
      if (cont)
        addAndConvert(cont, KEY_LISTENERS, listener), addAndConvert(cont, KEY_ERR, errHandler), addAndConvert(cont, KEY_RAW, rawEmitter);
      else {
        if (watcher = createFsWatchInstance(
          path,
          options,
          fsWatchBroadcast.bind(null, fullPath, KEY_LISTENERS),
          errHandler,
          // no need to use broadcast here
          fsWatchBroadcast.bind(null, fullPath, KEY_RAW)
        ), !watcher) return;
        watcher.on(EV_ERROR, async (error) => {
          let broadcastErr = fsWatchBroadcast.bind(null, fullPath, KEY_ERR);
          if (cont.watcherUnusable = !0, isWindows && error.code === "EPERM")
            try {
              let fd = await open(path, "r");
              await close(fd), broadcastErr(error);
            } catch {
            }
          else
            broadcastErr(error);
        }), cont = {
          listeners: listener,
          errHandlers: errHandler,
          rawEmitters: rawEmitter,
          watcher
        }, FsWatchInstances.set(fullPath, cont);
      }
      return () => {
        delFromSet(cont, KEY_LISTENERS, listener), delFromSet(cont, KEY_ERR, errHandler), delFromSet(cont, KEY_RAW, rawEmitter), isEmptySet(cont.listeners) && (cont.watcher.close(), FsWatchInstances.delete(fullPath), HANDLER_KEYS.forEach(clearItem(cont)), cont.watcher = void 0, Object.freeze(cont));
      };
    }, FsWatchFileInstances = /* @__PURE__ */ new Map(), setFsWatchFileListener = (path, fullPath, options, handlers) => {
      let { listener, rawEmitter } = handlers, cont = FsWatchFileInstances.get(fullPath), listeners = /* @__PURE__ */ new Set(), rawEmitters = /* @__PURE__ */ new Set(), copts = cont && cont.options;
      return copts && (copts.persistent < options.persistent || copts.interval > options.interval) && (listeners = cont.listeners, rawEmitters = cont.rawEmitters, fs.unwatchFile(fullPath), cont = void 0), cont ? (addAndConvert(cont, KEY_LISTENERS, listener), addAndConvert(cont, KEY_RAW, rawEmitter)) : (cont = {
        listeners: listener,
        rawEmitters: rawEmitter,
        options,
        watcher: fs.watchFile(fullPath, options, (curr, prev) => {
          foreach(cont.rawEmitters, (rawEmitter2) => {
            rawEmitter2(EV_CHANGE, fullPath, { curr, prev });
          });
          let currmtime = curr.mtimeMs;
          (curr.size !== prev.size || currmtime > prev.mtimeMs || currmtime === 0) && foreach(cont.listeners, (listener2) => listener2(path, curr));
        })
      }, FsWatchFileInstances.set(fullPath, cont)), () => {
        delFromSet(cont, KEY_LISTENERS, listener), delFromSet(cont, KEY_RAW, rawEmitter), isEmptySet(cont.listeners) && (FsWatchFileInstances.delete(fullPath), fs.unwatchFile(fullPath), cont.options = cont.watcher = void 0, Object.freeze(cont));
      };
    }, NodeFsHandler = class {
      /**
       * @param {import("../index").FSWatcher} fsW
       */
      constructor(fsW) {
        this.fsw = fsW, this._boundHandleError = (error) => fsW._handleError(error);
      }
      /**
       * Watch file for changes with fs_watchFile or fs_watch.
       * @param {String} path to file or dir
       * @param {Function} listener on fs change
       * @returns {Function} closer for the watcher instance
       */
      _watchWithNodeFs(path, listener) {
        let opts = this.fsw.options, directory = sysPath.dirname(path), basename = sysPath.basename(path);
        this.fsw._getWatchedDir(directory).add(basename);
        let absolutePath = sysPath.resolve(path), options = { persistent: opts.persistent };
        listener || (listener = EMPTY_FN);
        let closer;
        return opts.usePolling ? (options.interval = opts.enableBinaryInterval && isBinaryPath(basename) ? opts.binaryInterval : opts.interval, closer = setFsWatchFileListener(path, absolutePath, options, {
          listener,
          rawEmitter: this.fsw._emitRaw
        })) : closer = setFsWatchListener(path, absolutePath, options, {
          listener,
          errHandler: this._boundHandleError,
          rawEmitter: this.fsw._emitRaw
        }), closer;
      }
      /**
       * Watch a file and emit add event if warranted.
       * @param {Path} file Path
       * @param {fs.Stats} stats result of fs_stat
       * @param {Boolean} initialAdd was the file added at watch instantiation?
       * @returns {Function} closer for the watcher instance
       */
      _handleFile(file, stats, initialAdd) {
        if (this.fsw.closed)
          return;
        let dirname = sysPath.dirname(file), basename = sysPath.basename(file), parent = this.fsw._getWatchedDir(dirname), prevStats = stats;
        if (parent.has(basename)) return;
        let listener = async (path, newStats) => {
          if (this.fsw._throttle(THROTTLE_MODE_WATCH, file, 5)) {
            if (!newStats || newStats.mtimeMs === 0)
              try {
                let newStats2 = await stat(file);
                if (this.fsw.closed) return;
                let at = newStats2.atimeMs, mt = newStats2.mtimeMs;
                (!at || at <= mt || mt !== prevStats.mtimeMs) && this.fsw._emit(EV_CHANGE, file, newStats2), isLinux && prevStats.ino !== newStats2.ino ? (this.fsw._closeFile(path), prevStats = newStats2, this.fsw._addPathCloser(path, this._watchWithNodeFs(file, listener))) : prevStats = newStats2;
              } catch {
                this.fsw._remove(dirname, basename);
              }
            else if (parent.has(basename)) {
              let at = newStats.atimeMs, mt = newStats.mtimeMs;
              (!at || at <= mt || mt !== prevStats.mtimeMs) && this.fsw._emit(EV_CHANGE, file, newStats), prevStats = newStats;
            }
          }
        }, closer = this._watchWithNodeFs(file, listener);
        if (!(initialAdd && this.fsw.options.ignoreInitial) && this.fsw._isntIgnored(file)) {
          if (!this.fsw._throttle(EV_ADD, file, 0)) return;
          this.fsw._emit(EV_ADD, file, stats);
        }
        return closer;
      }
      /**
       * Handle symlinks encountered while reading a dir.
       * @param {Object} entry returned by readdirp
       * @param {String} directory path of dir being read
       * @param {String} path of this item
       * @param {String} item basename of this item
       * @returns {Promise<Boolean>} true if no more processing is needed for this entry.
       */
      async _handleSymlink(entry, directory, path, item) {
        if (this.fsw.closed)
          return;
        let full = entry.fullPath, dir = this.fsw._getWatchedDir(directory);
        if (!this.fsw.options.followSymlinks) {
          this.fsw._incrReadyCount();
          let linkPath;
          try {
            linkPath = await fsrealpath(path);
          } catch {
            return this.fsw._emitReady(), !0;
          }
          return this.fsw.closed ? void 0 : (dir.has(item) ? this.fsw._symlinkPaths.get(full) !== linkPath && (this.fsw._symlinkPaths.set(full, linkPath), this.fsw._emit(EV_CHANGE, path, entry.stats)) : (dir.add(item), this.fsw._symlinkPaths.set(full, linkPath), this.fsw._emit(EV_ADD, path, entry.stats)), this.fsw._emitReady(), !0);
        }
        if (this.fsw._symlinkPaths.has(full))
          return !0;
        this.fsw._symlinkPaths.set(full, !0);
      }
      _handleRead(directory, initialAdd, wh, target, dir, depth, throttler) {
        if (directory = sysPath.join(directory, EMPTY_STR), !wh.hasGlob && (throttler = this.fsw._throttle("readdir", directory, 1e3), !throttler))
          return;
        let previous = this.fsw._getWatchedDir(wh.path), current = /* @__PURE__ */ new Set(), stream = this.fsw._readdirp(directory, {
          fileFilter: (entry) => wh.filterPath(entry),
          directoryFilter: (entry) => wh.filterDir(entry),
          depth: 0
        }).on(STR_DATA, async (entry) => {
          if (this.fsw.closed) {
            stream = void 0;
            return;
          }
          let item = entry.path, path = sysPath.join(directory, item);
          if (current.add(item), !(entry.stats.isSymbolicLink() && await this._handleSymlink(entry, directory, path, item))) {
            if (this.fsw.closed) {
              stream = void 0;
              return;
            }
            (item === target || !target && !previous.has(item)) && (this.fsw._incrReadyCount(), path = sysPath.join(dir, sysPath.relative(dir, path)), this._addToNodeFs(path, initialAdd, wh, depth + 1));
          }
        }).on(EV_ERROR, this._boundHandleError);
        return new Promise(
          (resolve) => stream.once(STR_END, () => {
            if (this.fsw.closed) {
              stream = void 0;
              return;
            }
            let wasThrottled = throttler ? throttler.clear() : !1;
            resolve(), previous.getChildren().filter((item) => item !== directory && !current.has(item) && // in case of intersecting globs;
            // a path may have been filtered out of this readdir, but
            // shouldn't be removed because it matches a different glob
            (!wh.hasGlob || wh.filterPath({
              fullPath: sysPath.resolve(directory, item)
            }))).forEach((item) => {
              this.fsw._remove(directory, item);
            }), stream = void 0, wasThrottled && this._handleRead(directory, !1, wh, target, dir, depth, throttler);
          })
        );
      }
      /**
       * Read directory to add / remove files from `@watched` list and re-read it on change.
       * @param {String} dir fs path
       * @param {fs.Stats} stats
       * @param {Boolean} initialAdd
       * @param {Number} depth relative to user-supplied path
       * @param {String} target child path targeted for watch
       * @param {Object} wh Common watch helpers for this path
       * @param {String} realpath
       * @returns {Promise<Function>} closer for the watcher instance.
       */
      async _handleDir(dir, stats, initialAdd, depth, target, wh, realpath) {
        let parentDir = this.fsw._getWatchedDir(sysPath.dirname(dir)), tracked = parentDir.has(sysPath.basename(dir));
        !(initialAdd && this.fsw.options.ignoreInitial) && !target && !tracked && (!wh.hasGlob || wh.globFilter(dir)) && this.fsw._emit(EV_ADD_DIR, dir, stats), parentDir.add(sysPath.basename(dir)), this.fsw._getWatchedDir(dir);
        let throttler, closer, oDepth = this.fsw.options.depth;
        if ((oDepth == null || depth <= oDepth) && !this.fsw._symlinkPaths.has(realpath)) {
          if (!target && (await this._handleRead(dir, initialAdd, wh, target, dir, depth, throttler), this.fsw.closed))
            return;
          closer = this._watchWithNodeFs(dir, (dirPath, stats2) => {
            stats2 && stats2.mtimeMs === 0 || this._handleRead(dirPath, !1, wh, target, dir, depth, throttler);
          });
        }
        return closer;
      }
      /**
       * Handle added file, directory, or glob pattern.
       * Delegates call to _handleFile / _handleDir after checks.
       * @param {String} path to file or ir
       * @param {Boolean} initialAdd was the file added at watch instantiation?
       * @param {Object} priorWh depth relative to user-supplied path
       * @param {Number} depth Child path actually targeted for watch
       * @param {String=} target Child path actually targeted for watch
       * @returns {Promise}
       */
      async _addToNodeFs(path, initialAdd, priorWh, depth, target) {
        let ready = this.fsw._emitReady;
        if (this.fsw._isIgnored(path) || this.fsw.closed)
          return ready(), !1;
        let wh = this.fsw._getWatchHelpers(path, depth);
        !wh.hasGlob && priorWh && (wh.hasGlob = priorWh.hasGlob, wh.globFilter = priorWh.globFilter, wh.filterPath = (entry) => priorWh.filterPath(entry), wh.filterDir = (entry) => priorWh.filterDir(entry));
        try {
          let stats = await statMethods[wh.statMethod](wh.watchPath);
          if (this.fsw.closed) return;
          if (this.fsw._isIgnored(wh.watchPath, stats))
            return ready(), !1;
          let follow = this.fsw.options.followSymlinks && !path.includes(STAR) && !path.includes(BRACE_START), closer;
          if (stats.isDirectory()) {
            let absPath = sysPath.resolve(path), targetPath = follow ? await fsrealpath(path) : path;
            if (this.fsw.closed || (closer = await this._handleDir(wh.watchPath, stats, initialAdd, depth, target, wh, targetPath), this.fsw.closed)) return;
            absPath !== targetPath && targetPath !== void 0 && this.fsw._symlinkPaths.set(absPath, targetPath);
          } else if (stats.isSymbolicLink()) {
            let targetPath = follow ? await fsrealpath(path) : path;
            if (this.fsw.closed) return;
            let parent = sysPath.dirname(wh.watchPath);
            if (this.fsw._getWatchedDir(parent).add(wh.watchPath), this.fsw._emit(EV_ADD, wh.watchPath, stats), closer = await this._handleDir(parent, stats, initialAdd, depth, path, wh, targetPath), this.fsw.closed) return;
            targetPath !== void 0 && this.fsw._symlinkPaths.set(sysPath.resolve(path), targetPath);
          } else
            closer = this._handleFile(wh.watchPath, stats, initialAdd);
          return ready(), this.fsw._addPathCloser(path, closer), !1;
        } catch (error) {
          if (this.fsw._handleError(error))
            return ready(), path;
        }
      }
    };
    module.exports = NodeFsHandler;
  }
});

// ../../node_modules/.pnpm/chokidar@3.5.3/node_modules/chokidar/lib/fsevents-handler.js
var require_fsevents_handler = __commonJS({
  "../../node_modules/.pnpm/chokidar@3.5.3/node_modules/chokidar/lib/fsevents-handler.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var fs = __require("fs"), sysPath = __require("path"), { promisify } = __require("util"), fsevents;
    try {
      fsevents = __require("fsevents");
    } catch (error) {
      process.env.CHOKIDAR_PRINT_FSEVENTS_REQUIRE_ERROR && console.error(error);
    }
    if (fsevents) {
      let mtch = process.version.match(/v(\d+)\.(\d+)/);
      if (mtch && mtch[1] && mtch[2]) {
        let maj = Number.parseInt(mtch[1], 10), min = Number.parseInt(mtch[2], 10);
        maj === 8 && min < 16 && (fsevents = void 0);
      }
    }
    var {
      EV_ADD,
      EV_CHANGE,
      EV_ADD_DIR,
      EV_UNLINK,
      EV_ERROR,
      STR_DATA,
      STR_END,
      FSEVENT_CREATED,
      FSEVENT_MODIFIED,
      FSEVENT_DELETED,
      FSEVENT_MOVED,
      // FSEVENT_CLONED,
      FSEVENT_UNKNOWN,
      FSEVENT_TYPE_FILE,
      FSEVENT_TYPE_DIRECTORY,
      FSEVENT_TYPE_SYMLINK,
      ROOT_GLOBSTAR,
      DIR_SUFFIX,
      DOT_SLASH,
      FUNCTION_TYPE,
      EMPTY_FN,
      IDENTITY_FN
    } = require_constants(), Depth = (value) => isNaN(value) ? {} : { depth: value }, stat = promisify(fs.stat), lstat = promisify(fs.lstat), realpath = promisify(fs.realpath), statMethods = { stat, lstat }, FSEventsWatchers = /* @__PURE__ */ new Map(), consolidateThreshhold = 10, wrongEventFlags = /* @__PURE__ */ new Set([
      69888,
      70400,
      71424,
      72704,
      73472,
      131328,
      131840,
      262912
    ]), createFSEventsInstance = (path, callback) => ({ stop: fsevents.watch(path, callback) });
    function setFSEventsListener(path, realPath, listener, rawEmitter) {
      let watchPath = sysPath.extname(realPath) ? sysPath.dirname(realPath) : realPath, parentPath = sysPath.dirname(watchPath), cont = FSEventsWatchers.get(watchPath);
      couldConsolidate(parentPath) && (watchPath = parentPath);
      let resolvedPath = sysPath.resolve(path), hasSymlink = resolvedPath !== realPath, filteredListener = (fullPath, flags, info) => {
        hasSymlink && (fullPath = fullPath.replace(realPath, resolvedPath)), (fullPath === resolvedPath || !fullPath.indexOf(resolvedPath + sysPath.sep)) && listener(fullPath, flags, info);
      }, watchedParent = !1;
      for (let watchedPath of FSEventsWatchers.keys())
        if (realPath.indexOf(sysPath.resolve(watchedPath) + sysPath.sep) === 0) {
          watchPath = watchedPath, cont = FSEventsWatchers.get(watchPath), watchedParent = !0;
          break;
        }
      return cont || watchedParent ? cont.listeners.add(filteredListener) : (cont = {
        listeners: /* @__PURE__ */ new Set([filteredListener]),
        rawEmitter,
        watcher: createFSEventsInstance(watchPath, (fullPath, flags) => {
          if (!cont.listeners.size) return;
          let info = fsevents.getInfo(fullPath, flags);
          cont.listeners.forEach((list) => {
            list(fullPath, flags, info);
          }), cont.rawEmitter(info.event, fullPath, info);
        })
      }, FSEventsWatchers.set(watchPath, cont)), () => {
        let lst = cont.listeners;
        if (lst.delete(filteredListener), !lst.size && (FSEventsWatchers.delete(watchPath), cont.watcher))
          return cont.watcher.stop().then(() => {
            cont.rawEmitter = cont.watcher = void 0, Object.freeze(cont);
          });
      };
    }
    var couldConsolidate = (path) => {
      let count = 0;
      for (let watchPath of FSEventsWatchers.keys())
        if (watchPath.indexOf(path) === 0 && (count++, count >= consolidateThreshhold))
          return !0;
      return !1;
    }, canUse = () => fsevents && FSEventsWatchers.size < 128, calcDepth = (path, root) => {
      let i = 0;
      for (; !path.indexOf(root) && (path = sysPath.dirname(path)) !== root; ) i++;
      return i;
    }, sameTypes = (info, stats) => info.type === FSEVENT_TYPE_DIRECTORY && stats.isDirectory() || info.type === FSEVENT_TYPE_SYMLINK && stats.isSymbolicLink() || info.type === FSEVENT_TYPE_FILE && stats.isFile(), FsEventsHandler = class {
      /**
       * @param {import('../index').FSWatcher} fsw
       */
      constructor(fsw) {
        this.fsw = fsw;
      }
      checkIgnored(path, stats) {
        let ipaths = this.fsw._ignoredPaths;
        if (this.fsw._isIgnored(path, stats))
          return ipaths.add(path), stats && stats.isDirectory() && ipaths.add(path + ROOT_GLOBSTAR), !0;
        ipaths.delete(path), ipaths.delete(path + ROOT_GLOBSTAR);
      }
      addOrChange(path, fullPath, realPath, parent, watchedDir, item, info, opts) {
        let event = watchedDir.has(item) ? EV_CHANGE : EV_ADD;
        this.handleEvent(event, path, fullPath, realPath, parent, watchedDir, item, info, opts);
      }
      async checkExists(path, fullPath, realPath, parent, watchedDir, item, info, opts) {
        try {
          let stats = await stat(path);
          if (this.fsw.closed) return;
          sameTypes(info, stats) ? this.addOrChange(path, fullPath, realPath, parent, watchedDir, item, info, opts) : this.handleEvent(EV_UNLINK, path, fullPath, realPath, parent, watchedDir, item, info, opts);
        } catch (error) {
          error.code === "EACCES" ? this.addOrChange(path, fullPath, realPath, parent, watchedDir, item, info, opts) : this.handleEvent(EV_UNLINK, path, fullPath, realPath, parent, watchedDir, item, info, opts);
        }
      }
      handleEvent(event, path, fullPath, realPath, parent, watchedDir, item, info, opts) {
        if (!(this.fsw.closed || this.checkIgnored(path)))
          if (event === EV_UNLINK) {
            let isDirectory = info.type === FSEVENT_TYPE_DIRECTORY;
            (isDirectory || watchedDir.has(item)) && this.fsw._remove(parent, item, isDirectory);
          } else {
            if (event === EV_ADD) {
              if (info.type === FSEVENT_TYPE_DIRECTORY && this.fsw._getWatchedDir(path), info.type === FSEVENT_TYPE_SYMLINK && opts.followSymlinks) {
                let curDepth = opts.depth === void 0 ? void 0 : calcDepth(fullPath, realPath) + 1;
                return this._addToFsEvents(path, !1, !0, curDepth);
              }
              this.fsw._getWatchedDir(parent).add(item);
            }
            let eventName = info.type === FSEVENT_TYPE_DIRECTORY ? event + DIR_SUFFIX : event;
            this.fsw._emit(eventName, path), eventName === EV_ADD_DIR && this._addToFsEvents(path, !1, !0);
          }
      }
      /**
       * Handle symlinks encountered during directory scan
       * @param {String} watchPath  - file/dir path to be watched with fsevents
       * @param {String} realPath   - real path (in case of symlinks)
       * @param {Function} transform  - path transformer
       * @param {Function} globFilter - path filter in case a glob pattern was provided
       * @returns {Function} closer for the watcher instance
      */
      _watchWithFsEvents(watchPath, realPath, transform, globFilter) {
        if (this.fsw.closed || this.fsw._isIgnored(watchPath)) return;
        let opts = this.fsw.options, closer = setFSEventsListener(
          watchPath,
          realPath,
          async (fullPath, flags, info) => {
            if (this.fsw.closed || opts.depth !== void 0 && calcDepth(fullPath, realPath) > opts.depth) return;
            let path = transform(sysPath.join(
              watchPath,
              sysPath.relative(watchPath, fullPath)
            ));
            if (globFilter && !globFilter(path)) return;
            let parent = sysPath.dirname(path), item = sysPath.basename(path), watchedDir = this.fsw._getWatchedDir(
              info.type === FSEVENT_TYPE_DIRECTORY ? path : parent
            );
            if (wrongEventFlags.has(flags) || info.event === FSEVENT_UNKNOWN)
              if (typeof opts.ignored === FUNCTION_TYPE) {
                let stats;
                try {
                  stats = await stat(path);
                } catch {
                }
                if (this.fsw.closed || this.checkIgnored(path, stats)) return;
                sameTypes(info, stats) ? this.addOrChange(path, fullPath, realPath, parent, watchedDir, item, info, opts) : this.handleEvent(EV_UNLINK, path, fullPath, realPath, parent, watchedDir, item, info, opts);
              } else
                this.checkExists(path, fullPath, realPath, parent, watchedDir, item, info, opts);
            else
              switch (info.event) {
                case FSEVENT_CREATED:
                case FSEVENT_MODIFIED:
                  return this.addOrChange(path, fullPath, realPath, parent, watchedDir, item, info, opts);
                case FSEVENT_DELETED:
                case FSEVENT_MOVED:
                  return this.checkExists(path, fullPath, realPath, parent, watchedDir, item, info, opts);
              }
          },
          this.fsw._emitRaw
        );
        return this.fsw._emitReady(), closer;
      }
      /**
       * Handle symlinks encountered during directory scan
       * @param {String} linkPath path to symlink
       * @param {String} fullPath absolute path to the symlink
       * @param {Function} transform pre-existing path transformer
       * @param {Number} curDepth level of subdirectories traversed to where symlink is
       * @returns {Promise<void>}
       */
      async _handleFsEventsSymlink(linkPath, fullPath, transform, curDepth) {
        if (!(this.fsw.closed || this.fsw._symlinkPaths.has(fullPath))) {
          this.fsw._symlinkPaths.set(fullPath, !0), this.fsw._incrReadyCount();
          try {
            let linkTarget = await realpath(linkPath);
            if (this.fsw.closed) return;
            if (this.fsw._isIgnored(linkTarget))
              return this.fsw._emitReady();
            this.fsw._incrReadyCount(), this._addToFsEvents(linkTarget || linkPath, (path) => {
              let aliasedPath = linkPath;
              return linkTarget && linkTarget !== DOT_SLASH ? aliasedPath = path.replace(linkTarget, linkPath) : path !== DOT_SLASH && (aliasedPath = sysPath.join(linkPath, path)), transform(aliasedPath);
            }, !1, curDepth);
          } catch (error) {
            if (this.fsw._handleError(error))
              return this.fsw._emitReady();
          }
        }
      }
      /**
       *
       * @param {Path} newPath
       * @param {fs.Stats} stats
       */
      emitAdd(newPath, stats, processPath, opts, forceAdd) {
        let pp = processPath(newPath), isDir = stats.isDirectory(), dirObj = this.fsw._getWatchedDir(sysPath.dirname(pp)), base = sysPath.basename(pp);
        isDir && this.fsw._getWatchedDir(pp), !dirObj.has(base) && (dirObj.add(base), (!opts.ignoreInitial || forceAdd === !0) && this.fsw._emit(isDir ? EV_ADD_DIR : EV_ADD, pp, stats));
      }
      initWatch(realPath, path, wh, processPath) {
        if (this.fsw.closed) return;
        let closer = this._watchWithFsEvents(
          wh.watchPath,
          sysPath.resolve(realPath || wh.watchPath),
          processPath,
          wh.globFilter
        );
        this.fsw._addPathCloser(path, closer);
      }
      /**
       * Handle added path with fsevents
       * @param {String} path file/dir path or glob pattern
       * @param {Function|Boolean=} transform converts working path to what the user expects
       * @param {Boolean=} forceAdd ensure add is emitted
       * @param {Number=} priorDepth Level of subdirectories already traversed.
       * @returns {Promise<void>}
       */
      async _addToFsEvents(path, transform, forceAdd, priorDepth) {
        if (this.fsw.closed)
          return;
        let opts = this.fsw.options, processPath = typeof transform === FUNCTION_TYPE ? transform : IDENTITY_FN, wh = this.fsw._getWatchHelpers(path);
        try {
          let stats = await statMethods[wh.statMethod](wh.watchPath);
          if (this.fsw.closed) return;
          if (this.fsw._isIgnored(wh.watchPath, stats))
            throw null;
          if (stats.isDirectory()) {
            if (wh.globFilter || this.emitAdd(processPath(path), stats, processPath, opts, forceAdd), priorDepth && priorDepth > opts.depth) return;
            this.fsw._readdirp(wh.watchPath, {
              fileFilter: (entry) => wh.filterPath(entry),
              directoryFilter: (entry) => wh.filterDir(entry),
              ...Depth(opts.depth - (priorDepth || 0))
            }).on(STR_DATA, (entry) => {
              if (this.fsw.closed || entry.stats.isDirectory() && !wh.filterPath(entry)) return;
              let joinedPath = sysPath.join(wh.watchPath, entry.path), { fullPath } = entry;
              if (wh.followSymlinks && entry.stats.isSymbolicLink()) {
                let curDepth = opts.depth === void 0 ? void 0 : calcDepth(joinedPath, sysPath.resolve(wh.watchPath)) + 1;
                this._handleFsEventsSymlink(joinedPath, fullPath, processPath, curDepth);
              } else
                this.emitAdd(joinedPath, entry.stats, processPath, opts, forceAdd);
            }).on(EV_ERROR, EMPTY_FN).on(STR_END, () => {
              this.fsw._emitReady();
            });
          } else
            this.emitAdd(wh.watchPath, stats, processPath, opts, forceAdd), this.fsw._emitReady();
        } catch (error) {
          (!error || this.fsw._handleError(error)) && (this.fsw._emitReady(), this.fsw._emitReady());
        }
        if (opts.persistent && forceAdd !== !0)
          if (typeof transform === FUNCTION_TYPE)
            this.initWatch(void 0, path, wh, processPath);
          else {
            let realPath;
            try {
              realPath = await realpath(wh.watchPath);
            } catch {
            }
            this.initWatch(realPath, path, wh, processPath);
          }
      }
    };
    module.exports = FsEventsHandler;
    module.exports.canUse = canUse;
  }
});

// ../../node_modules/.pnpm/chokidar@3.5.3/node_modules/chokidar/index.js
var require_chokidar = __commonJS({
  "../../node_modules/.pnpm/chokidar@3.5.3/node_modules/chokidar/index.js"(exports) {
    init_cjs_shims();
    var { EventEmitter } = __require("events"), fs = __require("fs"), sysPath = __require("path"), { promisify } = __require("util"), readdirp = require_readdirp(), anymatch = require_anymatch().default, globParent = require_glob_parent(), isGlob = require_is_glob(), braces = require_braces(), normalizePath = require_normalize_path(), NodeFsHandler = require_nodefs_handler(), FsEventsHandler = require_fsevents_handler(), {
      EV_ALL,
      EV_READY,
      EV_ADD,
      EV_CHANGE,
      EV_UNLINK,
      EV_ADD_DIR,
      EV_UNLINK_DIR,
      EV_RAW,
      EV_ERROR,
      STR_CLOSE,
      STR_END,
      BACK_SLASH_RE,
      DOUBLE_SLASH_RE,
      SLASH_OR_BACK_SLASH_RE,
      DOT_RE,
      REPLACER_RE,
      SLASH,
      SLASH_SLASH,
      BRACE_START,
      BANG,
      ONE_DOT,
      TWO_DOTS,
      GLOBSTAR,
      SLASH_GLOBSTAR,
      ANYMATCH_OPTS,
      STRING_TYPE,
      FUNCTION_TYPE,
      EMPTY_STR,
      EMPTY_FN,
      isWindows,
      isMacos,
      isIBMi
    } = require_constants(), stat = promisify(fs.stat), readdir = promisify(fs.readdir), arrify = (value = []) => Array.isArray(value) ? value : [value], flatten = (list, result = []) => (list.forEach((item) => {
      Array.isArray(item) ? flatten(item, result) : result.push(item);
    }), result), unifyPaths = (paths_) => {
      let paths = flatten(arrify(paths_));
      if (!paths.every((p) => typeof p === STRING_TYPE))
        throw new TypeError(`Non-string provided as watch path: ${paths}`);
      return paths.map(normalizePathToUnix);
    }, toUnix = (string) => {
      let str = string.replace(BACK_SLASH_RE, SLASH), prepend = !1;
      for (str.startsWith(SLASH_SLASH) && (prepend = !0); str.match(DOUBLE_SLASH_RE); )
        str = str.replace(DOUBLE_SLASH_RE, SLASH);
      return prepend && (str = SLASH + str), str;
    }, normalizePathToUnix = (path) => toUnix(sysPath.normalize(toUnix(path))), normalizeIgnored = (cwd = EMPTY_STR) => (path) => typeof path !== STRING_TYPE ? path : normalizePathToUnix(sysPath.isAbsolute(path) ? path : sysPath.join(cwd, path)), getAbsolutePath = (path, cwd) => sysPath.isAbsolute(path) ? path : path.startsWith(BANG) ? BANG + sysPath.join(cwd, path.slice(1)) : sysPath.join(cwd, path), undef = (opts, key) => opts[key] === void 0, DirEntry = class {
      /**
       * @param {Path} dir
       * @param {Function} removeWatcher
       */
      constructor(dir, removeWatcher) {
        this.path = dir, this._removeWatcher = removeWatcher, this.items = /* @__PURE__ */ new Set();
      }
      add(item) {
        let { items } = this;
        items && item !== ONE_DOT && item !== TWO_DOTS && items.add(item);
      }
      async remove(item) {
        let { items } = this;
        if (!items || (items.delete(item), items.size > 0)) return;
        let dir = this.path;
        try {
          await readdir(dir);
        } catch {
          this._removeWatcher && this._removeWatcher(sysPath.dirname(dir), sysPath.basename(dir));
        }
      }
      has(item) {
        let { items } = this;
        if (items)
          return items.has(item);
      }
      /**
       * @returns {Array<String>}
       */
      getChildren() {
        let { items } = this;
        if (items)
          return [...items.values()];
      }
      dispose() {
        this.items.clear(), delete this.path, delete this._removeWatcher, delete this.items, Object.freeze(this);
      }
    }, STAT_METHOD_F = "stat", STAT_METHOD_L = "lstat", WatchHelper = class {
      constructor(path, watchPath, follow, fsw) {
        this.fsw = fsw, this.path = path = path.replace(REPLACER_RE, EMPTY_STR), this.watchPath = watchPath, this.fullWatchPath = sysPath.resolve(watchPath), this.hasGlob = watchPath !== path, path === EMPTY_STR && (this.hasGlob = !1), this.globSymlink = this.hasGlob && follow ? void 0 : !1, this.globFilter = this.hasGlob ? anymatch(path, void 0, ANYMATCH_OPTS) : !1, this.dirParts = this.getDirParts(path), this.dirParts.forEach((parts) => {
          parts.length > 1 && parts.pop();
        }), this.followSymlinks = follow, this.statMethod = follow ? STAT_METHOD_F : STAT_METHOD_L;
      }
      checkGlobSymlink(entry) {
        return this.globSymlink === void 0 && (this.globSymlink = entry.fullParentDir === this.fullWatchPath ? !1 : { realPath: entry.fullParentDir, linkPath: this.fullWatchPath }), this.globSymlink ? entry.fullPath.replace(this.globSymlink.realPath, this.globSymlink.linkPath) : entry.fullPath;
      }
      entryPath(entry) {
        return sysPath.join(
          this.watchPath,
          sysPath.relative(this.watchPath, this.checkGlobSymlink(entry))
        );
      }
      filterPath(entry) {
        let { stats } = entry;
        if (stats && stats.isSymbolicLink()) return this.filterDir(entry);
        let resolvedPath = this.entryPath(entry);
        return (this.hasGlob && typeof this.globFilter === FUNCTION_TYPE ? this.globFilter(resolvedPath) : !0) && this.fsw._isntIgnored(resolvedPath, stats) && this.fsw._hasReadPermissions(stats);
      }
      getDirParts(path) {
        if (!this.hasGlob) return [];
        let parts = [];
        return (path.includes(BRACE_START) ? braces.expand(path) : [path]).forEach((path2) => {
          parts.push(sysPath.relative(this.watchPath, path2).split(SLASH_OR_BACK_SLASH_RE));
        }), parts;
      }
      filterDir(entry) {
        if (this.hasGlob) {
          let entryParts = this.getDirParts(this.checkGlobSymlink(entry)), globstar = !1;
          this.unmatchedGlob = !this.dirParts.some((parts) => parts.every((part, i) => (part === GLOBSTAR && (globstar = !0), globstar || !entryParts[0][i] || anymatch(part, entryParts[0][i], ANYMATCH_OPTS))));
        }
        return !this.unmatchedGlob && this.fsw._isntIgnored(this.entryPath(entry), entry.stats);
      }
    }, FSWatcher = class extends EventEmitter {
      // Not indenting methods for history sake; for now.
      constructor(_opts) {
        super();
        let opts = {};
        _opts && Object.assign(opts, _opts), this._watched = /* @__PURE__ */ new Map(), this._closers = /* @__PURE__ */ new Map(), this._ignoredPaths = /* @__PURE__ */ new Set(), this._throttled = /* @__PURE__ */ new Map(), this._symlinkPaths = /* @__PURE__ */ new Map(), this._streams = /* @__PURE__ */ new Set(), this.closed = !1, undef(opts, "persistent") && (opts.persistent = !0), undef(opts, "ignoreInitial") && (opts.ignoreInitial = !1), undef(opts, "ignorePermissionErrors") && (opts.ignorePermissionErrors = !1), undef(opts, "interval") && (opts.interval = 100), undef(opts, "binaryInterval") && (opts.binaryInterval = 300), undef(opts, "disableGlobbing") && (opts.disableGlobbing = !1), opts.enableBinaryInterval = opts.binaryInterval !== opts.interval, undef(opts, "useFsEvents") && (opts.useFsEvents = !opts.usePolling), FsEventsHandler.canUse() || (opts.useFsEvents = !1), undef(opts, "usePolling") && !opts.useFsEvents && (opts.usePolling = isMacos), isIBMi && (opts.usePolling = !0);
        let envPoll = process.env.CHOKIDAR_USEPOLLING;
        if (envPoll !== void 0) {
          let envLower = envPoll.toLowerCase();
          envLower === "false" || envLower === "0" ? opts.usePolling = !1 : envLower === "true" || envLower === "1" ? opts.usePolling = !0 : opts.usePolling = !!envLower;
        }
        let envInterval = process.env.CHOKIDAR_INTERVAL;
        envInterval && (opts.interval = Number.parseInt(envInterval, 10)), undef(opts, "atomic") && (opts.atomic = !opts.usePolling && !opts.useFsEvents), opts.atomic && (this._pendingUnlinks = /* @__PURE__ */ new Map()), undef(opts, "followSymlinks") && (opts.followSymlinks = !0), undef(opts, "awaitWriteFinish") && (opts.awaitWriteFinish = !1), opts.awaitWriteFinish === !0 && (opts.awaitWriteFinish = {});
        let awf = opts.awaitWriteFinish;
        awf && (awf.stabilityThreshold || (awf.stabilityThreshold = 2e3), awf.pollInterval || (awf.pollInterval = 100), this._pendingWrites = /* @__PURE__ */ new Map()), opts.ignored && (opts.ignored = arrify(opts.ignored));
        let readyCalls = 0;
        this._emitReady = () => {
          readyCalls++, readyCalls >= this._readyCount && (this._emitReady = EMPTY_FN, this._readyEmitted = !0, process.nextTick(() => this.emit(EV_READY)));
        }, this._emitRaw = (...args) => this.emit(EV_RAW, ...args), this._readyEmitted = !1, this.options = opts, opts.useFsEvents ? this._fsEventsHandler = new FsEventsHandler(this) : this._nodeFsHandler = new NodeFsHandler(this), Object.freeze(opts);
      }
      // Public methods
      /**
       * Adds paths to be watched on an existing FSWatcher instance
       * @param {Path|Array<Path>} paths_
       * @param {String=} _origAdd private; for handling non-existent paths to be watched
       * @param {Boolean=} _internal private; indicates a non-user add
       * @returns {FSWatcher} for chaining
       */
      add(paths_, _origAdd, _internal) {
        let { cwd, disableGlobbing } = this.options;
        this.closed = !1;
        let paths = unifyPaths(paths_);
        return cwd && (paths = paths.map((path) => {
          let absPath = getAbsolutePath(path, cwd);
          return disableGlobbing || !isGlob(path) ? absPath : normalizePath(absPath);
        })), paths = paths.filter((path) => path.startsWith(BANG) ? (this._ignoredPaths.add(path.slice(1)), !1) : (this._ignoredPaths.delete(path), this._ignoredPaths.delete(path + SLASH_GLOBSTAR), this._userIgnored = void 0, !0)), this.options.useFsEvents && this._fsEventsHandler ? (this._readyCount || (this._readyCount = paths.length), this.options.persistent && (this._readyCount *= 2), paths.forEach((path) => this._fsEventsHandler._addToFsEvents(path))) : (this._readyCount || (this._readyCount = 0), this._readyCount += paths.length, Promise.all(
          paths.map(async (path) => {
            let res = await this._nodeFsHandler._addToNodeFs(path, !_internal, 0, 0, _origAdd);
            return res && this._emitReady(), res;
          })
        ).then((results) => {
          this.closed || results.filter((item) => item).forEach((item) => {
            this.add(sysPath.dirname(item), sysPath.basename(_origAdd || item));
          });
        })), this;
      }
      /**
       * Close watchers or start ignoring events from specified paths.
       * @param {Path|Array<Path>} paths_ - string or array of strings, file/directory paths and/or globs
       * @returns {FSWatcher} for chaining
      */
      unwatch(paths_) {
        if (this.closed) return this;
        let paths = unifyPaths(paths_), { cwd } = this.options;
        return paths.forEach((path) => {
          !sysPath.isAbsolute(path) && !this._closers.has(path) && (cwd && (path = sysPath.join(cwd, path)), path = sysPath.resolve(path)), this._closePath(path), this._ignoredPaths.add(path), this._watched.has(path) && this._ignoredPaths.add(path + SLASH_GLOBSTAR), this._userIgnored = void 0;
        }), this;
      }
      /**
       * Close watchers and remove all listeners from watched paths.
       * @returns {Promise<void>}.
      */
      close() {
        if (this.closed) return this._closePromise;
        this.closed = !0, this.removeAllListeners();
        let closers = [];
        return this._closers.forEach((closerList) => closerList.forEach((closer) => {
          let promise = closer();
          promise instanceof Promise && closers.push(promise);
        })), this._streams.forEach((stream) => stream.destroy()), this._userIgnored = void 0, this._readyCount = 0, this._readyEmitted = !1, this._watched.forEach((dirent) => dirent.dispose()), ["closers", "watched", "streams", "symlinkPaths", "throttled"].forEach((key) => {
          this[`_${key}`].clear();
        }), this._closePromise = closers.length ? Promise.all(closers).then(() => {
        }) : Promise.resolve(), this._closePromise;
      }
      /**
       * Expose list of watched paths
       * @returns {Object} for chaining
      */
      getWatched() {
        let watchList = {};
        return this._watched.forEach((entry, dir) => {
          let key = this.options.cwd ? sysPath.relative(this.options.cwd, dir) : dir;
          watchList[key || ONE_DOT] = entry.getChildren().sort();
        }), watchList;
      }
      emitWithAll(event, args) {
        this.emit(...args), event !== EV_ERROR && this.emit(EV_ALL, ...args);
      }
      // Common helpers
      // --------------
      /**
       * Normalize and emit events.
       * Calling _emit DOES NOT MEAN emit() would be called!
       * @param {EventName} event Type of event
       * @param {Path} path File or directory path
       * @param {*=} val1 arguments to be passed with event
       * @param {*=} val2
       * @param {*=} val3
       * @returns the error if defined, otherwise the value of the FSWatcher instance's `closed` flag
       */
      async _emit(event, path, val1, val2, val3) {
        if (this.closed) return;
        let opts = this.options;
        isWindows && (path = sysPath.normalize(path)), opts.cwd && (path = sysPath.relative(opts.cwd, path));
        let args = [event, path];
        val3 !== void 0 ? args.push(val1, val2, val3) : val2 !== void 0 ? args.push(val1, val2) : val1 !== void 0 && args.push(val1);
        let awf = opts.awaitWriteFinish, pw;
        if (awf && (pw = this._pendingWrites.get(path)))
          return pw.lastChange = /* @__PURE__ */ new Date(), this;
        if (opts.atomic) {
          if (event === EV_UNLINK)
            return this._pendingUnlinks.set(path, args), setTimeout(() => {
              this._pendingUnlinks.forEach((entry, path2) => {
                this.emit(...entry), this.emit(EV_ALL, ...entry), this._pendingUnlinks.delete(path2);
              });
            }, typeof opts.atomic == "number" ? opts.atomic : 100), this;
          event === EV_ADD && this._pendingUnlinks.has(path) && (event = args[0] = EV_CHANGE, this._pendingUnlinks.delete(path));
        }
        if (awf && (event === EV_ADD || event === EV_CHANGE) && this._readyEmitted) {
          let awfEmit = (err, stats) => {
            err ? (event = args[0] = EV_ERROR, args[1] = err, this.emitWithAll(event, args)) : stats && (args.length > 2 ? args[2] = stats : args.push(stats), this.emitWithAll(event, args));
          };
          return this._awaitWriteFinish(path, awf.stabilityThreshold, event, awfEmit), this;
        }
        if (event === EV_CHANGE && !this._throttle(EV_CHANGE, path, 50))
          return this;
        if (opts.alwaysStat && val1 === void 0 && (event === EV_ADD || event === EV_ADD_DIR || event === EV_CHANGE)) {
          let fullPath = opts.cwd ? sysPath.join(opts.cwd, path) : path, stats;
          try {
            stats = await stat(fullPath);
          } catch {
          }
          if (!stats || this.closed) return;
          args.push(stats);
        }
        return this.emitWithAll(event, args), this;
      }
      /**
       * Common handler for errors
       * @param {Error} error
       * @returns {Error|Boolean} The error if defined, otherwise the value of the FSWatcher instance's `closed` flag
       */
      _handleError(error) {
        let code = error && error.code;
        return error && code !== "ENOENT" && code !== "ENOTDIR" && (!this.options.ignorePermissionErrors || code !== "EPERM" && code !== "EACCES") && this.emit(EV_ERROR, error), error || this.closed;
      }
      /**
       * Helper utility for throttling
       * @param {ThrottleType} actionType type being throttled
       * @param {Path} path being acted upon
       * @param {Number} timeout duration of time to suppress duplicate actions
       * @returns {Object|false} tracking object or false if action should be suppressed
       */
      _throttle(actionType, path, timeout) {
        this._throttled.has(actionType) || this._throttled.set(actionType, /* @__PURE__ */ new Map());
        let action = this._throttled.get(actionType), actionPath = action.get(path);
        if (actionPath)
          return actionPath.count++, !1;
        let timeoutObject, clear = () => {
          let item = action.get(path), count = item ? item.count : 0;
          return action.delete(path), clearTimeout(timeoutObject), item && clearTimeout(item.timeoutObject), count;
        };
        timeoutObject = setTimeout(clear, timeout);
        let thr = { timeoutObject, clear, count: 0 };
        return action.set(path, thr), thr;
      }
      _incrReadyCount() {
        return this._readyCount++;
      }
      /**
       * Awaits write operation to finish.
       * Polls a newly created file for size variations. When files size does not change for 'threshold' milliseconds calls callback.
       * @param {Path} path being acted upon
       * @param {Number} threshold Time in milliseconds a file size must be fixed before acknowledging write OP is finished
       * @param {EventName} event
       * @param {Function} awfEmit Callback to be called when ready for event to be emitted.
       */
      _awaitWriteFinish(path, threshold, event, awfEmit) {
        let timeoutHandler, fullPath = path;
        this.options.cwd && !sysPath.isAbsolute(path) && (fullPath = sysPath.join(this.options.cwd, path));
        let now = /* @__PURE__ */ new Date(), awaitWriteFinish = (prevStat) => {
          fs.stat(fullPath, (err, curStat) => {
            if (err || !this._pendingWrites.has(path)) {
              err && err.code !== "ENOENT" && awfEmit(err);
              return;
            }
            let now2 = Number(/* @__PURE__ */ new Date());
            prevStat && curStat.size !== prevStat.size && (this._pendingWrites.get(path).lastChange = now2);
            let pw = this._pendingWrites.get(path);
            now2 - pw.lastChange >= threshold ? (this._pendingWrites.delete(path), awfEmit(void 0, curStat)) : timeoutHandler = setTimeout(
              awaitWriteFinish,
              this.options.awaitWriteFinish.pollInterval,
              curStat
            );
          });
        };
        this._pendingWrites.has(path) || (this._pendingWrites.set(path, {
          lastChange: now,
          cancelWait: () => (this._pendingWrites.delete(path), clearTimeout(timeoutHandler), event)
        }), timeoutHandler = setTimeout(
          awaitWriteFinish,
          this.options.awaitWriteFinish.pollInterval
        ));
      }
      _getGlobIgnored() {
        return [...this._ignoredPaths.values()];
      }
      /**
       * Determines whether user has asked to ignore this path.
       * @param {Path} path filepath or dir
       * @param {fs.Stats=} stats result of fs.stat
       * @returns {Boolean}
       */
      _isIgnored(path, stats) {
        if (this.options.atomic && DOT_RE.test(path)) return !0;
        if (!this._userIgnored) {
          let { cwd } = this.options, ign = this.options.ignored, ignored = ign && ign.map(normalizeIgnored(cwd)), paths = arrify(ignored).filter((path2) => typeof path2 === STRING_TYPE && !isGlob(path2)).map((path2) => path2 + SLASH_GLOBSTAR), list = this._getGlobIgnored().map(normalizeIgnored(cwd)).concat(ignored, paths);
          this._userIgnored = anymatch(list, void 0, ANYMATCH_OPTS);
        }
        return this._userIgnored([path, stats]);
      }
      _isntIgnored(path, stat2) {
        return !this._isIgnored(path, stat2);
      }
      /**
       * Provides a set of common helpers and properties relating to symlink and glob handling.
       * @param {Path} path file, directory, or glob pattern being watched
       * @param {Number=} depth at any depth > 0, this isn't a glob
       * @returns {WatchHelper} object containing helpers for this path
       */
      _getWatchHelpers(path, depth) {
        let watchPath = depth || this.options.disableGlobbing || !isGlob(path) ? path : globParent(path), follow = this.options.followSymlinks;
        return new WatchHelper(path, watchPath, follow, this);
      }
      // Directory helpers
      // -----------------
      /**
       * Provides directory tracking objects
       * @param {String} directory path of the directory
       * @returns {DirEntry} the directory's tracking object
       */
      _getWatchedDir(directory) {
        this._boundRemove || (this._boundRemove = this._remove.bind(this));
        let dir = sysPath.resolve(directory);
        return this._watched.has(dir) || this._watched.set(dir, new DirEntry(dir, this._boundRemove)), this._watched.get(dir);
      }
      // File helpers
      // ------------
      /**
       * Check for read permissions.
       * Based on this answer on SO: https://stackoverflow.com/a/11781404/1358405
       * @param {fs.Stats} stats - object, result of fs_stat
       * @returns {Boolean} indicates whether the file can be read
      */
      _hasReadPermissions(stats) {
        if (this.options.ignorePermissionErrors) return !0;
        let st = (stats && Number.parseInt(stats.mode, 10)) & 511;
        return !!(4 & Number.parseInt(st.toString(8)[0], 10));
      }
      /**
       * Handles emitting unlink events for
       * files and directories, and via recursion, for
       * files and directories within directories that are unlinked
       * @param {String} directory within which the following item is located
       * @param {String} item      base path of item/directory
       * @returns {void}
      */
      _remove(directory, item, isDirectory) {
        let path = sysPath.join(directory, item), fullPath = sysPath.resolve(path);
        if (isDirectory = isDirectory ?? (this._watched.has(path) || this._watched.has(fullPath)), !this._throttle("remove", path, 100)) return;
        !isDirectory && !this.options.useFsEvents && this._watched.size === 1 && this.add(directory, item, !0), this._getWatchedDir(path).getChildren().forEach((nested) => this._remove(path, nested));
        let parent = this._getWatchedDir(directory), wasTracked = parent.has(item);
        parent.remove(item), this._symlinkPaths.has(fullPath) && this._symlinkPaths.delete(fullPath);
        let relPath = path;
        if (this.options.cwd && (relPath = sysPath.relative(this.options.cwd, path)), this.options.awaitWriteFinish && this._pendingWrites.has(relPath) && this._pendingWrites.get(relPath).cancelWait() === EV_ADD)
          return;
        this._watched.delete(path), this._watched.delete(fullPath);
        let eventName = isDirectory ? EV_UNLINK_DIR : EV_UNLINK;
        wasTracked && !this._isIgnored(path) && this._emit(eventName, path), this.options.useFsEvents || this._closePath(path);
      }
      /**
       * Closes all watchers for a path
       * @param {Path} path
       */
      _closePath(path) {
        this._closeFile(path);
        let dir = sysPath.dirname(path);
        this._getWatchedDir(dir).remove(sysPath.basename(path));
      }
      /**
       * Closes only file-specific watchers
       * @param {Path} path
       */
      _closeFile(path) {
        let closers = this._closers.get(path);
        closers && (closers.forEach((closer) => closer()), this._closers.delete(path));
      }
      /**
       *
       * @param {Path} path
       * @param {Function} closer
       */
      _addPathCloser(path, closer) {
        if (!closer) return;
        let list = this._closers.get(path);
        list || (list = [], this._closers.set(path, list)), list.push(closer);
      }
      _readdirp(root, opts) {
        if (this.closed) return;
        let options = { type: EV_ALL, alwaysStat: !0, lstat: !0, ...opts }, stream = readdirp(root, options);
        return this._streams.add(stream), stream.once(STR_CLOSE, () => {
          stream = void 0;
        }), stream.once(STR_END, () => {
          stream && (this._streams.delete(stream), stream = void 0);
        }), stream;
      }
    };
    exports.FSWatcher = FSWatcher;
    var watch = (paths, options) => {
      let watcher = new FSWatcher(options);
      return watcher.add(paths), watcher;
    };
    exports.watch = watch;
  }
});
export default require_chokidar();
//# sourceMappingURL=chokidar-XUA2BN3J.js.map
