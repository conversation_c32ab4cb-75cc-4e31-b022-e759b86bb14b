import {
  describe,
  globalExpect,
  test,
  vi
} from "../../chunk-BQ3PZIHZ.js";
import {
  init_cjs_shims
} from "../../chunk-PKR7KJ6P.js";

// src/cli/commands/upgrade.test.ts
init_cjs_shims();
vi.mock("../services/upgrade.js");
describe("upgrade command", () => {
  test("launches service with path", async () => {
    globalExpect(!0).toBeTruthy();
  });
});
//# sourceMappingURL=upgrade.test.js.map
