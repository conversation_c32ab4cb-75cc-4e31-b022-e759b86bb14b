import {
  ClientError,
  GraphQLClient,
  GraphQLClientError,
  abortSignalFromRequestBehaviour,
  allAPIs,
  blockPartnersAccess,
  buildHeaders,
  getIdentityTokenInformation,
  getPartnersToken,
  hashString,
  httpsAgent,
  nonRandomUUID,
  requestMode,
  resolveRequestDocument,
  retryAwareRequest,
  sanitizeURL,
  sanitizedHeadersOutput,
  shopifyFetch,
  z
} from "./chunk-PUO72IWW.js";
import {
  cacheRetrieveOrRepopulate,
  getCurrentSessionId,
  getPackageManager,
  getSessions,
  packageManagerFromUserAgent,
  removeCurrentSessionId,
  removeSessions,
  setCurrentSessionId,
  setSessions,
  timeIntervalToMilliseconds
} from "./chunk-G2VTHDI5.js";
import {
  CLI_KIT_VERSION
} from "./chunk-WRIQTRQE.js";
import {
  AbortError,
  BugError,
  FatalError,
  addPublicMetadata,
  addSensitiveMetadata,
  ciPlatform,
  cloudEnvironment,
  currentProcessIsGlobal,
  environmentVariables,
  firstPartyDev,
  formatPackageManagerCommand,
  import_ts_error,
  isCI,
  isCloudEnvironment,
  isTTY,
  isWsl,
  keypress,
  macAddress,
  openURL,
  outputCompleted,
  outputContent,
  outputDebug,
  outputInfo,
  outputToken,
  platformAndArch,
  require_arrayLikeKeys,
  require_arrayMap,
  require_arrayPush,
  require_baseDifference,
  require_baseFlatten,
  require_baseForOwn,
  require_baseGet,
  require_baseGetAllKeys,
  require_baseIsEqual,
  require_baseIteratee,
  require_baseKeys,
  require_baseRest,
  require_castPath,
  require_defineProperty,
  require_eq,
  require_get,
  require_getSymbols,
  require_getTag,
  require_isArguments,
  require_isArray,
  require_isArrayLike,
  require_isArrayLikeObject,
  require_isBuffer,
  require_isIndex,
  require_isObject,
  require_isPrototype,
  require_isTypedArray,
  require_keys,
  require_mapToArray,
  require_overArg,
  require_stubArray,
  require_toKey,
  runWithTimer,
  sessionConstants,
  stringifyMessage,
  themeToken
} from "./chunk-B36FYNEM.js";
import {
  cwd
} from "./chunk-EG6MBBEN.js";
import {
  __commonJS,
  __require,
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/network-interfaces@1.1.0/node_modules/network-interfaces/index.js
var require_network_interfaces = __commonJS({
  "../../node_modules/.pnpm/network-interfaces@1.1.0/node_modules/network-interfaces/index.js"(exports2) {
    "use strict";
    init_cjs_shims();
    var os2 = __require("os");
    function isValid(address, options2) {
      return !(typeof options2.internal == "boolean" && address.internal !== options2.internal || options2.ipVersion === 4 && address.family !== "IPv4" || options2.ipVersion === 6 && address.family !== "IPv6");
    }
    function findAddresses(interfaceName, options2 = {}) {
      let addresses = os2.networkInterfaces()[interfaceName];
      if (!addresses)
        throw new Error(`Network interface "${interfaceName}" does not exist`);
      let result = [];
      for (let address of addresses)
        isValid(address, options2) && result.push(address);
      return result;
    }
    exports2.toIp = function(interfaceName, options2) {
      let addresses = findAddresses(interfaceName, options2);
      if (addresses.length === 0)
        throw new Error(`No suitable IP address found on interface "${interfaceName}"`);
      return addresses[0].address;
    };
    exports2.toIps = function(interfaceName, options2) {
      return findAddresses(interfaceName, options2).map((address) => address.address);
    };
    exports2.fromIp = function(ip, options2) {
      let interfaces = os2.networkInterfaces(), interfaceNames = Object.keys(interfaces);
      for (let interfaceName of interfaceNames)
        for (let address of interfaces[interfaceName])
          if (address.address === ip && isValid(address, options2))
            return interfaceName;
      throw new Error(`No suitable interfaces were found with IP address "${ip}"`);
    };
    exports2.getInterface = function(options2) {
      let interfaces = os2.networkInterfaces(), interfaceNames = Object.keys(interfaces);
      for (let interfaceName of interfaceNames)
        if (findAddresses(interfaceName, options2).length > 0)
          return interfaceName;
      throw new Error("No suitable interfaces were found");
    };
    exports2.getInterfaces = function(options2) {
      let interfaces = os2.networkInterfaces(), interfaceNames = Object.keys(interfaces), result = [];
      for (let interfaceName of interfaceNames)
        findAddresses(interfaceName, options2).length > 0 && result.push(interfaceName);
      return result;
    };
  }
});

// ../../node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge/dist/cjs.js
var require_cjs = __commonJS({
  "../../node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge/dist/cjs.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var isMergeableObject = function(value) {
      return isNonNullObject(value) && !isSpecial(value);
    };
    function isNonNullObject(value) {
      return !!value && typeof value == "object";
    }
    function isSpecial(value) {
      var stringValue = Object.prototype.toString.call(value);
      return stringValue === "[object RegExp]" || stringValue === "[object Date]" || isReactElement(value);
    }
    var canUseSymbol = typeof Symbol == "function" && Symbol.for, REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for("react.element") : 60103;
    function isReactElement(value) {
      return value.$$typeof === REACT_ELEMENT_TYPE;
    }
    function emptyTarget(val) {
      return Array.isArray(val) ? [] : {};
    }
    function cloneUnlessOtherwiseSpecified(value, options2) {
      return options2.clone !== !1 && options2.isMergeableObject(value) ? deepmerge(emptyTarget(value), value, options2) : value;
    }
    function defaultArrayMerge(target, source, options2) {
      return target.concat(source).map(function(element) {
        return cloneUnlessOtherwiseSpecified(element, options2);
      });
    }
    function getMergeFunction(key, options2) {
      if (!options2.customMerge)
        return deepmerge;
      var customMerge = options2.customMerge(key);
      return typeof customMerge == "function" ? customMerge : deepmerge;
    }
    function getEnumerableOwnPropertySymbols(target) {
      return Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(target).filter(function(symbol) {
        return Object.propertyIsEnumerable.call(target, symbol);
      }) : [];
    }
    function getKeys(target) {
      return Object.keys(target).concat(getEnumerableOwnPropertySymbols(target));
    }
    function propertyIsOnObject(object, property) {
      try {
        return property in object;
      } catch {
        return !1;
      }
    }
    function propertyIsUnsafe(target, key) {
      return propertyIsOnObject(target, key) && !(Object.hasOwnProperty.call(target, key) && Object.propertyIsEnumerable.call(target, key));
    }
    function mergeObject(target, source, options2) {
      var destination = {};
      return options2.isMergeableObject(target) && getKeys(target).forEach(function(key) {
        destination[key] = cloneUnlessOtherwiseSpecified(target[key], options2);
      }), getKeys(source).forEach(function(key) {
        propertyIsUnsafe(target, key) || (propertyIsOnObject(target, key) && options2.isMergeableObject(source[key]) ? destination[key] = getMergeFunction(key, options2)(target[key], source[key], options2) : destination[key] = cloneUnlessOtherwiseSpecified(source[key], options2));
      }), destination;
    }
    function deepmerge(target, source, options2) {
      options2 = options2 || {}, options2.arrayMerge = options2.arrayMerge || defaultArrayMerge, options2.isMergeableObject = options2.isMergeableObject || isMergeableObject, options2.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;
      var sourceIsArray = Array.isArray(source), targetIsArray = Array.isArray(target), sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;
      return sourceAndTargetTypesMatch ? sourceIsArray ? options2.arrayMerge(target, source, options2) : mergeObject(target, source, options2) : cloneUnlessOtherwiseSpecified(source, options2);
    }
    deepmerge.all = function(array, options2) {
      if (!Array.isArray(array))
        throw new Error("first argument should be an array");
      return array.reduce(function(prev, next) {
        return deepmerge(prev, next, options2);
      }, {});
    };
    var deepmerge_1 = deepmerge;
    module2.exports = deepmerge_1;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseAssignValue.js
var require_baseAssignValue = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseAssignValue.js"(exports2, module2) {
    init_cjs_shims();
    var defineProperty = require_defineProperty();
    function baseAssignValue(object, key, value) {
      key == "__proto__" && defineProperty ? defineProperty(object, key, {
        configurable: !0,
        enumerable: !0,
        value,
        writable: !0
      }) : object[key] = value;
    }
    module2.exports = baseAssignValue;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_assignValue.js
var require_assignValue = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_assignValue.js"(exports2, module2) {
    init_cjs_shims();
    var baseAssignValue = require_baseAssignValue(), eq = require_eq(), objectProto = Object.prototype, hasOwnProperty = objectProto.hasOwnProperty;
    function assignValue(object, key, value) {
      var objValue = object[key];
      (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === void 0 && !(key in object)) && baseAssignValue(object, key, value);
    }
    module2.exports = assignValue;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseSet.js
var require_baseSet = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseSet.js"(exports2, module2) {
    init_cjs_shims();
    var assignValue = require_assignValue(), castPath = require_castPath(), isIndex = require_isIndex(), isObject2 = require_isObject(), toKey = require_toKey();
    function baseSet(object, path, value, customizer) {
      if (!isObject2(object))
        return object;
      path = castPath(path, object);
      for (var index = -1, length = path.length, lastIndex = length - 1, nested = object; nested != null && ++index < length; ) {
        var key = toKey(path[index]), newValue = value;
        if (key === "__proto__" || key === "constructor" || key === "prototype")
          return object;
        if (index != lastIndex) {
          var objValue = nested[key];
          newValue = customizer ? customizer(objValue, key, nested) : void 0, newValue === void 0 && (newValue = isObject2(objValue) ? objValue : isIndex(path[index + 1]) ? [] : {});
        }
        assignValue(nested, key, newValue), nested = nested[key];
      }
      return object;
    }
    module2.exports = baseSet;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_basePickBy.js
var require_basePickBy = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_basePickBy.js"(exports2, module2) {
    init_cjs_shims();
    var baseGet = require_baseGet(), baseSet = require_baseSet(), castPath = require_castPath();
    function basePickBy(object, paths, predicate) {
      for (var index = -1, length = paths.length, result = {}; ++index < length; ) {
        var path = paths[index], value = baseGet(object, path);
        predicate(value, path) && baseSet(result, castPath(path, object), value);
      }
      return result;
    }
    module2.exports = basePickBy;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getPrototype.js
var require_getPrototype = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getPrototype.js"(exports2, module2) {
    init_cjs_shims();
    var overArg = require_overArg(), getPrototype = overArg(Object.getPrototypeOf, Object);
    module2.exports = getPrototype;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getSymbolsIn.js
var require_getSymbolsIn = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getSymbolsIn.js"(exports2, module2) {
    init_cjs_shims();
    var arrayPush = require_arrayPush(), getPrototype = require_getPrototype(), getSymbols = require_getSymbols(), stubArray = require_stubArray(), nativeGetSymbols = Object.getOwnPropertySymbols, getSymbolsIn = nativeGetSymbols ? function(object) {
      for (var result = []; object; )
        arrayPush(result, getSymbols(object)), object = getPrototype(object);
      return result;
    } : stubArray;
    module2.exports = getSymbolsIn;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_nativeKeysIn.js
var require_nativeKeysIn = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_nativeKeysIn.js"(exports2, module2) {
    init_cjs_shims();
    function nativeKeysIn(object) {
      var result = [];
      if (object != null)
        for (var key in Object(object))
          result.push(key);
      return result;
    }
    module2.exports = nativeKeysIn;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseKeysIn.js
var require_baseKeysIn = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseKeysIn.js"(exports2, module2) {
    init_cjs_shims();
    var isObject2 = require_isObject(), isPrototype = require_isPrototype(), nativeKeysIn = require_nativeKeysIn(), objectProto = Object.prototype, hasOwnProperty = objectProto.hasOwnProperty;
    function baseKeysIn(object) {
      if (!isObject2(object))
        return nativeKeysIn(object);
      var isProto = isPrototype(object), result = [];
      for (var key in object)
        key == "constructor" && (isProto || !hasOwnProperty.call(object, key)) || result.push(key);
      return result;
    }
    module2.exports = baseKeysIn;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/keysIn.js
var require_keysIn = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/keysIn.js"(exports2, module2) {
    init_cjs_shims();
    var arrayLikeKeys = require_arrayLikeKeys(), baseKeysIn = require_baseKeysIn(), isArrayLike = require_isArrayLike();
    function keysIn(object) {
      return isArrayLike(object) ? arrayLikeKeys(object, !0) : baseKeysIn(object);
    }
    module2.exports = keysIn;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getAllKeysIn.js
var require_getAllKeysIn = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getAllKeysIn.js"(exports2, module2) {
    init_cjs_shims();
    var baseGetAllKeys = require_baseGetAllKeys(), getSymbolsIn = require_getSymbolsIn(), keysIn = require_keysIn();
    function getAllKeysIn(object) {
      return baseGetAllKeys(object, keysIn, getSymbolsIn);
    }
    module2.exports = getAllKeysIn;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/pickBy.js
var require_pickBy = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/pickBy.js"(exports2, module2) {
    init_cjs_shims();
    var arrayMap = require_arrayMap(), baseIteratee = require_baseIteratee(), basePickBy = require_basePickBy(), getAllKeysIn = require_getAllKeysIn();
    function pickBy2(object, predicate) {
      if (object == null)
        return {};
      var props = arrayMap(getAllKeysIn(object), function(prop) {
        return [prop];
      });
      return predicate = baseIteratee(predicate), basePickBy(object, props, function(value, path) {
        return predicate(value, path[0]);
      });
    }
    module2.exports = pickBy2;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/mapValues.js
var require_mapValues = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/mapValues.js"(exports2, module2) {
    init_cjs_shims();
    var baseAssignValue = require_baseAssignValue(), baseForOwn = require_baseForOwn(), baseIteratee = require_baseIteratee();
    function mapValues(object, iteratee) {
      var result = {};
      return iteratee = baseIteratee(iteratee, 3), baseForOwn(object, function(value, key, object2) {
        baseAssignValue(result, key, iteratee(value, key, object2));
      }), result;
    }
    module2.exports = mapValues;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEqual.js
var require_isEqual = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEqual.js"(exports2, module2) {
    init_cjs_shims();
    var baseIsEqual = require_baseIsEqual();
    function isEqual(value, other) {
      return baseIsEqual(value, other);
    }
    module2.exports = isEqual;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/last.js
var require_last = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/last.js"(exports2, module2) {
    init_cjs_shims();
    function last(array) {
      var length = array == null ? 0 : array.length;
      return length ? array[length - 1] : void 0;
    }
    module2.exports = last;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/differenceWith.js
var require_differenceWith = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/differenceWith.js"(exports2, module2) {
    init_cjs_shims();
    var baseDifference = require_baseDifference(), baseFlatten = require_baseFlatten(), baseRest = require_baseRest(), isArrayLikeObject = require_isArrayLikeObject(), last = require_last(), differenceWith2 = baseRest(function(array, values) {
      var comparator = last(values);
      return isArrayLikeObject(comparator) && (comparator = void 0), isArrayLikeObject(array) ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, !0), void 0, comparator) : [];
    });
    module2.exports = differenceWith2;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/fromPairs.js
var require_fromPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/fromPairs.js"(exports2, module2) {
    init_cjs_shims();
    function fromPairs2(pairs) {
      for (var index = -1, length = pairs == null ? 0 : pairs.length, result = {}; ++index < length; ) {
        var pair = pairs[index];
        result[pair[0]] = pair[1];
      }
      return result;
    }
    module2.exports = fromPairs2;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseToPairs.js
var require_baseToPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseToPairs.js"(exports2, module2) {
    init_cjs_shims();
    var arrayMap = require_arrayMap();
    function baseToPairs(object, props) {
      return arrayMap(props, function(key) {
        return [key, object[key]];
      });
    }
    module2.exports = baseToPairs;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_setToPairs.js
var require_setToPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_setToPairs.js"(exports2, module2) {
    init_cjs_shims();
    function setToPairs(set2) {
      var index = -1, result = Array(set2.size);
      return set2.forEach(function(value) {
        result[++index] = [value, value];
      }), result;
    }
    module2.exports = setToPairs;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createToPairs.js
var require_createToPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createToPairs.js"(exports2, module2) {
    init_cjs_shims();
    var baseToPairs = require_baseToPairs(), getTag = require_getTag(), mapToArray = require_mapToArray(), setToPairs = require_setToPairs(), mapTag = "[object Map]", setTag = "[object Set]";
    function createToPairs(keysFunc) {
      return function(object) {
        var tag = getTag(object);
        return tag == mapTag ? mapToArray(object) : tag == setTag ? setToPairs(object) : baseToPairs(object, keysFunc(object));
      };
    }
    module2.exports = createToPairs;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/toPairs.js
var require_toPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/toPairs.js"(exports2, module2) {
    init_cjs_shims();
    var createToPairs = require_createToPairs(), keys = require_keys(), toPairs2 = createToPairs(keys);
    module2.exports = toPairs2;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/set.js
var require_set = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/set.js"(exports2, module2) {
    init_cjs_shims();
    var baseSet = require_baseSet();
    function set2(object, path, value) {
      return object == null ? object : baseSet(object, path, value);
    }
    module2.exports = set2;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseSlice.js
var require_baseSlice = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseSlice.js"(exports2, module2) {
    init_cjs_shims();
    function baseSlice(array, start, end) {
      var index = -1, length = array.length;
      start < 0 && (start = -start > length ? 0 : length + start), end = end > length ? length : end, end < 0 && (end += length), length = start > end ? 0 : end - start >>> 0, start >>>= 0;
      for (var result = Array(length); ++index < length; )
        result[index] = array[index + start];
      return result;
    }
    module2.exports = baseSlice;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_parent.js
var require_parent = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_parent.js"(exports2, module2) {
    init_cjs_shims();
    var baseGet = require_baseGet(), baseSlice = require_baseSlice();
    function parent(object, path) {
      return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));
    }
    module2.exports = parent;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseUnset.js
var require_baseUnset = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseUnset.js"(exports2, module2) {
    init_cjs_shims();
    var castPath = require_castPath(), last = require_last(), parent = require_parent(), toKey = require_toKey();
    function baseUnset(object, path) {
      return path = castPath(path, object), object = parent(object, path), object == null || delete object[toKey(last(path))];
    }
    module2.exports = baseUnset;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/unset.js
var require_unset = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/unset.js"(exports2, module2) {
    init_cjs_shims();
    var baseUnset = require_baseUnset();
    function unset2(object, path) {
      return object == null ? !0 : baseUnset(object, path);
    }
    module2.exports = unset2;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js
var require_isEmpty = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js"(exports2, module2) {
    init_cjs_shims();
    var baseKeys = require_baseKeys(), getTag = require_getTag(), isArguments = require_isArguments(), isArray = require_isArray(), isArrayLike = require_isArrayLike(), isBuffer = require_isBuffer(), isPrototype = require_isPrototype(), isTypedArray = require_isTypedArray(), mapTag = "[object Map]", setTag = "[object Set]", objectProto = Object.prototype, hasOwnProperty = objectProto.hasOwnProperty;
    function isEmpty2(value) {
      if (value == null)
        return !0;
      if (isArrayLike(value) && (isArray(value) || typeof value == "string" || typeof value.splice == "function" || isBuffer(value) || isTypedArray(value) || isArguments(value)))
        return !value.length;
      var tag = getTag(value);
      if (tag == mapTag || tag == setTag)
        return !value.size;
      if (isPrototype(value))
        return !baseKeys(value).length;
      for (var key in value)
        if (hasOwnProperty.call(value, key))
          return !1;
      return !0;
    }
    module2.exports = isEmpty2;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/parser.js
var require_parser = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/parser.js"(exports2) {
    "use strict";
    init_cjs_shims();
    exports2.load = function(received, defaults, onto = {}) {
      var k, ref, v;
      for (k in defaults)
        v = defaults[k], onto[k] = (ref = received[k]) != null ? ref : v;
      return onto;
    };
    exports2.overwrite = function(received, defaults, onto = {}) {
      var k, v;
      for (k in received)
        v = received[k], defaults[k] !== void 0 && (onto[k] = v);
      return onto;
    };
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/DLList.js
var require_DLList = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/DLList.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var DLList;
    DLList = class {
      constructor(incr, decr) {
        this.incr = incr, this.decr = decr, this._first = null, this._last = null, this.length = 0;
      }
      push(value) {
        var node;
        this.length++, typeof this.incr == "function" && this.incr(), node = {
          value,
          prev: this._last,
          next: null
        }, this._last != null ? (this._last.next = node, this._last = node) : this._first = this._last = node;
      }
      shift() {
        var value;
        if (this._first != null)
          return this.length--, typeof this.decr == "function" && this.decr(), value = this._first.value, (this._first = this._first.next) != null ? this._first.prev = null : this._last = null, value;
      }
      first() {
        if (this._first != null)
          return this._first.value;
      }
      getArray() {
        var node, ref, results;
        for (node = this._first, results = []; node != null; )
          results.push((ref = node, node = node.next, ref.value));
        return results;
      }
      forEachShift(cb) {
        var node;
        for (node = this.shift(); node != null; )
          cb(node), node = this.shift();
      }
      debug() {
        var node, ref, ref1, ref2, results;
        for (node = this._first, results = []; node != null; )
          results.push((ref = node, node = node.next, {
            value: ref.value,
            prev: (ref1 = ref.prev) != null ? ref1.value : void 0,
            next: (ref2 = ref.next) != null ? ref2.value : void 0
          }));
        return results;
      }
    };
    module2.exports = DLList;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Events.js
var require_Events = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Events.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    function asyncGeneratorStep2(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator2(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var Events2;
    Events2 = class {
      constructor(instance) {
        if (this.instance = instance, this._events = {}, this.instance.on != null || this.instance.once != null || this.instance.removeAllListeners != null)
          throw new Error("An Emitter already exists for this object");
        this.instance.on = (name, cb) => this._addListener(name, "many", cb), this.instance.once = (name, cb) => this._addListener(name, "once", cb), this.instance.removeAllListeners = (name = null) => name != null ? delete this._events[name] : this._events = {};
      }
      _addListener(name, status, cb) {
        var base;
        return (base = this._events)[name] == null && (base[name] = []), this._events[name].push({
          cb,
          status
        }), this.instance;
      }
      listenerCount(name) {
        return this._events[name] != null ? this._events[name].length : 0;
      }
      trigger(name, ...args) {
        var _this = this;
        return _asyncToGenerator2(function* () {
          var e, promises;
          try {
            return name !== "debug" && _this.trigger("debug", `Event triggered: ${name}`, args), _this._events[name] == null ? void 0 : (_this._events[name] = _this._events[name].filter(function(listener) {
              return listener.status !== "none";
            }), promises = _this._events[name].map(
              /* @__PURE__ */ function() {
                var _ref = _asyncToGenerator2(function* (listener) {
                  var e2, returned;
                  if (listener.status !== "none") {
                    listener.status === "once" && (listener.status = "none");
                    try {
                      return returned = typeof listener.cb == "function" ? listener.cb(...args) : void 0, typeof returned?.then == "function" ? yield returned : returned;
                    } catch (error) {
                      return e2 = error, _this.trigger("error", e2), null;
                    }
                  }
                });
                return function(_x) {
                  return _ref.apply(this, arguments);
                };
              }()
            ), (yield Promise.all(promises)).find(function(x) {
              return x != null;
            }));
          } catch (error) {
            return e = error, _this.trigger("error", e), null;
          }
        })();
      }
    };
    module2.exports = Events2;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Queues.js
var require_Queues = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Queues.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var DLList, Events2, Queues;
    DLList = require_DLList();
    Events2 = require_Events();
    Queues = class {
      constructor(num_priorities) {
        var i;
        this.Events = new Events2(this), this._length = 0, this._lists = function() {
          var j, ref, results;
          for (results = [], i = j = 1, ref = num_priorities; 1 <= ref ? j <= ref : j >= ref; i = 1 <= ref ? ++j : --j)
            results.push(new DLList(() => this.incr(), () => this.decr()));
          return results;
        }.call(this);
      }
      incr() {
        if (this._length++ === 0)
          return this.Events.trigger("leftzero");
      }
      decr() {
        if (--this._length === 0)
          return this.Events.trigger("zero");
      }
      push(job) {
        return this._lists[job.options.priority].push(job);
      }
      queued(priority) {
        return priority != null ? this._lists[priority].length : this._length;
      }
      shiftAll(fn) {
        return this._lists.forEach(function(list) {
          return list.forEachShift(fn);
        });
      }
      getFirst(arr = this._lists) {
        var j, len, list;
        for (j = 0, len = arr.length; j < len; j++)
          if (list = arr[j], list.length > 0)
            return list;
        return [];
      }
      shiftLastFrom(priority) {
        return this.getFirst(this._lists.slice(priority).reverse()).shift();
      }
    };
    module2.exports = Queues;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/BottleneckError.js
var require_BottleneckError = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/BottleneckError.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var BottleneckError;
    BottleneckError = class extends Error {
    };
    module2.exports = BottleneckError;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Job.js
var require_Job = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Job.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    function asyncGeneratorStep2(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator2(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var BottleneckError, DEFAULT_PRIORITY, Job, NUM_PRIORITIES, parser2;
    NUM_PRIORITIES = 10;
    DEFAULT_PRIORITY = 5;
    parser2 = require_parser();
    BottleneckError = require_BottleneckError();
    Job = class {
      constructor(task, args, options2, jobDefaults, rejectOnDrop, Events2, _states, Promise2) {
        this.task = task, this.args = args, this.rejectOnDrop = rejectOnDrop, this.Events = Events2, this._states = _states, this.Promise = Promise2, this.options = parser2.load(options2, jobDefaults), this.options.priority = this._sanitizePriority(this.options.priority), this.options.id === jobDefaults.id && (this.options.id = `${this.options.id}-${this._randomIndex()}`), this.promise = new this.Promise((_resolve, _reject) => {
          this._resolve = _resolve, this._reject = _reject;
        }), this.retryCount = 0;
      }
      _sanitizePriority(priority) {
        var sProperty;
        return sProperty = ~~priority !== priority ? DEFAULT_PRIORITY : priority, sProperty < 0 ? 0 : sProperty > NUM_PRIORITIES - 1 ? NUM_PRIORITIES - 1 : sProperty;
      }
      _randomIndex() {
        return Math.random().toString(36).slice(2);
      }
      doDrop({
        error,
        message = "This job has been dropped by Bottleneck"
      } = {}) {
        return this._states.remove(this.options.id) ? (this.rejectOnDrop && this._reject(error ?? new BottleneckError(message)), this.Events.trigger("dropped", {
          args: this.args,
          options: this.options,
          task: this.task,
          promise: this.promise
        }), !0) : !1;
      }
      _assertStatus(expected) {
        var status;
        if (status = this._states.jobStatus(this.options.id), !(status === expected || expected === "DONE" && status === null))
          throw new BottleneckError(`Invalid job status ${status}, expected ${expected}. Please open an issue at https://github.com/SGrondin/bottleneck/issues`);
      }
      doReceive() {
        return this._states.start(this.options.id), this.Events.trigger("received", {
          args: this.args,
          options: this.options
        });
      }
      doQueue(reachedHWM, blocked) {
        return this._assertStatus("RECEIVED"), this._states.next(this.options.id), this.Events.trigger("queued", {
          args: this.args,
          options: this.options,
          reachedHWM,
          blocked
        });
      }
      doRun() {
        return this.retryCount === 0 ? (this._assertStatus("QUEUED"), this._states.next(this.options.id)) : this._assertStatus("EXECUTING"), this.Events.trigger("scheduled", {
          args: this.args,
          options: this.options
        });
      }
      doExecute(chained, clearGlobalState, run, free) {
        var _this = this;
        return _asyncToGenerator2(function* () {
          var error, eventInfo, passed;
          _this.retryCount === 0 ? (_this._assertStatus("RUNNING"), _this._states.next(_this.options.id)) : _this._assertStatus("EXECUTING"), eventInfo = {
            args: _this.args,
            options: _this.options,
            retryCount: _this.retryCount
          }, _this.Events.trigger("executing", eventInfo);
          try {
            if (passed = yield chained != null ? chained.schedule(_this.options, _this.task, ..._this.args) : _this.task(..._this.args), clearGlobalState())
              return _this.doDone(eventInfo), yield free(_this.options, eventInfo), _this._assertStatus("DONE"), _this._resolve(passed);
          } catch (error1) {
            return error = error1, _this._onFailure(error, eventInfo, clearGlobalState, run, free);
          }
        })();
      }
      doExpire(clearGlobalState, run, free) {
        var error, eventInfo;
        return this._states.jobStatus(this.options.id === "RUNNING") && this._states.next(this.options.id), this._assertStatus("EXECUTING"), eventInfo = {
          args: this.args,
          options: this.options,
          retryCount: this.retryCount
        }, error = new BottleneckError(`This job timed out after ${this.options.expiration} ms.`), this._onFailure(error, eventInfo, clearGlobalState, run, free);
      }
      _onFailure(error, eventInfo, clearGlobalState, run, free) {
        var _this2 = this;
        return _asyncToGenerator2(function* () {
          var retry, retryAfter;
          if (clearGlobalState())
            return retry = yield _this2.Events.trigger("failed", error, eventInfo), retry != null ? (retryAfter = ~~retry, _this2.Events.trigger("retry", `Retrying ${_this2.options.id} after ${retryAfter} ms`, eventInfo), _this2.retryCount++, run(retryAfter)) : (_this2.doDone(eventInfo), yield free(_this2.options, eventInfo), _this2._assertStatus("DONE"), _this2._reject(error));
        })();
      }
      doDone(eventInfo) {
        return this._assertStatus("EXECUTING"), this._states.next(this.options.id), this.Events.trigger("done", eventInfo);
      }
    };
    module2.exports = Job;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/LocalDatastore.js
var require_LocalDatastore = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/LocalDatastore.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    function asyncGeneratorStep2(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator2(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var BottleneckError, LocalDatastore, parser2;
    parser2 = require_parser();
    BottleneckError = require_BottleneckError();
    LocalDatastore = class {
      constructor(instance, storeOptions, storeInstanceOptions) {
        this.instance = instance, this.storeOptions = storeOptions, this.clientId = this.instance._randomIndex(), parser2.load(storeInstanceOptions, storeInstanceOptions, this), this._nextRequest = this._lastReservoirRefresh = this._lastReservoirIncrease = Date.now(), this._running = 0, this._done = 0, this._unblockTime = 0, this.ready = this.Promise.resolve(), this.clients = {}, this._startHeartbeat();
      }
      _startHeartbeat() {
        var base;
        return this.heartbeat == null && (this.storeOptions.reservoirRefreshInterval != null && this.storeOptions.reservoirRefreshAmount != null || this.storeOptions.reservoirIncreaseInterval != null && this.storeOptions.reservoirIncreaseAmount != null) ? typeof (base = this.heartbeat = setInterval(() => {
          var amount, incr, maximum, now, reservoir;
          if (now = Date.now(), this.storeOptions.reservoirRefreshInterval != null && now >= this._lastReservoirRefresh + this.storeOptions.reservoirRefreshInterval && (this._lastReservoirRefresh = now, this.storeOptions.reservoir = this.storeOptions.reservoirRefreshAmount, this.instance._drainAll(this.computeCapacity())), this.storeOptions.reservoirIncreaseInterval != null && now >= this._lastReservoirIncrease + this.storeOptions.reservoirIncreaseInterval) {
            var _this$storeOptions = this.storeOptions;
            if (amount = _this$storeOptions.reservoirIncreaseAmount, maximum = _this$storeOptions.reservoirIncreaseMaximum, reservoir = _this$storeOptions.reservoir, this._lastReservoirIncrease = now, incr = maximum != null ? Math.min(amount, maximum - reservoir) : amount, incr > 0)
              return this.storeOptions.reservoir += incr, this.instance._drainAll(this.computeCapacity());
          }
        }, this.heartbeatInterval)).unref == "function" ? base.unref() : void 0 : clearInterval(this.heartbeat);
      }
      __publish__(message) {
        var _this = this;
        return _asyncToGenerator2(function* () {
          return yield _this.yieldLoop(), _this.instance.Events.trigger("message", message.toString());
        })();
      }
      __disconnect__(flush) {
        var _this2 = this;
        return _asyncToGenerator2(function* () {
          return yield _this2.yieldLoop(), clearInterval(_this2.heartbeat), _this2.Promise.resolve();
        })();
      }
      yieldLoop(t = 0) {
        return new this.Promise(function(resolve, reject) {
          return setTimeout(resolve, t);
        });
      }
      computePenalty() {
        var ref;
        return (ref = this.storeOptions.penalty) != null ? ref : 15 * this.storeOptions.minTime || 5e3;
      }
      __updateSettings__(options2) {
        var _this3 = this;
        return _asyncToGenerator2(function* () {
          return yield _this3.yieldLoop(), parser2.overwrite(options2, options2, _this3.storeOptions), _this3._startHeartbeat(), _this3.instance._drainAll(_this3.computeCapacity()), !0;
        })();
      }
      __running__() {
        var _this4 = this;
        return _asyncToGenerator2(function* () {
          return yield _this4.yieldLoop(), _this4._running;
        })();
      }
      __queued__() {
        var _this5 = this;
        return _asyncToGenerator2(function* () {
          return yield _this5.yieldLoop(), _this5.instance.queued();
        })();
      }
      __done__() {
        var _this6 = this;
        return _asyncToGenerator2(function* () {
          return yield _this6.yieldLoop(), _this6._done;
        })();
      }
      __groupCheck__(time) {
        var _this7 = this;
        return _asyncToGenerator2(function* () {
          return yield _this7.yieldLoop(), _this7._nextRequest + _this7.timeout < time;
        })();
      }
      computeCapacity() {
        var maxConcurrent, reservoir, _this$storeOptions2 = this.storeOptions;
        return maxConcurrent = _this$storeOptions2.maxConcurrent, reservoir = _this$storeOptions2.reservoir, maxConcurrent != null && reservoir != null ? Math.min(maxConcurrent - this._running, reservoir) : maxConcurrent != null ? maxConcurrent - this._running : reservoir ?? null;
      }
      conditionsCheck(weight) {
        var capacity;
        return capacity = this.computeCapacity(), capacity == null || weight <= capacity;
      }
      __incrementReservoir__(incr) {
        var _this8 = this;
        return _asyncToGenerator2(function* () {
          var reservoir;
          return yield _this8.yieldLoop(), reservoir = _this8.storeOptions.reservoir += incr, _this8.instance._drainAll(_this8.computeCapacity()), reservoir;
        })();
      }
      __currentReservoir__() {
        var _this9 = this;
        return _asyncToGenerator2(function* () {
          return yield _this9.yieldLoop(), _this9.storeOptions.reservoir;
        })();
      }
      isBlocked(now) {
        return this._unblockTime >= now;
      }
      check(weight, now) {
        return this.conditionsCheck(weight) && this._nextRequest - now <= 0;
      }
      __check__(weight) {
        var _this10 = this;
        return _asyncToGenerator2(function* () {
          var now;
          return yield _this10.yieldLoop(), now = Date.now(), _this10.check(weight, now);
        })();
      }
      __register__(index, weight, expiration) {
        var _this11 = this;
        return _asyncToGenerator2(function* () {
          var now, wait;
          return yield _this11.yieldLoop(), now = Date.now(), _this11.conditionsCheck(weight) ? (_this11._running += weight, _this11.storeOptions.reservoir != null && (_this11.storeOptions.reservoir -= weight), wait = Math.max(_this11._nextRequest - now, 0), _this11._nextRequest = now + wait + _this11.storeOptions.minTime, {
            success: !0,
            wait,
            reservoir: _this11.storeOptions.reservoir
          }) : {
            success: !1
          };
        })();
      }
      strategyIsBlock() {
        return this.storeOptions.strategy === 3;
      }
      __submit__(queueLength, weight) {
        var _this12 = this;
        return _asyncToGenerator2(function* () {
          var blocked, now, reachedHWM;
          if (yield _this12.yieldLoop(), _this12.storeOptions.maxConcurrent != null && weight > _this12.storeOptions.maxConcurrent)
            throw new BottleneckError(`Impossible to add a job having a weight of ${weight} to a limiter having a maxConcurrent setting of ${_this12.storeOptions.maxConcurrent}`);
          return now = Date.now(), reachedHWM = _this12.storeOptions.highWater != null && queueLength === _this12.storeOptions.highWater && !_this12.check(weight, now), blocked = _this12.strategyIsBlock() && (reachedHWM || _this12.isBlocked(now)), blocked && (_this12._unblockTime = now + _this12.computePenalty(), _this12._nextRequest = _this12._unblockTime + _this12.storeOptions.minTime, _this12.instance._dropAllQueued()), {
            reachedHWM,
            blocked,
            strategy: _this12.storeOptions.strategy
          };
        })();
      }
      __free__(index, weight) {
        var _this13 = this;
        return _asyncToGenerator2(function* () {
          return yield _this13.yieldLoop(), _this13._running -= weight, _this13._done += weight, _this13.instance._drainAll(_this13.computeCapacity()), {
            running: _this13._running
          };
        })();
      }
    };
    module2.exports = LocalDatastore;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/lua.json
var require_lua = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/lua.json"(exports2, module2) {
    module2.exports = {
      "blacklist_client.lua": `local blacklist = ARGV[num_static_argv + 1]

if redis.call('zscore', client_last_seen_key, blacklist) then
  redis.call('zadd', client_last_seen_key, 0, blacklist)
end


return {}
`,
      "check.lua": `local weight = tonumber(ARGV[num_static_argv + 1])

local capacity = process_tick(now, false)['capacity']
local nextRequest = tonumber(redis.call('hget', settings_key, 'nextRequest'))

return conditions_check(capacity, weight) and nextRequest - now <= 0
`,
      "conditions_check.lua": `local conditions_check = function (capacity, weight)
  return capacity == nil or weight <= capacity
end
`,
      "current_reservoir.lua": `return process_tick(now, false)['reservoir']
`,
      "done.lua": `process_tick(now, false)

return tonumber(redis.call('hget', settings_key, 'done'))
`,
      "free.lua": `local index = ARGV[num_static_argv + 1]

redis.call('zadd', job_expirations_key, 0, index)

return process_tick(now, false)['running']
`,
      "get_time.lua": `redis.replicate_commands()

local get_time = function ()
  local time = redis.call('time')

  return tonumber(time[1]..string.sub(time[2], 1, 3))
end
`,
      "group_check.lua": `return not (redis.call('exists', settings_key) == 1)
`,
      "heartbeat.lua": `process_tick(now, true)
`,
      "increment_reservoir.lua": `local incr = tonumber(ARGV[num_static_argv + 1])

redis.call('hincrby', settings_key, 'reservoir', incr)

local reservoir = process_tick(now, true)['reservoir']

local groupTimeout = tonumber(redis.call('hget', settings_key, 'groupTimeout'))
refresh_expiration(0, 0, groupTimeout)

return reservoir
`,
      "init.lua": `local clear = tonumber(ARGV[num_static_argv + 1])
local limiter_version = ARGV[num_static_argv + 2]
local num_local_argv = num_static_argv + 2

if clear == 1 then
  redis.call('del', unpack(KEYS))
end

if redis.call('exists', settings_key) == 0 then
  -- Create
  local args = {'hmset', settings_key}

  for i = num_local_argv + 1, #ARGV do
    table.insert(args, ARGV[i])
  end

  redis.call(unpack(args))
  redis.call('hmset', settings_key,
    'nextRequest', now,
    'lastReservoirRefresh', now,
    'lastReservoirIncrease', now,
    'running', 0,
    'done', 0,
    'unblockTime', 0,
    'capacityPriorityCounter', 0
  )

else
  -- Apply migrations
  local settings = redis.call('hmget', settings_key,
    'id',
    'version'
  )
  local id = settings[1]
  local current_version = settings[2]

  if current_version ~= limiter_version then
    local version_digits = {}
    for k, v in string.gmatch(current_version, "([^.]+)") do
      table.insert(version_digits, tonumber(k))
    end

    -- 2.10.0
    if version_digits[2] < 10 then
      redis.call('hsetnx', settings_key, 'reservoirRefreshInterval', '')
      redis.call('hsetnx', settings_key, 'reservoirRefreshAmount', '')
      redis.call('hsetnx', settings_key, 'lastReservoirRefresh', '')
      redis.call('hsetnx', settings_key, 'done', 0)
      redis.call('hset', settings_key, 'version', '2.10.0')
    end

    -- 2.11.1
    if version_digits[2] < 11 or (version_digits[2] == 11 and version_digits[3] < 1) then
      if redis.call('hstrlen', settings_key, 'lastReservoirRefresh') == 0 then
        redis.call('hmset', settings_key,
          'lastReservoirRefresh', now,
          'version', '2.11.1'
        )
      end
    end

    -- 2.14.0
    if version_digits[2] < 14 then
      local old_running_key = 'b_'..id..'_running'
      local old_executing_key = 'b_'..id..'_executing'

      if redis.call('exists', old_running_key) == 1 then
        redis.call('rename', old_running_key, job_weights_key)
      end
      if redis.call('exists', old_executing_key) == 1 then
        redis.call('rename', old_executing_key, job_expirations_key)
      end
      redis.call('hset', settings_key, 'version', '2.14.0')
    end

    -- 2.15.2
    if version_digits[2] < 15 or (version_digits[2] == 15 and version_digits[3] < 2) then
      redis.call('hsetnx', settings_key, 'capacityPriorityCounter', 0)
      redis.call('hset', settings_key, 'version', '2.15.2')
    end

    -- 2.17.0
    if version_digits[2] < 17 then
      redis.call('hsetnx', settings_key, 'clientTimeout', 10000)
      redis.call('hset', settings_key, 'version', '2.17.0')
    end

    -- 2.18.0
    if version_digits[2] < 18 then
      redis.call('hsetnx', settings_key, 'reservoirIncreaseInterval', '')
      redis.call('hsetnx', settings_key, 'reservoirIncreaseAmount', '')
      redis.call('hsetnx', settings_key, 'reservoirIncreaseMaximum', '')
      redis.call('hsetnx', settings_key, 'lastReservoirIncrease', now)
      redis.call('hset', settings_key, 'version', '2.18.0')
    end

  end

  process_tick(now, false)
end

local groupTimeout = tonumber(redis.call('hget', settings_key, 'groupTimeout'))
refresh_expiration(0, 0, groupTimeout)

return {}
`,
      "process_tick.lua": `local process_tick = function (now, always_publish)

  local compute_capacity = function (maxConcurrent, running, reservoir)
    if maxConcurrent ~= nil and reservoir ~= nil then
      return math.min((maxConcurrent - running), reservoir)
    elseif maxConcurrent ~= nil then
      return maxConcurrent - running
    elseif reservoir ~= nil then
      return reservoir
    else
      return nil
    end
  end

  local settings = redis.call('hmget', settings_key,
    'id',
    'maxConcurrent',
    'running',
    'reservoir',
    'reservoirRefreshInterval',
    'reservoirRefreshAmount',
    'lastReservoirRefresh',
    'reservoirIncreaseInterval',
    'reservoirIncreaseAmount',
    'reservoirIncreaseMaximum',
    'lastReservoirIncrease',
    'capacityPriorityCounter',
    'clientTimeout'
  )
  local id = settings[1]
  local maxConcurrent = tonumber(settings[2])
  local running = tonumber(settings[3])
  local reservoir = tonumber(settings[4])
  local reservoirRefreshInterval = tonumber(settings[5])
  local reservoirRefreshAmount = tonumber(settings[6])
  local lastReservoirRefresh = tonumber(settings[7])
  local reservoirIncreaseInterval = tonumber(settings[8])
  local reservoirIncreaseAmount = tonumber(settings[9])
  local reservoirIncreaseMaximum = tonumber(settings[10])
  local lastReservoirIncrease = tonumber(settings[11])
  local capacityPriorityCounter = tonumber(settings[12])
  local clientTimeout = tonumber(settings[13])

  local initial_capacity = compute_capacity(maxConcurrent, running, reservoir)

  --
  -- Process 'running' changes
  --
  local expired = redis.call('zrangebyscore', job_expirations_key, '-inf', '('..now)

  if #expired > 0 then
    redis.call('zremrangebyscore', job_expirations_key, '-inf', '('..now)

    local flush_batch = function (batch, acc)
      local weights = redis.call('hmget', job_weights_key, unpack(batch))
                      redis.call('hdel',  job_weights_key, unpack(batch))
      local clients = redis.call('hmget', job_clients_key, unpack(batch))
                      redis.call('hdel',  job_clients_key, unpack(batch))

      -- Calculate sum of removed weights
      for i = 1, #weights do
        acc['total'] = acc['total'] + (tonumber(weights[i]) or 0)
      end

      -- Calculate sum of removed weights by client
      local client_weights = {}
      for i = 1, #clients do
        local removed = tonumber(weights[i]) or 0
        if removed > 0 then
          acc['client_weights'][clients[i]] = (acc['client_weights'][clients[i]] or 0) + removed
        end
      end
    end

    local acc = {
      ['total'] = 0,
      ['client_weights'] = {}
    }
    local batch_size = 1000

    -- Compute changes to Zsets and apply changes to Hashes
    for i = 1, #expired, batch_size do
      local batch = {}
      for j = i, math.min(i + batch_size - 1, #expired) do
        table.insert(batch, expired[j])
      end

      flush_batch(batch, acc)
    end

    -- Apply changes to Zsets
    if acc['total'] > 0 then
      redis.call('hincrby', settings_key, 'done', acc['total'])
      running = tonumber(redis.call('hincrby', settings_key, 'running', -acc['total']))
    end

    for client, weight in pairs(acc['client_weights']) do
      redis.call('zincrby', client_running_key, -weight, client)
    end
  end

  --
  -- Process 'reservoir' changes
  --
  local reservoirRefreshActive = reservoirRefreshInterval ~= nil and reservoirRefreshAmount ~= nil
  if reservoirRefreshActive and now >= lastReservoirRefresh + reservoirRefreshInterval then
    reservoir = reservoirRefreshAmount
    redis.call('hmset', settings_key,
      'reservoir', reservoir,
      'lastReservoirRefresh', now
    )
  end

  local reservoirIncreaseActive = reservoirIncreaseInterval ~= nil and reservoirIncreaseAmount ~= nil
  if reservoirIncreaseActive and now >= lastReservoirIncrease + reservoirIncreaseInterval then
    local num_intervals = math.floor((now - lastReservoirIncrease) / reservoirIncreaseInterval)
    local incr = reservoirIncreaseAmount * num_intervals
    if reservoirIncreaseMaximum ~= nil then
      incr = math.min(incr, reservoirIncreaseMaximum - (reservoir or 0))
    end
    if incr > 0 then
      reservoir = (reservoir or 0) + incr
    end
    redis.call('hmset', settings_key,
      'reservoir', reservoir,
      'lastReservoirIncrease', lastReservoirIncrease + (num_intervals * reservoirIncreaseInterval)
    )
  end

  --
  -- Clear unresponsive clients
  --
  local unresponsive = redis.call('zrangebyscore', client_last_seen_key, '-inf', (now - clientTimeout))
  local unresponsive_lookup = {}
  local terminated_clients = {}
  for i = 1, #unresponsive do
    unresponsive_lookup[unresponsive[i]] = true
    if tonumber(redis.call('zscore', client_running_key, unresponsive[i])) == 0 then
      table.insert(terminated_clients, unresponsive[i])
    end
  end
  if #terminated_clients > 0 then
    redis.call('zrem', client_running_key,         unpack(terminated_clients))
    redis.call('hdel', client_num_queued_key,      unpack(terminated_clients))
    redis.call('zrem', client_last_registered_key, unpack(terminated_clients))
    redis.call('zrem', client_last_seen_key,       unpack(terminated_clients))
  end

  --
  -- Broadcast capacity changes
  --
  local final_capacity = compute_capacity(maxConcurrent, running, reservoir)

  if always_publish or (initial_capacity ~= nil and final_capacity == nil) then
    -- always_publish or was not unlimited, now unlimited
    redis.call('publish', 'b_'..id, 'capacity:'..(final_capacity or ''))

  elseif initial_capacity ~= nil and final_capacity ~= nil and final_capacity > initial_capacity then
    -- capacity was increased
    -- send the capacity message to the limiter having the lowest number of running jobs
    -- the tiebreaker is the limiter having not registered a job in the longest time

    local lowest_concurrency_value = nil
    local lowest_concurrency_clients = {}
    local lowest_concurrency_last_registered = {}
    local client_concurrencies = redis.call('zrange', client_running_key, 0, -1, 'withscores')

    for i = 1, #client_concurrencies, 2 do
      local client = client_concurrencies[i]
      local concurrency = tonumber(client_concurrencies[i+1])

      if (
        lowest_concurrency_value == nil or lowest_concurrency_value == concurrency
      ) and (
        not unresponsive_lookup[client]
      ) and (
        tonumber(redis.call('hget', client_num_queued_key, client)) > 0
      ) then
        lowest_concurrency_value = concurrency
        table.insert(lowest_concurrency_clients, client)
        local last_registered = tonumber(redis.call('zscore', client_last_registered_key, client))
        table.insert(lowest_concurrency_last_registered, last_registered)
      end
    end

    if #lowest_concurrency_clients > 0 then
      local position = 1
      local earliest = lowest_concurrency_last_registered[1]

      for i,v in ipairs(lowest_concurrency_last_registered) do
        if v < earliest then
          position = i
          earliest = v
        end
      end

      local next_client = lowest_concurrency_clients[position]
      redis.call('publish', 'b_'..id,
        'capacity-priority:'..(final_capacity or '')..
        ':'..next_client..
        ':'..capacityPriorityCounter
      )
      redis.call('hincrby', settings_key, 'capacityPriorityCounter', '1')
    else
      redis.call('publish', 'b_'..id, 'capacity:'..(final_capacity or ''))
    end
  end

  return {
    ['capacity'] = final_capacity,
    ['running'] = running,
    ['reservoir'] = reservoir
  }
end
`,
      "queued.lua": `local clientTimeout = tonumber(redis.call('hget', settings_key, 'clientTimeout'))
local valid_clients = redis.call('zrangebyscore', client_last_seen_key, (now - clientTimeout), 'inf')
local client_queued = redis.call('hmget', client_num_queued_key, unpack(valid_clients))

local sum = 0
for i = 1, #client_queued do
  sum = sum + tonumber(client_queued[i])
end

return sum
`,
      "refresh_expiration.lua": `local refresh_expiration = function (now, nextRequest, groupTimeout)

  if groupTimeout ~= nil then
    local ttl = (nextRequest + groupTimeout) - now

    for i = 1, #KEYS do
      redis.call('pexpire', KEYS[i], ttl)
    end
  end

end
`,
      "refs.lua": `local settings_key = KEYS[1]
local job_weights_key = KEYS[2]
local job_expirations_key = KEYS[3]
local job_clients_key = KEYS[4]
local client_running_key = KEYS[5]
local client_num_queued_key = KEYS[6]
local client_last_registered_key = KEYS[7]
local client_last_seen_key = KEYS[8]

local now = tonumber(ARGV[1])
local client = ARGV[2]

local num_static_argv = 2
`,
      "register.lua": `local index = ARGV[num_static_argv + 1]
local weight = tonumber(ARGV[num_static_argv + 2])
local expiration = tonumber(ARGV[num_static_argv + 3])

local state = process_tick(now, false)
local capacity = state['capacity']
local reservoir = state['reservoir']

local settings = redis.call('hmget', settings_key,
  'nextRequest',
  'minTime',
  'groupTimeout'
)
local nextRequest = tonumber(settings[1])
local minTime = tonumber(settings[2])
local groupTimeout = tonumber(settings[3])

if conditions_check(capacity, weight) then

  redis.call('hincrby', settings_key, 'running', weight)
  redis.call('hset', job_weights_key, index, weight)
  if expiration ~= nil then
    redis.call('zadd', job_expirations_key, now + expiration, index)
  end
  redis.call('hset', job_clients_key, index, client)
  redis.call('zincrby', client_running_key, weight, client)
  redis.call('hincrby', client_num_queued_key, client, -1)
  redis.call('zadd', client_last_registered_key, now, client)

  local wait = math.max(nextRequest - now, 0)
  local newNextRequest = now + wait + minTime

  if reservoir == nil then
    redis.call('hset', settings_key,
      'nextRequest', newNextRequest
    )
  else
    reservoir = reservoir - weight
    redis.call('hmset', settings_key,
      'reservoir', reservoir,
      'nextRequest', newNextRequest
    )
  end

  refresh_expiration(now, newNextRequest, groupTimeout)

  return {true, wait, reservoir}

else
  return {false}
end
`,
      "register_client.lua": `local queued = tonumber(ARGV[num_static_argv + 1])

-- Could have been re-registered concurrently
if not redis.call('zscore', client_last_seen_key, client) then
  redis.call('zadd', client_running_key, 0, client)
  redis.call('hset', client_num_queued_key, client, queued)
  redis.call('zadd', client_last_registered_key, 0, client)
end

redis.call('zadd', client_last_seen_key, now, client)

return {}
`,
      "running.lua": `return process_tick(now, false)['running']
`,
      "submit.lua": `local queueLength = tonumber(ARGV[num_static_argv + 1])
local weight = tonumber(ARGV[num_static_argv + 2])

local capacity = process_tick(now, false)['capacity']

local settings = redis.call('hmget', settings_key,
  'id',
  'maxConcurrent',
  'highWater',
  'nextRequest',
  'strategy',
  'unblockTime',
  'penalty',
  'minTime',
  'groupTimeout'
)
local id = settings[1]
local maxConcurrent = tonumber(settings[2])
local highWater = tonumber(settings[3])
local nextRequest = tonumber(settings[4])
local strategy = tonumber(settings[5])
local unblockTime = tonumber(settings[6])
local penalty = tonumber(settings[7])
local minTime = tonumber(settings[8])
local groupTimeout = tonumber(settings[9])

if maxConcurrent ~= nil and weight > maxConcurrent then
  return redis.error_reply('OVERWEIGHT:'..weight..':'..maxConcurrent)
end

local reachedHWM = (highWater ~= nil and queueLength == highWater
  and not (
    conditions_check(capacity, weight)
    and nextRequest - now <= 0
  )
)

local blocked = strategy == 3 and (reachedHWM or unblockTime >= now)

if blocked then
  local computedPenalty = penalty
  if computedPenalty == nil then
    if minTime == 0 then
      computedPenalty = 5000
    else
      computedPenalty = 15 * minTime
    end
  end

  local newNextRequest = now + computedPenalty + minTime

  redis.call('hmset', settings_key,
    'unblockTime', now + computedPenalty,
    'nextRequest', newNextRequest
  )

  local clients_queued_reset = redis.call('hkeys', client_num_queued_key)
  local queued_reset = {}
  for i = 1, #clients_queued_reset do
    table.insert(queued_reset, clients_queued_reset[i])
    table.insert(queued_reset, 0)
  end
  redis.call('hmset', client_num_queued_key, unpack(queued_reset))

  redis.call('publish', 'b_'..id, 'blocked:')

  refresh_expiration(now, newNextRequest, groupTimeout)
end

if not blocked and not reachedHWM then
  redis.call('hincrby', client_num_queued_key, client, 1)
end

return {reachedHWM, blocked, strategy}
`,
      "update_settings.lua": `local args = {'hmset', settings_key}

for i = num_static_argv + 1, #ARGV do
  table.insert(args, ARGV[i])
end

redis.call(unpack(args))

process_tick(now, true)

local groupTimeout = tonumber(redis.call('hget', settings_key, 'groupTimeout'))
refresh_expiration(0, 0, groupTimeout)

return {}
`,
      "validate_client.lua": `if not redis.call('zscore', client_last_seen_key, client) then
  return redis.error_reply('UNKNOWN_CLIENT')
end

redis.call('zadd', client_last_seen_key, now, client)
`,
      "validate_keys.lua": `if not (redis.call('exists', settings_key) == 1) then
  return redis.error_reply('SETTINGS_KEY_NOT_FOUND')
end
`
    };
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Scripts.js
var require_Scripts = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Scripts.js"(exports2) {
    "use strict";
    init_cjs_shims();
    var headers, lua, templates;
    lua = require_lua();
    headers = {
      refs: lua["refs.lua"],
      validate_keys: lua["validate_keys.lua"],
      validate_client: lua["validate_client.lua"],
      refresh_expiration: lua["refresh_expiration.lua"],
      process_tick: lua["process_tick.lua"],
      conditions_check: lua["conditions_check.lua"],
      get_time: lua["get_time.lua"]
    };
    exports2.allKeys = function(id) {
      return [
        /*
        HASH
        */
        `b_${id}_settings`,
        /*
        HASH
        job index -> weight
        */
        `b_${id}_job_weights`,
        /*
        ZSET
        job index -> expiration
        */
        `b_${id}_job_expirations`,
        /*
        HASH
        job index -> client
        */
        `b_${id}_job_clients`,
        /*
        ZSET
        client -> sum running
        */
        `b_${id}_client_running`,
        /*
        HASH
        client -> num queued
        */
        `b_${id}_client_num_queued`,
        /*
        ZSET
        client -> last job registered
        */
        `b_${id}_client_last_registered`,
        /*
        ZSET
        client -> last seen
        */
        `b_${id}_client_last_seen`
      ];
    };
    templates = {
      init: {
        keys: exports2.allKeys,
        headers: ["process_tick"],
        refresh_expiration: !0,
        code: lua["init.lua"]
      },
      group_check: {
        keys: exports2.allKeys,
        headers: [],
        refresh_expiration: !1,
        code: lua["group_check.lua"]
      },
      register_client: {
        keys: exports2.allKeys,
        headers: ["validate_keys"],
        refresh_expiration: !1,
        code: lua["register_client.lua"]
      },
      blacklist_client: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client"],
        refresh_expiration: !1,
        code: lua["blacklist_client.lua"]
      },
      heartbeat: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick"],
        refresh_expiration: !1,
        code: lua["heartbeat.lua"]
      },
      update_settings: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick"],
        refresh_expiration: !0,
        code: lua["update_settings.lua"]
      },
      running: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick"],
        refresh_expiration: !1,
        code: lua["running.lua"]
      },
      queued: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client"],
        refresh_expiration: !1,
        code: lua["queued.lua"]
      },
      done: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick"],
        refresh_expiration: !1,
        code: lua["done.lua"]
      },
      check: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick", "conditions_check"],
        refresh_expiration: !1,
        code: lua["check.lua"]
      },
      submit: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick", "conditions_check"],
        refresh_expiration: !0,
        code: lua["submit.lua"]
      },
      register: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick", "conditions_check"],
        refresh_expiration: !0,
        code: lua["register.lua"]
      },
      free: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick"],
        refresh_expiration: !0,
        code: lua["free.lua"]
      },
      current_reservoir: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick"],
        refresh_expiration: !1,
        code: lua["current_reservoir.lua"]
      },
      increment_reservoir: {
        keys: exports2.allKeys,
        headers: ["validate_keys", "validate_client", "process_tick"],
        refresh_expiration: !0,
        code: lua["increment_reservoir.lua"]
      }
    };
    exports2.names = Object.keys(templates);
    exports2.keys = function(name, id) {
      return templates[name].keys(id);
    };
    exports2.payload = function(name) {
      var template;
      return template = templates[name], Array.prototype.concat(headers.refs, template.headers.map(function(h) {
        return headers[h];
      }), template.refresh_expiration ? headers.refresh_expiration : "", template.code).join(`
`);
    };
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/RedisConnection.js
var require_RedisConnection = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/RedisConnection.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var Events, RedisConnection, Scripts, parser;
    parser = require_parser();
    Events = require_Events();
    Scripts = require_Scripts();
    RedisConnection = function() {
      class RedisConnection {
        constructor(options = {}) {
          parser.load(options, this.defaults, this), this.Redis == null && (this.Redis = eval("require")("redis")), this.Events == null && (this.Events = new Events(this)), this.terminated = !1, this.client == null && (this.client = this.Redis.createClient(this.clientOptions)), this.subscriber = this.client.duplicate(), this.limiters = {}, this.shas = {}, this.ready = this.Promise.all([this._setup(this.client, !1), this._setup(this.subscriber, !0)]).then(() => this._loadScripts()).then(() => ({
            client: this.client,
            subscriber: this.subscriber
          }));
        }
        _setup(client, sub) {
          return client.setMaxListeners(0), new this.Promise((resolve, reject) => (client.on("error", (e) => this.Events.trigger("error", e)), sub && client.on("message", (channel, message) => {
            var ref;
            return (ref = this.limiters[channel]) != null ? ref._store.onMessage(channel, message) : void 0;
          }), client.ready ? resolve() : client.once("ready", resolve)));
        }
        _loadScript(name) {
          return new this.Promise((resolve, reject) => {
            var payload;
            return payload = Scripts.payload(name), this.client.multi([["script", "load", payload]]).exec((err2, replies) => err2 != null ? reject(err2) : (this.shas[name] = replies[0], resolve(replies[0])));
          });
        }
        _loadScripts() {
          return this.Promise.all(Scripts.names.map((k) => this._loadScript(k)));
        }
        __runCommand__(cmd) {
          var _this = this;
          return _asyncToGenerator(function* () {
            return yield _this.ready, new _this.Promise((resolve, reject) => _this.client.multi([cmd]).exec_atomic(function(err2, replies) {
              return err2 != null ? reject(err2) : resolve(replies[0]);
            }));
          })();
        }
        __addLimiter__(instance) {
          return this.Promise.all([instance.channel(), instance.channel_client()].map((channel) => new this.Promise((resolve, reject) => {
            var handler;
            return handler = (chan) => {
              if (chan === channel)
                return this.subscriber.removeListener("subscribe", handler), this.limiters[channel] = instance, resolve();
            }, this.subscriber.on("subscribe", handler), this.subscriber.subscribe(channel);
          })));
        }
        __removeLimiter__(instance) {
          var _this2 = this;
          return this.Promise.all([instance.channel(), instance.channel_client()].map(
            /* @__PURE__ */ function() {
              var _ref = _asyncToGenerator(function* (channel) {
                return _this2.terminated || (yield new _this2.Promise((resolve, reject) => _this2.subscriber.unsubscribe(channel, function(err2, chan) {
                  if (err2 != null)
                    return reject(err2);
                  if (chan === channel)
                    return resolve();
                }))), delete _this2.limiters[channel];
              });
              return function(_x) {
                return _ref.apply(this, arguments);
              };
            }()
          ));
        }
        __scriptArgs__(name, id, args, cb) {
          var keys;
          return keys = Scripts.keys(name, id), [this.shas[name], keys.length].concat(keys, args, cb);
        }
        __scriptFn__(name) {
          return this.client.evalsha.bind(this.client);
        }
        disconnect(flush = !0) {
          var i, k, len, ref;
          for (ref = Object.keys(this.limiters), i = 0, len = ref.length; i < len; i++)
            k = ref[i], clearInterval(this.limiters[k]._store.heartbeat);
          return this.limiters = {}, this.terminated = !0, this.client.end(flush), this.subscriber.end(flush), this.Promise.resolve();
        }
      }
      return RedisConnection.prototype.datastore = "redis", RedisConnection.prototype.defaults = {
        Redis: null,
        clientOptions: {},
        client: null,
        Promise,
        Events: null
      }, RedisConnection;
    }.call(void 0);
    module.exports = RedisConnection;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/IORedisConnection.js
var require_IORedisConnection = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/IORedisConnection.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    function _slicedToArray(arr, i) {
      return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _nonIterableRest();
    }
    function _nonIterableRest() {
      throw new TypeError("Invalid attempt to destructure non-iterable instance");
    }
    function _iterableToArrayLimit(arr, i) {
      var _arr = [], _n = !0, _d = !1, _e = void 0;
      try {
        for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done) && (_arr.push(_s.value), !(i && _arr.length === i)); _n = !0)
          ;
      } catch (err2) {
        _d = !0, _e = err2;
      } finally {
        try {
          !_n && _i.return != null && _i.return();
        } finally {
          if (_d) throw _e;
        }
      }
      return _arr;
    }
    function _arrayWithHoles(arr) {
      if (Array.isArray(arr)) return arr;
    }
    function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var Events, IORedisConnection, Scripts, parser;
    parser = require_parser();
    Events = require_Events();
    Scripts = require_Scripts();
    IORedisConnection = function() {
      class IORedisConnection {
        constructor(options = {}) {
          parser.load(options, this.defaults, this), this.Redis == null && (this.Redis = eval("require")("ioredis")), this.Events == null && (this.Events = new Events(this)), this.terminated = !1, this.clusterNodes != null ? (this.client = new this.Redis.Cluster(this.clusterNodes, this.clientOptions), this.subscriber = new this.Redis.Cluster(this.clusterNodes, this.clientOptions)) : this.client != null && this.client.duplicate == null ? this.subscriber = new this.Redis.Cluster(this.client.startupNodes, this.client.options) : (this.client == null && (this.client = new this.Redis(this.clientOptions)), this.subscriber = this.client.duplicate()), this.limiters = {}, this.ready = this.Promise.all([this._setup(this.client, !1), this._setup(this.subscriber, !0)]).then(() => (this._loadScripts(), {
            client: this.client,
            subscriber: this.subscriber
          }));
        }
        _setup(client, sub) {
          return client.setMaxListeners(0), new this.Promise((resolve, reject) => (client.on("error", (e) => this.Events.trigger("error", e)), sub && client.on("message", (channel, message) => {
            var ref;
            return (ref = this.limiters[channel]) != null ? ref._store.onMessage(channel, message) : void 0;
          }), client.status === "ready" ? resolve() : client.once("ready", resolve)));
        }
        _loadScripts() {
          return Scripts.names.forEach((name) => this.client.defineCommand(name, {
            lua: Scripts.payload(name)
          }));
        }
        __runCommand__(cmd) {
          var _this = this;
          return _asyncToGenerator(function* () {
            var _, deleted;
            yield _this.ready;
            var _ref = yield _this.client.pipeline([cmd]).exec(), _ref2 = _slicedToArray(_ref, 1), _ref2$ = _slicedToArray(_ref2[0], 2);
            return _ = _ref2$[0], deleted = _ref2$[1], deleted;
          })();
        }
        __addLimiter__(instance) {
          return this.Promise.all([instance.channel(), instance.channel_client()].map((channel) => new this.Promise((resolve, reject) => this.subscriber.subscribe(channel, () => (this.limiters[channel] = instance, resolve())))));
        }
        __removeLimiter__(instance) {
          var _this2 = this;
          return [instance.channel(), instance.channel_client()].forEach(
            /* @__PURE__ */ function() {
              var _ref3 = _asyncToGenerator(function* (channel) {
                return _this2.terminated || (yield _this2.subscriber.unsubscribe(channel)), delete _this2.limiters[channel];
              });
              return function(_x) {
                return _ref3.apply(this, arguments);
              };
            }()
          );
        }
        __scriptArgs__(name, id, args, cb) {
          var keys;
          return keys = Scripts.keys(name, id), [keys.length].concat(keys, args, cb);
        }
        __scriptFn__(name) {
          return this.client[name].bind(this.client);
        }
        disconnect(flush = !0) {
          var i, k, len, ref;
          for (ref = Object.keys(this.limiters), i = 0, len = ref.length; i < len; i++)
            k = ref[i], clearInterval(this.limiters[k]._store.heartbeat);
          return this.limiters = {}, this.terminated = !0, flush ? this.Promise.all([this.client.quit(), this.subscriber.quit()]) : (this.client.disconnect(), this.subscriber.disconnect(), this.Promise.resolve());
        }
      }
      return IORedisConnection.prototype.datastore = "ioredis", IORedisConnection.prototype.defaults = {
        Redis: null,
        clientOptions: {},
        clusterNodes: null,
        client: null,
        Promise,
        Events: null
      }, IORedisConnection;
    }.call(void 0);
    module.exports = IORedisConnection;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/RedisDatastore.js
var require_RedisDatastore = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/RedisDatastore.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    function _slicedToArray2(arr, i) {
      return _arrayWithHoles2(arr) || _iterableToArrayLimit2(arr, i) || _nonIterableRest2();
    }
    function _nonIterableRest2() {
      throw new TypeError("Invalid attempt to destructure non-iterable instance");
    }
    function _iterableToArrayLimit2(arr, i) {
      var _arr = [], _n = !0, _d = !1, _e = void 0;
      try {
        for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done) && (_arr.push(_s.value), !(i && _arr.length === i)); _n = !0)
          ;
      } catch (err2) {
        _d = !0, _e = err2;
      } finally {
        try {
          !_n && _i.return != null && _i.return();
        } finally {
          if (_d) throw _e;
        }
      }
      return _arr;
    }
    function _arrayWithHoles2(arr) {
      if (Array.isArray(arr)) return arr;
    }
    function asyncGeneratorStep2(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator2(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var BottleneckError, IORedisConnection2, RedisConnection2, RedisDatastore, parser2;
    parser2 = require_parser();
    BottleneckError = require_BottleneckError();
    RedisConnection2 = require_RedisConnection();
    IORedisConnection2 = require_IORedisConnection();
    RedisDatastore = class {
      constructor(instance, storeOptions, storeInstanceOptions) {
        this.instance = instance, this.storeOptions = storeOptions, this.originalId = this.instance.id, this.clientId = this.instance._randomIndex(), parser2.load(storeInstanceOptions, storeInstanceOptions, this), this.clients = {}, this.capacityPriorityCounters = {}, this.sharedConnection = this.connection != null, this.connection == null && (this.connection = this.instance.datastore === "redis" ? new RedisConnection2({
          Redis: this.Redis,
          clientOptions: this.clientOptions,
          Promise: this.Promise,
          Events: this.instance.Events
        }) : this.instance.datastore === "ioredis" ? new IORedisConnection2({
          Redis: this.Redis,
          clientOptions: this.clientOptions,
          clusterNodes: this.clusterNodes,
          Promise: this.Promise,
          Events: this.instance.Events
        }) : void 0), this.instance.connection = this.connection, this.instance.datastore = this.connection.datastore, this.ready = this.connection.ready.then((clients) => (this.clients = clients, this.runScript("init", this.prepareInitSettings(this.clearDatastore)))).then(() => this.connection.__addLimiter__(this.instance)).then(() => this.runScript("register_client", [this.instance.queued()])).then(() => {
          var base;
          return typeof (base = this.heartbeat = setInterval(() => this.runScript("heartbeat", []).catch((e) => this.instance.Events.trigger("error", e)), this.heartbeatInterval)).unref == "function" && base.unref(), this.clients;
        });
      }
      __publish__(message) {
        var _this = this;
        return _asyncToGenerator2(function* () {
          var client, _ref = yield _this.ready;
          return client = _ref.client, client.publish(_this.instance.channel(), `message:${message.toString()}`);
        })();
      }
      onMessage(channel, message) {
        var _this2 = this;
        return _asyncToGenerator2(function* () {
          var capacity, counter, data, drained, e, newCapacity, pos, priorityClient, rawCapacity, type;
          try {
            pos = message.indexOf(":");
            var _ref2 = [message.slice(0, pos), message.slice(pos + 1)];
            if (type = _ref2[0], data = _ref2[1], type === "capacity")
              return yield _this2.instance._drainAll(data.length > 0 ? ~~data : void 0);
            if (type === "capacity-priority") {
              var _data$split = data.split(":"), _data$split2 = _slicedToArray2(_data$split, 3);
              return rawCapacity = _data$split2[0], priorityClient = _data$split2[1], counter = _data$split2[2], capacity = rawCapacity.length > 0 ? ~~rawCapacity : void 0, priorityClient === _this2.clientId ? (drained = yield _this2.instance._drainAll(capacity), newCapacity = capacity != null ? capacity - (drained || 0) : "", yield _this2.clients.client.publish(_this2.instance.channel(), `capacity-priority:${newCapacity}::${counter}`)) : priorityClient === "" ? (clearTimeout(_this2.capacityPriorityCounters[counter]), delete _this2.capacityPriorityCounters[counter], _this2.instance._drainAll(capacity)) : _this2.capacityPriorityCounters[counter] = setTimeout(
                /* @__PURE__ */ _asyncToGenerator2(function* () {
                  var e2;
                  try {
                    return delete _this2.capacityPriorityCounters[counter], yield _this2.runScript("blacklist_client", [priorityClient]), yield _this2.instance._drainAll(capacity);
                  } catch (error) {
                    return e2 = error, _this2.instance.Events.trigger("error", e2);
                  }
                }),
                1e3
              );
            } else {
              if (type === "message")
                return _this2.instance.Events.trigger("message", data);
              if (type === "blocked")
                return yield _this2.instance._dropAllQueued();
            }
          } catch (error) {
            return e = error, _this2.instance.Events.trigger("error", e);
          }
        })();
      }
      __disconnect__(flush) {
        return clearInterval(this.heartbeat), this.sharedConnection ? this.connection.__removeLimiter__(this.instance) : this.connection.disconnect(flush);
      }
      runScript(name, args) {
        var _this3 = this;
        return _asyncToGenerator2(function* () {
          return name === "init" || name === "register_client" || (yield _this3.ready), new _this3.Promise((resolve, reject) => {
            var all_args, arr;
            return all_args = [Date.now(), _this3.clientId].concat(args), _this3.instance.Events.trigger("debug", `Calling Redis script: ${name}.lua`, all_args), arr = _this3.connection.__scriptArgs__(name, _this3.originalId, all_args, function(err2, replies) {
              return err2 != null ? reject(err2) : resolve(replies);
            }), _this3.connection.__scriptFn__(name)(...arr);
          }).catch((e) => e.message === "SETTINGS_KEY_NOT_FOUND" ? name === "heartbeat" ? _this3.Promise.resolve() : _this3.runScript("init", _this3.prepareInitSettings(!1)).then(() => _this3.runScript(name, args)) : e.message === "UNKNOWN_CLIENT" ? _this3.runScript("register_client", [_this3.instance.queued()]).then(() => _this3.runScript(name, args)) : _this3.Promise.reject(e));
        })();
      }
      prepareArray(arr) {
        var i, len, results, x;
        for (results = [], i = 0, len = arr.length; i < len; i++)
          x = arr[i], results.push(x != null ? x.toString() : "");
        return results;
      }
      prepareObject(obj) {
        var arr, k, v;
        arr = [];
        for (k in obj)
          v = obj[k], arr.push(k, v != null ? v.toString() : "");
        return arr;
      }
      prepareInitSettings(clear) {
        var args;
        return args = this.prepareObject(Object.assign({}, this.storeOptions, {
          id: this.originalId,
          version: this.instance.version,
          groupTimeout: this.timeout,
          clientTimeout: this.clientTimeout
        })), args.unshift(clear ? 1 : 0, this.instance.version), args;
      }
      convertBool(b) {
        return !!b;
      }
      __updateSettings__(options2) {
        var _this4 = this;
        return _asyncToGenerator2(function* () {
          return yield _this4.runScript("update_settings", _this4.prepareObject(options2)), parser2.overwrite(options2, options2, _this4.storeOptions);
        })();
      }
      __running__() {
        return this.runScript("running", []);
      }
      __queued__() {
        return this.runScript("queued", []);
      }
      __done__() {
        return this.runScript("done", []);
      }
      __groupCheck__() {
        var _this5 = this;
        return _asyncToGenerator2(function* () {
          return _this5.convertBool(yield _this5.runScript("group_check", []));
        })();
      }
      __incrementReservoir__(incr) {
        return this.runScript("increment_reservoir", [incr]);
      }
      __currentReservoir__() {
        return this.runScript("current_reservoir", []);
      }
      __check__(weight) {
        var _this6 = this;
        return _asyncToGenerator2(function* () {
          return _this6.convertBool(yield _this6.runScript("check", _this6.prepareArray([weight])));
        })();
      }
      __register__(index, weight, expiration) {
        var _this7 = this;
        return _asyncToGenerator2(function* () {
          var reservoir, success, wait, _ref4 = yield _this7.runScript("register", _this7.prepareArray([index, weight, expiration])), _ref5 = _slicedToArray2(_ref4, 3);
          return success = _ref5[0], wait = _ref5[1], reservoir = _ref5[2], {
            success: _this7.convertBool(success),
            wait,
            reservoir
          };
        })();
      }
      __submit__(queueLength, weight) {
        var _this8 = this;
        return _asyncToGenerator2(function* () {
          var blocked, e, maxConcurrent, overweight, reachedHWM, strategy;
          try {
            var _ref6 = yield _this8.runScript("submit", _this8.prepareArray([queueLength, weight])), _ref7 = _slicedToArray2(_ref6, 3);
            return reachedHWM = _ref7[0], blocked = _ref7[1], strategy = _ref7[2], {
              reachedHWM: _this8.convertBool(reachedHWM),
              blocked: _this8.convertBool(blocked),
              strategy
            };
          } catch (error) {
            if (e = error, e.message.indexOf("OVERWEIGHT") === 0) {
              var _e$message$split = e.message.split(":"), _e$message$split2 = _slicedToArray2(_e$message$split, 3);
              throw overweight = _e$message$split2[0], weight = _e$message$split2[1], maxConcurrent = _e$message$split2[2], new BottleneckError(`Impossible to add a job having a weight of ${weight} to a limiter having a maxConcurrent setting of ${maxConcurrent}`);
            } else
              throw e;
          }
        })();
      }
      __free__(index, weight) {
        var _this9 = this;
        return _asyncToGenerator2(function* () {
          var running;
          return running = yield _this9.runScript("free", _this9.prepareArray([index])), {
            running
          };
        })();
      }
    };
    module2.exports = RedisDatastore;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/States.js
var require_States = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/States.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var BottleneckError, States;
    BottleneckError = require_BottleneckError();
    States = class {
      constructor(status1) {
        this.status = status1, this._jobs = {}, this.counts = this.status.map(function() {
          return 0;
        });
      }
      next(id) {
        var current, next;
        if (current = this._jobs[id], next = current + 1, current != null && next < this.status.length)
          return this.counts[current]--, this.counts[next]++, this._jobs[id]++;
        if (current != null)
          return this.counts[current]--, delete this._jobs[id];
      }
      start(id) {
        var initial;
        return initial = 0, this._jobs[id] = initial, this.counts[initial]++;
      }
      remove(id) {
        var current;
        return current = this._jobs[id], current != null && (this.counts[current]--, delete this._jobs[id]), current != null;
      }
      jobStatus(id) {
        var ref;
        return (ref = this.status[this._jobs[id]]) != null ? ref : null;
      }
      statusJobs(status) {
        var k, pos, ref, results, v;
        if (status != null) {
          if (pos = this.status.indexOf(status), pos < 0)
            throw new BottleneckError(`status must be one of ${this.status.join(", ")}`);
          ref = this._jobs, results = [];
          for (k in ref)
            v = ref[k], v === pos && results.push(k);
          return results;
        } else
          return Object.keys(this._jobs);
      }
      statusCounts() {
        return this.counts.reduce((acc, v, i) => (acc[this.status[i]] = v, acc), {});
      }
    };
    module2.exports = States;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Sync.js
var require_Sync = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Sync.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    function asyncGeneratorStep2(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator2(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var DLList, Sync;
    DLList = require_DLList();
    Sync = class {
      constructor(name, Promise2) {
        this.schedule = this.schedule.bind(this), this.name = name, this.Promise = Promise2, this._running = 0, this._queue = new DLList();
      }
      isEmpty() {
        return this._queue.length === 0;
      }
      _tryToRun() {
        var _this = this;
        return _asyncToGenerator2(function* () {
          var args, cb, error, reject, resolve, returned, task;
          if (_this._running < 1 && _this._queue.length > 0) {
            _this._running++;
            var _this$_queue$shift = _this._queue.shift();
            return task = _this$_queue$shift.task, args = _this$_queue$shift.args, resolve = _this$_queue$shift.resolve, reject = _this$_queue$shift.reject, cb = yield _asyncToGenerator2(function* () {
              try {
                return returned = yield task(...args), function() {
                  return resolve(returned);
                };
              } catch (error1) {
                return error = error1, function() {
                  return reject(error);
                };
              }
            })(), _this._running--, _this._tryToRun(), cb();
          }
        })();
      }
      schedule(task, ...args) {
        var promise, reject, resolve;
        return resolve = reject = null, promise = new this.Promise(function(_resolve, _reject) {
          return resolve = _resolve, reject = _reject;
        }), this._queue.push({
          task,
          args,
          resolve,
          reject
        }), this._tryToRun(), promise;
      }
    };
    module2.exports = Sync;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/version.json
var require_version = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/version.json"(exports2, module2) {
    module2.exports = { version: "2.19.5" };
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Group.js
var require_Group = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Group.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    function _slicedToArray2(arr, i) {
      return _arrayWithHoles2(arr) || _iterableToArrayLimit2(arr, i) || _nonIterableRest2();
    }
    function _nonIterableRest2() {
      throw new TypeError("Invalid attempt to destructure non-iterable instance");
    }
    function _iterableToArrayLimit2(arr, i) {
      var _arr = [], _n = !0, _d = !1, _e = void 0;
      try {
        for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done) && (_arr.push(_s.value), !(i && _arr.length === i)); _n = !0)
          ;
      } catch (err2) {
        _d = !0, _e = err2;
      } finally {
        try {
          !_n && _i.return != null && _i.return();
        } finally {
          if (_d) throw _e;
        }
      }
      return _arr;
    }
    function _arrayWithHoles2(arr) {
      if (Array.isArray(arr)) return arr;
    }
    function asyncGeneratorStep2(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator2(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var Events2, Group, IORedisConnection2, RedisConnection2, Scripts2, parser2;
    parser2 = require_parser();
    Events2 = require_Events();
    RedisConnection2 = require_RedisConnection();
    IORedisConnection2 = require_IORedisConnection();
    Scripts2 = require_Scripts();
    Group = function() {
      class Group2 {
        constructor(limiterOptions = {}) {
          this.deleteKey = this.deleteKey.bind(this), this.limiterOptions = limiterOptions, parser2.load(this.limiterOptions, this.defaults, this), this.Events = new Events2(this), this.instances = {}, this.Bottleneck = require_Bottleneck(), this._startAutoCleanup(), this.sharedConnection = this.connection != null, this.connection == null && (this.limiterOptions.datastore === "redis" ? this.connection = new RedisConnection2(Object.assign({}, this.limiterOptions, {
            Events: this.Events
          })) : this.limiterOptions.datastore === "ioredis" && (this.connection = new IORedisConnection2(Object.assign({}, this.limiterOptions, {
            Events: this.Events
          }))));
        }
        key(key = "") {
          var ref;
          return (ref = this.instances[key]) != null ? ref : (() => {
            var limiter2;
            return limiter2 = this.instances[key] = new this.Bottleneck(Object.assign(this.limiterOptions, {
              id: `${this.id}-${key}`,
              timeout: this.timeout,
              connection: this.connection
            })), this.Events.trigger("created", limiter2, key), limiter2;
          })();
        }
        deleteKey(key = "") {
          var _this = this;
          return _asyncToGenerator2(function* () {
            var deleted, instance;
            return instance = _this.instances[key], _this.connection && (deleted = yield _this.connection.__runCommand__(["del", ...Scripts2.allKeys(`${_this.id}-${key}`)])), instance != null && (delete _this.instances[key], yield instance.disconnect()), instance != null || deleted > 0;
          })();
        }
        limiters() {
          var k, ref, results, v;
          ref = this.instances, results = [];
          for (k in ref)
            v = ref[k], results.push({
              key: k,
              limiter: v
            });
          return results;
        }
        keys() {
          return Object.keys(this.instances);
        }
        clusterKeys() {
          var _this2 = this;
          return _asyncToGenerator2(function* () {
            var cursor, end, found, i, k, keys, len, next, start;
            if (_this2.connection == null)
              return _this2.Promise.resolve(_this2.keys());
            for (keys = [], cursor = null, start = `b_${_this2.id}-`.length, end = 9; cursor !== 0; ) {
              var _ref = yield _this2.connection.__runCommand__(["scan", cursor ?? 0, "match", `b_${_this2.id}-*_settings`, "count", 1e4]), _ref2 = _slicedToArray2(_ref, 2);
              for (next = _ref2[0], found = _ref2[1], cursor = ~~next, i = 0, len = found.length; i < len; i++)
                k = found[i], keys.push(k.slice(start, -end));
            }
            return keys;
          })();
        }
        _startAutoCleanup() {
          var _this3 = this, base;
          return clearInterval(this.interval), typeof (base = this.interval = setInterval(
            /* @__PURE__ */ _asyncToGenerator2(function* () {
              var e, k, ref, results, time, v;
              time = Date.now(), ref = _this3.instances, results = [];
              for (k in ref) {
                v = ref[k];
                try {
                  (yield v._store.__groupCheck__(time)) ? results.push(_this3.deleteKey(k)) : results.push(void 0);
                } catch (error) {
                  e = error, results.push(v.Events.trigger("error", e));
                }
              }
              return results;
            }),
            this.timeout / 2
          )).unref == "function" ? base.unref() : void 0;
        }
        updateSettings(options2 = {}) {
          if (parser2.overwrite(options2, this.defaults, this), parser2.overwrite(options2, options2, this.limiterOptions), options2.timeout != null)
            return this._startAutoCleanup();
        }
        disconnect(flush = !0) {
          var ref;
          if (!this.sharedConnection)
            return (ref = this.connection) != null ? ref.disconnect(flush) : void 0;
        }
      }
      return Group2.prototype.defaults = {
        timeout: 1e3 * 60 * 5,
        connection: null,
        Promise,
        id: "group-key"
      }, Group2;
    }.call(void 0);
    module2.exports = Group;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Batcher.js
var require_Batcher = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Batcher.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    var Batcher, Events2, parser2;
    parser2 = require_parser();
    Events2 = require_Events();
    Batcher = function() {
      class Batcher2 {
        constructor(options2 = {}) {
          this.options = options2, parser2.load(this.options, this.defaults, this), this.Events = new Events2(this), this._arr = [], this._resetPromise(), this._lastFlush = Date.now();
        }
        _resetPromise() {
          return this._promise = new this.Promise((res, rej) => this._resolve = res);
        }
        _flush() {
          return clearTimeout(this._timeout), this._lastFlush = Date.now(), this._resolve(), this.Events.trigger("batch", this._arr), this._arr = [], this._resetPromise();
        }
        add(data) {
          var ret;
          return this._arr.push(data), ret = this._promise, this._arr.length === this.maxSize ? this._flush() : this.maxTime != null && this._arr.length === 1 && (this._timeout = setTimeout(() => this._flush(), this.maxTime)), ret;
        }
      }
      return Batcher2.prototype.defaults = {
        maxTime: null,
        maxSize: null,
        Promise
      }, Batcher2;
    }.call(void 0);
    module2.exports = Batcher;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Bottleneck.js
var require_Bottleneck = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/Bottleneck.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    function _slicedToArray2(arr, i) {
      return _arrayWithHoles2(arr) || _iterableToArrayLimit2(arr, i) || _nonIterableRest2();
    }
    function _iterableToArrayLimit2(arr, i) {
      var _arr = [], _n = !0, _d = !1, _e = void 0;
      try {
        for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done) && (_arr.push(_s.value), !(i && _arr.length === i)); _n = !0)
          ;
      } catch (err2) {
        _d = !0, _e = err2;
      } finally {
        try {
          !_n && _i.return != null && _i.return();
        } finally {
          if (_d) throw _e;
        }
      }
      return _arr;
    }
    function _toArray(arr) {
      return _arrayWithHoles2(arr) || _iterableToArray(arr) || _nonIterableRest2();
    }
    function _nonIterableRest2() {
      throw new TypeError("Invalid attempt to destructure non-iterable instance");
    }
    function _iterableToArray(iter) {
      if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === "[object Arguments]") return Array.from(iter);
    }
    function _arrayWithHoles2(arr) {
      if (Array.isArray(arr)) return arr;
    }
    function asyncGeneratorStep2(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg), value = info.value;
      } catch (error) {
        reject(error);
        return;
      }
      info.done ? resolve(value) : Promise.resolve(value).then(_next, _throw);
    }
    function _asyncToGenerator2(fn) {
      return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
          var gen = fn.apply(self, args);
          function _next(value) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "next", value);
          }
          function _throw(err2) {
            asyncGeneratorStep2(gen, resolve, reject, _next, _throw, "throw", err2);
          }
          _next(void 0);
        });
      };
    }
    var Bottleneck2, DEFAULT_PRIORITY, Events2, Job, LocalDatastore, NUM_PRIORITIES, Queues, RedisDatastore, States, Sync, parser2, splice = [].splice;
    NUM_PRIORITIES = 10;
    DEFAULT_PRIORITY = 5;
    parser2 = require_parser();
    Queues = require_Queues();
    Job = require_Job();
    LocalDatastore = require_LocalDatastore();
    RedisDatastore = require_RedisDatastore();
    Events2 = require_Events();
    States = require_States();
    Sync = require_Sync();
    Bottleneck2 = function() {
      class Bottleneck3 {
        constructor(options2 = {}, ...invalid) {
          var storeInstanceOptions, storeOptions;
          this._addToQueue = this._addToQueue.bind(this), this._validateOptions(options2, invalid), parser2.load(options2, this.instanceDefaults, this), this._queues = new Queues(NUM_PRIORITIES), this._scheduled = {}, this._states = new States(["RECEIVED", "QUEUED", "RUNNING", "EXECUTING"].concat(this.trackDoneStatus ? ["DONE"] : [])), this._limiter = null, this.Events = new Events2(this), this._submitLock = new Sync("submit", this.Promise), this._registerLock = new Sync("register", this.Promise), storeOptions = parser2.load(options2, this.storeDefaults, {}), this._store = function() {
            if (this.datastore === "redis" || this.datastore === "ioredis" || this.connection != null)
              return storeInstanceOptions = parser2.load(options2, this.redisStoreDefaults, {}), new RedisDatastore(this, storeOptions, storeInstanceOptions);
            if (this.datastore === "local")
              return storeInstanceOptions = parser2.load(options2, this.localStoreDefaults, {}), new LocalDatastore(this, storeOptions, storeInstanceOptions);
            throw new Bottleneck3.prototype.BottleneckError(`Invalid datastore type: ${this.datastore}`);
          }.call(this), this._queues.on("leftzero", () => {
            var ref;
            return (ref = this._store.heartbeat) != null && typeof ref.ref == "function" ? ref.ref() : void 0;
          }), this._queues.on("zero", () => {
            var ref;
            return (ref = this._store.heartbeat) != null && typeof ref.unref == "function" ? ref.unref() : void 0;
          });
        }
        _validateOptions(options2, invalid) {
          if (!(options2 != null && typeof options2 == "object" && invalid.length === 0))
            throw new Bottleneck3.prototype.BottleneckError("Bottleneck v2 takes a single object argument. Refer to https://github.com/SGrondin/bottleneck#upgrading-to-v2 if you're upgrading from Bottleneck v1.");
        }
        ready() {
          return this._store.ready;
        }
        clients() {
          return this._store.clients;
        }
        channel() {
          return `b_${this.id}`;
        }
        channel_client() {
          return `b_${this.id}_${this._store.clientId}`;
        }
        publish(message) {
          return this._store.__publish__(message);
        }
        disconnect(flush = !0) {
          return this._store.__disconnect__(flush);
        }
        chain(_limiter) {
          return this._limiter = _limiter, this;
        }
        queued(priority) {
          return this._queues.queued(priority);
        }
        clusterQueued() {
          return this._store.__queued__();
        }
        empty() {
          return this.queued() === 0 && this._submitLock.isEmpty();
        }
        running() {
          return this._store.__running__();
        }
        done() {
          return this._store.__done__();
        }
        jobStatus(id) {
          return this._states.jobStatus(id);
        }
        jobs(status) {
          return this._states.statusJobs(status);
        }
        counts() {
          return this._states.statusCounts();
        }
        _randomIndex() {
          return Math.random().toString(36).slice(2);
        }
        check(weight = 1) {
          return this._store.__check__(weight);
        }
        _clearGlobalState(index) {
          return this._scheduled[index] != null ? (clearTimeout(this._scheduled[index].expiration), delete this._scheduled[index], !0) : !1;
        }
        _free(index, job, options2, eventInfo) {
          var _this = this;
          return _asyncToGenerator2(function* () {
            var e, running;
            try {
              var _ref = yield _this._store.__free__(index, options2.weight);
              if (running = _ref.running, _this.Events.trigger("debug", `Freed ${options2.id}`, eventInfo), running === 0 && _this.empty())
                return _this.Events.trigger("idle");
            } catch (error1) {
              return e = error1, _this.Events.trigger("error", e);
            }
          })();
        }
        _run(index, job, wait) {
          var clearGlobalState, free, run;
          return job.doRun(), clearGlobalState = this._clearGlobalState.bind(this, index), run = this._run.bind(this, index, job), free = this._free.bind(this, index, job), this._scheduled[index] = {
            timeout: setTimeout(() => job.doExecute(this._limiter, clearGlobalState, run, free), wait),
            expiration: job.options.expiration != null ? setTimeout(function() {
              return job.doExpire(clearGlobalState, run, free);
            }, wait + job.options.expiration) : void 0,
            job
          };
        }
        _drainOne(capacity) {
          return this._registerLock.schedule(() => {
            var args, index, next, options2, queue;
            if (this.queued() === 0)
              return this.Promise.resolve(null);
            queue = this._queues.getFirst();
            var _next2 = next = queue.first();
            return options2 = _next2.options, args = _next2.args, capacity != null && options2.weight > capacity ? this.Promise.resolve(null) : (this.Events.trigger("debug", `Draining ${options2.id}`, {
              args,
              options: options2
            }), index = this._randomIndex(), this._store.__register__(index, options2.weight, options2.expiration).then(({
              success,
              wait,
              reservoir
            }) => {
              var empty;
              return this.Events.trigger("debug", `Drained ${options2.id}`, {
                success,
                args,
                options: options2
              }), success ? (queue.shift(), empty = this.empty(), empty && this.Events.trigger("empty"), reservoir === 0 && this.Events.trigger("depleted", empty), this._run(index, next, wait), this.Promise.resolve(options2.weight)) : this.Promise.resolve(null);
            }));
          });
        }
        _drainAll(capacity, total = 0) {
          return this._drainOne(capacity).then((drained) => {
            var newCapacity;
            return drained != null ? (newCapacity = capacity != null ? capacity - drained : capacity, this._drainAll(newCapacity, total + drained)) : this.Promise.resolve(total);
          }).catch((e) => this.Events.trigger("error", e));
        }
        _dropAllQueued(message) {
          return this._queues.shiftAll(function(job) {
            return job.doDrop({
              message
            });
          });
        }
        stop(options2 = {}) {
          var done, waitForExecuting;
          return options2 = parser2.load(options2, this.stopDefaults), waitForExecuting = (at) => {
            var finished;
            return finished = () => {
              var counts;
              return counts = this._states.counts, counts[0] + counts[1] + counts[2] + counts[3] === at;
            }, new this.Promise((resolve, reject) => finished() ? resolve() : this.on("done", () => {
              if (finished())
                return this.removeAllListeners("done"), resolve();
            }));
          }, done = options2.dropWaitingJobs ? (this._run = function(index, next) {
            return next.doDrop({
              message: options2.dropErrorMessage
            });
          }, this._drainOne = () => this.Promise.resolve(null), this._registerLock.schedule(() => this._submitLock.schedule(() => {
            var k, ref, v;
            ref = this._scheduled;
            for (k in ref)
              v = ref[k], this.jobStatus(v.job.options.id) === "RUNNING" && (clearTimeout(v.timeout), clearTimeout(v.expiration), v.job.doDrop({
                message: options2.dropErrorMessage
              }));
            return this._dropAllQueued(options2.dropErrorMessage), waitForExecuting(0);
          }))) : this.schedule({
            priority: NUM_PRIORITIES - 1,
            weight: 0
          }, () => waitForExecuting(1)), this._receive = function(job) {
            return job._reject(new Bottleneck3.prototype.BottleneckError(options2.enqueueErrorMessage));
          }, this.stop = () => this.Promise.reject(new Bottleneck3.prototype.BottleneckError("stop() has already been called")), done;
        }
        _addToQueue(job) {
          var _this2 = this;
          return _asyncToGenerator2(function* () {
            var args, blocked, error, options2, reachedHWM, shifted, strategy;
            args = job.args, options2 = job.options;
            try {
              var _ref2 = yield _this2._store.__submit__(_this2.queued(), options2.weight);
              reachedHWM = _ref2.reachedHWM, blocked = _ref2.blocked, strategy = _ref2.strategy;
            } catch (error1) {
              return error = error1, _this2.Events.trigger("debug", `Could not queue ${options2.id}`, {
                args,
                options: options2,
                error
              }), job.doDrop({
                error
              }), !1;
            }
            return blocked ? (job.doDrop(), !0) : reachedHWM && (shifted = strategy === Bottleneck3.prototype.strategy.LEAK ? _this2._queues.shiftLastFrom(options2.priority) : strategy === Bottleneck3.prototype.strategy.OVERFLOW_PRIORITY ? _this2._queues.shiftLastFrom(options2.priority + 1) : strategy === Bottleneck3.prototype.strategy.OVERFLOW ? job : void 0, shifted?.doDrop(), shifted == null || strategy === Bottleneck3.prototype.strategy.OVERFLOW) ? (shifted == null && job.doDrop(), reachedHWM) : (job.doQueue(reachedHWM, blocked), _this2._queues.push(job), yield _this2._drainAll(), reachedHWM);
          })();
        }
        _receive(job) {
          return this._states.jobStatus(job.options.id) != null ? (job._reject(new Bottleneck3.prototype.BottleneckError(`A job with the same id already exists (id=${job.options.id})`)), !1) : (job.doReceive(), this._submitLock.schedule(this._addToQueue, job));
        }
        submit(...args) {
          var cb, fn, job, options2, ref, ref1, task;
          if (typeof args[0] == "function") {
            var _ref3, _ref4, _splice$call, _splice$call2;
            ref = args, _ref3 = ref, _ref4 = _toArray(_ref3), fn = _ref4[0], args = _ref4.slice(1), _splice$call = splice.call(args, -1), _splice$call2 = _slicedToArray2(_splice$call, 1), cb = _splice$call2[0], options2 = parser2.load({}, this.jobDefaults);
          } else {
            var _ref5, _ref6, _splice$call3, _splice$call4;
            ref1 = args, _ref5 = ref1, _ref6 = _toArray(_ref5), options2 = _ref6[0], fn = _ref6[1], args = _ref6.slice(2), _splice$call3 = splice.call(args, -1), _splice$call4 = _slicedToArray2(_splice$call3, 1), cb = _splice$call4[0], options2 = parser2.load(options2, this.jobDefaults);
          }
          return task = (...args2) => new this.Promise(function(resolve, reject) {
            return fn(...args2, function(...args3) {
              return (args3[0] != null ? reject : resolve)(args3);
            });
          }), job = new Job(task, args, options2, this.jobDefaults, this.rejectOnDrop, this.Events, this._states, this.Promise), job.promise.then(function(args2) {
            return typeof cb == "function" ? cb(...args2) : void 0;
          }).catch(function(args2) {
            return Array.isArray(args2) ? typeof cb == "function" ? cb(...args2) : void 0 : typeof cb == "function" ? cb(args2) : void 0;
          }), this._receive(job);
        }
        schedule(...args) {
          var job, options2, task;
          if (typeof args[0] == "function") {
            var _args = args, _args2 = _toArray(_args);
            task = _args2[0], args = _args2.slice(1), options2 = {};
          } else {
            var _args3 = args, _args4 = _toArray(_args3);
            options2 = _args4[0], task = _args4[1], args = _args4.slice(2);
          }
          return job = new Job(task, args, options2, this.jobDefaults, this.rejectOnDrop, this.Events, this._states, this.Promise), this._receive(job), job.promise;
        }
        wrap(fn) {
          var schedule, wrapped;
          return schedule = this.schedule.bind(this), wrapped = function(...args) {
            return schedule(fn.bind(this), ...args);
          }, wrapped.withOptions = function(options2, ...args) {
            return schedule(options2, fn, ...args);
          }, wrapped;
        }
        updateSettings(options2 = {}) {
          var _this3 = this;
          return _asyncToGenerator2(function* () {
            return yield _this3._store.__updateSettings__(parser2.overwrite(options2, _this3.storeDefaults)), parser2.overwrite(options2, _this3.instanceDefaults, _this3), _this3;
          })();
        }
        currentReservoir() {
          return this._store.__currentReservoir__();
        }
        incrementReservoir(incr = 0) {
          return this._store.__incrementReservoir__(incr);
        }
      }
      return Bottleneck3.default = Bottleneck3, Bottleneck3.Events = Events2, Bottleneck3.version = Bottleneck3.prototype.version = require_version().version, Bottleneck3.strategy = Bottleneck3.prototype.strategy = {
        LEAK: 1,
        OVERFLOW: 2,
        OVERFLOW_PRIORITY: 4,
        BLOCK: 3
      }, Bottleneck3.BottleneckError = Bottleneck3.prototype.BottleneckError = require_BottleneckError(), Bottleneck3.Group = Bottleneck3.prototype.Group = require_Group(), Bottleneck3.RedisConnection = Bottleneck3.prototype.RedisConnection = require_RedisConnection(), Bottleneck3.IORedisConnection = Bottleneck3.prototype.IORedisConnection = require_IORedisConnection(), Bottleneck3.Batcher = Bottleneck3.prototype.Batcher = require_Batcher(), Bottleneck3.prototype.jobDefaults = {
        priority: DEFAULT_PRIORITY,
        weight: 1,
        expiration: null,
        id: "<no-id>"
      }, Bottleneck3.prototype.storeDefaults = {
        maxConcurrent: null,
        minTime: 0,
        highWater: null,
        strategy: Bottleneck3.prototype.strategy.LEAK,
        penalty: null,
        reservoir: null,
        reservoirRefreshInterval: null,
        reservoirRefreshAmount: null,
        reservoirIncreaseInterval: null,
        reservoirIncreaseAmount: null,
        reservoirIncreaseMaximum: null
      }, Bottleneck3.prototype.localStoreDefaults = {
        Promise,
        timeout: null,
        heartbeatInterval: 250
      }, Bottleneck3.prototype.redisStoreDefaults = {
        Promise,
        timeout: null,
        heartbeatInterval: 5e3,
        clientTimeout: 1e4,
        Redis: null,
        clientOptions: {},
        clusterNodes: null,
        clearDatastore: !1,
        connection: null
      }, Bottleneck3.prototype.instanceDefaults = {
        datastore: "local",
        connection: null,
        id: "<no-id>",
        rejectOnDrop: !0,
        trackDoneStatus: !1,
        Promise
      }, Bottleneck3.prototype.stopDefaults = {
        enqueueErrorMessage: "This limiter has been stopped and cannot accept new jobs.",
        dropWaitingJobs: !0,
        dropErrorMessage: "This limiter has been stopped."
      }, Bottleneck3;
    }.call(void 0);
    module2.exports = Bottleneck2;
  }
});

// ../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/index.js
var require_lib = __commonJS({
  "../../node_modules/.pnpm/bottleneck@2.19.5/node_modules/bottleneck/lib/index.js"(exports2, module2) {
    "use strict";
    init_cjs_shims();
    module2.exports = require_Bottleneck();
  }
});

// ../cli-kit/dist/private/node/context/service.js
init_cjs_shims();
var Environment;
(function(Environment2) {
  Environment2.Local = "local", Environment2.Production = "production";
})(Environment || (Environment = {}));
function serviceEnvironment(env = process.env) {
  return env[environmentVariables.serviceEnv] === "local" ? Environment.Local : Environment.Production;
}
function isLocalEnvironment(env = process.env) {
  return serviceEnvironment(env) === Environment.Local;
}

// ../cli-kit/dist/public/node/session.js
init_cjs_shims();

// ../cli-kit/dist/private/node/session/store.js
init_cjs_shims();

// ../cli-kit/dist/private/node/session/schema.js
init_cjs_shims();
var DateSchema = z.preprocess((arg) => typeof arg == "string" || arg instanceof Date ? new Date(arg) : null, z.date()), IdentityTokenSchema = z.object({
  accessToken: z.string(),
  refreshToken: z.string(),
  expiresAt: DateSchema,
  scopes: z.array(z.string()),
  userId: z.string(),
  alias: z.string().optional()
}), ApplicationTokenSchema = z.object({
  accessToken: z.string(),
  expiresAt: DateSchema,
  scopes: z.array(z.string()),
  storeFqdn: z.string().optional()
}), SessionSchema = z.object({
  identity: IdentityTokenSchema,
  applications: z.object({}).catchall(ApplicationTokenSchema)
}), SessionsSchema = z.object({}).catchall(z.object({}).catchall(SessionSchema));
function validateCachedIdentityTokenStructure(identityToken) {
  return IdentityTokenSchema.safeParse(identityToken).success;
}

// ../cli-kit/dist/public/node/context/fqdn.js
init_cjs_shims();

// ../cli-kit/dist/public/node/vendor/dev_server/index.js
init_cjs_shims();

// ../cli-kit/dist/public/node/vendor/dev_server/env.js
init_cjs_shims();
var isDevServerEnvironment = process.env.USING_DEV === "1";
function assertCompatibleEnvironment() {
  if (!isDevServerEnvironment)
    throw new Error("DevServer is not supported in this environment");
}

// ../cli-kit/dist/public/node/vendor/dev_server/dev-server.js
init_cjs_shims();
import fs4 from "node:fs";

// ../cli-kit/dist/public/node/vendor/dev_server/dev-server-2024.js
init_cjs_shims();
var ni = __toESM(require_network_interfaces(), 1);
import fs2 from "node:fs";

// ../cli-kit/dist/public/node/vendor/dev_server/network/index.js
init_cjs_shims();
import { spawnSync } from "node:child_process";

// ../cli-kit/dist/public/node/vendor/dev_server/network/host.js
init_cjs_shims();
import fs from "node:fs";
var HOSTS_FILE = "/etc/hosts", hostToIpCache = {}, lastModifiedTime = 0;
function loadHostsFile() {
  try {
    let modifiedTime = fs.statSync(HOSTS_FILE).mtimeMs;
    if (modifiedTime === lastModifiedTime)
      return;
    let lines = fs.readFileSync(HOSTS_FILE, "utf8").split(/\r?\n/);
    hostToIpCache = {};
    for (let line of lines) {
      if (line.trim().startsWith("#") || line.trim() === "")
        continue;
      let matches = /^\s*(?<ipAddress>[^\s#]+)\s+(?<matchedHostName>[^\s#]+)\s*(#.*)?$/.exec(line);
      if (matches && matches.groups) {
        let { ipAddress, matchedHostName } = matches.groups;
        matchedHostName && ipAddress && (hostToIpCache[matchedHostName] = ipAddress);
      }
    }
    lastModifiedTime = modifiedTime;
  } catch (error) {
    console.log("Error reading hosts file:", error);
  }
}
function getIpFromHosts(hostname) {
  loadHostsFile();
  let ipAddress = hostToIpCache[hostname];
  if (ipAddress)
    return ipAddress;
  throw new Error(`No IP found for hostname: ${hostname}`);
}

// ../cli-kit/dist/public/node/vendor/dev_server/network/index.js
var DEFAULT_CONNECT_TIMEOUT = 100, checkPort;
function assertConnectable(options2) {
  checkPort || (checkPort = getCheckPortHelper());
  let { port, addr, timeout = DEFAULT_CONNECT_TIMEOUT } = options2;
  try {
    if (!checkPort(addr === "localhost" ? "127.0.0.1" : addr, port, timeout))
      throw new Error(`DevServer for '${options2.projectName}' is not running on ${port} / ${addr}: \`dev up ${options2.projectName}\` to start it.`);
  } catch (err2) {
    throw new Error(`DevServer check for '${options2.projectName}' on ${port} / ${addr} failed (${err2})`);
  }
}
function getCheckPortHelper() {
  return fallbackCheckPort;
}
function fallbackCheckPort(address, port, timeout) {
  return spawnSync("nc", ["-z", "-w", "1", address, port.toString()], {
    timeout,
    stdio: "ignore"
  }).status === 0;
}

// ../cli-kit/dist/public/node/vendor/dev_server/dev-server-2024.js
var NON_SHOP_PREFIXES = ["app", "dev", "shopify"], BACKEND_PORT = 8080;
function createServer(projectName) {
  return {
    host: (options2 = {}) => host(projectName, options2),
    url: (options2 = {}) => url(projectName, options2)
  };
}
function host(projectName, options2 = {}) {
  assertCompatibleEnvironment(), (assertRunningOverride || assertRunning2024)(projectName);
  let prefix = (options2.nonstandardHostPrefix || projectName).replace(/_/g, "-");
  if (projectName === "shopify") {
    if (prefix.endsWith("-dev-api"))
      return `${prefix.replace("-dev-api", "")}.dev-api.shop.dev`;
    if (!NON_SHOP_PREFIXES.includes(prefix))
      return `${prefix}.my.shop.dev`;
  }
  return `${prefix}.shop.dev`;
}
function url(projectName, options2 = {}) {
  return `https://${host(projectName, options2)}`;
}
function assertRunning2024(projectName) {
  assertConnectable({
    projectName,
    addr: getBackendIp(projectName),
    port: BACKEND_PORT
  });
}
function getBackendIp(projectName) {
  try {
    let backendIp = resolveBackendHost(projectName);
    return ni.fromIp(backendIp, { internal: !0, ipVersion: 4 }), backendIp;
  } catch {
    throw new Error(`DevServer for '${projectName}' is not running: \`dev up ${projectName}\` to start it.`);
  }
}
function resolveBackendHost(name) {
  let host3;
  try {
    host3 = fs2.readlinkSync(`/opt/nginx/etc/manifest/${name}/current`);
  } catch {
    host3 = `${name}.root.shopify.dev.internal`;
  }
  try {
    return getIpFromHosts(host3);
  } catch {
    return host3;
  }
}
var assertRunningOverride;

// ../cli-kit/dist/public/node/vendor/dev_server/dev-server-2016.js
init_cjs_shims();
import fs3 from "fs";
import * as os from "node:os";
function createServer2(projectName) {
  return {
    host: (options2 = {}) => host2(projectName, options2),
    url: (options2 = {}) => url2(projectName, options2)
  };
}
function host2(projectName, options2 = {}) {
  return assertCompatibleEnvironment(), (assertRunningOverride2 || assertRunning2016)(projectName), `${options2.nonstandardHostPrefix || projectName}.myshopify.io`;
}
function url2(projectName, options2 = {}) {
  return `https://${host2(projectName, options2)}`;
}
function assertRunning2016(projectName) {
  let [addr, port] = getAddrPort(projectName);
  assertConnectable({ projectName, addr, port });
}
function getAddrPort(name) {
  try {
    let portContent = fs3.readFileSync(`${os.homedir()}/.local/run/services/${name}/server/port`, "utf-8");
    return ["localhost", parseInt(portContent, 10)];
  } catch {
    throw new Error(`DevServer for '${name}' is not running: \`dev up ${name}\` to start it.`);
  }
}
var assertRunningOverride2;

// ../cli-kit/dist/public/node/vendor/dev_server/dev-server.js
var DevServer = class {
  constructor(projectName) {
    if (this.projectName = projectName, projectName === "shopify")
      throw new Error("Use `import {DevServerCore}` for the 'shopify' project");
    this.serverImpl = inferProjectServer(projectName);
  }
  host(options2) {
    return this.serverImpl.host(options2);
  }
  url(options2) {
    return this.serverImpl.url(options2);
  }
}, DevServerCore = class {
  constructor() {
    this.serverImpl = inferProjectServer("shopify");
  }
  host(prefix) {
    return this.serverImpl.host({ nonstandardHostPrefix: prefix });
  }
  url(prefix) {
    return this.serverImpl.url({ nonstandardHostPrefix: prefix });
  }
}, INFERENCE_MODE_SENTINEL = "/opt/dev/misc/dev-server-inference-mode";
function inferProjectServer(projectName) {
  return inferenceModeAndProjectIsEdition2016(projectName) ? createServer2(projectName) : createServer(projectName);
}
function inferenceModeAndProjectIsEdition2016(projectName) {
  try {
    fs4.accessSync(INFERENCE_MODE_SENTINEL);
    try {
      return fs4.accessSync(`/opt/nginx/etc/manifest/${projectName}/current/edition-2024`), !1;
    } catch {
      return !0;
    }
  } catch {
    return !1;
  }
}

// ../cli-kit/dist/public/node/context/fqdn.js
var NotProvidedStoreFQDNError = new AbortError("Couldn't obtain the Shopify FQDN because the store FQDN was not provided.");
async function partnersFqdn() {
  if (blockPartnersAccess())
    throw new BugError("Partners API is blocked by the SHOPIFY_CLI_NEVER_USE_PARTNERS_API environment variable.");
  let environment = serviceEnvironment(), productionFqdn = "partners.shopify.com";
  switch (environment) {
    case "local":
      return new DevServer("partners").host();
    default:
      return productionFqdn;
  }
}
async function adminFqdn() {
  let environment = serviceEnvironment(), productionFqdn = "admin.shopify.com";
  switch (environment) {
    case "local":
      return new DevServerCore().host("admin");
    default:
      return productionFqdn;
  }
}
async function appManagementFqdn() {
  let environment = serviceEnvironment(), productionFqdn = "app.shopify.com";
  switch (environment) {
    case "local":
      return new DevServerCore().host("app");
    default:
      return productionFqdn;
  }
}
async function appDevFqdn(storeFqdn) {
  switch (serviceEnvironment()) {
    case "local":
      return new DevServerCore().host("app");
    default:
      return storeFqdn;
  }
}
async function developerDashboardFqdn() {
  let environment = serviceEnvironment(), productionFqdn = "dev.shopify.com";
  switch (environment) {
    case "local":
      return new DevServerCore().host("dev");
    default:
      return productionFqdn;
  }
}
async function businessPlatformFqdn() {
  let environment = serviceEnvironment(), productionFqdn = "destinations.shopifysvc.com";
  switch (environment) {
    case "local":
      return new DevServer("business-platform").host();
    default:
      return productionFqdn;
  }
}
async function identityFqdn() {
  let environment = serviceEnvironment(), productionFqdn = "accounts.shopify.com";
  switch (environment) {
    case "local":
      return new DevServer("identity").host();
    default:
      return productionFqdn;
  }
}
async function normalizeStoreFqdn(store2) {
  let storeFqdn = store2.replace(/^https?:\/\//, "").replace(/\/$/, ""), addDomain = async (storeFqdn2) => {
    switch (serviceEnvironment()) {
      case "local":
        return new DevServerCore().host(storeFqdn2);
      default:
        return `${storeFqdn2}.myshopify.com`;
    }
  };
  return ((storeFqdn2) => storeFqdn2.endsWith(".myshopify.com") || storeFqdn2.endsWith("shopify.io") || storeFqdn2.endsWith(".shop.dev"))(storeFqdn) ? storeFqdn : addDomain(storeFqdn);
}

// ../cli-kit/dist/private/node/session/store.js
async function store(sessions) {
  let jsonSessions = JSON.stringify(sessions);
  setSessions(jsonSessions);
}
async function fetch() {
  let content = getSessions();
  if (!content)
    return;
  let contentJson = JSON.parse(content), parsedSessions = await SessionsSchema.safeParseAsync(contentJson);
  if (parsedSessions.success)
    return parsedSessions.data;
  await remove();
}
async function remove() {
  removeSessions(), removeCurrentSessionId();
}
async function getSessionAlias(userId2) {
  let sessions = await fetch();
  if (!sessions)
    return;
  let fqdn = await identityFqdn();
  if (!(!sessions[fqdn] || !sessions[fqdn][userId2]))
    return sessions[fqdn][userId2].identity.alias;
}
async function findSessionByAlias(alias) {
  let sessions = await fetch();
  if (!sessions)
    return;
  let fqdn = await identityFqdn(), fqdnSessions = sessions[fqdn];
  if (fqdnSessions) {
    for (let [userId2, session] of Object.entries(fqdnSessions))
      if (session.identity.alias === alias)
        return userId2;
  }
}

// ../cli-kit/dist/private/node/session/exchange.js
init_cjs_shims();

// ../cli-kit/dist/private/node/session/identity.js
init_cjs_shims();
function clientId() {
  let environment = serviceEnvironment();
  return environment === Environment.Local ? "e5380e02-312a-7408-5718-e07017e9cf52" : environment === Environment.Production ? "fbdb2649-e327-4907-8f67-908d24cfd7e3" : "e5380e02-312a-7408-5718-e07017e9cf52";
}
function applicationId(api) {
  switch (api) {
    case "admin": {
      let environment = serviceEnvironment();
      return environment === Environment.Local ? "e92482cebb9bfb9fb5a0199cc770fde3de6c8d16b798ee73e36c9d815e070e52" : environment === Environment.Production ? "7ee65a63608843c577db8b23c4d7316ea0a01bd2f7594f8a9c06ea668c1b775c" : "e92482cebb9bfb9fb5a0199cc770fde3de6c8d16b798ee73e36c9d815e070e52";
    }
    case "partners": {
      let environment = serviceEnvironment();
      return environment === Environment.Local ? "df89d73339ac3c6c5f0a98d9ca93260763e384d51d6038da129889c308973978" : environment === Environment.Production ? "271e16d403dfa18082ffb3d197bd2b5f4479c3fc32736d69296829cbb28d41a6" : "df89d73339ac3c6c5f0a98d9ca93260763e384d51d6038da129889c308973978";
    }
    case "storefront-renderer": {
      let environment = serviceEnvironment();
      return environment === Environment.Local ? "46f603de-894f-488d-9471-5b721280ff49" : environment === Environment.Production ? "ee139b3d-5861-4d45-b387-1bc3ada7811c" : "46f603de-894f-488d-9471-5b721280ff49";
    }
    case "business-platform": {
      let environment = serviceEnvironment();
      return environment === Environment.Local ? "ace6dc89-b526-456d-a942-4b8ef6acda4b" : environment === Environment.Production ? "32ff8ee5-82b8-4d93-9f8a-c6997cefb7dc" : "ace6dc89-b526-456d-a942-4b8ef6acda4b";
    }
    case "app-management":
      return serviceEnvironment() === Environment.Production ? "7ee65a63608843c577db8b23c4d7316ea0a01bd2f7594f8a9c06ea668c1b775c" : "e92482cebb9bfb9fb5a0199cc770fde3de6c8d16b798ee73e36c9d815e070e52";
    default:
      throw new BugError(`Application id for API of type: ${api}`);
  }
}

// ../cli-kit/dist/private/node/session/scopes.js
init_cjs_shims();
function allDefaultScopes(extraScopes = []) {
  let scopes = allAPIs.map((api) => defaultApiScopes(api)).flat();
  return scopes = ["openid", ...scopes, ...extraScopes].map(scopeTransform), Array.from(new Set(scopes));
}
function apiScopes(api, extraScopes = []) {
  let scopes = [...defaultApiScopes(api), ...extraScopes.map(scopeTransform)].map(scopeTransform);
  return Array.from(new Set(scopes));
}
function tokenExchangeScopes(api) {
  switch (api) {
    case "partners":
      return [scopeTransform("cli")];
    case "app-management":
      return [scopeTransform("app-management")];
    case "business-platform":
      return [scopeTransform("destinations")];
    default:
      throw new BugError(`API not supported for token exchange: ${api}`);
  }
}
function defaultApiScopes(api) {
  switch (api) {
    case "admin":
      return ["graphql", "themes", "collaborator"];
    case "storefront-renderer":
      return ["devtools"];
    case "partners":
      return ["cli"];
    case "business-platform":
      return ["destinations", "store-management", "on-demand-user-access"];
    case "app-management":
      return ["app-management"];
    default:
      throw new BugError(`Unknown API: ${api}`);
  }
}
function scopeTransform(scope) {
  switch (scope) {
    case "graphql":
      return "https://api.shopify.com/auth/shop.admin.graphql";
    case "themes":
      return "https://api.shopify.com/auth/shop.admin.themes";
    case "collaborator":
      return "https://api.shopify.com/auth/partners.collaborator-relationships.readonly";
    case "cli":
      return "https://api.shopify.com/auth/partners.app.cli.access";
    case "devtools":
      return "https://api.shopify.com/auth/shop.storefront-renderer.devtools";
    case "destinations":
      return "https://api.shopify.com/auth/destinations.readonly";
    case "store-management":
      return "https://api.shopify.com/auth/organization.store-management";
    case "on-demand-user-access":
      return "https://api.shopify.com/auth/organization.on-demand-user-access";
    case "app-management":
      return "https://api.shopify.com/auth/organization.apps.manage";
    default:
      return scope;
  }
}

// ../cli-kit/dist/public/node/result.js
init_cjs_shims();
var ok = (value) => new Ok(value), err = (err2) => new Err(err2), Ok = class {
  constructor(value) {
    this.value = value;
  }
  /**
   * Check if a `Result` is an `Err` inferring its type. `!isErr()` should be used before accessing the `value`
   *
   * @returns `false` as the `Resul` is `OK`
   */
  isErr() {
    return !1;
  }
  /**
   * Runs the `handler` method an return the same an unaltered copy of the `Result`. It could be used to log an
   * output when the result is `Ok` without breaking the flow
   *
   * @param handler - method to be run when the result is `Ok`
   * @returns a copy of the same `Result`
   */
  doOnOk(handler) {
    return handler(this.value), ok(this.value);
  }
  /**
   * A safe mode to throw the `error` of the `Result`
   */
  valueOrBug() {
    return this.value;
  }
  /**
   * Throws an abort error if the result doesn't represent a value.
   */
  valueOrAbort() {
    return this.value;
  }
  /**
   * Maps the value to another one with a different type. It leaves the `Error` type unaltered
   *
   * @param mapper - The mapper method to apply an `OK` value
   * @returns a new result with the new mapped value
   */
  map(mapper) {
    return ok(mapper(this.value));
  }
  /**
   * Maps the error type to another one. It leaves the `Ok` type and value unaltered
   *
   * @param _mapper - This mapper method is not used for an `Ok` value
   * @returns a new result with the new mapped error type and an value
   */
  mapError(_mapper) {
    return ok(this.value);
  }
}, Err = class {
  // eslint-disable-next-line node/handle-callback-err
  constructor(error) {
    this.error = error;
  }
  /**
   * Check if a `Result` is an `Err` inferring its type. `!isErr()` should be used before accessing the `value`
   *
   * @returns `false` as the `Resul` is `OK`
   */
  isErr() {
    return !0;
  }
  /**
   * Return an unaltered copy of the `Error` without doing anything.
   *
   * @param _handler - This handler method is not used for an `Error`
   * @returns a copy of the same `Error`
   */
  doOnOk(_handler) {
    return err(this.error);
  }
  /**
   * A safe mode to throw the `error` of the `Result`
   */
  valueOrBug() {
    throw this.error;
  }
  /**
   * Throws an abort error if the result doesn't represent a value.
   */
  valueOrAbort() {
    if (this.error instanceof FatalError)
      throw this.error;
    if (this.error instanceof import_ts_error.ExtendableError || this.error instanceof Error) {
      let error = new AbortError(this.error.message);
      throw error.stack = this.error.stack, error;
    } else
      throw new AbortError(`${this.error}`);
  }
  /**
   * Maps the value type to another one. It leaves the `Error` unaltered
   *
   * @param _mapper - This mapper method is not used for an `Error` value
   * @returns a new result with the new value type and an unaltered error
   */
  map(_mapper) {
    return err(this.error);
  }
  /**
   * Maps the error to another one with a different type. It leaves the value type unaltered
   *
   * @param mapper - The mapper method to apply an `Error` value
   * @returns a new result with the new mapped error
   */
  mapError(mapper) {
    return err(mapper(this.error));
  }
};

// ../cli-kit/dist/private/node/session.js
init_cjs_shims();

// ../cli-kit/dist/private/node/session/validate.js
init_cjs_shims();
function validateScopes(requestedScopes, identity) {
  let currentScopes = identity.scopes;
  return firstPartyDev() !== currentScopes.includes("employee") ? !1 : requestedScopes.every((scope) => currentScopes.includes(scope));
}
async function validateSession(scopes, applications, session) {
  if (!session || !validateScopes(scopes, session.identity))
    return "needs_full_auth";
  let tokensAreExpired = isTokenExpired(session.identity);
  if (applications.partnersApi) {
    let appId = applicationId("partners"), token = session.applications[appId];
    tokensAreExpired = tokensAreExpired || isTokenExpired(token);
  }
  if (applications.appManagementApi) {
    let appId = applicationId("app-management"), token = session.applications[appId];
    tokensAreExpired = tokensAreExpired || isTokenExpired(token);
  }
  if (applications.storefrontRendererApi) {
    let appId = applicationId("storefront-renderer"), token = session.applications[appId];
    tokensAreExpired = tokensAreExpired || isTokenExpired(token);
  }
  if (applications.adminApi) {
    let appId = applicationId("admin"), realAppId = `${applications.adminApi.storeFqdn}-${appId}`, token = session.applications[realAppId];
    tokensAreExpired = tokensAreExpired || isTokenExpired(token);
  }
  return outputDebug(`- Token validation -> It's expired: ${tokensAreExpired}`), validateCachedIdentityTokenStructure(session.identity) ? tokensAreExpired ? "needs_refresh" : "ok" : "needs_full_auth";
}
function isTokenExpired(token) {
  return token ? token.expiresAt < expireThreshold() : !0;
}
function expireThreshold() {
  return new Date(Date.now() + sessionConstants.expirationTimeMarginInMinutes * 60 * 1e3);
}

// ../cli-kit/dist/private/node/session/device-authorization.js
init_cjs_shims();
async function requestDeviceAuthorization(scopes) {
  let fqdn = await identityFqdn(), queryParams = { client_id: clientId(), scope: scopes.join(" ") }, url3 = `https://${fqdn}/oauth/device_authorization`, response = await shopifyFetch(url3, {
    method: "POST",
    headers: { "Content-type": "application/x-www-form-urlencoded" },
    body: convertRequestToParams(queryParams)
  }), jsonResult;
  try {
    jsonResult = await response.json();
  } catch {
    throw new BugError("Received unexpected response from the authorization service. If this issue persists, please contact support at https://help.shopify.com");
  }
  if (outputDebug(outputContent`Received device authorization code: ${outputToken.json(jsonResult)}`), !jsonResult.device_code || !jsonResult.verification_uri_complete)
    throw new BugError("Failed to start authorization process");
  if (outputInfo(`
To run this command, log in to Shopify.`), isCI())
    throw new AbortError("Authorization is required to continue, but the current environment does not support interactive prompts.", "To resolve this, specify credentials in your environment, or run the command in an interactive environment such as your local terminal.");
  outputInfo(outputContent`User verification code: ${jsonResult.user_code}`);
  let linkToken = outputToken.link(jsonResult.verification_uri_complete), cloudMessage = () => {
    outputInfo(outputContent`👉 Open this link to start the auth process: ${linkToken}`);
  };
  return isCloudEnvironment() || !isTTY() ? cloudMessage() : (outputInfo("\u{1F449} Press any key to open the login page on your browser"), await keypress(), await openURL(jsonResult.verification_uri_complete) ? outputInfo(outputContent`Opened link to start the auth process: ${linkToken}`) : cloudMessage()), {
    deviceCode: jsonResult.device_code,
    userCode: jsonResult.user_code,
    verificationUri: jsonResult.verification_uri,
    expiresIn: jsonResult.expires_in,
    verificationUriComplete: jsonResult.verification_uri_complete,
    interval: jsonResult.interval
  };
}
async function pollForDeviceAuthorization(code, interval = 5) {
  let currentIntervalInSeconds = interval;
  return new Promise((resolve, reject) => {
    let onPoll = async () => {
      let result = await exchangeDeviceCodeForAccessToken(code);
      if (!result.isErr()) {
        resolve(result.value);
        return;
      }
      let error = result.error ?? "unknown_failure";
      switch (outputDebug(outputContent`Polling for device authorization... status: ${error}`), error) {
        case "authorization_pending": {
          startPolling();
          return;
        }
        case "slow_down":
          currentIntervalInSeconds += 5;
          {
            startPolling();
            return;
          }
        case "access_denied":
        case "expired_token":
        case "unknown_failure":
          reject(result);
      }
    }, startPolling = () => {
      setTimeout(onPoll, currentIntervalInSeconds * 1e3);
    };
    startPolling();
  });
}
function convertRequestToParams(queryParams) {
  return Object.entries(queryParams).map(([key, value]) => value && `${key}=${value}`).filter((hasValue) => !!hasValue).join("&");
}

// ../cli-kit/dist/private/node/api/rest.js
init_cjs_shims();
function isThemeAccessSession(session) {
  return session.token.startsWith("shptka_");
}

// ../cli-kit/dist/private/node/api/graphql/business-platform-destinations/user-email.js
init_cjs_shims();
var UserEmailQueryString = `
  query UserEmail {
    currentUserAccount {
      email
    }
  }
`;

// ../cli-kit/dist/public/common/object.js
init_cjs_shims();

// ../cli-kit/dist/private/common/array.js
init_cjs_shims();
function unionArrayStrategy(destinationArray, sourceArray) {
  return Array.from(/* @__PURE__ */ new Set([...destinationArray, ...sourceArray]));
}

// ../cli-kit/dist/public/common/object.js
var import_deepmerge = __toESM(require_cjs(), 1), import_pickBy = __toESM(require_pickBy(), 1), import_mapValues = __toESM(require_mapValues(), 1), import_isEqual = __toESM(require_isEqual(), 1), import_differenceWith = __toESM(require_differenceWith(), 1), import_fromPairs = __toESM(require_fromPairs(), 1), import_toPairs = __toESM(require_toPairs(), 1), import_get = __toESM(require_get(), 1), import_set = __toESM(require_set(), 1), import_unset = __toESM(require_unset(), 1), import_isEmpty = __toESM(require_isEmpty(), 1);
function deepMergeObjects(lhs, rhs, arrayMergeStrategy = unionArrayStrategy) {
  return (0, import_deepmerge.default)(lhs, rhs, { arrayMerge: arrayMergeStrategy });
}
function pickBy(object, predicate) {
  return (0, import_pickBy.default)(object, predicate);
}
function deepCompare(one, two) {
  return (0, import_isEqual.default)(one, two);
}
function deepDifference(one, two) {
  let changes = (0, import_differenceWith.default)((0, import_toPairs.default)(one), (0, import_toPairs.default)(two), deepCompare), changes2 = (0, import_differenceWith.default)((0, import_toPairs.default)(two), (0, import_toPairs.default)(one), deepCompare);
  return [(0, import_fromPairs.default)(changes), (0, import_fromPairs.default)(changes2)];
}
function getPathValue(object, path) {
  return (0, import_get.default)(object, path) === void 0 ? void 0 : (0, import_get.default)(object, path);
}
function setPathValue(object, path, value) {
  return (0, import_set.default)(object, path, value);
}
function isEmpty(object) {
  return (0, import_isEmpty.default)(object);
}
function compact(object) {
  return Object.fromEntries(Object.entries(object).filter(([_, value]) => value != null));
}

// ../cli-kit/dist/public/node/api/business-platform.js
init_cjs_shims();

// ../cli-kit/dist/public/node/api/graphql.js
init_cjs_shims();

// ../cli-kit/dist/private/node/api/graphql.js
init_cjs_shims();
function debugLogRequestInfo(api, query, url3, variables, headers = {}) {
  outputDebug(outputContent`Sending ${outputToken.json(api)} GraphQL request:
  ${outputToken.raw(query.toString().trim())}
${variables ? `
With variables:
${sanitizeVariables(variables)}
` : ""}
With request headers:
${sanitizedHeadersOutput(headers)}\n
to ${sanitizeURL(url3)}`);
}
function sanitizeVariables(variables) {
  let result = { ...variables }, sanitizedResult = sanitizeDeepVariables(result, ["apiKey", "serialized_script"]);
  return JSON.stringify(sanitizedResult, null, 2);
}
function sanitizeDeepVariables(value, sensitiveKeys) {
  if (typeof value == "string")
    try {
      let parsed = JSON.parse(value);
      if (typeof parsed == "object" && parsed !== null) {
        let sanitized = sanitizeDeepVariables(parsed, sensitiveKeys);
        return JSON.stringify(sanitized, null);
      }
    } catch {
      return value;
    }
  if (typeof value != "object" || value === null)
    return value;
  if (Array.isArray(value))
    return value.map((item) => sanitizeDeepVariables(item, sensitiveKeys));
  let result = {};
  for (let [key, val] of Object.entries(value)) {
    if (sensitiveKeys.includes(key) && typeof val == "string") {
      result[key] = "*****";
      continue;
    }
    result[key] = sanitizeDeepVariables(val, sensitiveKeys);
  }
  return result;
}
function errorHandler(api) {
  return (error, requestId) => {
    if (error instanceof ClientError) {
      let { status } = error.response, errorMessage = stringifyMessage(outputContent`
The ${outputToken.raw(api)} GraphQL API responded unsuccessfully with${status === 200 ? "" : ` the HTTP status ${status} and`} errors:

${outputToken.json(error.response.errors)}
      `);
      requestId && (errorMessage += `
Request ID: ${requestId}
`);
      let mappedError;
      return status < 500 ? mappedError = new GraphQLClientError(errorMessage, status, error.response.errors) : mappedError = new AbortError(errorMessage), mappedError.stack = error.stack, mappedError;
    } else
      return error;
  };
}

// ../cli-kit/dist/private/node/request-ids.js
init_cjs_shims();
var RequestIDCollection = class _RequestIDCollection {
  constructor() {
    this.requestIds = [];
  }
  static getInstance() {
    return _RequestIDCollection.instance || (_RequestIDCollection.instance = new _RequestIDCollection()), _RequestIDCollection.instance;
  }
  /**
   * Add a request ID to the collection
   * We only report the first MAX_REQUEST_IDS request IDs.
   */
  addRequestId(requestId) {
    requestId && this.requestIds.length < 100 && this.requestIds.push(requestId);
  }
  /**
   * Get all collected request IDs
   */
  getRequestIds() {
    return this.requestIds;
  }
  /**
   * Clear all stored request IDs
   */
  clear() {
    this.requestIds = [];
  }
}, requestIdsCollection = RequestIDCollection.getInstance();

// ../cli-kit/dist/public/node/api/graphql.js
async function createGraphQLClient({ url: url3, addedHeaders, token }) {
  let headers = {
    ...addedHeaders,
    ...buildHeaders(token)
  }, clientOptions = { agent: await httpsAgent(), headers };
  return {
    client: new GraphQLClient(url3, clientOptions),
    headers
  };
}
async function performGraphQLRequest(options2) {
  let { token, addedHeaders, queryAsString, variables, api, url: url3, responseOptions, unauthorizedHandler, cacheOptions } = options2, behaviour = requestMode(options2.preferredBehaviour ?? "default"), { headers, client } = await createGraphQLClient({ url: url3, addedHeaders, token });
  debugLogRequestInfo(api, queryAsString, url3, variables, headers);
  let rawGraphQLRequest = async () => {
    let fullResponse;
    try {
      return client.requestConfig.signal = abortSignalFromRequestBehaviour(behaviour), fullResponse = await client.rawRequest(queryAsString, variables), await logLastRequestIdFromResponse(fullResponse), fullResponse;
    } catch (error) {
      throw error instanceof ClientError && await logLastRequestIdFromResponse(error.response), error;
    }
  }, tokenRefreshHandler = unauthorizedHandler?.handler, tokenRefreshUnauthorizedHandlerFunction = tokenRefreshHandler ? async () => {
    let refreshTokenResult = await tokenRefreshHandler();
    if (refreshTokenResult.token) {
      let { client: newClient, headers: newHeaders } = await createGraphQLClient({
        url: url3,
        addedHeaders,
        token: refreshTokenResult.token
      });
      return client = newClient, headers = newHeaders, !0;
    } else
      return !1;
  } : void 0, request = () => retryAwareRequest({ request: rawGraphQLRequest, url: url3, ...behaviour }, responseOptions?.handleErrors === !1 ? void 0 : errorHandler(api)), executeWithTimer = () => runWithTimer("cmd_all_timing_network_ms")(async () => {
    let response;
    try {
      response = await request();
    } catch (error) {
      if (error instanceof ClientError && error.response.status === 401 && tokenRefreshUnauthorizedHandlerFunction)
        if (await tokenRefreshUnauthorizedHandlerFunction())
          response = await request();
        else
          throw error;
      else
        throw error;
    }
    return responseOptions?.onResponse && responseOptions.onResponse(response), response.data;
  });
  if (cacheOptions === void 0)
    return executeWithTimer();
  let { cacheTTL, cacheExtraKey, cacheStore } = cacheOptions, queryHash = nonRandomUUID(queryAsString), variablesHash = nonRandomUUID(JSON.stringify(variables ?? {})), cacheKey = `q-${queryHash}-${variablesHash}-${CLI_KIT_VERSION}-${cacheExtraKey ?? ""}`, result = await cacheRetrieveOrRepopulate(cacheKey, async () => {
    let result2 = await executeWithTimer();
    return JSON.stringify(result2);
  }, timeIntervalToMilliseconds(cacheTTL), cacheStore);
  return JSON.parse(result);
}
async function logLastRequestIdFromResponse(response) {
  try {
    let requestId = response.headers.get("x-request-id");
    requestIdsCollection.addRequestId(requestId), await addPublicMetadata(() => ({
      cmd_all_last_graphql_request_id: requestId ?? void 0
    }));
  } catch {
  }
}
async function graphqlRequest(options2) {
  return performGraphQLRequest({
    ...options2,
    queryAsString: options2.query
  });
}
async function graphqlRequestDoc(options2) {
  return performGraphQLRequest({
    ...options2,
    queryAsString: resolveRequestDocument(options2.query).query
  });
}

// ../cli-kit/dist/public/node/api/partners.js
init_cjs_shims();

// ../cli-kit/dist/public/node/api/utilities.js
init_cjs_shims();
var addCursorAndFiltersToAppLogsUrl = (baseUrl, cursor, filters) => {
  let url3 = new URL(baseUrl);
  return cursor && url3.searchParams.append("cursor", cursor), filters?.status && url3.searchParams.append("status", filters.status), filters?.source && url3.searchParams.append("source", filters.source), url3.toString();
};

// ../cli-kit/dist/private/node/context/deprecations-store.js
init_cjs_shims();
var globalWithDeprecationsStore = {
  ...globalThis,
  deprecationsStore: {
    nextDeprecationDate: void 0
  }
};
function getNextDeprecationDate() {
  return globalWithDeprecationsStore.deprecationsStore.nextDeprecationDate;
}
function setNextDeprecationDate(dates) {
  if (dates.length < 1)
    return;
  let earliestFutureDateTime = earliestDateTimeAfter(Date.now(), dates);
  if (!earliestFutureDateTime)
    return;
  let nextDeprecationDate = getNextDeprecationDate();
  (!nextDeprecationDate || earliestFutureDateTime < nextDeprecationDate.getTime()) && (globalWithDeprecationsStore.deprecationsStore.nextDeprecationDate = new Date(earliestFutureDateTime));
}
function earliestDateTimeAfter(afterTime, dates) {
  return dates.map((date) => date.getTime()).sort().find((time) => time > afterTime);
}

// ../cli-kit/dist/public/node/api/partners.js
var import_bottleneck = __toESM(require_lib(), 1), limiter = new import_bottleneck.default({
  minTime: 150,
  maxConcurrent: 10
});
async function setupRequest(token) {
  let api = "Partners", url3 = `https://${await partnersFqdn()}/api/cli/graphql`;
  return {
    token,
    api,
    url: url3,
    responseOptions: { onResponse: handleDeprecations }
  };
}
async function partnersRequest(query, token, variables, cacheOptions, preferredBehaviour, unauthorizedHandler) {
  let opts = await setupRequest(token);
  return limiter.schedule(() => graphqlRequest({
    ...opts,
    query,
    variables,
    cacheOptions,
    preferredBehaviour,
    unauthorizedHandler
  }));
}
var generateFetchAppLogUrl = async (cursor, filters) => {
  let url3 = `https://${await partnersFqdn()}/app_logs/poll`;
  return addCursorAndFiltersToAppLogsUrl(url3, cursor, filters);
};
async function partnersRequestDoc(query, token, variables, preferredBehaviour, unauthorizedHandler) {
  try {
    let opts = await setupRequest(token);
    return limiter.schedule(() => graphqlRequestDoc({
      ...opts,
      query,
      variables,
      preferredBehaviour,
      unauthorizedHandler
    }));
  } catch (error) {
    if (error.errors?.[0]?.extensions?.type === "unsupported_client_version") {
      let packageManager = await getPackageManager(cwd());
      throw new AbortError(["Upgrade your CLI version to run this command."], null, [
        ["Run", { command: formatPackageManagerCommand(packageManager, "shopify upgrade") }]
      ]);
    }
    throw error;
  }
}
function handleDeprecations(response) {
  if (!response.extensions)
    return;
  let deprecationDates = [];
  for (let deprecation of response.extensions.deprecations)
    deprecation.supportedUntilDate && deprecationDates.push(new Date(deprecation.supportedUntilDate));
  setNextDeprecationDate(deprecationDates);
}

// ../cli-kit/dist/public/node/api/business-platform.js
async function setupRequest2(token) {
  let api = "BusinessPlatform", url3 = `https://${await businessPlatformFqdn()}/destinations/api/2020-07/graphql`;
  return {
    token,
    api,
    url: url3,
    responseOptions: { onResponse: handleDeprecations }
  };
}
async function businessPlatformRequest(query, token, variables, cacheOptions) {
  return graphqlRequest({
    ...await setupRequest2(token),
    query,
    variables,
    cacheOptions
  });
}
async function businessPlatformRequestDoc(options2) {
  return graphqlRequestDoc({
    ...await setupRequest2(options2.token),
    query: options2.query,
    variables: options2.variables,
    cacheOptions: options2.cacheOptions,
    unauthorizedHandler: options2.unauthorizedHandler
  });
}
async function setupOrganizationsRequest(token, organizationId) {
  let api = "BusinessPlatform", url3 = `https://${await businessPlatformFqdn()}/organizations/api/unstable/organization/${organizationId}/graphql`;
  return {
    token,
    api,
    url: url3,
    responseOptions: { onResponse: handleDeprecations }
  };
}
async function businessPlatformOrganizationsRequest(options2) {
  return graphqlRequest({
    query: options2.query,
    ...await setupOrganizationsRequest(options2.token, options2.organizationId),
    variables: options2.variables,
    unauthorizedHandler: options2.unauthorizedHandler
  });
}
async function businessPlatformOrganizationsRequestDoc(options2) {
  return graphqlRequestDoc({
    query: options2.query,
    ...await setupOrganizationsRequest(options2.token, options2.organizationId),
    variables: options2.variables,
    unauthorizedHandler: options2.unauthorizedHandler
  });
}

// ../cli-kit/dist/private/node/session.js
async function fetchEmail(businessPlatformToken) {
  if (businessPlatformToken)
    try {
      return (await businessPlatformRequest(UserEmailQueryString, businessPlatformToken)).currentUserAccount?.email;
    } catch (error) {
      outputDebug(outputContent`Failed to fetch user email: ${error.message ?? String(error)}`);
      return;
    }
}
var userId, authMethod = "none";
async function getLastSeenUserIdAfterAuth() {
  if (userId)
    return userId;
  let currentSessionId = getCurrentSessionId();
  if (currentSessionId)
    return currentSessionId;
  let customToken = getPartnersToken() ?? themeToken();
  return customToken ? nonRandomUUID(customToken) : "unknown";
}
function setLastSeenUserIdAfterAuth(id) {
  userId = id;
}
async function getLastSeenAuthMethod() {
  if (authMethod !== "none")
    return authMethod;
  if (getCurrentSessionId())
    return "device_auth";
  if (getPartnersToken())
    return "partners_token";
  let themePassword = themeToken();
  return themePassword ? isThemeAccessSession({ token: themePassword, storeFqdn: "" }) ? "theme_access_token" : "custom_app_token" : "none";
}
function setLastSeenAuthMethod(method) {
  authMethod = method;
}
async function ensureAuthenticated(applications, _env, { forceRefresh = !1, noPrompt = !1, forceNewSession = !1 } = {}) {
  let fqdn = await identityFqdn(), previousStoreFqdn = applications.adminApi?.storeFqdn;
  if (previousStoreFqdn) {
    let normalizedStoreName = await normalizeStoreFqdn(previousStoreFqdn);
    previousStoreFqdn === applications.adminApi?.storeFqdn && (applications.adminApi.storeFqdn = normalizedStoreName);
  }
  let sessions = await fetch() ?? {}, currentSessionId = getCurrentSessionId();
  if (!currentSessionId) {
    let userIds = Object.keys(sessions[fqdn] ?? {});
    userIds.length > 0 && (currentSessionId = userIds[0]);
  }
  let currentSession = currentSessionId && !forceNewSession ? sessions[fqdn]?.[currentSessionId] : void 0, scopes = getFlattenScopes(applications);
  outputDebug(outputContent`Validating existing session against the scopes:
${outputToken.json(scopes)}
For applications:
${outputToken.json(applications)}
`);
  let validationResult = await validateSession(scopes, applications, currentSession), newSession = {};
  if (validationResult === "needs_full_auth")
    await throwOnNoPrompt(noPrompt), outputDebug(outputContent`Initiating the full authentication flow...`), newSession = await executeCompleteFlow(applications);
  else if (validationResult === "needs_refresh" || forceRefresh) {
    outputDebug(outputContent`The current session is valid but needs refresh. Refreshing...`);
    try {
      newSession = await refreshTokens(currentSession, applications);
    } catch (error) {
      if (error instanceof InvalidGrantError)
        await throwOnNoPrompt(noPrompt), newSession = await executeCompleteFlow(applications);
      else throw error instanceof InvalidRequestError ? (await remove(), new AbortError(`
Error validating auth session`, "We've cleared the current session, please try again")) : error;
    }
  }
  let completeSession = { ...currentSession, ...newSession }, newSessionId = completeSession.identity.userId, updatedSessions = {
    ...sessions,
    [fqdn]: { ...sessions[fqdn], [newSessionId]: completeSession }
  };
  isEmpty(newSession) || (await store(updatedSessions), setCurrentSessionId(newSessionId));
  let tokens = await tokensFor(applications, completeSession), envToken = getPartnersToken();
  return envToken && applications.partnersApi && (tokens.partners = (await exchangeCustomPartnerToken(envToken)).accessToken), setLastSeenAuthMethod(envToken ? "partners_token" : "device_auth"), setLastSeenUserIdAfterAuth(tokens.userId), tokens;
}
async function throwOnNoPrompt(noPrompt) {
  if (noPrompt)
    throw await logout(), new AbortError(`The currently available CLI credentials are invalid.

The CLI is currently unable to prompt for reauthentication.`, "Restart the CLI process you were running. If in an interactive terminal, you will be prompted to reauthenticate. If in a non-interactive terminal, ensure the correct credentials are available in the program environment.");
}
async function executeCompleteFlow(applications) {
  let scopes = getFlattenScopes(applications), exchangeScopes = getExchangeScopes(applications), store2 = applications.adminApi?.storeFqdn;
  firstPartyDev() && (outputDebug(outputContent`Authenticating as Shopify Employee...`), scopes.push("employee"));
  let identityToken, identityTokenInformation = getIdentityTokenInformation();
  if (identityTokenInformation)
    identityToken = buildIdentityTokenFromEnv(scopes, identityTokenInformation);
  else {
    outputDebug(outputContent`Requesting device authorization code...`);
    let deviceAuth = await requestDeviceAuthorization(scopes);
    outputDebug(outputContent`Starting polling for the identity token...`), identityToken = await pollForDeviceAuthorization(deviceAuth.deviceCode, deviceAuth.interval);
  }
  outputDebug(outputContent`CLI token received. Exchanging it for application tokens...`);
  let result = await exchangeAccessForApplicationTokens(identityToken, exchangeScopes, store2), businessPlatformToken = result[applicationId("business-platform")]?.accessToken, alias = await fetchEmail(businessPlatformToken) ?? identityToken.userId, session = {
    identity: {
      ...identityToken,
      alias
    },
    applications: result
  };
  return outputCompleted("Logged in."), session;
}
async function refreshTokens(session, applications) {
  let identityToken = await refreshAccessToken(session.identity), exchangeScopes = getExchangeScopes(applications), applicationTokens = await exchangeAccessForApplicationTokens(identityToken, exchangeScopes, applications.adminApi?.storeFqdn);
  return {
    identity: identityToken,
    applications: applicationTokens
  };
}
async function tokensFor(applications, session) {
  let tokens = {
    userId: session.identity.userId
  };
  if (applications.adminApi) {
    let appId = applicationId("admin"), realAppId = `${applications.adminApi.storeFqdn}-${appId}`, token = session.applications[realAppId]?.accessToken;
    token && (tokens.admin = { token, storeFqdn: applications.adminApi.storeFqdn });
  }
  if (applications.partnersApi) {
    let appId = applicationId("partners");
    tokens.partners = session.applications[appId]?.accessToken;
  }
  if (applications.storefrontRendererApi) {
    let appId = applicationId("storefront-renderer");
    tokens.storefront = session.applications[appId]?.accessToken;
  }
  if (applications.businessPlatformApi) {
    let appId = applicationId("business-platform");
    tokens.businessPlatform = session.applications[appId]?.accessToken;
  }
  if (applications.appManagementApi) {
    let appId = applicationId("app-management");
    tokens.appManagement = session.applications[appId]?.accessToken;
  }
  return tokens;
}
function getFlattenScopes(apps) {
  let admin = apps.adminApi?.scopes ?? [], partner = apps.partnersApi?.scopes ?? [], storefront = apps.storefrontRendererApi?.scopes ?? [], businessPlatform = apps.businessPlatformApi?.scopes ?? [], appManagement = apps.appManagementApi?.scopes ?? [], requestedScopes = [...admin, ...partner, ...storefront, ...businessPlatform, ...appManagement];
  return allDefaultScopes(requestedScopes);
}
function getExchangeScopes(apps) {
  let adminScope = apps.adminApi?.scopes ?? [], partnerScope = apps.partnersApi?.scopes ?? [], storefrontScopes = apps.storefrontRendererApi?.scopes ?? [], businessPlatformScopes = apps.businessPlatformApi?.scopes ?? [], appManagementScopes = apps.appManagementApi?.scopes ?? [];
  return {
    admin: apiScopes("admin", adminScope),
    partners: apiScopes("partners", partnerScope),
    storefront: apiScopes("storefront-renderer", storefrontScopes),
    businessPlatform: apiScopes("business-platform", businessPlatformScopes),
    appManagement: apiScopes("app-management", appManagementScopes)
  };
}
function buildIdentityTokenFromEnv(scopes, identityTokenInformation) {
  return {
    ...identityTokenInformation,
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3),
    scopes,
    alias: identityTokenInformation.userId
  };
}

// ../../node_modules/.pnpm/jose@5.9.6/node_modules/jose/dist/node/esm/index.js
init_cjs_shims();

// ../../node_modules/.pnpm/jose@5.9.6/node_modules/jose/dist/node/esm/runtime/base64url.js
init_cjs_shims();
import { Buffer } from "node:buffer";

// ../../node_modules/.pnpm/jose@5.9.6/node_modules/jose/dist/node/esm/lib/buffer_utils.js
init_cjs_shims();
var encoder = new TextEncoder(), decoder = new TextDecoder(), MAX_INT32 = 2 ** 32;

// ../../node_modules/.pnpm/jose@5.9.6/node_modules/jose/dist/node/esm/runtime/base64url.js
function normalize(input) {
  let encoded = input;
  return encoded instanceof Uint8Array && (encoded = decoder.decode(encoded)), encoded;
}
var decode = (input) => new Uint8Array(Buffer.from(normalize(input), "base64url"));

// ../../node_modules/.pnpm/jose@5.9.6/node_modules/jose/dist/node/esm/util/errors.js
init_cjs_shims();
var JOSEError = class extends Error {
  static code = "ERR_JOSE_GENERIC";
  code = "ERR_JOSE_GENERIC";
  constructor(message, options2) {
    super(message, options2), this.name = this.constructor.name, Error.captureStackTrace?.(this, this.constructor);
  }
};
var JWTInvalid = class extends JOSEError {
  static code = "ERR_JWT_INVALID";
  code = "ERR_JWT_INVALID";
};

// ../../node_modules/.pnpm/jose@5.9.6/node_modules/jose/dist/node/esm/lib/is_object.js
init_cjs_shims();
function isObjectLike(value) {
  return typeof value == "object" && value !== null;
}
function isObject(input) {
  if (!isObjectLike(input) || Object.prototype.toString.call(input) !== "[object Object]")
    return !1;
  if (Object.getPrototypeOf(input) === null)
    return !0;
  let proto = input;
  for (; Object.getPrototypeOf(proto) !== null; )
    proto = Object.getPrototypeOf(proto);
  return Object.getPrototypeOf(input) === proto;
}

// ../../node_modules/.pnpm/jose@5.9.6/node_modules/jose/dist/node/esm/util/base64url.js
init_cjs_shims();
var decode2 = decode;

// ../../node_modules/.pnpm/jose@5.9.6/node_modules/jose/dist/node/esm/util/decode_jwt.js
init_cjs_shims();
function decodeJwt(jwt) {
  if (typeof jwt != "string")
    throw new JWTInvalid("JWTs must use Compact JWS serialization, JWT must be a string");
  let { 1: payload, length } = jwt.split(".");
  if (length === 5)
    throw new JWTInvalid("Only JWTs using Compact JWS serialization can be decoded");
  if (length !== 3)
    throw new JWTInvalid("Invalid JWT");
  if (!payload)
    throw new JWTInvalid("JWTs must contain a payload");
  let decoded;
  try {
    decoded = decode2(payload);
  } catch {
    throw new JWTInvalid("Failed to base64url decode the payload");
  }
  let result;
  try {
    result = JSON.parse(decoder.decode(decoded));
  } catch {
    throw new JWTInvalid("Failed to parse the decoded payload as JSON");
  }
  if (!isObject(result))
    throw new JWTInvalid("Invalid JWT Claims Set");
  return result;
}

// ../cli-kit/dist/private/node/session/exchange.js
var InvalidGrantError = class extends import_ts_error.ExtendableError {
}, InvalidRequestError = class extends import_ts_error.ExtendableError {
}, InvalidTargetError = class extends AbortError {
};
async function exchangeAccessForApplicationTokens(identityToken, scopes, store2) {
  let token = identityToken.accessToken, [partners, storefront, businessPlatform, admin, appManagement] = await Promise.all([
    requestAppToken("partners", token, scopes.partners),
    requestAppToken("storefront-renderer", token, scopes.storefront),
    requestAppToken("business-platform", token, scopes.businessPlatform),
    store2 ? requestAppToken("admin", token, scopes.admin, store2) : {},
    requestAppToken("app-management", token, scopes.appManagement)
  ]);
  return {
    ...partners,
    ...storefront,
    ...businessPlatform,
    ...admin,
    ...appManagement
  };
}
async function refreshAccessToken(currentToken) {
  let clientId2 = clientId(), params = {
    grant_type: "refresh_token",
    access_token: currentToken.accessToken,
    refresh_token: currentToken.refreshToken,
    client_id: clientId2
  }, value = (await tokenRequest(params)).mapError(tokenRequestErrorHandler).valueOrBug();
  return buildIdentityToken(value, currentToken.userId);
}
async function exchangeCliTokenForAccessToken(apiName, token, scopes) {
  let appId = applicationId(apiName);
  try {
    let accessToken = (await requestAppToken(apiName, token, scopes))[appId].accessToken, userId2 = nonRandomUUID(token);
    return setLastSeenUserIdAfterAuth(userId2), setLastSeenAuthMethod("partners_token"), { accessToken, userId: userId2 };
  } catch {
    let prettyName = apiName.replace(/-/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
    throw new AbortError(`The custom token provided can't be used for the ${prettyName} API.`, "Ensure the token is correct and not expired.");
  }
}
async function exchangeCustomPartnerToken(token) {
  return exchangeCliTokenForAccessToken("partners", token, tokenExchangeScopes("partners"));
}
async function exchangeCliTokenForAppManagementAccessToken(token) {
  return exchangeCliTokenForAccessToken("app-management", token, tokenExchangeScopes("app-management"));
}
async function exchangeCliTokenForBusinessPlatformAccessToken(token) {
  return exchangeCliTokenForAccessToken("business-platform", token, tokenExchangeScopes("business-platform"));
}
async function exchangeDeviceCodeForAccessToken(deviceCode) {
  let clientId2 = await clientId(), tokenResult = await tokenRequest({
    grant_type: "urn:ietf:params:oauth:grant-type:device_code",
    device_code: deviceCode,
    client_id: clientId2
  });
  if (tokenResult.isErr())
    return err(tokenResult.error.error);
  let identityToken = buildIdentityToken(tokenResult.value);
  return ok(identityToken);
}
async function requestAppToken(api, token, scopes = [], store2) {
  let appId = applicationId(api), params = {
    grant_type: "urn:ietf:params:oauth:grant-type:token-exchange",
    requested_token_type: "urn:ietf:params:oauth:token-type:access_token",
    subject_token_type: "urn:ietf:params:oauth:token-type:access_token",
    client_id: await clientId(),
    audience: appId,
    scope: scopes.join(" "),
    subject_token: token,
    ...api === "admin" && { destination: `https://${store2}/admin`, store: store2 }
  }, identifier = appId;
  api === "admin" && store2 && (identifier = `${store2}-${appId}`);
  let value = (await tokenRequest(params)).mapError(tokenRequestErrorHandler).valueOrBug(), appToken = buildApplicationToken(value);
  return { [identifier]: appToken };
}
function tokenRequestErrorHandler({ error, store: store2 }) {
  let invalidTargetErrorMessage = `You are not authorized to use the CLI to develop in the provided store${store2 ? `: ${store2}` : "."}`;
  return error === "invalid_grant" ? new InvalidGrantError() : error === "invalid_request" ? new InvalidRequestError() : error === "invalid_target" ? new InvalidTargetError(invalidTargetErrorMessage, "", [
    "Ensure you have logged in to the store using the Shopify admin at least once.",
    "Ensure you are the store owner, or have a staff account if you are attempting to log in to a development store.",
    "Ensure you are using the permanent store domain, not a vanity domain."
  ]) : new AbortError(error);
}
async function tokenRequest(params) {
  let fqdn = await identityFqdn(), url3 = new URL(`https://${fqdn}/oauth/token`);
  url3.search = new URLSearchParams(Object.entries(params)).toString();
  let res = await shopifyFetch(url3.href, { method: "POST" }), payload = await res.json();
  return res.ok ? ok(payload) : err({ error: payload.error, store: params.store });
}
function buildIdentityToken(result, existingUserId) {
  let userId2 = existingUserId ?? (result.id_token ? decodeJwt(result.id_token).sub : void 0);
  if (!userId2)
    throw new BugError("Error setting userId for session. No id_token or pre-existing user ID provided.");
  return {
    accessToken: result.access_token,
    refreshToken: result.refresh_token,
    expiresAt: new Date(Date.now() + result.expires_in * 1e3),
    scopes: result.scope.split(" "),
    userId: userId2
  };
}
function buildApplicationToken(result) {
  return {
    accessToken: result.access_token,
    expiresAt: new Date(Date.now() + result.expires_in * 1e3),
    scopes: result.scope.split(" ")
  };
}

// ../cli-kit/dist/public/node/session.js
async function ensureAuthenticatedUser(env = process.env, options2 = {}) {
  return outputDebug(outputContent`Ensuring that the user is authenticated with no particular scopes`), { userId: (await ensureAuthenticated({}, env, options2)).userId };
}
async function ensureAuthenticatedPartners(scopes = [], env = process.env, options2 = {}) {
  outputDebug(outputContent`Ensuring that the user is authenticated with the Partners API with the following scopes:
${outputToken.json(scopes)}
`);
  let envToken = getPartnersToken();
  if (envToken) {
    let result = await exchangeCustomPartnerToken(envToken);
    return { token: result.accessToken, userId: result.userId };
  }
  let tokens = await ensureAuthenticated({ partnersApi: { scopes } }, env, options2);
  if (!tokens.partners)
    throw new BugError("No partners token found after ensuring authenticated");
  return { token: tokens.partners, userId: tokens.userId };
}
async function ensureAuthenticatedAppManagementAndBusinessPlatform(options2 = {}, appManagementScopes = [], businessPlatformScopes = [], env = process.env) {
  outputDebug(outputContent`Ensuring that the user is authenticated with the App Management API with the following scopes:
${outputToken.json(appManagementScopes)}
`);
  let envToken = getPartnersToken();
  if (envToken) {
    let appManagmentToken = await exchangeCliTokenForAppManagementAccessToken(envToken), businessPlatformToken = await exchangeCliTokenForBusinessPlatformAccessToken(envToken);
    return {
      appManagementToken: appManagmentToken.accessToken,
      userId: appManagmentToken.userId,
      businessPlatformToken: businessPlatformToken.accessToken
    };
  }
  let tokens = await ensureAuthenticated({ appManagementApi: { scopes: appManagementScopes }, businessPlatformApi: { scopes: businessPlatformScopes } }, env, options2);
  if (!tokens.appManagement || !tokens.businessPlatform)
    throw new BugError("No App Management or Business Platform token found after ensuring authenticated");
  return {
    appManagementToken: tokens.appManagement,
    userId: tokens.userId,
    businessPlatformToken: tokens.businessPlatform
  };
}
async function ensureAuthenticatedStorefront(scopes = [], password = void 0, options2 = {}) {
  if (password) {
    let authMethod2 = isThemeAccessSession({ token: password, storeFqdn: "" }) ? "theme_access_token" : "custom_app_token";
    return setLastSeenAuthMethod(authMethod2), setLastSeenUserIdAfterAuth(nonRandomUUID(password)), password;
  }
  outputDebug(outputContent`Ensuring that the user is authenticated with the Storefront API with the following scopes:
${outputToken.json(scopes)}
`);
  let tokens = await ensureAuthenticated({ storefrontRendererApi: { scopes } }, process.env, options2);
  if (!tokens.storefront)
    throw new BugError("No storefront token found after ensuring authenticated");
  return tokens.storefront;
}
async function ensureAuthenticatedAdmin(store2, scopes = [], options2 = {}) {
  outputDebug(outputContent`Ensuring that the user is authenticated with the Admin API with the following scopes for the store ${outputToken.raw(store2)}:
${outputToken.json(scopes)}
`);
  let tokens = await ensureAuthenticated({ adminApi: { scopes, storeFqdn: store2 } }, process.env, {
    ...options2
  });
  if (!tokens.admin)
    throw new BugError("No admin token found after ensuring authenticated");
  return tokens.admin;
}
async function ensureAuthenticatedThemes(store2, password, scopes = [], options2 = {}) {
  if (outputDebug(outputContent`Ensuring that the user is authenticated with the Theme API with the following scopes:
${outputToken.json(scopes)}
`), password) {
    let session = { token: password, storeFqdn: store2 }, authMethod2 = isThemeAccessSession(session) ? "theme_access_token" : "custom_app_token";
    return setLastSeenAuthMethod(authMethod2), setLastSeenUserIdAfterAuth(nonRandomUUID(password)), session;
  }
  return ensureAuthenticatedAdmin(store2, scopes, options2);
}
async function ensureAuthenticatedBusinessPlatform(scopes = []) {
  outputDebug(outputContent`Ensuring that the user is authenticated with the Business Platform API with the following scopes:
${outputToken.json(scopes)}
`);
  let tokens = await ensureAuthenticated({ businessPlatformApi: { scopes } }, process.env);
  if (!tokens.businessPlatform)
    throw new BugError("No business-platform token found after ensuring authenticated");
  return tokens.businessPlatform;
}
function logout() {
  return remove();
}

// ../cli-kit/dist/private/node/analytics.js
init_cjs_shims();
async function startAnalytics({ commandContent, args, currentTime = (/* @__PURE__ */ new Date()).getTime(), commandClass }) {
  let startCommand = commandContent.command;
  commandClass && Object.prototype.hasOwnProperty.call(commandClass, "analyticsNameOverride") && (startCommand = commandClass.analyticsNameOverride() ?? commandContent.command);
  let pluginName = commandClass?.plugin?.name;
  commandClass && "customPluginName" in commandClass && (pluginName = commandClass.customPluginName), await addSensitiveMetadata(() => ({
    commandStartOptions: {
      startTime: currentTime,
      startCommand,
      startArgs: args
    }
  })), await addPublicMetadata(() => ({
    cmd_all_launcher: packageManagerFromUserAgent(),
    cmd_all_alias_used: commandContent.alias,
    cmd_all_topic: commandContent.topic,
    cmd_all_plugin: pluginName,
    cmd_all_force: flagIncluded("force", commandClass) ? args.includes("--force") : void 0
  }));
}
async function getEnvironmentData(config) {
  let ciplatform = ciPlatform(), pluginNames = getPluginNames(config), shopifyPlugins = pluginNames.filter((plugin) => plugin.startsWith("@shopify/")), { platform, arch } = platformAndArch();
  return {
    uname: `${platform} ${arch}`,
    env_ci: ciplatform.isCI,
    env_ci_platform: ciplatform.name,
    env_plugin_installed_any_custom: pluginNames.length !== shopifyPlugins.length,
    env_plugin_installed_shopify: JSON.stringify(shopifyPlugins),
    env_shell: config.shell,
    env_web_ide: cloudEnvironment().editor ? cloudEnvironment().platform : void 0,
    env_device_id: hashString(await macAddress()),
    env_cloud: cloudEnvironment().platform,
    env_package_manager: await getPackageManager(cwd()),
    env_is_global: currentProcessIsGlobal(),
    env_auth_method: await getLastSeenAuthMethod(),
    env_is_wsl: await isWsl(),
    env_build_repository: "Shopify/cli"
  };
}
async function getSensitiveEnvironmentData(config) {
  return {
    env_plugin_installed_all: JSON.stringify(getPluginNames(config))
  };
}
function getPluginNames(config) {
  return [...config.plugins.keys()].sort().filter((plugin) => !plugin.startsWith("@oclif/"));
}
function flagIncluded(flag, commandClass) {
  if (!commandClass)
    return !1;
  let commandFlags = commandClass.flags ?? {};
  return Object.keys(commandFlags).includes(flag);
}

export {
  serviceEnvironment,
  isLocalEnvironment,
  DevServerCore,
  partnersFqdn,
  adminFqdn,
  appManagementFqdn,
  appDevFqdn,
  developerDashboardFqdn,
  businessPlatformFqdn,
  identityFqdn,
  normalizeStoreFqdn,
  ok,
  err,
  fetch,
  getSessionAlias,
  findSessionByAlias,
  isThemeAccessSession,
  ensureAuthenticatedUser,
  ensureAuthenticatedPartners,
  ensureAuthenticatedAppManagementAndBusinessPlatform,
  ensureAuthenticatedStorefront,
  ensureAuthenticatedAdmin,
  ensureAuthenticatedThemes,
  ensureAuthenticatedBusinessPlatform,
  logout,
  require_cjs,
  require_baseAssignValue,
  require_assignValue,
  require_getPrototype,
  require_getSymbolsIn,
  require_keysIn,
  require_getAllKeysIn,
  deepMergeObjects,
  pickBy,
  deepCompare,
  deepDifference,
  getPathValue,
  setPathValue,
  isEmpty,
  compact,
  requestIdsCollection,
  graphqlRequest,
  graphqlRequestDoc,
  addCursorAndFiltersToAppLogsUrl,
  getNextDeprecationDate,
  setNextDeprecationDate,
  require_lib,
  partnersRequest,
  generateFetchAppLogUrl,
  partnersRequestDoc,
  businessPlatformRequest,
  businessPlatformRequestDoc,
  businessPlatformOrganizationsRequest,
  businessPlatformOrganizationsRequestDoc,
  getLastSeenUserIdAfterAuth,
  startAnalytics,
  getEnvironmentData,
  getSensitiveEnvironmentData
};
//# sourceMappingURL=chunk-ZAVXS5HH.js.map
