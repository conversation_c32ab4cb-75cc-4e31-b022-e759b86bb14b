import {DataType} from '@shopify/shopify-api';
import {ShopGqlWorker} from '../gql/ShopGqlWorker.js';
import { ShopSettingDBWorker } from '../db/ShopSettingDBWorker.js';
import { ServiceAccountDBWorker } from '../db/ServiceAccountDBWorker.js';
import { IndexDBWorker } from '../db/IndexDBWorker.js';


import  crypto from 'crypto';
import { ServiceAccountHandler } from './ServiceAccountHandler.js';
import { ServiceAccountGscWorker } from '../gsc/ServiceAccountGscWorker.js';


// 示例的原始数据和密钥
const secretKey = 'fd90508065bb8d74d464c3b5eb657793'; // 密钥必须是长度合适的字符串，如16字节、32字节等



export class ShopHandler {


    // 加密函数
    static encrypt(data) {
        const cipher = crypto.createCipher('aes256', secretKey);  
        var encrypted = cipher.update(data, 'utf8', 'hex') + cipher.final('hex');
        return encrypted
    }

    // 解密函数

    static decrypt(data) {

        const decipher = crypto.createDecipher('aes256', secretKey);
        
        var decrypted = decipher.update(data, 'hex', 'utf8') + decipher.final('utf8');
        return decrypted;
    }

    static async getShopSetting(session) {

        try{
            const myShopID= await ShopHandler.getShopIDbySession(session);
            const result=await ShopSettingDBWorker.getSetting(myShopID)[0];
            return result[0];

        }catch (error){
            console.error(error);
            return null
        }
        
    }

    static async consoleMetris(session) {

        try{
            const myShopID= await ShopHandler.getShopIDbySession(session);
            const result=await IndexDBWorker.consoleMetris(myShopID);
            return result;

        }catch (error){
            console.error(error);
            return null;
        }
        
    }

    static async getShopIDbySession(session) {

        try{
            const myShop=session.shop;
            console.log(myShop);
            let myShopID=await ShopSettingDBWorker.getShopID(myShop);
            console.log(myShopID);
            if(myShopID.length>0){
                return myShopID[0].shopID;
            }else{
                myShopID=await ShopGqlWorker.getShopIDbySession(session);

                await ShopSettingDBWorker.insertShopID(myShop,myShopID);
                return myShopID;
            }
            
        }catch(error){
            console.error(`getShopIDbySession Error:${error}`);
            console.error(`shop:${session.shop}`);   
            return null;
        }
    }

    static async checkShopStatus(session) {

        const myShopID=await ShopHandler.getShopIDbySession(session);
        console.log(myShopID);
        const privateShop= ShopHandler.encrypt(myShopID);
        const myToken=await global.gscAuthWorker.getOauthToken(privateShop); 



        let remainIndexToday;


        try{ 
            const serviceAccount= await ServiceAccountDBWorker.getServiceAccounts(myShopID);

            if(serviceAccount.length>0 ){ //先判断是否有Service，如果有ServicesAccount，就判断是否有网站权限
                const workReady=await ServiceAccountHandler.checkWorkReadyLocal(session);

                if(workReady===true){
                        const currentDate = new Date();
                        const countDate = new Date(serviceAccount[0].countDate);
                        countDate.setHours(countDate.getHours() + 8); // 假设是将 UTC 时间转为 UTC+8

                        let differenceInDays = currentDate.getDate() - countDate.getDate();
                        
                        if (differenceInDays != 0) {
                            await ServiceAccountDBWorker.UpdateCount(myShopID, serviceAccount[0].clientMail, 200);
                        }
                        
                        remainIndexToday=serviceAccount[0].dailyCount;

                    }else{
                        remainIndexToday= -999; //没有网站权限 
                    }
            }else{ //如果没有ServiceAccount，就先判断是否有拿到OauthToken，如果拿到了就判断是否有云权限，有就尝试初始化Services；如果没OauthToken，就返回前端
                if(myToken){
                        // 检查global.stopCreateService中是否存在当前privateShop
                        const stopCreateTime = global.stopCreateService.get(privateShop);
                        console.log(`Checking stopCreateService for ${privateShop}: ${stopCreateTime}`);

                        if (stopCreateTime) {
                            // 验证时间是否已过去10秒
                            const currentTime = new Date();
                            const stopTime = new Date(stopCreateTime);
                            const timeDifference = (currentTime - stopTime) / 1000; // 转换为秒

                            if (timeDifference >= 10) {
                                // 时间已过去10秒，移除记录并执行createServiceAccount
                                global.stopCreateService.delete(privateShop);
                                console.log(`Removed ${privateShop} from stopCreateService after ${timeDifference} seconds`);

                                const clientID=await ServiceAccountHandler.createServiceAccount(privateShop);
                                if(clientID===null || clientID===undefined){
                                    remainIndexToday=-666; //创建ServiceAccount失败,从Oauth开始
                                    global.gscAuthWorker.removeOauthToken(privateShop);

                                }
                                else if(clientID=='NoAgreement'){
                                    remainIndexToday=-777; //没有修改云的权限 -777
                                }
                                else {
                                    remainIndexToday=200; //成功创建ServiceAccount，设置默认值
                                }
                            } else { 
                                // 时间未过去10秒，直接返回-666
                                console.log(`${privateShop} in stopCreateService, waiting ${10 - timeDifference} more seconds`);
                                remainIndexToday=-666;
                            }
                        } else {
                            // stopCreateService中没有记录，直接执行createServiceAccount
                            console.log(`Try to create Service Account for ${privateShop}`);

                            const clientID=await ServiceAccountHandler.createServiceAccount(privateShop);
                            if(clientID===null || clientID===undefined){
                                remainIndexToday=-666; //创建ServiceAccount失败,从Oauth开始
                            }
                            else if(clientID=='NoAgreement'){
                                remainIndexToday=-777; //没有修改云的权限 -777 
                            }
                            else {
                                remainIndexToday=200; //成功创建ServiceAccount，设置默认值
                            }
                        }

                }else{
                    remainIndexToday=-666; //没有OauthToken
                }
            }

            const shopSettings = await ShopSettingDBWorker.getSetting(myShopID);
            const isInitializing = shopSettings.length > 0 ? shopSettings[0].isInitializing : 1;

            let mybill=await ShopSettingDBWorker.getBillPlan(myShopID);
            if (mybill.length===0){

                ShopHandler.initateShop(myShopID,session);
                mybill=["Free"];
            } 

            // 获取初始化状态
            
            console.log("mybill:"+JSON.stringify(mybill[0]));
            // 获取商店的主域名而不是session.shop
            const myShop = await ShopHandler.getShopDomains(session);
            if (mybill[0].billPlan=='Monthly-A'){
                const currentDate = new Date();

                // 将日期字符串转换为日期对象
                const billDate = new Date(mybill[0].billDate);

                // 计算日期差距
                const differenceInTime = currentDate.getTime() - billDate.getTime();
                const differenceInDays = differenceInTime / (1000 * 3600 * 24);

                // 判断是否在31天之前【改逻辑删除，默认Shopify会自动更新订阅，但不会通过webhook更新】
                //if (differenceInDays> 31){
                //    mybill[0].billPlan='Free';
                //    await ShopSettingDBWorker.setBillPlan(myShopID, 'Free');
                //}
            }

            

            //dev-test
            return {billPlan:mybill[0].billPlan, remainIndexToday:remainIndexToday, shop:myShop, privateShop:privateShop, isInitializing:isInitializing};
            //return {billPlan:mybill[0].billPlan, remainIndexToday:remainIndexToday,shop:myShop,privateShop:privateShop};

        }catch(error){
            console.error(`checkShopStatus Error:${error}`);
            console.error(`shop:${session.shop}`);   
            // 尝试获取shop域名，如果失败则使用session.shop作为fallback
            let errorShop = 'error'; 
            try {
                errorShop = await ShopHandler.getShopDomains(session) || session.shop;
            } catch (domainError) {
                errorShop = session.shop || 'error';
            }
            return {billPlan:'error', remainIndexToday:-888, shop:errorShop, privateShop:'error', isInitializing:isInitializing};
        }
        
    }

    static async getURLsByComponent (session,Component,ComponentID) { 
        try{
            const myShopID=await ShopHandler.getShopIDbySession(session);
            const urls=await IndexDBWorker.getURLsByComponent(myShopID,Component,ComponentID);
            return urls;
        } catch(error){

            console.error(`getURLsByComponent Error:${error}`);
            console.error(`shop:${session.shop}`);   
            console.error(`Component:${Component}`);   
            console.error(`ComponentID:${ComponentID}`);   

            return [];
        }

    };   

    static async makeComponentURL(session,component,handle){
        const myDomain=await ShopGqlWorker.getShopDomains(session);
        const myHandle=handle;
        let midPath='';

        switch (component) {
            case 'Blog':
                midPath=`${myDomain}/blogs/${myHandle}`;
                break;
            case 'Product':
                midPath=`${myDomain}/products/${myHandle}`;
                break;
            case 'Page':
                midPath=`${myDomain}/pages/${myHandle}`;
                break;
            case 'Collection':
                midPath=`${myDomain}/collections/${myHandle}`;
                break;

            default:
              break;  
          }
        return midPath;
    }

    // SHOP_ID TEXT PRIMARY KEY,
    // COMPONENT_ID TEXT,
    // COMPONENT TEXT,
    // HANDLE TEXT,
    // URL TEXT,
    // INX_STS INTEGER DEFAULT 0,

    static async urlScan(currentSession){
        //考虑处理页面数量太大的问题
        //考虑后台异步的问题
        console.log("Start Shop URL Scan");
        try{
            
            let myComponents=[];
            let tempComponents;

            tempComponents=await ShopGqlWorker.getProducts(currentSession);


            if (tempComponents){
                 myComponents=myComponents.concat(tempComponents);
            }

            tempComponents=await ShopGqlWorker.getBlogs(currentSession);

            if (tempComponents){

                myComponents=myComponents.concat(tempComponents);
            }

            tempComponents=await ShopGqlWorker.getCollections(currentSession);

            if (tempComponents){
                myComponents=myComponents.concat(tempComponents);


            }

            tempComponents=await ShopGqlWorker.getPages(currentSession);

            if (tempComponents){
                myComponents=myComponents.concat(tempComponents);

            }

 
            for (const obj of myComponents) {
                if(obj.url===null || obj.url===undefined){
                    const myURL=await ShopHandler.makeComponentURL(currentSession,obj.component,obj.handle);
                    obj.url=myURL;
                }
                await IndexDBWorker.InsertIndex(obj.shopID, obj.componentID, obj.component, obj.title, obj.url, 0);
            }
            return true; 
  
        }catch(error){
            console.error(`urlScan Error:${error}`);
            console.error(`shop:${currentSession.shop}`); 
            return false;
        }
    }

    static async initateShop(shopID,session){
        try{
            await ShopSettingDBWorker.insertShop(shopID);
            // 设置初始化状态为True
            await ShopSettingDBWorker.setInitializingStatus(shopID, true);
            await ShopHandler.urlScan(session);
            // 初始化完成，设置状态为False
            await ShopSettingDBWorker.setInitializingStatus(shopID, false);
        }catch(error){
            console.error(`initateShop Error:${error}`);
            console.error(`shop:${session.shop}`);             
            // 出错时也要重置初始化状态
            await ShopSettingDBWorker.setInitializingStatus(shopID, false);
        }
    }
    static async getConfigs(session){

        try{
            const myShopID=await ShopHandler.getShopIDbySession(session);
            const result = await ShopSettingDBWorker.getSetting(myShopID);
    
            return result[0];
        }catch(error){
            console.error(`getConfigs Error:${error}`);
            console.error(`shop:${session.shop}`);   
            return null;
        }

 
    }

    static async setConfig(session,prodEnb,blogEnb,collnEnb,pageEnb){

        try{
            const myShopID=await ShopHandler.getShopIDbySession(session);
            const result = await ShopSettingDBWorker.UpdateSetting( myShopID,prodEnb,blogEnb,collnEnb,pageEnb);
            return result !== 0;
        }catch(error){
            console.error(`setConfig Error:${error}`);
            console.error(`shop:${session.shop}`);  
            return false;
        }
       
    }

    static async getShopDomains(session){

        try{
            const domains = await ShopGqlWorker.getShopDomains(session);

            return domains;
        }catch(error){
            console.error(`getShopDomains Error:${error}`);
            console.error(`shop:${session.shop}`);  
            return null;
        }


    }


    static async billPaid(session,billplan){

        try{
            const myShopID=await ShopHandler.getShopIDbySession(session);
            const result=await ShopSettingDBWorker.setBillPlan( myShopID,billplan);
            
            return result !== 0;

    
        }catch(error){
            console.error(error);
            console.error(`billPaid Error:${error}`);
            console.error(`shop:${session.shop}`);  
            console.error(`billplan:${billplan}`);  
            return false;
        }
    }

    static async removeShop(session){

        try{
            if (session) {
                const myShopID = await ShopHandler.getShopIDbySession(session);
                
                if (myShopID) {
                    // 清空 shop_setting 表中的数据
                    await global.DBconnecter.executeQuery(
                    'DELETE FROM shop_setting WHERE shop_id = ?',
                    [myShopID]
                    );

                    // 清空 shop_id_map 表中的数据
                    await global.DBconnecter.executeQuery(
                    'DELETE FROM shop_id_map WHERE shop_id = ?',
                    [myShopID]
                    );

                    // 清空 index_log 表中的数据
                    await global.DBconnecter.executeQuery(
                    'DELETE FROM shop_index WHERE shop_id = ?',
                    [myShopID]
                    );

                    console.log(`App uninstalled: Cleared all data for shop ${myShopID}`);
                }
            }
        }catch(error){
            console.error('Error handling app uninstall:', error);
        }
         
    }

    

}
