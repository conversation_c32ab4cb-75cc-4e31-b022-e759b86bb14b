import {
  H2O_BINDING_NAME,
  SUBREQUEST_PROFILER_ENDPOINT,
  TUNNEL_DOMAIN,
  createLogRequestEvent,
  getDebugBannerLine,
  getUtilityBannerlines,
  handleDebugNetworkRequest,
  handleMiniOxygenImportFail,
  importLocal,
  logRequestLine,
  setConstructors
} from "./chunk-XTCT7ABH.js";
import "./chunk-EFOOQV72.js";
import "./chunk-CTFDRWUN.js";
import "./chunk-7IK72W75.js";
import "./chunk-2IA24ROR.js";
import "./chunk-VLSFD7SJ.js";
import "./chunk-SHWOPMLQ.js";
import "./chunk-K2VBTQSL.js";
import "./chunk-ZAVXS5HH.js";
import "./chunk-C4XAKIGB.js";
import "./chunk-PUO72IWW.js";
import "./chunk-25IMI7TH.js";
import "./chunk-G2VTHDI5.js";
import "./chunk-WRIQTRQE.js";
import {
  AbortError,
  createFileReadStream,
  outputNewline,
  readFile,
  renderSuccess,
  source_default
} from "./chunk-B36FYNEM.js";
import "./chunk-F7F4BQYW.js";
import "./chunk-UMUTXITN.js";
import "./chunk-UATXMR5F.js";
import "./chunk-B5EXYCV3.js";
import "./chunk-G2ZZKGSV.js";
import "./chunk-75LV6AQS.js";
import "./chunk-UV5N2VL7.js";
import "./chunk-XE5EOEBL.js";
import {
  dirname,
  resolvePath
} from "./chunk-EG6MBBEN.js";
import {
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/@shopify+cli-hydrogen@11.1.3_@graphql-codegen+cli@5.0.4_@parcel+watcher@2.5.1_@types+no_c5825d46f8c022877c16d44d9004dd56/node_modules/@shopify/cli-hydrogen/dist/lib/mini-oxygen/workerd.js
init_cjs_shims();
import { createRequire } from "node:module";
async function startWorkerdServer({
  root,
  appPort,
  inspectorPort: publicInspectorPort,
  assetsPort,
  debug = !1,
  watch = !1,
  buildPathWorkerFile,
  buildPathClient,
  env
}) {
  let { createMiniOxygen, Response } = await importLocal(
    "@shopify/mini-oxygen",
    root
  ).catch(handleMiniOxygenImportFail);
  setConstructors({ Response });
  async function handleCustomerAccountSchema() {
    let filePath = createRequire(import.meta.url).resolve(
      "@shopify/hydrogen/customer-account.schema.json"
    );
    return new Response(createFileReadStream(filePath), {
      headers: { "Content-Type": "application/json" }
    });
  }
  let mainWorkerName = "hydrogen", absoluteBundlePath = resolvePath(root, buildPathWorkerFile), readWorkerFile = () => readFile(absoluteBundlePath).catch((error) => {
    throw new AbortError(
      `Could not read worker file.

` + error.stack,
      "Did you build the project?"
    );
  }), miniOxygen = createMiniOxygen({
    debug,
    port: appPort,
    host: "localhost",
    liveReload: watch,
    requestHook: logRequestLine,
    inspectorPort: publicInspectorPort,
    inspectWorkerName: mainWorkerName,
    assets: { port: assetsPort, directory: buildPathClient },
    workers: [
      {
        name: "hydrogen:middleware",
        modules: !0,
        script: `export default { fetch: (request, env) => {
          const url = new URL(request.url);
          if (url.hostname.endsWith('${TUNNEL_DOMAIN.ORIGINAL}')) {
            url.hostname = url.hostname.replace(
              '${TUNNEL_DOMAIN.ORIGINAL}',
              '${TUNNEL_DOMAIN.REBRANDED}',
            );
          }

          return url.pathname === '${SUBREQUEST_PROFILER_ENDPOINT}'
            ? env.profiler.fetch(url, request)
            : url.pathname === '/graphiql/customer-account.schema.json'
            ? env.assets.fetch(url, request)
            : env.next.fetch(url, request)
          }
        }`,
        serviceBindings: {
          profiler: handleDebugNetworkRequest,
          assets: handleCustomerAccountSchema,
          next: mainWorkerName
        }
      },
      {
        name: mainWorkerName,
        modulesRoot: dirname(absoluteBundlePath),
        modules: [
          {
            type: "ESModule",
            path: absoluteBundlePath,
            contents: await readWorkerFile()
          }
        ],
        bindings: { ...env },
        serviceBindings: {
          [H2O_BINDING_NAME]: createLogRequestEvent({
            transformLocation: () => absoluteBundlePath
          })
        }
      }
    ]
  }), { workerUrl, inspectorUrl } = await miniOxygen.ready;
  return {
    port: Number(workerUrl.port),
    listeningAt: workerUrl.origin,
    reload(nextOptions) {
      return miniOxygen.reload(async ({ workers }) => {
        let mainWorker = workers.find(({ name }) => name === mainWorkerName);
        return Array.isArray(mainWorker.modules) && mainWorker.modules[0] && (mainWorker.modules[0].contents = await readWorkerFile()), nextOptions && (mainWorker.bindings = { ...nextOptions?.env ?? env }), { workers };
      });
    },
    showBanner(options) {
      outputNewline();
      let customSections = [];
      options?.host && customSections.push({ body: getUtilityBannerlines(options.host) }), inspectorUrl && customSections.push({
        body: { warn: getDebugBannerLine(Number(inspectorUrl.port)) }
      }), renderSuccess({
        headline: `${options?.headlinePrefix ?? ""}MiniOxygen (Worker Runtime) ${options?.mode ?? "development"} server running.`,
        body: [
          `View ${options?.appName ? source_default.cyan(options?.appName) : "Hydrogen"} app:`,
          { link: { url: options?.host || workerUrl.origin } }
        ],
        customSections
      }), console.log("");
    },
    async close() {
      await miniOxygen.dispose();
    }
  };
}
export {
  startWorkerdServer
};
//# sourceMappingURL=workerd-KHWXFNPK.js.map
