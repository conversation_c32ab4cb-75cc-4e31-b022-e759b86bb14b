import {
  base_command_default
} from "./chunk-2IA24ROR.js";
import {
  cacheClear
} from "./chunk-G2VTHDI5.js";
import {
  environmentVariables,
  isTruthy
} from "./chunk-B36FYNEM.js";
import {
  require_lib
} from "./chunk-F7F4BQYW.js";
import {
  __toESM,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// src/cli/commands/cache/clear.ts
init_cjs_shims();

// ../cli-kit/dist/public/node/cli.js
init_cjs_shims();

// ../cli-kit/dist/public/node/cli-launcher.js
init_cjs_shims();
import { fileURLToPath } from "node:url";
async function launchCLI(options) {
  let { errorHandler } = await import("./error-handler-JJ76ZQMC.js"), { isDevelopment } = await import("./local-2JWTG2IJ.js"), oclif = await import("./lib-DEEC6IKU.js"), { ShopifyConfig } = await import("./custom-oclif-loader-J2DN7UTY.js");
  isDevelopment() && (oclif.default.settings.debug = !0);
  try {
    let config = new ShopifyConfig({ root: fileURLToPath(options.moduleURL) });
    await config.load(), await oclif.default.run(options.argv, config), await oclif.default.flush();
  } catch (error) {
    return await errorHandler(error), oclif.default.Errors.handle(error);
  }
}

// ../cli-kit/dist/public/node/cli.js
var import_core = __toESM(require_lib());
async function exitIfOldNodeVersion(versions = process.versions) {
  let nodeVersion = versions.node, nodeMajorVersion = Number(nodeVersion.split(".")[0]);
  if (nodeMajorVersion < 18) {
    let { renderError } = await import("./ui-HITLA3H3.js");
    renderError({
      headline: "Upgrade to a supported Node version now.",
      body: [
        `Node ${nodeMajorVersion} has reached end-of-life and poses security risks. When you upgrade to a`,
        {
          link: {
            url: "https://nodejs.dev/en/about/previous-releases",
            label: "supported version"
          }
        },
        { char: "," },
        "you'll be able to use Shopify CLI without interruption."
      ]
    }), process.exit(1);
  }
}
function setupEnvironmentVariables(options, argv = process.argv, env = process.env) {
  argv.includes("--verbose") && (env.DEBUG = env.DEBUG ?? "*"), options.development && (env.SHOPIFY_CLI_ENV = env.SHOPIFY_CLI_ENV ?? "development");
}
function forceNoColor(argv = process.argv, env = process.env) {
  (argv.includes("--no-color") || isTruthy(env.NO_COLOR) || isTruthy(env.SHOPIFY_FLAG_NO_COLOR) || env.TERM === "dumb") && (env.FORCE_COLOR = "0");
}
async function runCLI(options, launchCLI2 = launchCLI, argv = process.argv, env = process.env, versions = process.versions) {
  return setupEnvironmentVariables(options, argv, env), options.runInCreateMode && await addInitToArgvWhenRunningCreateCLI(options, argv), forceNoColor(argv, env), await exitIfOldNodeVersion(versions), launchCLI2({ moduleURL: options.moduleURL });
}
async function addInitToArgvWhenRunningCreateCLI(options, argv = process.argv) {
  let { findUpAndReadPackageJson } = await import("./node-package-manager-YCQL3774.js"), { moduleDirectory } = await import("./path-GB4VIEM6.js"), name = (await findUpAndReadPackageJson(moduleDirectory(options.moduleURL))).content.name.replace("@shopify/create-", "");
  if (argv.findIndex((arg) => arg.includes("init")) === -1) {
    let initIndex2 = argv.findIndex((arg) => arg.match(new RegExp(`bin(\\/|\\\\)+(create-${name}|dev|run)`))) + 1;
    argv.splice(initIndex2, 0, "init");
  }
}
var globalFlags = {
  "no-color": import_core.Flags.boolean({
    hidden: !1,
    description: "Disable color output.",
    env: "SHOPIFY_FLAG_NO_COLOR"
  }),
  verbose: import_core.Flags.boolean({
    hidden: !1,
    description: "Increase the verbosity of the output.",
    env: "SHOPIFY_FLAG_VERBOSE"
  })
}, jsonFlag = {
  json: import_core.Flags.boolean({
    char: "j",
    description: "Output the result as JSON.",
    hidden: !1,
    default: !1,
    env: environmentVariables.json
  })
};
function clearCache() {
  cacheClear();
}

// src/cli/commands/cache/clear.ts
var ClearCache = class extends base_command_default {
  static {
    this.description = "Clear the CLI cache, used to store some API responses and handle notifications status";
  }
  static {
    this.hidden = !0;
  }
  async run() {
    clearCache();
  }
};

export {
  runCLI,
  globalFlags,
  jsonFlag,
  ClearCache
};
//# sourceMappingURL=chunk-A26YXFMX.js.map
