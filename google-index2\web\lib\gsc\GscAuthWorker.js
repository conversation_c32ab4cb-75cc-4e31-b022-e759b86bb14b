import { google } from "googleapis";
import passport from "passport";
import {ShopHandler} from "../handler/ShopHandler.js";
import {OAuth2Strategy} from "passport-google-oauth";
import { ServiceAccountGscWorker } from "./ServiceAccountGscWorker.js";
import {ServiceAccountHandler} from "../handler/ServiceAccountHandler.js";
import { ServiceAccountDBWorker } from "../db/ServiceAccountDBWorker.js";


export class GSCAuthWorker {
  constructor() {
    this.myPassport=passport;
    this.authScope=['https://www.googleapis.com/auth/indexing','https://www.googleapis.com/auth/cloud-platform','profile'];
    this.authScope2=['https://www.googleapis.com/auth/indexing'];

    this.initialize=this.myPassport.initialize();
    this.session=this.myPassport.session();
    this.tokenMem=new Map();


    // 实现 serializeUser 方法
    this.myPassport.serializeUser((user, done) => {

      done(null, user);
    });

    // 实现 deserializeUser 方法
    this.myPassport.deserializeUser((id, done) => {
      // 在这里根据用户ID从数据库中获取用户信息

        done(null, id);
    });

    this.myPassport.use(new OAuth2Strategy({
        clientID: "************-gdfaniqaqg982tp997es38co58j1j1q2.apps.googleusercontent.com",
        clientSecret: "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",
        passReqToCallback: true,
        //callbackURL: "/google/callback",
        callbackURL: "https://app.jindex.org/google/callback",
      }, 

      function(req,accessToken, refreshToken, profile, done) {
        //const userProfile=profile;
        const tokens = accessToken;


        const myPrivateShop=req.query.state;
        console.log(`Gauth myPrivateShop:${myPrivateShop}`);
        const myShop= ShopHandler.decrypt(myPrivateShop);


        this.tokenMem.set(myShop,accessToken);

        done(null, profile);
      }.bind(this)
    ));


  };

  async removeOauthToken(privateShop){
    try{
      const myShopID=ShopHandler.decrypt(privateShop);
      this.tokenMem.delete(myShopID);
    }catch(error){
      console.error(`removeOauthToken: ${error}`);
      console.error(`privateShop: ${privateShop}`);
    }
  };

  async getAccessTokenbySession(shopSession){
    const myShopID=await ShopHandler.getShopIDbySession(shopSession);
    const myServiceAccount=await ServiceAccountDBWorker.getServiceAccounts(myShopID);
    console.log("getAccessTokenbySession:"+JSON.stringify(myServiceAccount));
    console.log("myServiceAccountXXX  :"+myServiceAccount.length);
    if(myServiceAccount.length){
        console.log("1");

          const orignalKey=myServiceAccount[0].privateKey;
                console.log("1");

          const decodedData = Buffer.from(orignalKey, 'base64').toString('utf-8');
                  console.log("2");

      const dataObject = JSON.parse(decodedData);
              console.log("3");

      const privateKey = dataObject.private_key;
              console.log("4");

      console.log(        myServiceAccount[0].clientMail);
            console.log(        privateKey);
            console.log(        this.authScope2);

      const jwtClient = new google.auth.JWT({
        email:myServiceAccount[0].clientMail,
        key:privateKey,
        scopes:this.authScope2,
        }
      );
              console.log("5");
      try{
              const token = await jwtClient.authorize();
              console.log("6");
              console.log("mytoken~"+JSON.stringify(token));
              return token.access_token;

      }catch(error){
          console.log("getAccessTokenbySession error:");
            console.log(error);
            return null;
      }
                    

    }else{
      return null;
    }

    
  }

  async getOauthToken(privateShop){
    try{
      const myShopID=ShopHandler.decrypt(privateShop);
      const myToken=this.tokenMem.get(myShopID);
      if (myToken){
        return myToken;
        
      }else {
        return null;
      }
    }catch(err){
      console.error(`getOauthToken error:${err}`);
      return null;
    }


  }

  passportInit(){
    return this.myPassport.initialize();
  };

  passportSession(){
    return this.myPassport.session();
  };
 

  authCallback(){
    try{

      return this.myPassport.authenticate('google', { 
          failureRedirect: '/',
          successRedirect: '/' 
      });
    }catch(error){
      console.error(`authCallback error:${error}`);
      return null;
    }
  }

   authLogin(privateShop){

    try{
      console.log(`authLogin privateShop:${privateShop}`);
      const myResult= this.myPassport.authenticate('google', { 
        scope : this.authScope,
        session: false,
        state: privateShop,
        accessType: 'online', 
        prompt: 'consent'
      });
      return myResult;
    }catch(error){
      console.error(`authLogin error:${error}`);
      console.error(`privateShop:${privateShop}`);
      return null;
    }

  }

  static async fetchRetry(url, options) {
      let retryCount = 3;
      let response;

      while (retryCount > 0) {
          try {
              console.log(`Requesting URL: ${url}, Option:${options}`);
              response = await fetch(url, options);
              
              if (response.status < 400) {
                  return response; // 成功，直接返回
              }
              
              // 失败时减少重试次数
              retryCount--;
              
              if (retryCount > 0) {
                  console.log(`Request failed with status ${response.status}, retrying... (${retryCount} attempts left)`);
                  await new Promise(resolve => setTimeout(resolve, (3 - retryCount) * 2000));
              }
              
          } catch (err) {
              retryCount--;
              console.log(`Fetch error: ${err.message}, retrying... (${retryCount} attempts left)`);
              
              if (retryCount > 0) {
                  await new Promise(resolve => setTimeout(resolve, (3 - retryCount) * 2000));
              } else {
                  throw err; // 最后一次重试失败，抛出错误
              }
          }
      }

      // 所有重试都失败了，记录错误并返回最后的响应
      if (response && response.status >= 400) {
          try {
              const body = await response.text();
              console.error(`${url}\n ${JSON.stringify(options)}\n Server error code ${response.status}\n${body}`);
          } catch (textError) {
              console.error(`${url}\n ${JSON.stringify(options)}\n Server error code ${response.status}\n[Could not read response body]`);
          }
          return response;
      }
      
      return response;
  }
}
