html {
  font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
  text-size-adjust: 100%;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-size: 26px;
  line-height: normal;
  margin: 0;
  padding: 0;
}

button, input, optgroup, select, textarea {
  font-family: inherit;
}

h1 {
  font-weight: 600;
  font-size: 1em;
}

p {
  font-weight: 400;
}

.body-success {
  color: #F6F6F7;
}

.body-error {
  color: #202223;
}

.app-success {
  width: 100vw;
  height: 100vh;
  background-color: #054A49;
  display: flex;
}

.app-error {
  width: 100vw;
  height: 100vh;
  background-color: #F6F6F7;
  display: flex;
}

.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding-left: 7.5em;
}
