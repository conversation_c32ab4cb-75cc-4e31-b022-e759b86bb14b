import {
  require_glob
} from "./chunk-IG5SOACB.js";
import {
  require_globby,
  require_slash
} from "./chunk-EFOOQV72.js";
import "./chunk-CTFDRWUN.js";
import {
  require_is_glob
} from "./chunk-7IK72W75.js";
import "./chunk-SHWOPMLQ.js";
import {
  require_graceful_fs
} from "./chunk-75LV6AQS.js";
import {
  require_indent_string
} from "./chunk-UV5N2VL7.js";
import "./chunk-XE5EOEBL.js";
import {
  __commonJS,
  __require,
  init_cjs_shims
} from "./chunk-PKR7KJ6P.js";

// ../../node_modules/.pnpm/is-path-cwd@2.2.0/node_modules/is-path-cwd/index.js
var require_is_path_cwd = __commonJS({
  "../../node_modules/.pnpm/is-path-cwd@2.2.0/node_modules/is-path-cwd/index.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var path = __require("path");
    module.exports = (path_) => {
      let cwd = process.cwd();
      return path_ = path.resolve(path_), process.platform === "win32" && (cwd = cwd.toLowerCase(), path_ = path_.toLowerCase()), path_ === cwd;
    };
  }
});

// ../../node_modules/.pnpm/is-path-inside@3.0.3/node_modules/is-path-inside/index.js
var require_is_path_inside = __commonJS({
  "../../node_modules/.pnpm/is-path-inside@3.0.3/node_modules/is-path-inside/index.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var path = __require("path");
    module.exports = (childPath, parentPath) => {
      let relation = path.relative(parentPath, childPath);
      return !!(relation && relation !== ".." && !relation.startsWith(`..${path.sep}`) && relation !== path.resolve(childPath));
    };
  }
});

// ../../node_modules/.pnpm/rimraf@3.0.2/node_modules/rimraf/rimraf.js
var require_rimraf = __commonJS({
  "../../node_modules/.pnpm/rimraf@3.0.2/node_modules/rimraf/rimraf.js"(exports, module) {
    init_cjs_shims();
    var assert = __require("assert"), path = __require("path"), fs = __require("fs"), glob;
    try {
      glob = require_glob();
    } catch {
    }
    var defaultGlobOpts = {
      nosort: !0,
      silent: !0
    }, timeout = 0, isWindows = process.platform === "win32", defaults = (options) => {
      if ([
        "unlink",
        "chmod",
        "stat",
        "lstat",
        "rmdir",
        "readdir"
      ].forEach((m) => {
        options[m] = options[m] || fs[m], m = m + "Sync", options[m] = options[m] || fs[m];
      }), options.maxBusyTries = options.maxBusyTries || 3, options.emfileWait = options.emfileWait || 1e3, options.glob === !1 && (options.disableGlob = !0), options.disableGlob !== !0 && glob === void 0)
        throw Error("glob dependency not found, set `options.disableGlob = true` if intentional");
      options.disableGlob = options.disableGlob || !1, options.glob = options.glob || defaultGlobOpts;
    }, rimraf = (p, options, cb) => {
      typeof options == "function" && (cb = options, options = {}), assert(p, "rimraf: missing path"), assert.equal(typeof p, "string", "rimraf: path should be a string"), assert.equal(typeof cb, "function", "rimraf: callback function required"), assert(options, "rimraf: invalid options argument provided"), assert.equal(typeof options, "object", "rimraf: options should be object"), defaults(options);
      let busyTries = 0, errState = null, n = 0, next = (er) => {
        errState = errState || er, --n === 0 && cb(errState);
      }, afterGlob = (er, results) => {
        if (er)
          return cb(er);
        if (n = results.length, n === 0)
          return cb();
        results.forEach((p2) => {
          let CB = (er2) => {
            if (er2) {
              if ((er2.code === "EBUSY" || er2.code === "ENOTEMPTY" || er2.code === "EPERM") && busyTries < options.maxBusyTries)
                return busyTries++, setTimeout(() => rimraf_(p2, options, CB), busyTries * 100);
              if (er2.code === "EMFILE" && timeout < options.emfileWait)
                return setTimeout(() => rimraf_(p2, options, CB), timeout++);
              er2.code === "ENOENT" && (er2 = null);
            }
            timeout = 0, next(er2);
          };
          rimraf_(p2, options, CB);
        });
      };
      if (options.disableGlob || !glob.hasMagic(p))
        return afterGlob(null, [p]);
      options.lstat(p, (er, stat) => {
        if (!er)
          return afterGlob(null, [p]);
        glob(p, options.glob, afterGlob);
      });
    }, rimraf_ = (p, options, cb) => {
      assert(p), assert(options), assert(typeof cb == "function"), options.lstat(p, (er, st) => {
        if (er && er.code === "ENOENT")
          return cb(null);
        if (er && er.code === "EPERM" && isWindows && fixWinEPERM(p, options, er, cb), st && st.isDirectory())
          return rmdir(p, options, er, cb);
        options.unlink(p, (er2) => {
          if (er2) {
            if (er2.code === "ENOENT")
              return cb(null);
            if (er2.code === "EPERM")
              return isWindows ? fixWinEPERM(p, options, er2, cb) : rmdir(p, options, er2, cb);
            if (er2.code === "EISDIR")
              return rmdir(p, options, er2, cb);
          }
          return cb(er2);
        });
      });
    }, fixWinEPERM = (p, options, er, cb) => {
      assert(p), assert(options), assert(typeof cb == "function"), options.chmod(p, 438, (er2) => {
        er2 ? cb(er2.code === "ENOENT" ? null : er) : options.stat(p, (er3, stats) => {
          er3 ? cb(er3.code === "ENOENT" ? null : er) : stats.isDirectory() ? rmdir(p, options, er, cb) : options.unlink(p, cb);
        });
      });
    }, fixWinEPERMSync = (p, options, er) => {
      assert(p), assert(options);
      try {
        options.chmodSync(p, 438);
      } catch (er2) {
        if (er2.code === "ENOENT")
          return;
        throw er;
      }
      let stats;
      try {
        stats = options.statSync(p);
      } catch (er3) {
        if (er3.code === "ENOENT")
          return;
        throw er;
      }
      stats.isDirectory() ? rmdirSync(p, options, er) : options.unlinkSync(p);
    }, rmdir = (p, options, originalEr, cb) => {
      assert(p), assert(options), assert(typeof cb == "function"), options.rmdir(p, (er) => {
        er && (er.code === "ENOTEMPTY" || er.code === "EEXIST" || er.code === "EPERM") ? rmkids(p, options, cb) : er && er.code === "ENOTDIR" ? cb(originalEr) : cb(er);
      });
    }, rmkids = (p, options, cb) => {
      assert(p), assert(options), assert(typeof cb == "function"), options.readdir(p, (er, files) => {
        if (er)
          return cb(er);
        let n = files.length;
        if (n === 0)
          return options.rmdir(p, cb);
        let errState;
        files.forEach((f) => {
          rimraf(path.join(p, f), options, (er2) => {
            if (!errState) {
              if (er2)
                return cb(errState = er2);
              --n === 0 && options.rmdir(p, cb);
            }
          });
        });
      });
    }, rimrafSync = (p, options) => {
      options = options || {}, defaults(options), assert(p, "rimraf: missing path"), assert.equal(typeof p, "string", "rimraf: path should be a string"), assert(options, "rimraf: missing options"), assert.equal(typeof options, "object", "rimraf: options should be object");
      let results;
      if (options.disableGlob || !glob.hasMagic(p))
        results = [p];
      else
        try {
          options.lstatSync(p), results = [p];
        } catch {
          results = glob.sync(p, options.glob);
        }
      if (results.length)
        for (let i = 0; i < results.length; i++) {
          let p2 = results[i], st;
          try {
            st = options.lstatSync(p2);
          } catch (er) {
            if (er.code === "ENOENT")
              return;
            er.code === "EPERM" && isWindows && fixWinEPERMSync(p2, options, er);
          }
          try {
            st && st.isDirectory() ? rmdirSync(p2, options, null) : options.unlinkSync(p2);
          } catch (er) {
            if (er.code === "ENOENT")
              return;
            if (er.code === "EPERM")
              return isWindows ? fixWinEPERMSync(p2, options, er) : rmdirSync(p2, options, er);
            if (er.code !== "EISDIR")
              throw er;
            rmdirSync(p2, options, er);
          }
        }
    }, rmdirSync = (p, options, originalEr) => {
      assert(p), assert(options);
      try {
        options.rmdirSync(p);
      } catch (er) {
        if (er.code === "ENOENT")
          return;
        if (er.code === "ENOTDIR")
          throw originalEr;
        (er.code === "ENOTEMPTY" || er.code === "EEXIST" || er.code === "EPERM") && rmkidsSync(p, options);
      }
    }, rmkidsSync = (p, options) => {
      assert(p), assert(options), options.readdirSync(p).forEach((f) => rimrafSync(path.join(p, f), options));
      let retries = isWindows ? 100 : 1, i = 0;
      do {
        let threw = !0;
        try {
          let ret = options.rmdirSync(p, options);
          return threw = !1, ret;
        } finally {
          if (++i < retries && threw)
            continue;
        }
      } while (!0);
    };
    module.exports = rimraf;
    rimraf.sync = rimrafSync;
  }
});

// ../../node_modules/.pnpm/clean-stack@2.2.0/node_modules/clean-stack/index.js
var require_clean_stack = __commonJS({
  "../../node_modules/.pnpm/clean-stack@2.2.0/node_modules/clean-stack/index.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var os = __require("os"), extractPathRegex = /\s+at.*(?:\(|\s)(.*)\)?/, pathRegex = /^(?:(?:(?:node|(?:internal\/[\w/]*|.*node_modules\/(?:babel-polyfill|pirates)\/.*)?\w+)\.js:\d+:\d+)|native)/, homeDir = typeof os.homedir > "u" ? "" : os.homedir();
    module.exports = (stack, options) => (options = Object.assign({ pretty: !1 }, options), stack.replace(/\\/g, "/").split(`
`).filter((line) => {
      let pathMatches = line.match(extractPathRegex);
      if (pathMatches === null || !pathMatches[1])
        return !0;
      let match = pathMatches[1];
      return match.includes(".app/Contents/Resources/electron.asar") || match.includes(".app/Contents/Resources/default_app.asar") ? !1 : !pathRegex.test(match);
    }).filter((line) => line.trim() !== "").map((line) => options.pretty ? line.replace(extractPathRegex, (m, p1) => m.replace(p1, p1.replace(homeDir, "~"))) : line).join(`
`));
  }
});

// ../../node_modules/.pnpm/aggregate-error@3.1.0/node_modules/aggregate-error/index.js
var require_aggregate_error = __commonJS({
  "../../node_modules/.pnpm/aggregate-error@3.1.0/node_modules/aggregate-error/index.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var indentString = require_indent_string(), cleanStack = require_clean_stack(), cleanInternalStack = (stack) => stack.replace(/\s+at .*aggregate-error\/index.js:\d+:\d+\)?/g, ""), AggregateError = class extends Error {
      constructor(errors) {
        if (!Array.isArray(errors))
          throw new TypeError(`Expected input to be an Array, got ${typeof errors}`);
        errors = [...errors].map((error) => error instanceof Error ? error : error !== null && typeof error == "object" ? Object.assign(new Error(error.message), error) : new Error(error));
        let message = errors.map((error) => typeof error.stack == "string" ? cleanInternalStack(cleanStack(error.stack)) : String(error)).join(`
`);
        message = `
` + indentString(message, 4), super(message), this.name = "AggregateError", Object.defineProperty(this, "_errors", { value: errors });
      }
      *[Symbol.iterator]() {
        for (let error of this._errors)
          yield error;
      }
    };
    module.exports = AggregateError;
  }
});

// ../../node_modules/.pnpm/p-map@4.0.0/node_modules/p-map/index.js
var require_p_map = __commonJS({
  "../../node_modules/.pnpm/p-map@4.0.0/node_modules/p-map/index.js"(exports, module) {
    "use strict";
    init_cjs_shims();
    var AggregateError = require_aggregate_error();
    module.exports = async (iterable, mapper, {
      concurrency = 1 / 0,
      stopOnError = !0
    } = {}) => new Promise((resolve, reject) => {
      if (typeof mapper != "function")
        throw new TypeError("Mapper function is required");
      if (!((Number.isSafeInteger(concurrency) || concurrency === 1 / 0) && concurrency >= 1))
        throw new TypeError(`Expected \`concurrency\` to be an integer from 1 and up or \`Infinity\`, got \`${concurrency}\` (${typeof concurrency})`);
      let result = [], errors = [], iterator = iterable[Symbol.iterator](), isRejected = !1, isIterableDone = !1, resolvingCount = 0, currentIndex = 0, next = () => {
        if (isRejected)
          return;
        let nextItem = iterator.next(), index = currentIndex;
        if (currentIndex++, nextItem.done) {
          isIterableDone = !0, resolvingCount === 0 && (!stopOnError && errors.length !== 0 ? reject(new AggregateError(errors)) : resolve(result));
          return;
        }
        resolvingCount++, (async () => {
          try {
            let element = await nextItem.value;
            result[index] = await mapper(element, index), resolvingCount--, next();
          } catch (error) {
            stopOnError ? (isRejected = !0, reject(error)) : (errors.push(error), resolvingCount--, next());
          }
        })();
      };
      for (let i = 0; i < concurrency && (next(), !isIterableDone); i++)
        ;
    });
  }
});

// ../../node_modules/.pnpm/del@6.1.1/node_modules/del/index.js
var require_del = __commonJS({
  "../../node_modules/.pnpm/del@6.1.1/node_modules/del/index.js"(exports, module) {
    init_cjs_shims();
    var { promisify } = __require("util"), path = __require("path"), globby = require_globby(), isGlob = require_is_glob(), slash = require_slash(), gracefulFs = require_graceful_fs(), isPathCwd = require_is_path_cwd(), isPathInside = require_is_path_inside(), rimraf = require_rimraf(), pMap = require_p_map(), rimrafP = promisify(rimraf), rimrafOptions = {
      glob: !1,
      unlink: gracefulFs.unlink,
      unlinkSync: gracefulFs.unlinkSync,
      chmod: gracefulFs.chmod,
      chmodSync: gracefulFs.chmodSync,
      stat: gracefulFs.stat,
      statSync: gracefulFs.statSync,
      lstat: gracefulFs.lstat,
      lstatSync: gracefulFs.lstatSync,
      rmdir: gracefulFs.rmdir,
      rmdirSync: gracefulFs.rmdirSync,
      readdir: gracefulFs.readdir,
      readdirSync: gracefulFs.readdirSync
    };
    function safeCheck(file, cwd) {
      if (isPathCwd(file))
        throw new Error("Cannot delete the current working directory. Can be overridden with the `force` option.");
      if (!isPathInside(file, cwd))
        throw new Error("Cannot delete files/directories outside the current working directory. Can be overridden with the `force` option.");
    }
    function normalizePatterns(patterns) {
      return patterns = Array.isArray(patterns) ? patterns : [patterns], patterns = patterns.map((pattern) => process.platform === "win32" && isGlob(pattern) === !1 ? slash(pattern) : pattern), patterns;
    }
    module.exports = async (patterns, { force, dryRun, cwd = process.cwd(), onProgress = () => {
    }, ...options } = {}) => {
      options = {
        expandDirectories: !1,
        onlyFiles: !1,
        followSymbolicLinks: !1,
        cwd,
        ...options
      }, patterns = normalizePatterns(patterns);
      let files = (await globby(patterns, options)).sort((a, b) => b.localeCompare(a));
      files.length === 0 && onProgress({
        totalCount: 0,
        deletedCount: 0,
        percent: 1
      });
      let deletedCount = 0, removedFiles = await pMap(files, async (file) => (file = path.resolve(cwd, file), force || safeCheck(file, cwd), dryRun || await rimrafP(file, rimrafOptions), deletedCount += 1, onProgress({
        totalCount: files.length,
        deletedCount,
        percent: deletedCount / files.length
      }), file), options);
      return removedFiles.sort((a, b) => a.localeCompare(b)), removedFiles;
    };
    module.exports.sync = (patterns, { force, dryRun, cwd = process.cwd(), ...options } = {}) => {
      options = {
        expandDirectories: !1,
        onlyFiles: !1,
        followSymbolicLinks: !1,
        cwd,
        ...options
      }, patterns = normalizePatterns(patterns);
      let removedFiles = globby.sync(patterns, options).sort((a, b) => b.localeCompare(a)).map((file) => (file = path.resolve(cwd, file), force || safeCheck(file, cwd), dryRun || rimraf.sync(file, rimrafOptions), file));
      return removedFiles.sort((a, b) => a.localeCompare(b)), removedFiles;
    };
  }
});
export default require_del();
//# sourceMappingURL=del-DNZ7X2HW.js.map
